# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 244 à 264
# Type: Méthode de la classe DynamicRangeAdjuster
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, config_path: str = None):
        """
        Initialise l'ajusteur de plages dynamique.

        Args:
            config_path: Chemin vers le fichier config.py. Si None, utilise le chemin par défaut.
        """
        if config_path is None:
            # Utiliser le répertoire courant par défaut
            self.config_path = os.path.join(os.getcwd(), "config.py")
        else:
            self.config_path = config_path

        self.original_ranges = {}  # Stocke les plages originales
        self.adjusted_ranges = {}  # Stocke les plages ajustées
        self.study_lock = None     # Verrou pour l'étude en cours
        self.adjustment_history = []  # Historique des ajustements
        self.last_adjustment_time = 0  # Timestamp du dernier ajustement
        self.adjustment_interval = 60  # Intervalle minimum entre les ajustements (en secondes)

        logger.info(f"DynamicRangeAdjuster initialisé avec config_path: {self.config_path}")