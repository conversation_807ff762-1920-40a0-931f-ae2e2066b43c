# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 2732 à 2788
# Type: Méthode de la classe OptunaOptimizer

    def _export_to_onnx(self, model, output_path, input_shape=None, input_names=None, output_names=None,
                       opset_version=12, target_opset=None, dynamic_axes=None):
        """
        Exporte un modèle optimisé au format ONNX pour le déploiement.
        Cette méthode permet de convertir un modèle entraîné en un format
        standard pour le déploiement sur différentes plateformes.

        Args:
            model: Modèle à exporter (scikit-learn, PyTorch, etc.)
            output_path: Chemin du fichier ONNX de sortie
            input_shape: Forme des données d'entrée (ex: [None, 10] pour des vecteurs de taille 10)
            input_names: Noms des entrées du modèle
            output_names: Noms des sorties du modèle
            opset_version: Version de l'ensemble d'opérations ONNX
            target_opset: Dictionnaire des versions cibles pour des domaines spécifiques
            dynamic_axes: Axes dynamiques pour les entrées/sorties

        Returns:
            str: Chemin du fichier ONNX généré
        """
        import os
        import numpy as np
        import warnings

        # Vérifier que le modèle est valide
        if model is None:
            logger.warning("Aucun modèle fourni pour l'exportation ONNX")
            return None

        # Créer le répertoire parent si nécessaire
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

        # Valeurs par défaut pour les noms d'entrée/sortie
        if input_names is None:
            input_names = ['input']
        if output_names is None:
            output_names = ['output']

        # Déterminer le type de modèle et utiliser la méthode d'exportation appropriée
        model_type = type(model).__module__.split('.')[0]

        if model_type == 'sklearn':
            # Exportation d'un modèle scikit-learn
            return self._export_sklearn_to_onnx(model, output_path, input_shape, input_names, output_names, opset_version)

        elif model_type == 'torch':
            # Exportation d'un modèle PyTorch
            return self._export_pytorch_to_onnx(model, output_path, input_shape, input_names, output_names, opset_version, dynamic_axes)

        elif model_type == 'tensorflow' or model_type == 'keras':
            # Exportation d'un modèle TensorFlow/Keras
            return self._export_tensorflow_to_onnx(model, output_path, input_shape, input_names, output_names, opset_version)

        else:
            # Tentative d'exportation générique
            logger.warning(f"Type de modèle non reconnu: {model_type}. Tentative d'exportation générique.")
            return self._export_generic_to_onnx(model, output_path, input_shape, input_names, output_names, opset_version)