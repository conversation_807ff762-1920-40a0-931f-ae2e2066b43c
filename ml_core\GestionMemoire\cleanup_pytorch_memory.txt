# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 446 à 467
# Type: Méthode de la classe MemoryManager

    def cleanup_pytorch_memory():
        """
        Nettoie la mémoire PyTorch après utilisation.
        Cette fonction doit être appelée à la fin de chaque époque d'entraînement.
        """
        # 1. Forcer la libération de la mémoire inutilisée
        if hasattr(torch.cuda, 'empty_cache'):
            torch.cuda.empty_cache()

        # 2. Réactiver le garbage collector et forcer une collection
        gc.enable()
        gc.collect()

        # 3. Libérer la mémoire des tenseurs inutilisés
        for obj in gc.get_objects():
            try:
                if torch.is_tensor(obj):
                    obj.detach_()
            except:
                pass

        return True