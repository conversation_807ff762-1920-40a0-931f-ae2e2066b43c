DESCRIPTIF DÉTAILLÉ DES MÉTHODES - MODÈLES LSTM
================================================================================

Ce fichier contient la description détaillée des méthodes des modèles LSTM du système models.py.

ÉTAT DOCUMENTATION : PREMIÈRE VAGUE FONCTIONNELLE
- **Couverture** : Méthodes des modèles LSTM documentées
- **Niveau** : Documentation fonctionnelle standardisée
- **Qualité** : 15-25 lignes par méthode minimum

DOMAINE FONCTIONNEL : MODÈLES LSTM
- Architecture LSTM avec attention
- Forward pass et connexions résiduelles
- Couches neuronales et normalisation

================================================================================

1. __init___1.txt (EnhancedLSTMModel.__init__ - INITIALISATION MODÈLE LSTM AVANCÉ)
   - Lignes 72-118 dans models.py (47 lignes)
   - FONCTION : Initialise un modèle LSTM avancé avec mécanisme d'attention, connexions résiduelles et normalisation multicouche pour prédiction Baccarat
   - PARAMÈTRES :
     * self - Instance de EnhancedLSTMModel
     * input_size (int) - Taille des features d'entrée
     * hidden_dim (int) - Dimension des couches cachées LSTM
     * output_size (int) - Taille de sortie (2 pour Player/Banker)
     * num_layers (int) - Nombre de couches LSTM (défaut: 1)
     * dropout_prob (float) - Probabilité dropout général (défaut: 0.2)
     * bidirectional (bool) - LSTM bidirectionnel (défaut: False)
     * use_attention (bool) - Activation mécanisme attention (défaut: True)
     * use_residual (bool) - Activation connexions résiduelles (défaut: True)
     * dropout_input/hidden/output (float) - Dropouts spécialisés par couche
   - FONCTIONNEMENT DÉTAILLÉ :
     * **HÉRITAGE PYTORCH** : `super(EnhancedLSTMModel, self).__init__()` initialisation nn.Module
     * **STOCKAGE ATTRIBUTS** : `self.hidden_dim = hidden_dim`, `self.num_layers = num_layers`, `self.bidirectional = bidirectional`
     * **FLAGS ARCHITECTURE** : `self.use_attention = use_attention`, `self.use_residual = use_residual`
     * **NORMALISATION ENTRÉE** : `self.input_norm = nn.LayerNorm(input_size)` commentaire "stabiliser l'entraînement et réduire la val_loss"
     * **DROPOUT ENTRÉE** : `self.input_dropout = nn.Dropout(dropout_input)` commentaire "modéré pour permettre un bon flux d'information"
     * **LSTM PRINCIPAL** : `self.lstm = nn.LSTM(input_size=input_size, hidden_size=hidden_dim, num_layers=num_layers, batch_first=True, dropout=dropout_hidden if num_layers > 1 else 0, bidirectional=bidirectional)`
     * **CALCUL DIMENSION** : `lstm_output_dim = hidden_dim * 2 if bidirectional else hidden_dim` commentaire "x2 si bidirectionnel"
     * **ATTENTION CONDITIONNELLE** : `if use_attention: self.attention = nn.Sequential(nn.Linear(lstm_output_dim, lstm_output_dim // 2), nn.Tanh(), nn.Linear(lstm_output_dim // 2, 1))`
     * **NORMALISATION COUCHE** : `self.layer_norm = nn.LayerNorm(lstm_output_dim)` commentaire "stabiliser l'entraînement"
     * **COUCHES DENSES** : `self.fc1 = nn.Linear(lstm_output_dim, lstm_output_dim)`, `self.relu = nn.ReLU()`, `self.dropout = nn.Dropout(dropout_output)`
     * **SORTIE FINALE** : `self.fc2 = nn.Linear(lstm_output_dim, output_size)` commentaire "Couche de sortie finale"
     * **DROPOUT CONDITIONNEL** : `dropout=dropout_hidden if num_layers > 1 else 0` pour éviter dropout inutile
   - RETOUR : None (constructeur)
   - UTILITÉ : Méthode fondamentale pour créer un modèle LSTM state-of-the-art. Essentielle pour l'architecture neuronale avancée. Critique pour les performances de prédiction Baccarat.

2. forward.txt (EnhancedLSTMModel.forward - PASSE AVANT LSTM AVEC ATTENTION)
   - Lignes 120-188 dans models.py (69 lignes)
   - FONCTION : Effectue la passe avant du modèle LSTM avec mécanisme d'attention et connexions résiduelles, retournant des logits non normalisés
   - PARAMÈTRES :
     * self - Instance de EnhancedLSTMModel
     * x (torch.Tensor) - Tenseur d'entrée 2D ou 3D (seq_len, features) ou (batch, seq_len, features)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DOCSTRING IMPORTANTE** : Commentaire "retourne des logits (non normalisés)" et "système zero-based standard: Indice 0 = Player, Indice 1 = Banker"
     * **GESTION 2D** : `if x.dim() == 2: x = x.unsqueeze(0)` commentaire "(seq_len, features) -> (1, seq_len, features)"
     * **VALIDATION DIMENSIONS** : `elif x.dim() != 3: raise ValueError(f"Attendu input 2D ou 3D, reçu {x.dim()}D tensor.")`
     * **EXTRACTION MÉTADONNÉES** : `batch_size = x.size(0)`, `seq_len = x.size(1)`, `device = x.device`, `num_directions = 2 if self.bidirectional else 1`
     * **NORMALISATION ENTRÉE** : `x = self.input_norm(x)` commentaire "stabiliser l'entraînement et réduire la val_loss"
     * **DROPOUT ENTRÉE** : `x = self.input_dropout(x)` commentaire "Appliquer le dropout à l'entrée"
     * **INITIALISATION ÉTATS** : `h0 = torch.zeros(self.num_layers * num_directions, batch_size, self.hidden_dim, device=device)` et `c0 = torch.zeros(...)`
     * **PASSE LSTM** : `lstm_out, _ = self.lstm(x, (h0, c0))` commentaire "lstm_out: (batch, seq_len, hidden*directions)"
     * **ATTENTION CONDITIONNELLE** : `if self.use_attention:` puis `attention_scores = self.attention(lstm_out).squeeze(-1)` commentaire "(batch, seq_len)"
     * **SOFTMAX ATTENTION** : `attention_weights = torch.softmax(attention_scores, dim=1)` commentaire "(batch, seq_len)"
     * **APPLICATION ATTENTION** : `attention_weights = attention_weights.unsqueeze(-1)` puis `weighted_output = lstm_out * attention_weights` puis `context_vector = weighted_output.sum(dim=1)`
     * **FALLBACK SANS ATTENTION** : `else: context_vector = lstm_out[:, -1, :]` commentaire "utiliser simplement la dernière sortie"
     * **NORMALISATION COUCHE** : `normalized_out = self.layer_norm(context_vector)`
     * **COUCHE DENSE** : `dense_out = self.fc1(normalized_out)` puis `activated_out = self.relu(dense_out)`
     * **CONNEXION RÉSIDUELLE** : `if self.use_residual: combined_out = activated_out + normalized_out else: combined_out = activated_out`
     * **DROPOUT FINAL** : `dropped_out = self.dropout(combined_out)`
     * **SORTIE LOGITS** : `logits = self.fc2(dropped_out)` commentaire "(batch, output_size)"
   - RETOUR : torch.Tensor - Logits de sortie (batch, output_size) nécessitant softmax pour probabilités
   - UTILITÉ : Méthode centrale pour l'inférence du modèle LSTM. Essentielle pour les prédictions. Critique pour l'architecture avec attention et connexions résiduelles.