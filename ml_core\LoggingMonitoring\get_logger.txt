# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 194 à 212
# Type: Méthode de la classe LoggingManager

    def get_logger(self, name: str, level: Optional[int] = None) -> logging.Logger:
        """
        Récupère un logger avec le nom donné.

        Args:
            name: Nom du logger
            level: Niveau de journalisation (si None, utilise le niveau du logger parent)

        Returns:
            Le logger
        """
        logger = logging.getLogger(name)

        if level is not None:
            logger.setLevel(level)

        self._loggers[name] = logger

        return logger