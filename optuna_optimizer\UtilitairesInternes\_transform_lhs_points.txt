# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 5720 à 5810
# Type: Méthode de la classe OptunaOptimizer

    def _transform_lhs_points(self, points):
        """
        Transforme les points LHS pour améliorer leur distribution.
        Applique des techniques avancées pour optimiser la couverture de l'espace.

        Args:
            points: Tableau numpy de points LHS (n_samples, n_dims)

        Returns:
            numpy.ndarray: Points LHS transformés
        """
        import numpy as np

        # Nombre d'échantillons et de dimensions
        n_samples, n_dims = points.shape

        # Si nous avons peu de points ou de dimensions, retourner les points d'origine
        if n_samples < 5 or n_dims < 2:
            return points

        # 1. Appliquer une transformation pour améliorer la distribution des points
        # Utiliser la méthode de maximin pour maximiser la distance minimale entre les points

        # Calculer la matrice des distances entre tous les points
        distances = np.zeros((n_samples, n_samples))
        for i in range(n_samples):
            for j in range(i+1, n_samples):
                # Distance euclidienne - utiliser une méthode robuste
                try:
                    # Méthode 1: Utiliser np.linalg.norm qui est plus stable numériquement
                    dist = np.linalg.norm(points[i] - points[j])
                except Exception as e:
                    # Méthode 2: Calculer manuellement avec protection contre les dépassements
                    diff = np.clip(points[i] - points[j], -1e6, 1e6)
                    dist = np.sqrt(np.sum(diff * diff))
                distances[i, j] = dist
                distances[j, i] = dist

        # Identifier les points trop proches les uns des autres
        # Créer une copie de la matrice des distances pour éviter de la modifier
        dist_copy = distances.copy()
        # Remplir la diagonale avec une grande valeur finie pour éviter l'avertissement
        np.fill_diagonal(dist_copy, np.finfo(distances.dtype).max)
        min_distances = np.min(dist_copy, axis=1)
        close_points_idx = np.where(min_distances < 0.1 / np.sqrt(n_dims))[0]

        # Perturber légèrement les points trop proches
        if len(close_points_idx) > 0:
            logger.warning(f"Optimisation de {len(close_points_idx)} points LHS trop proches")
            for idx in close_points_idx:
                # Ajouter une petite perturbation aléatoire
                perturbation = np.random.uniform(-0.05, 0.05, n_dims)
                points[idx] = np.clip(points[idx] + perturbation, 0, 1)

        # 2. Optimiser la couverture des bords de l'espace
        # Vérifier si nous avons des points près des bords
        edge_margin = 0.1
        has_points_near_edges = np.any((points < edge_margin) | (points > (1 - edge_margin)))

        if not has_points_near_edges and n_samples > 10:
            # Ajouter quelques points aux coins de l'espace
            logger.warning("Ajout de points aux coins de l'espace pour améliorer la couverture")

            # Sélectionner aléatoirement quelques points à remplacer
            replace_idx = np.random.choice(n_samples, min(4, n_samples // 10), replace=False)

            # Remplacer ces points par des points proches des coins
            for i, idx in enumerate(replace_idx):
                if i == 0 and n_dims >= 1:
                    # Point proche du coin inférieur
                    points[idx] = np.random.uniform(0, 0.2, n_dims)
                elif i == 1 and n_dims >= 2:
                    # Point proche du coin supérieur
                    points[idx] = np.random.uniform(0.8, 1.0, n_dims)
                elif i == 2 and n_dims >= 2:
                    # Point avec un mélange de coordonnées basses et hautes
                    corner = np.zeros(n_dims)
                    corner[::2] = np.random.uniform(0, 0.2, (n_dims + 1) // 2)
                    corner[1::2] = np.random.uniform(0.8, 1.0, n_dims // 2)
                    points[idx] = corner
                elif i == 3 and n_dims >= 2:
                    # Point avec un autre mélange de coordonnées basses et hautes
                    corner = np.zeros(n_dims)
                    corner[::2] = np.random.uniform(0.8, 1.0, (n_dims + 1) // 2)
                    corner[1::2] = np.random.uniform(0, 0.2, n_dims // 2)
                    points[idx] = corner

        # 3. Normaliser les points pour s'assurer qu'ils sont dans [0, 1]
        points = np.clip(points, 0, 1)

        return points