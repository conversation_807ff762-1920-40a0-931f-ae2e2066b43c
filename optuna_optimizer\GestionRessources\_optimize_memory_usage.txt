# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 7000 à 7104
# Type: Méthode de la classe OptunaOptimizer

    def _optimize_memory_usage(self):
        """
        Optimise l'utilisation de la mémoire en libérant les ressources inutilisées.
        Implémente des stratégies avancées de gestion de la mémoire entre les phases d'optimisation.
        """
        import gc
        import sys
        import numpy as np
        import torch
        import os

        logger.warning("Optimisation avancée de la mémoire entre phases...")

        # 1. Libération des caches temporaires
        temp_attributes = [
            '_cached_historical_data',
            '_temp_model_cache',
            '_temp_predictions',
            '_temp_evaluation_results',
            '_temp_feature_importances'
        ]

        for attr in temp_attributes:
            if hasattr(self, attr):
                delattr(self, attr)
                logger.warning(f"Attribut temporaire {attr} libéré")

        # 2. Optimisation des modèles en mémoire
        if hasattr(self, 'model') and self.model is not None:
            try:
                # Tenter de réduire l'empreinte mémoire du modèle
                if hasattr(self.model, 'model_dump'):
                    # Pour LightGBM, on peut sauvegarder et recharger le modèle pour optimiser la mémoire
                    import tempfile
                    with tempfile.NamedTemporaryFile(suffix='.txt', delete=True) as tmp:
                        self.model.booster_.save_model(tmp.name)
                        del self.model
                        gc.collect()
                        self.model = lgb.Booster(model_file=tmp.name)
                        logger.warning("Modèle LightGBM optimisé en mémoire")
            except:
                logger.warning("Impossible d'optimiser le modèle en mémoire")

        # 3. Optimisation des tenseurs PyTorch
        if torch.cuda.is_available():
            # Libérer le cache CUDA
            torch.cuda.empty_cache()
            logger.warning("Cache CUDA vidé")

        # 4. Optimisation des tableaux NumPy
        for attr_name in dir(self):
            if attr_name.startswith('_'):
                continue

            try:
                attr = getattr(self, attr_name)
                if isinstance(attr, np.ndarray) and attr.size > 1000000:  # Tableaux de plus de 1M d'éléments
                    # Vérifier si on peut convertir en type plus compact
                    if attr.dtype == np.float64:
                        setattr(self, attr_name, attr.astype(np.float32))
                        logger.warning(f"Tableau NumPy {attr_name} converti de float64 à float32")
                    elif attr.dtype == np.float32 and np.max(np.abs(attr)) < 65504:
                        setattr(self, attr_name, attr.astype(np.float16))
                        logger.warning(f"Tableau NumPy {attr_name} converti de float32 à float16")
            except:
                pass

        # 5. Forcer plusieurs cycles de collecte des objets non référencés
        gc.collect()
        gc.collect()

        # 6. Compactage de la mémoire (si possible)
        try:
            import ctypes
            ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1)
            logger.warning("Mémoire système compactée")
        except:
            logger.warning("Impossible de compacter la mémoire système")

        # 7. Rapport détaillé sur l'utilisation de la mémoire
        try:
            import psutil
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            memory_usage_process = memory_info.rss / (1024 * 1024 * 1024)  # En GB

            # Mémoire système
            system_memory = psutil.virtual_memory()
            available_memory = system_memory.available / (1024 * 1024 * 1024)  # En GB

            logger.warning(f"Utilisation mémoire du processus après optimisation: {memory_usage_process:.2f} GB")
            logger.warning(f"Mémoire système disponible: {available_memory:.2f} GB")

            # Alerte si la mémoire est critique
            if available_memory < 2:
                logger.warning("ALERTE: Mémoire système critique après optimisation! Risque d'erreur OOM")

        except ImportError:
            # Fallback si psutil n'est pas disponible
            memory_usage = sys.getsizeof(self) / (1024 * 1024)
            logger.warning(f"Mémoire utilisée par l'instance OptunaOptimizer: {memory_usage:.2f} MiB")
            logger.warning("Note: Mesure limitée à l'objet principal, la mémoire totale n'est pas disponible sans psutil")

        logger.warning("Optimisation avancée de la mémoire terminée")
        return True