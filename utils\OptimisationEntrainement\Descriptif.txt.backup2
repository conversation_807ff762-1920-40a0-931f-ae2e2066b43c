DESCRIPTIF DÉTAILLÉ DES MÉTHODES - OPTIMISATION ET ENTRAÎNEMENT
================================================================================

Ce fichier contient la description détaillée des méthodes d'optimisation,
d'entraînement et de configuration des modèles ML.

DOMAINE FONCTIONNEL : Optimisation, entraînement, schedulers et configuration modèles

TOTAL : 7 MÉTHODES ANALYSÉES

================================================================================

1. create_scheduler.txt (create_scheduler - CRÉATION SCHEDULER LEARNING RATE OPTUNA)
   - Lignes 148-221 dans utils.py (74 lignes)
   - FONCTION : Crée scheduler learning rate optimisé pour Optuna avec support multiple types et adaptation époque/batch
   - PARAMÈTRES :
     * optimizer (torch.optim.Optimizer) - Optimiseur à scheduler
     * scheduler_name (str, défaut='cosine') - Type scheduler ('cosine', 'step', 'plateau', 'exponential')
     * **kwargs - Paramètres spécifiques au scheduler
   - FONCTIONNEMENT DÉTAILLÉ :
     * **NORMALISATION :** Convertit scheduler_name en minuscules pour robustesse
     * **COSINE :** CosineAnnealingLR avec T_max et eta_min pour cycles d'apprentissage
     * **STEP :** StepLR avec step_size et gamma pour réduction par paliers
     * **PLATEAU :** ReduceLROnPlateau avec patience et factor pour adaptation métrique
     * **EXPONENTIAL :** ExponentialLR avec gamma pour décroissance exponentielle
     * **GESTION ERREUR :** ValueError si scheduler non supporté avec liste disponibles
     * **LOGGING :** Journalise création scheduler avec paramètres pour suivi
   - RETOUR : torch.optim.lr_scheduler - Instance scheduler configuré
   - UTILITÉ : Factory pattern pour création schedulers avec stratégies d'apprentissage optimales

2. get_criterion.txt (get_criterion - FONCTION CRÉATION CRITÈRE PERTE)
   - Lignes 2072-2113 dans utils.py (42 lignes)
   - FONCTION : Crée fonction de perte configurée selon type spécifié avec support pertes avancées
   - PARAMÈTRES :
     * criterion_name (str, défaut='crossentropy') - Type critère ('crossentropy', 'focal', 'mse', 'mae')
     * **kwargs - Paramètres spécifiques au critère (gamma, alpha, label_smoothing, etc.)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **NORMALISATION :** Convertit criterion_name en minuscules pour robustesse
     * **CROSSENTROPY :** nn.CrossEntropyLoss avec weight et label_smoothing configurables
     * **FOCAL :** FocalLoss personnalisé avec gamma et alpha pour exemples difficiles
     * **MSE :** nn.MSELoss pour régression avec réduction configurable
     * **MAE :** nn.L1Loss pour régression robuste aux outliers
     * **GESTION ERREUR :** ValueError si critère non supporté avec liste disponibles
     * **LOGGING :** Journalise création critère avec paramètres pour débogage
   - RETOUR : nn.Module - Instance critère de perte configuré
   - UTILITÉ : Factory pattern pour création critères avec support pertes spécialisées ML

3. get_optimizer.txt (get_optimizer - FONCTION CRÉATION OPTIMISEUR)
   - Lignes 2029-2070 dans utils.py (42 lignes)
   - FONCTION : Crée optimiseur PyTorch configuré selon paramètres spécifiés avec support multiple algorithmes
   - PARAMÈTRES :
     * model (nn.Module) - Modèle PyTorch pour optimisation
     * optimizer_name (str, défaut='adamw') - Type optimiseur ('adam', 'adamw', 'sgd', 'rmsprop')
     * learning_rate (float, défaut=0.001) - Taux d'apprentissage initial
     * weight_decay (float, défaut=0.01) - Régularisation L2
     * **kwargs - Paramètres additionnels spécifiques à l'optimiseur
   - FONCTIONNEMENT DÉTAILLÉ :
     * **NORMALISATION NOM :** Convertit optimizer_name en minuscules pour robustesse
     * **ADAM :** torch.optim.Adam avec lr, weight_decay et kwargs (betas, eps)
     * **ADAMW :** torch.optim.AdamW optimisé pour transformers avec meilleure régularisation
     * **SGD :** torch.optim.SGD avec support momentum et nesterov via kwargs
     * **RMSPROP :** torch.optim.RMSprop avec alpha et momentum configurables
     * **GESTION ERREUR :** ValueError si optimiseur non supporté avec liste disponibles
     * **LOGGING :** Journalise création optimiseur avec paramètres pour traçabilité
   - RETOUR : torch.optim.Optimizer - Instance optimiseur configuré
   - UTILITÉ : Factory pattern pour création optimiseurs avec configuration flexible et robuste

4. mixup_criterion.txt (mixup_criterion - FONCTION PERTE MIXUP SIMPLE)
   - Lignes 284-298 dans utils.py (15 lignes)
   - FONCTION : Calcule perte pour données MixUp avec combinaison pondérée simple et efficace
   - PARAMÈTRES :
     * criterion (nn.Module) - Fonction de perte à utiliser
     * pred (torch.Tensor) - Prédictions modèle
     * y_a (torch.Tensor) - Premières étiquettes
     * y_b (torch.Tensor) - Secondes étiquettes
     * lam (float) - Coefficient mixage lambda
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PERTE COMPOSÉE :** return lam * criterion(pred, y_a) + (1 - lam) * criterion(pred, y_b)
     * **PONDÉRATION DIRECTE :** Combinaison linéaire simple des deux pertes
     * **EFFICACITÉ :** Implémentation minimaliste pour performance optimale
   - RETOUR : torch.Tensor - Perte mixup scalaire
   - UTILITÉ : Fonction perte MixUp simple et efficace pour entraînement avec données mixées

5. mixup_data.txt (mixup_data - FONCTION AUGMENTATION DONNÉES MIXUP AVANCÉE)
   - Lignes 223-282 dans utils.py (60 lignes)
   - FONCTION : Applique augmentation MixUp avancée avec adaptation automatique et gestion GPU optimisée
   - PARAMÈTRES :
     * x (torch.Tensor) - Données d'entrée batch
     * y (torch.Tensor) - Étiquettes correspondantes
     * alpha (float, défaut=0.2) - Paramètre distribution Beta pour mixage
     * device (torch.device, optionnel) - Device PyTorch (auto-détecté si None)
     * adaptive (bool, défaut=True) - Active mixup adaptatif selon difficulté exemples
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION SÉCURITÉ :** Retourne données originales si batch_size <= 1
     * **GÉNÉRATION LAMBDA :** np.random.beta(alpha, alpha) si alpha > 0, sinon 1.0
     * **MIXUP ADAPTATIF :** Ajuste lambda selon difficulté exemples via variance features
     * **ESTIMATION DIFFICULTÉ :** difficulty = sigmoid(5 * (0.1 - feature_variance.mean()))
     * **AJUSTEMENT LAMBDA :** lam = lam * (1 - difficulty * 0.5) + difficulty * 0.5 pour exemples difficiles
     * **GESTION DEVICE :** Auto-détection device depuis x.device si x.is_cuda
     * **PERMUTATION OPTIMISÉE :** torch.randperm(batch_size).to(device) pour performance GPU
     * **MIXAGE LINÉAIRE :** mixed_x = lam * x + (1 - lam) * x[index, :]
   - RETOUR : Tuple[torch.Tensor, torch.Tensor, torch.Tensor, float] - (mixed_x, y_a, y_b, lam)
   - UTILITÉ : Augmentation données sophistiquée avec adaptation automatique difficulté et optimisation GPU

6. objective_consecutive.txt (objective_consecutive - FONCTION OBJECTIF CONSÉCUTIF OBSOLÈTE)
   - Lignes 3037-3058 dans utils.py (22 lignes)
   - FONCTION : Fonction objectif Phase 2 simplifiée et obsolète pour éviter erreurs référence
   - PARAMÈTRES :
     * trial (optuna.Trial) - Essai Optuna pour optimisation hyperparamètres
     * config - Configuration système avec données et paramètres
   - FONCTIONNEMENT DÉTAILLÉ :
     * **HYPERPARAMÈTRES :** Suggère learning_rate, batch_size, hidden_size via trial
     * **ENTRAÎNEMENT MODÈLE :** Crée et entraîne modèle avec paramètres suggérés
     * **ÉVALUATION PERFORMANCE :** Calcule métriques sur données validation
     * **OBJECTIF SPÉCIALISÉ :** Optimise spécifiquement pour séquences consécutives
     * **PÉNALITÉS WAIT :** Applique malus pour recommandations WAIT excessives
   - RETOUR : float - Score objectif à maximiser (séquences consécutives)
   - UTILITÉ : Fonction objectif Optuna spécialisée pour optimisation objectif 1

7. objective_precision.txt (objective_precision - FONCTION OBJECTIF PRÉCISION AVANCÉE)
   - Lignes 2344-3035 dans utils.py (692 lignes)
   - FONCTION : Fonction objectif Optuna Phase 1 sophistiquée pour optimisation 100% précision NON-WAIT avec gestion viabilité
   - PARAMÈTRES :
     * trial (optuna.Trial) - Essai Optuna pour optimisation hyperparamètres
     * config - Configuration système avec données et paramètres
   - FONCTIONNEMENT DÉTAILLÉ :
     * **HYPERPARAMÈTRES :** Suggère learning_rate, batch_size, hidden_size via trial
     * **ENTRAÎNEMENT MODÈLE :** Crée et entraîne modèle avec paramètres suggérés
     * **ÉVALUATION PERFORMANCE :** Calcule métriques sur données validation
     * **OBJECTIF GÉNÉRAL :** Optimise pour précision globale du système
     * **ÉQUILIBRAGE :** Balance précision et recall pour F1-score optimal
   - RETOUR : float - Score précision à maximiser
   - UTILITÉ : Fonction objectif Optuna pour optimisation performance générale


================================================================================
