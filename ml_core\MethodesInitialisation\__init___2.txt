# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1367 à 1386
# Type: Méthode de la classe ThreadedTrainer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, trainer_instance, callback=None, error_callback=None, progress_callback=None):
        """
        Initialise l'entraîneur threadé.

        Args:
            trainer_instance: Instance de HybridBaccaratPredictor à utiliser pour l'entraînement
            callback: Fonction à appeler lorsque l'entraînement est terminé
            error_callback: Fonction à appeler en cas d'erreur
            progress_callback: Fonction à appeler pour mettre à jour la progression
        """
        self.trainer_instance = trainer_instance
        self.callback = callback
        self.error_callback = error_callback
        self.progress_callback = progress_callback
        self.thread = None
        self.stop_event = threading.Event()
        self.is_running = False
        self.result = None
        self.error = None
        self.start_time = None