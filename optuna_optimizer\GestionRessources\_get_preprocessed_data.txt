# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 6183 à 6209
# Type: Méthode de la classe OptunaOptimizer

    def _get_preprocessed_data(self, subset_size):
        """
        Récupère les données prétraitées pour une taille de sous-ensemble donnée.

        Args:
            subset_size: Taille du sous-ensemble

        Returns:
            tuple: Données prétraitées ou None si non trouvées
        """
        if not hasattr(self, '_advanced_data_cache'):
            self._initialize_advanced_data_cache()

        # Vérifier si le cache est activé
        if not getattr(self.config, 'use_advanced_cache', True):
            self._advanced_data_cache['cache_misses'] += 1
            return None

        # Récupérer les données du cache
        if subset_size in self._advanced_data_cache['preprocessed_data']:
            self._advanced_data_cache['cache_hits'] += 1
            logger.warning(f"Utilisation des données prétraitées en cache pour subset_size={subset_size}")
            return self._advanced_data_cache['preprocessed_data'][subset_size]

        # Données non trouvées dans le cache
        self._advanced_data_cache['cache_misses'] += 1
        return None