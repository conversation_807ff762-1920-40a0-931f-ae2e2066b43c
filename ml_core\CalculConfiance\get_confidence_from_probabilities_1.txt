# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1330 à 1341
# Type: Méthode
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

def get_confidence_from_probabilities(probabilities, predicted_class):
    """
    Obtient la confiance pour une classe prédite à partir des probabilités.

    Args:
        probabilities: Tenseur de probabilités (après softmax)
        predicted_class: Classe prédite (indice 0-based, 0 ou 1)

    Returns:
        float: Confiance pour la classe prédite
    """
    return ConfidenceCalculator.get_confidence_from_probabilities(probabilities, predicted_class)