# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 343 à 352
# Type: Méthode de la classe ModuleInterface

    def register_lazy_loader(self, name: str, loader: Callable) -> None:
        """
        Enregistre un chargeur paresseux qui sera exécuté uniquement lorsque la dépendance sera demandée.

        Args:
            name: Nom de la dépendance à charger paresseusement
            loader: Fonction qui charge la dépendance
        """
        self._lazy_loaders[name] = loader
        logger.debug(f"Chargeur paresseux pour '{name}' enregistré dans l'interface")