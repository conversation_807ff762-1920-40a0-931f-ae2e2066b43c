# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 560 à 567
# Type: Méthode de la classe PyTorchMemoryContext

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        Appelé à la sortie du bloc with.
        Nettoie la mémoire PyTorch si cleanup_on_exit est True.
        """
        if self.cleanup_on_exit:
            MemoryManager.cleanup_pytorch_memory()
        return False  # Ne pas supprimer l'exception si elle existe