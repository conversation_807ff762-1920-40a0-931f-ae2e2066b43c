# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 835 à 952
# Type: Méthode de la classe ConfidenceCalculator

    def train_consecutive_confidence_calculator(sequence_history: List[str],
                                               current_position: int,
                                               config=None) -> Dict[str, float]:
        """
        Calcule la confiance pour les recommandations consécutives basée sur l'historique des séquences.

        Args:
            sequence_history: Historique des résultats (liste de 'banker' ou 'player')
            current_position: Position actuelle dans la séquence
            config: Configuration optionnelle

        Returns:
            Dict[str, float]: Dictionnaire contenant les métriques de confiance
        """
        # Vérifier que nous avons suffisamment d'historique
        if not sequence_history or current_position < 10:
            return {
                "confidence": 0.5,
                "expected_consecutive": 0,
                "similar_patterns_count": 0,
                "success_rate": 0.0,
                "is_target_round": False,
                "wait_recommendation_strength": 0.0,
                "non_wait_recommendation_strength": 0.0,
                "wait_recommendation": True,
                "wait_reason": "Historique insuffisant"
            }

        # Récupérer les paramètres de configuration
        target_round_min = getattr(config, 'target_round_min', 31) if config else 31
        target_round_max = getattr(config, 'target_round_max', 60) if config else 60

        # Vérifier si nous sommes dans les manches cibles
        game_round = current_position + 1  # +1 car les positions commencent à 0
        is_target_round = target_round_min <= game_round <= target_round_max

        # Si nous ne sommes pas dans les manches cibles, retourner une confiance neutre
        if not is_target_round:
            return {
                "confidence": 0.5,  # Confiance neutre hors manches cibles
                "expected_consecutive": 0,
                "similar_patterns_count": 0,
                "success_rate": 0.0,
                "is_target_round": is_target_round,
                "wait_recommendation_strength": 0.0,
                "non_wait_recommendation_strength": 0.0,
                "wait_recommendation": True,
                "wait_reason": "Hors manches cibles"
            }

        # Extraire les N derniers résultats pour l'analyse des motifs
        pattern_length = min(10, current_position)
        current_pattern = sequence_history[current_position - pattern_length:current_position]

        # Rechercher des motifs similaires dans l'historique
        similar_patterns = []
        for i in range(pattern_length, current_position - pattern_length):
            historical_pattern = sequence_history[i - pattern_length:i]

            # Calculer la similarité (nombre de correspondances)
            similarity = sum(1 for a, b in zip(current_pattern, historical_pattern) if a == b)

            # Si la similarité est suffisante (au moins 70%)
            if similarity >= pattern_length * 0.7:
                # Ajouter le résultat qui a suivi ce motif similaire
                similar_patterns.append(sequence_history[i])

        # Calculer les statistiques basées sur les motifs similaires
        similar_patterns_count = len(similar_patterns)

        if similar_patterns_count > 0:
            # Compter les occurrences de 'banker' et 'player'
            banker_count = sum(1 for result in similar_patterns if result == 'banker')
            player_count = similar_patterns_count - banker_count

            # Déterminer la prédiction majoritaire
            majority_prediction = 'banker' if banker_count >= player_count else 'player'

            # Calculer la confiance basée sur la proportion
            confidence = max(banker_count, player_count) / similar_patterns_count

            # Calculer le nombre attendu de recommandations consécutives valides
            # basé sur les séquences historiques similaires
            expected_consecutive = min(5, int(confidence * 5))

            # Calculer la force de la recommandation WAIT ou NON-WAIT
            wait_threshold = getattr(config, 'wait_threshold', 0.65) if config else 0.65

            wait_recommendation = confidence < wait_threshold
            wait_reason = f"Confiance {confidence:.2f} < seuil {wait_threshold:.2f}" if wait_recommendation else "Confiance suffisante"

            wait_recommendation_strength = max(0.0, wait_threshold - confidence) / wait_threshold
            non_wait_recommendation_strength = max(0.0, confidence - wait_threshold) / (1.0 - wait_threshold)

            return {
                "confidence": confidence,
                "expected_consecutive": expected_consecutive,
                "similar_patterns_count": similar_patterns_count,
                "success_rate": confidence,
                "is_target_round": is_target_round,
                "wait_recommendation_strength": wait_recommendation_strength,
                "non_wait_recommendation_strength": non_wait_recommendation_strength,
                "wait_recommendation": wait_recommendation,
                "wait_reason": wait_reason
            }
        else:
            # Pas assez de motifs similaires trouvés
            return {
                "confidence": 0.5,  # Confiance neutre
                "expected_consecutive": 0,
                "similar_patterns_count": 0,
                "success_rate": 0.0,
                "is_target_round": is_target_round,
                "wait_recommendation_strength": 1.0,  # Forte recommandation WAIT
                "non_wait_recommendation_strength": 0.0,
                "wait_recommendation": True,
                "wait_reason": "Pas de motifs similaires"
            }