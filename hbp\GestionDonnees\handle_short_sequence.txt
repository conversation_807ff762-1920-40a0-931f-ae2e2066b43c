# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 5107 à 5233
# Type: Méthode de la classe HybridBaccaratPredictor

    def handle_short_sequence(self, sequence: Optional[List[str]]) -> np.ndarray:
        """Gère les séquences trop courtes pour LSTM en ajoutant un padding intelligent.
           Retourne toujours un array de shape (lstm_sequence_length, lstm_input_size).

        Args:
            sequence: Liste des résultats ('player'/'banker'), potentiellement None ou courte.

        Returns:
            np.ndarray: Array numpy de shape (lstm_sequence_length, lstm_input_size) prêt pour LSTM.
        """
        # Utiliser toujours lstm_sequence_length pour uniformiser le code
        target_length = self.config.lstm_sequence_length
        num_features = self.config.lstm_input_size

        # Vérifier si le modèle LSTM existe et si sa taille configurée correspond
        if self.lstm and hasattr(self.lstm, 'input_size') and self.lstm.input_size != num_features:
             logger.warning(f"handle_short_sequence: Taille input LSTM du modèle ({self.lstm.input_size}) ne correspond pas à la taille cible ({num_features}) selon handle_short_sequence.")

        # 1. Cas séquence vide ou None -> Retourne un padding neutre (zeros)
        if not sequence:
            return np.zeros((target_length, num_features), dtype=np.float32)

        current_length = len(sequence)

        # 2. Cas séquence de longueur exacte ou plus longue -> Extraire les features normalement
        if current_length >= target_length:
             # Utiliser create_hybrid_features pour obtenir la séquence formatée (qui retourne maintenant 12 features)
             _, lstm_features = self.create_hybrid_features(sequence) # Utilise les 'target_length' derniers éléments
             # Vérifier contre 12 features
             if lstm_features is not None and lstm_features.shape == (target_length, num_features):
                 return lstm_features

             else:
                 # S'il échoue ici, create_hybrid_features a déjà loggé l'erreur
                 logger.warning(f"handle_short_sequence: Échec création features pour séquence longue ({current_length}). Retour padding zeros.")
                 return np.zeros((target_length, num_features), dtype=np.float32)

        # 3. Cas séquence courte -> Appliquer le padding (pré-padding)
        num_padding = target_length - current_length
        padding_array = np.zeros((num_padding, num_features), dtype=np.float32)


        # Générer les features pour la partie existante de la séquence
        actual_features_list = []
        for idx in range(current_length):
            outcome = sequence[idx]

            pos_norm = (num_padding + idx) / float(target_length) # Position globale
            is_banker = 1.0 if outcome == 'banker' else 0.0
            sub_sequence = sequence[:idx+1]
            sub_len = len(sub_sequence)
            ratio_banker = sub_sequence.count('banker') / sub_len if sub_len > 0 else 0.0
            ratio_player = 1.0 - ratio_banker
            is_repeat = 1.0 if idx > 0 and sequence[idx] == sequence[idx-1] else 0.0
            recent_3_count_banker = 0.0
            for lookback in range(3):
                 if idx - lookback >= 0 and sequence[idx - lookback] == 'banker':
                      recent_3_count_banker += 1.0
            imbalance = ratio_banker - 0.5
            # Streak features (simple)
            streak_length = 1
            if idx > 0 and sequence[idx] == sequence[idx-1]:
                streak_length_calc = 2 # Minimum len
                for lookback in range(2, idx+1):# On itère en arrière à partir de 2
                    if sequence[idx] == sequence[idx - lookback]:
                        streak_length_calc += 1
                    else: break
                streak_length = streak_length_calc

            seq_odd_even = (idx % 2) * 1.0  # 1 si impair, 0 si pair
            is_streak = 1.0

	     #Assembler les features LSMALL

            # Créer une liste de base avec les features principales
            # Utiliser une liste de features configurables
            feature_list = [
                pos_norm,                # Position normalisée
                is_banker,               # Est-ce un banker (1) ou player (0)
                ratio_banker,            # Ratio banker cumulatif
                ratio_player,            # Ratio player cumulatif
                is_repeat,               # Est-ce une répétition du résultat précédent
                recent_3_count_banker,   # Nombre de banker dans les 3 derniers coups
                imbalance,               # Déséquilibre banker-player
                streak_length,           # Longueur du streak actuel
                seq_odd_even,            # Position paire/impaire
                # Ajouter les 3 features spécifiques à l'optimisation Optuna
                0.5,                     # confidence (valeur par défaut)
                0.5,                     # error_pattern_threshold (valeur par défaut)
                0.5                      # transition_uncertainty_threshold (valeur par défaut)
            ]

            # Récupérer le nombre de features de base à utiliser depuis la configuration
            base_features_count = getattr(self.config, 'lstm_base_features_count', 9)
            # Limiter au nombre de features disponibles
            base_features_count = min(base_features_count, len(feature_list))

            # Utiliser seulement le nombre de features configuré
            current_step_features = feature_list[:base_features_count]

            # Ajouter des zéros pour compléter jusqu'au nombre de features attendu
            zeros_to_add = num_features - len(current_step_features)
            current_step_features.extend([0.0] * zeros_to_add)

            # Vérifier si le nombre de features correspond (maintenant 12)
            if len(current_step_features) != num_features:
                logger.critical(f"ERREUR INTERNE handle_short_sequence: Nombre de features ({len(current_step_features)}) != attendu ({num_features}) pour padding.")
                return np.zeros((target_length, num_features), dtype=np.float32)

            actual_features_list.append(current_step_features)

        # S'assurer que la liste n'est pas vide avant de convertir
        if not actual_features_list:
            logger.warning("handle_short_sequence: Aucune feature réelle générée pour séquence courte. Retour padding zeros.")
            return np.zeros((target_length, num_features), dtype=np.float32)

        actual_features_array = np.array(actual_features_list, dtype=np.float32)

        # Concaténer padding (au début) et features réelles
        padded_sequence_features = np.concatenate((padding_array, actual_features_array), axis=0)

        # Vérification finale de la shape (maintenant contre 12)
        if padded_sequence_features.shape != (target_length, num_features):
             logger.error(f"handle_short_sequence: Erreur shape finale après padding. Attendu {(target_length, num_features)}, Obtenu {padded_sequence_features.shape}. Retour padding zeros.")
             return np.zeros((target_length, num_features), dtype=np.float32)

        return padded_sequence_features