DESCRIPTIF DÉTAILLÉ DES MÉTHODES - LOGGING ET MONITORING
================================================================================

Ce fichier contient la description détaillée des méthodes liées au logging et monitoring.

ÉTAT DOCUMENTATION : PREMIÈRE VAGUE EN COURS
- **Couverture** : En cours de documentation
- **Niveau** : Documentation fonctionnelle standardisée
- **Qualité** : 15-25 lignes par méthode minimum

DOMAINE FONCTIONNEL : LOGGING ET MONITORING
- Configuration et gestion des loggers
- Journalisation et monitoring système
- Formatage et handlers de logs
- Surveillance des performances

MÉTHODES DOCUMENTÉES :
================================================================================

1. configure_root_logger.txt (LoggingManager.configure_root_logger - CONFIGURATION LOGGER RACINE)
   - Lignes 154-192 dans ml_core.py (39 lignes)
   - FONCTION : Configure le logger racine du système avec handlers personnalisés pour console et fichier, formatage standardisé et gestion complète des niveaux de journalisation
   - PARAMÈTRES :
     * self - Instance de LoggingManager
     * level (int, défaut=logging.INFO) - Niveau de journalisation (DEBUG, INFO, WARNING, ERROR, CRITICAL)
     * console (bool, défaut=True) - Active/désactive l'affichage des logs sur la console
     * file (bool, défaut=True) - Active/désactive l'écriture des logs dans un fichier
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RÉCUPÉRATION LOGGER** : Obtient le logger racine avec logging.getLogger() sans paramètres
     * **CONFIGURATION NIVEAU** : Définit le niveau de journalisation avec root_logger.setLevel(level)
     * **NETTOYAGE HANDLERS** : Supprime tous les handlers existants pour éviter les doublons
     * **CRÉATION FORMATTER** : Instancie un formatter avec timestamp, nom, niveau et message
     * **STOCKAGE FORMATTER** : Sauvegarde le formatter dans self._formatters['default']
     * **HANDLER CONSOLE** : `console_handler = logging.StreamHandler(); root_logger.addHandler(console_handler)` si console=True
     * **HANDLER FICHIER** : `file_handler = logging.FileHandler(f"ml_core_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")` si file=True
     * **APPLICATION FORMATTER** : `handler.setFormatter(formatter)` application format à tous handlers
     * **STOCKAGE HANDLERS** : `self._handlers['console'] = console_handler; self._handlers['file'] = file_handler` référence
     * **STOCKAGE LOGGER** : `self._loggers['root'] = root_logger` enregistrement logger configuré
     * **LOGGING CONFIRMATION** : `root_logger.info("Logger racine configuré avec succès")` confirmation démarrage
     * **TYPE HINTS** : `level: int = logging.INFO`, `console: bool = True`, `file: bool = True` validation statique
   - RETOUR : None (configuration en place, pas de retour)
   - UTILITÉ : Méthode fondamentale pour initialiser le système de journalisation. Essentielle pour tracer l'exécution, déboguer et monitorer l'application. Permet la journalisation simultanée console/fichier avec formatage professionnel.

2. get_logger.txt (LoggingManager.get_logger - RÉCUPÉRATION LOGGER NOMMÉ)
   - Lignes 194-212 dans ml_core.py (19 lignes)
   - FONCTION : Récupère ou crée un logger avec un nom spécifique et un niveau de journalisation optionnel, avec enregistrement automatique dans le gestionnaire pour suivi et réutilisation
   - PARAMÈTRES :
     * self - Instance de LoggingManager
     * name (str) - Nom unique du logger à récupérer ou créer
     * level (Optional[int], défaut=None) - Niveau de journalisation spécifique, si None utilise le niveau parent
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RÉCUPÉRATION/CRÉATION** : `logger = logging.getLogger(name)` obtention logger existant ou création nouveau
     * **CONFIGURATION NIVEAU** : `if level is not None: logger.setLevel(level)` application niveau personnalisé si fourni
     * **HÉRITAGE NIVEAU** : Si level=None, logger hérite niveau parent (logger racine) automatiquement
     * **ENREGISTREMENT GESTIONNAIRE** : `self._loggers[name] = logger` stockage dans dictionnaire pour référence future
     * **RETOUR LOGGER** : `return logger` retourne instance logger configurée et enregistrée
     * **RÉUTILISATION** : Appels ultérieurs avec même nom retournent même logger (pattern Singleton)
     * **HIÉRARCHIE LOGGERS** : Respecte hiérarchie loggers Python (ex: 'app.module.submodule')
     * **TYPE HINTS** : `name: str`, `level: Optional[int] = None`, `-> logging.Logger` validation statique
     * **DOCSTRING COMPLÈTE** : Args, Returns avec descriptions détaillées
   - RETOUR : logging.Logger - Instance du logger configuré et enregistré
   - UTILITÉ : Méthode essentielle pour créer des loggers spécialisés par module ou composant. Permet la journalisation granulaire avec niveaux différents par zone de l'application. Facilite le debugging ciblé et la surveillance modulaire.
