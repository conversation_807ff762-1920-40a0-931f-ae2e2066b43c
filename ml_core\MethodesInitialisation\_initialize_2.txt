# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 236 à 242
# Type: Méthode de la classe ModuleInterface
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def _initialize(self):
        """Initialise les dictionnaires de dépendances."""
        self._functions = {}
        self._classes = {}
        self._instances = {}
        self._factories = {}
        self._lazy_loaders = {}