# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 1665 à 2027
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

    def calculate_confidence(self, features: List[float], game_round: int, config=None) -> Dict[str, Any]:
        """
        Calcule la confiance pour une recommandation basée sur les données historiques.
        Version améliorée qui prend en compte plus de facteurs pour une meilleure précision,
        spécifiquement optimisée pour l'objectif 1 (recommandations NON-WAIT valides consécutives).

        Args:
            features: Vecteur de features pour la position actuelle
            game_round: Numéro de la manche actuelle (1-indexé)
            config: Configuration du prédicteur (optionnel, pour mise à jour des paramètres)

        Returns:
            Dictionnaire contenant:
                - confidence: Score de confiance entre 0 et 1
                - expected_consecutive: Nombre attendu de recommandations consécutives valides
                - similar_patterns_count: Nombre de patterns similaires trouvés
                - success_rate: Taux de succès des patterns similaires
                - wait_recommendation: Booléen indiquant si une recommandation WAIT est suggérée
                - wait_reason: Raison de la recommandation WAIT (si applicable)
                - et d'autres métriques détaillées
        """
        # Mettre à jour les paramètres si une nouvelle configuration est fournie
        if config:
            self.target_round_min = getattr(config, 'target_round_min', self.target_round_min)
            self.target_round_max = getattr(config, 'target_round_max', self.target_round_max)
            self.late_game_factor = getattr(config, 'late_game_factor', self.late_game_factor)
            self.occurrence_factor_divisor = getattr(config, 'occurrence_factor_divisor', self.occurrence_factor_divisor)
            self.consecutive_factor_divisor = getattr(config, 'consecutive_factor_divisor', self.consecutive_factor_divisor)
            self.max_occurrence_factor = getattr(config, 'max_occurrence_factor', self.max_occurrence_factor)
            self.max_consecutive_factor = getattr(config, 'max_consecutive_factor', self.max_consecutive_factor)
            self.pattern_similarity_threshold = getattr(config, 'pattern_similarity_threshold', self.pattern_similarity_threshold)
            self.max_similar_patterns = getattr(config, 'max_similar_patterns', self.max_similar_patterns)
            self.optimal_wait_ratio = getattr(config, 'optimal_wait_ratio', self.optimal_wait_ratio)
            self.wait_ratio_tolerance = getattr(config, 'wait_ratio_tolerance', self.wait_ratio_tolerance)
            self.sequence_bonus_threshold = getattr(config, 'sequence_bonus_threshold', self.sequence_bonus_threshold)
            self.sequence_bonus_factor = getattr(config, 'sequence_bonus_factor', self.sequence_bonus_factor)

            # Mettre à jour les facteurs de pondération
            self.success_rate_weight = getattr(config, 'success_rate_weight', self.success_rate_weight)
            self.consecutive_length_weight = getattr(config, 'consecutive_length_weight', self.consecutive_length_weight)
            self.pattern_frequency_weight = getattr(config, 'pattern_frequency_weight', self.pattern_frequency_weight)

        # Vérifier si nous sommes dans les manches cibles
        is_target_round = self.target_round_min <= game_round <= self.target_round_max

        # Si nous ne sommes pas dans les manches cibles, retourner une confiance neutre
        if not is_target_round:
            return {
                "confidence": 0.5,  # Confiance neutre hors manches cibles
                "expected_consecutive": 0,
                "similar_patterns_count": 0,
                "success_rate": 0.0,
                "is_target_round": is_target_round,
                "wait_recommendation_strength": 0.0,  # Nouvelle métrique
                "non_wait_recommendation_strength": 0.0,  # Nouvelle métrique
                "wait_recommendation": False,
                "wait_reason": "Hors manches cibles"
            }

        # Trouver des patterns similaires avec le nouveau seuil de similarité
        similar_patterns = self.find_similar_patterns(features, threshold=self.pattern_similarity_threshold)

        # Limiter le nombre de patterns similaires pour éviter le bruit
        if len(similar_patterns) > self.max_similar_patterns:
            similar_patterns = similar_patterns[:self.max_similar_patterns]

        if not similar_patterns:
            return {
                "confidence": 0.5,  # Confiance par défaut
                "expected_consecutive": 0,
                "similar_patterns_count": 0,
                "success_rate": 0.0,
                "is_target_round": is_target_round,
                "wait_recommendation_strength": 0.8,  # Forte recommandation WAIT car aucun pattern similaire
                "non_wait_recommendation_strength": 0.2,
                "wait_recommendation": True,
                "wait_reason": "Aucun pattern similaire trouvé"
            }

        # Calculer les statistiques agrégées des patterns similaires
        total_occurrences = 0
        total_success = 0
        consecutive_lengths = []
        max_consecutive_length = 0
        pattern_weights = []  # Poids pour chaque pattern basé sur sa similarité

        for pattern_key, similarity in similar_patterns:
            stats = self.pattern_stats[pattern_key]
            pattern_weights.append(similarity)

            # Pondérer par la similarité
            weighted_occurrences = stats["total"] * similarity
            weighted_success = stats["success"] * similarity

            total_occurrences += weighted_occurrences
            total_success += weighted_success

            # Ajouter les longueurs de séquences consécutives
            for length in stats["consecutive_lengths"]:
                consecutive_lengths.append(length * similarity)  # Pondérer par la similarité

            # Trouver la longueur maximale de séquence consécutive
            if stats["consecutive_lengths"]:
                pattern_max_consecutive = max(stats["consecutive_lengths"])
                max_consecutive_length = max(max_consecutive_length, pattern_max_consecutive)

        # Calculer le taux de succès
        success_rate = total_success / total_occurrences if total_occurrences > 0 else 0.5

        # Calculer la longueur moyenne des séquences consécutives (pondérée par similarité)
        avg_consecutive = sum(consecutive_lengths) / len(consecutive_lengths) if consecutive_lengths else 0

        # Calculer la médiane des séquences consécutives (plus robuste aux valeurs extrêmes)
        median_consecutive = np.median(consecutive_lengths) if consecutive_lengths else 0

        # Position dans la plage de manches cibles (0 au début, 1 à la fin)
        position_in_range = (game_round - self.target_round_min) / max(1, (self.target_round_max - self.target_round_min))

        # Récupérer le facteur de bonus en forme de cloche depuis la configuration
        bell_curve_factor = getattr(config, 'bell_curve_factor', 0.2) if config else 0.2

        # Bonus en forme de cloche pour les manches au milieu de la plage cible
        # Maximum au milieu de la plage (où l'objectif 1 est le plus important)
        bell_curve_bonus = 1.0 + bell_curve_factor * (1.0 - 4.0 * (position_in_range - 0.5) ** 2)

        # Bonus pour les manches cibles (augmente progressivement et inclut le bonus en cloche)
        late_game_factor_applied = (1.0 + (self.late_game_factor - 1.0) * position_in_range) * bell_curve_bonus

        # Bonus pour les patterns avec beaucoup d'occurrences (plus fiables)
        occurrence_factor = min(1.0 + (total_occurrences / self.occurrence_factor_divisor), self.max_occurrence_factor)

        # Bonus pour les patterns avec des séquences consécutives longues
        # Utiliser à la fois la moyenne et le maximum pour une meilleure précision
        consecutive_factor = min(
            self.max_consecutive_factor,
            1.0 + (avg_consecutive / self.consecutive_factor_divisor) + (max_consecutive_length / (self.consecutive_factor_divisor * 2))
        )

        # Bonus supplémentaire pour les séquences très longues
        sequence_bonus = 1.0
        if max_consecutive_length >= self.sequence_bonus_threshold:
            # Bonus proportionnel à la longueur au-delà du seuil
            sequence_bonus += self.sequence_bonus_factor * (max_consecutive_length - self.sequence_bonus_threshold)

        # Calculer la confiance finale avec pondération des composantes
        confidence = (
            self.success_rate_weight * success_rate +
            self.consecutive_length_weight * (consecutive_factor / self.max_consecutive_factor) +
            self.pattern_frequency_weight * (occurrence_factor / self.max_occurrence_factor)
        ) * late_game_factor_applied * sequence_bonus

        # Limiter entre 0 et 1
        confidence = np.clip(confidence, 0.0, 1.0)

        # Calculer la force de recommandation WAIT vs NON-WAIT
        # Plus cette valeur est élevée, plus une recommandation WAIT est appropriée
        wait_recommendation_strength = 0.0

        # Récupérer les seuils et facteurs depuis la configuration
        wait_success_rate_threshold = getattr(config, 'wait_success_rate_threshold', 0.6) if config else 0.6
        wait_success_rate_factor = getattr(config, 'wait_success_rate_factor', 2.0) if config else 2.0
        wait_consecutive_threshold = getattr(config, 'wait_consecutive_threshold', 2.0) if config else 2.0
        wait_consecutive_factor = getattr(config, 'wait_consecutive_factor', 0.2) if config else 0.2
        wait_occurrences_threshold = getattr(config, 'wait_occurrences_threshold', 10) if config else 10
        wait_occurrences_factor = getattr(config, 'wait_occurrences_factor', 0.02) if config else 0.02

        # Facteurs qui favorisent une recommandation WAIT:
        # 1. Faible taux de succès historique
        if success_rate < wait_success_rate_threshold:
            wait_recommendation_strength += (wait_success_rate_threshold - success_rate) * wait_success_rate_factor

        # 2. Séquences consécutives courtes
        if avg_consecutive < wait_consecutive_threshold:
            wait_recommendation_strength += (wait_consecutive_threshold - avg_consecutive) * wait_consecutive_factor

        # 3. Peu d'occurrences du pattern (incertitude élevée)
        if total_occurrences < wait_occurrences_threshold:
            wait_recommendation_strength += (wait_occurrences_threshold - total_occurrences) * wait_occurrences_factor

        # Limiter entre 0 et 1
        wait_recommendation_strength = np.clip(wait_recommendation_strength, 0.0, 1.0)

        # Force de recommandation NON-WAIT (inverse de WAIT)
        non_wait_recommendation_strength = 1.0 - wait_recommendation_strength

        # Ajuster la confiance en fonction du ratio WAIT/NON-WAIT actuel
        # Si nous sommes loin du ratio optimal, ajuster la confiance
        current_wait_ratio = self.get_current_wait_ratio(config) if hasattr(self, 'get_current_wait_ratio') else self.optimal_wait_ratio
        ratio_deviation = abs(current_wait_ratio - self.optimal_wait_ratio)

        if ratio_deviation > self.wait_ratio_tolerance:
            # Si le ratio actuel est trop élevé (trop de WAIT), augmenter la confiance pour favoriser NON-WAIT
            if current_wait_ratio > self.optimal_wait_ratio:
                confidence_adjustment = min(0.1, ratio_deviation * 0.5)
                confidence = min(1.0, confidence + confidence_adjustment)
            # Si le ratio actuel est trop bas (pas assez de WAIT), diminuer la confiance pour favoriser WAIT
            else:
                confidence_adjustment = min(0.1, ratio_deviation * 0.5)
                confidence = max(0.0, confidence - confidence_adjustment)

        # Log détaillé pour le débogage
        logger.debug(f"Confiance consécutive améliorée: round={game_round}, target={is_target_round}, " +
                    f"success_rate={success_rate:.3f}, avg_consecutive={avg_consecutive:.1f}, " +
                    f"median_consecutive={median_consecutive:.1f}, max_consecutive={max_consecutive_length}, " +
                    f"patterns={len(similar_patterns)}, bell_curve={bell_curve_bonus:.2f}, " +
                    f"sequence_bonus={sequence_bonus:.2f}, wait_strength={wait_recommendation_strength:.2f}, " +
                    f"confidence={confidence:.3f}")

        # Déterminer si une recommandation WAIT est nécessaire
        wait_recommendation = False
        wait_reason = ""

        # Récupérer les seuils de la configuration
        success_rate_threshold = getattr(config, 'success_rate_wait_threshold', 0.55) if config else 0.55
        confidence_threshold = getattr(config, 'confidence_wait_threshold', 0.6) if config else 0.6
        avg_consecutive_threshold = getattr(config, 'avg_consecutive_wait_threshold', 1.5) if config else 1.5
        wait_ratio_min_threshold = getattr(config, 'wait_ratio_min_threshold', self.optimal_wait_ratio - self.wait_ratio_tolerance) if config else self.optimal_wait_ratio - self.wait_ratio_tolerance
        wait_recommendation_threshold = getattr(config, 'wait_recommendation_threshold', 0.7) if config else 0.7
        position_range_lower = getattr(config, 'position_range_lower', 0.3) if config else 0.3
        position_range_upper = getattr(config, 'position_range_upper', 0.7) if config else 0.7
        mid_range_confidence_threshold = getattr(config, 'mid_range_confidence_threshold', 0.75) if config else 0.75

        # Facteurs qui peuvent déclencher une recommandation WAIT (EXTRÊMEMENT réduits)
        # 1. Faible taux de succès historique (seulement si EXTRÊMEMENT faible)
        if success_rate < success_rate_threshold * 0.4:  # Réduction EXTRÊME du seuil
            wait_recommendation = True
            wait_reason = "Taux de succès catastrophique"
            logger.debug(f"WAIT recommandé: Taux de succès catastrophique ({success_rate:.2f} < {success_rate_threshold * 0.4:.2f})")

        # 2. Faible confiance dans la prédiction (seulement si EXTRÊMEMENT faible)
        elif confidence < confidence_threshold * 0.4:  # Réduction EXTRÊME du seuil
            wait_recommendation = True
            wait_reason = "Confiance catastrophique"
            logger.debug(f"WAIT recommandé: Confiance catastrophique ({confidence:.2f} < {confidence_threshold * 0.4:.2f})")

        # 3. Ratio WAIT/NON-WAIT déséquilibré (seulement si EXTRÊMEMENT peu de WAIT)
        elif current_wait_ratio < wait_ratio_min_threshold * 0.3:  # Réduction EXTRÊME du seuil
            wait_recommendation = True
            wait_reason = "Ratio WAIT catastrophique"
            logger.debug(f"WAIT recommandé: Ratio WAIT catastrophique ({current_wait_ratio:.2f} < {wait_ratio_min_threshold * 0.3:.2f})")

        # 4. Force de recommandation WAIT EXTRÊMEMENT élevée
        elif wait_recommendation_strength > wait_recommendation_threshold * 1.3:  # Augmentation EXTRÊME du seuil
            wait_recommendation = True
            wait_reason = "Conditions catastrophiques pour NON-WAIT"
            logger.debug(f"WAIT recommandé: Force de recommandation WAIT catastrophique ({wait_recommendation_strength:.2f} > {wait_recommendation_threshold * 1.3:.2f})")

        # 5. Ajustement basé sur les performances récentes (seulement si ÉNORMÉMENT d'erreurs)
        # Si nous avons eu ÉNORMÉMENT d'erreurs récentes, être plus conservateur
        elif hasattr(self, 'current_consecutive_errors') and self.current_consecutive_errors > 4:  # Augmentation EXTRÊME du seuil
            error_penalty = self.current_consecutive_errors * getattr(config, 'consecutive_error_penalty', 0.1) * 0.3  # Réduction EXTRÊME de la pénalité
            adjusted_confidence = confidence - error_penalty
            if adjusted_confidence < confidence_threshold * 0.4:  # Réduction EXTRÊME du seuil
                wait_recommendation = True
                wait_reason = "Récupération après erreurs récentes catastrophiques"
                logger.debug(f"WAIT recommandé: Récupération après erreurs récentes catastrophiques (erreurs: {self.current_consecutive_errors}, confiance ajustée: {adjusted_confidence:.2f})")

        # Ajuster la recommandation WAIT en fonction de la position dans la plage cible
        # Au milieu de la plage cible (où l'objectif 1 est le plus important), être BEAUCOUP plus strict
        if position_in_range > position_range_lower * 1.2 and position_in_range < position_range_upper * 0.8:
            # Si nous sommes au milieu de la plage cible et que la confiance est même modérée,
            # annuler la recommandation WAIT pour maximiser les séquences consécutives
            if confidence > mid_range_confidence_threshold * 0.6 and wait_recommendation:
                wait_recommendation = False
                wait_reason = "PRIORITÉ ABSOLUE aux séquences consécutives au milieu de la plage cible"
                logger.debug(f"NON-WAIT PRIORITAIRE: Au milieu de la plage cible avec confiance acceptable ({confidence:.2f} > {mid_range_confidence_threshold * 0.6:.2f})")

            # Ajouter un log très visible pour confirmer que nos modifications sont appliquées
            logger.warning("=" * 80)
            logger.warning(f"MODIFICATION APPLIQUÉE: PRIORITÉ ABSOLUE AUX SÉQUENCES CONSÉCUTIVES AU MILIEU DE LA PLAGE CIBLE")
            logger.warning("=" * 80)

        # Si nous avons trop de WAIT, réduire DRASTIQUEMENT les recommandations WAIT
        if current_wait_ratio > getattr(config, 'wait_ratio_max_threshold', 0.25) and wait_recommendation:
            # Annuler la recommandation WAIT si la confiance est même minimale
            confidence_ratio_threshold = confidence_threshold * (0.5 - (current_wait_ratio - 0.25) * 0.8)
            if confidence > confidence_ratio_threshold:
                wait_recommendation = False
                wait_reason = "Équilibrage URGENT du ratio WAIT (trop élevé)"
                logger.debug(f"NON-WAIT FORCÉ pour équilibrage: Ratio WAIT trop élevé ({current_wait_ratio:.2f}), confiance minimale suffisante ({confidence:.2f} > {confidence_ratio_threshold:.2f})")

            # Ajouter un log très visible pour confirmer que nos modifications sont appliquées
            logger.warning("=" * 80)
            logger.warning(f"MODIFICATION APPLIQUÉE: ÉQUILIBRAGE URGENT DU RATIO WAIT (TROP ÉLEVÉ: {current_wait_ratio:.2f})")
            logger.warning("=" * 80)

        # Si le ratio WAIT est très élevé, forcer SYSTÉMATIQUEMENT des recommandations NON-WAIT
        if current_wait_ratio > 0.4 and wait_recommendation:
            # Forcer NON-WAIT même avec une confiance très faible
            if confidence > confidence_threshold * 0.4:
                wait_recommendation = False
                wait_reason = "Correction CRITIQUE du ratio WAIT (BEAUCOUP trop élevé)"
                logger.debug(f"NON-WAIT SYSTÉMATIQUE: Ratio WAIT CRITIQUE ({current_wait_ratio:.2f})")

            # Ajouter un log très visible pour confirmer que nos modifications sont appliquées
            logger.warning("=" * 80)
            logger.warning(f"MODIFICATION APPLIQUÉE: CORRECTION CRITIQUE DU RATIO WAIT (BEAUCOUP TROP ÉLEVÉ: {current_wait_ratio:.2f})")
            logger.warning("=" * 80)

        # Appliquer un facteur de focus EXTRÊME sur les recommandations consécutives
        # TOUJOURS favoriser les NON-WAIT dans la plage cible
        if hasattr(self, 'consecutive_focus_factor'):
            # Élargir considérablement la plage pour favoriser les NON-WAIT
            if position_in_range >= 0.2 and position_in_range <= 0.8:  # Plage TRÈS élargie
                # Réduire DRASTIQUEMENT la probabilité de recommander WAIT pour favoriser les séquences consécutives
                focus_threshold = confidence_threshold * 0.3  # Seuil EXTRÊMEMENT réduit
                if wait_recommendation and confidence > focus_threshold:
                    wait_recommendation = False
                    wait_reason = "FOCUS EXTRÊME sur les recommandations consécutives"
                    logger.debug(f"NON-WAIT FORCÉ pour focus consécutif: Confiance minimale suffisante ({confidence:.2f} > {focus_threshold:.2f})")

                # Ajouter un log très visible pour confirmer que nos modifications sont appliquées
                logger.warning("=" * 80)
                logger.warning(f"MODIFICATION APPLIQUÉE: FOCUS EXTRÊME SUR LES RECOMMANDATIONS CONSÉCUTIVES")
                logger.warning("=" * 80)

        # Favoriser SYSTÉMATIQUEMENT NON-WAIT si nous avons déjà une séquence consécutive en cours
        if hasattr(self, 'current_consecutive_valid') and self.current_consecutive_valid > 0 and wait_recommendation:
            # Plus la séquence est longue, plus nous sommes enclins à continuer avec NON-WAIT
            # Boost EXTRÊMEMENT augmenté
            sequence_confidence_boost = min(0.4, self.current_consecutive_valid * 0.1)
            if confidence > (confidence_threshold * 0.4 - sequence_confidence_boost):
                wait_recommendation = False
                wait_reason = "MAINTIEN PRIORITAIRE de la séquence consécutive en cours"
                logger.debug(f"NON-WAIT SYSTÉMATIQUE pour maintien de séquence: {self.current_consecutive_valid} recommandations valides, boost de confiance: {sequence_confidence_boost:.2f}")

            # Ajouter un log très visible pour confirmer que nos modifications sont appliquées
            logger.warning("=" * 80)
            logger.warning(f"MODIFICATION APPLIQUÉE: MAINTIEN PRIORITAIRE DE LA SÉQUENCE CONSÉCUTIVE EN COURS ({self.current_consecutive_valid} recommandations valides)")
            logger.warning("=" * 80)

        # Log détaillé de la décision finale
        if wait_recommendation:
            logger.debug(f"Décision finale: WAIT - Raison: {wait_reason}")
        else:
            logger.debug(f"Décision finale: NON-WAIT - Confiance: {confidence:.2f}")

        # Mettre à jour les compteurs internes
        if hasattr(self, 'total_recommendations'):
            if wait_recommendation:
                self.wait_recommendations += 1
            else:
                self.non_wait_recommendations += 1

        return {
            "confidence": confidence,
            "expected_consecutive": median_consecutive,
            "max_consecutive": max_consecutive_length,
            "similar_patterns_count": len(similar_patterns),
            "success_rate": success_rate,
            "is_target_round": is_target_round,
            "position_in_range": position_in_range,
            "wait_recommendation_strength": wait_recommendation_strength,
            "non_wait_recommendation_strength": non_wait_recommendation_strength,
            "bell_curve_bonus": bell_curve_bonus,
            "sequence_bonus": sequence_bonus,
            "late_game_factor": late_game_factor_applied,
            "occurrence_factor": occurrence_factor,
            "consecutive_factor": consecutive_factor,
            "current_wait_ratio": current_wait_ratio,
            "wait_recommendation": wait_recommendation,
            "wait_reason": wait_reason
        }