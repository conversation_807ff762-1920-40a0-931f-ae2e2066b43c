# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1654 à 1675
# Type: Méthode de la classe ThreadedOptimizer

    def _run_optimization(self):
        """
        Exécute l'optimisation dans le thread.
        Cette méthode est appelée par le thread et ne doit pas être appelée directement.
        """
        try:
            # Exécuter l'optimisation
            self.result = self.optimizer_instance.optimize(stop_event=self.stop_event)

            # Appeler le callback si l'optimisation s'est terminée normalement
            if not self.stop_event.is_set() and self.callback:
                self.callback(self.result)

        except Exception as e:
            logger.error(f"Erreur lors de l'optimisation: {e}")
            self.error = e

            # Appeler le callback d'erreur si une erreur s'est produite
            if self.error_callback:
                self.error_callback(e)
        finally:
            self.is_running = False