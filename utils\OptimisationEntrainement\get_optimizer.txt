# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 383 à 428
# Type: Méthode

def get_optimizer(model, config):
    """
    Crée un optimiseur en fonction de la configuration

    Args:
        model: <PERSON><PERSON><PERSON><PERSON> PyTorch
        config: Configuration

    Returns:
        Optimiseur
    """
    optimizer_type = getattr(config, 'optimizer_type', 'adamw')
    lr = getattr(config, 'lstm_learning_rate', 5e-5)
    weight_decay = getattr(config, 'lstm_weight_decay', 2e-6)

    if optimizer_type.lower() == 'adamw':
        return optim.AdamW(
            model.parameters(),
            lr=lr,
            weight_decay=weight_decay,
            betas=(0.9, 0.999),
            eps=1e-8
        )
    elif optimizer_type.lower() == 'adam':
        return optim.Adam(
            model.parameters(),
            lr=lr,
            weight_decay=weight_decay,
            betas=(0.9, 0.999),
            eps=1e-8
        )
    elif optimizer_type.lower() == 'sgd':
        return optim.SGD(
            model.parameters(),
            lr=lr,
            momentum=0.9,
            weight_decay=weight_decay,
            nesterov=True
        )
    else:
        # Par défaut, utiliser AdamW
        return optim.AdamW(
            model.parameters(),
            lr=lr,
            weight_decay=weight_decay
        )