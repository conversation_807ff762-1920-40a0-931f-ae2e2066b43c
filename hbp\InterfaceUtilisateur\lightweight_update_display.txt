# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 11343 à 11452
# Type: Méthode de la classe HybridBaccaratPredictor

    def lightweight_update_display(self, pred: Dict[str, Union[float, str, Dict]]) -> None:
        """Met à jour uniquement les éléments essentiels de l'UI après chaque coup (thread-safe via appelant)."""
        if not hasattr(self, 'pred_vars'): return # Sécurité si appelé trop tôt

        # Mettre à jour les probabilités affichées
        try:
            self.pred_vars['player'].set(f"Player: {pred.get('player', 0.5)*100:.1f}%")
            self.pred_vars['banker'].set(f"Banker: {pred.get('banker', 0.5)*100:.1f}%")

            # Mettre à jour la recommandation
            rec_text_map = {'player': "<PERSON>uer PLAYER", 'banker': "<PERSON><PERSON> BANKER", 'wait': "Attendre"}
            rec = pred.get('recommendation', 'wait')
            self.pred_vars['recommendation'].set(f"Recommandation: {rec_text_map.get(rec, 'Attendre')}")

            # Mettre à jour le numéro de manche
            with self.sequence_lock:
                 round_num = len(self.sequence)
            self.pred_vars['round'].set(f"Manche: {round_num}")

            # Mettre à jour l'incertitude affichée
            unc = pred.get('uncertainty', 0.5)

            # Vérifier si nous sommes dans la plage de manches cibles (31-60)
            target_round_min = getattr(self.config, 'target_round_min', 31)
            target_round_max = getattr(self.config, 'target_round_max', 60)
            is_target_round = target_round_min <= round_num <= target_round_max

            # Ajuster l'affichage de l'incertitude pour la session en cours (sans modifier le calcul sous-jacent)
            # Utiliser try/except pour éviter les erreurs si stats_vars n'est pas encore initialisé
            try:
                if not is_target_round:
                    # Si nous ne sommes pas dans la plage cible (31-60), indiquer que l'incertitude n'est pas applicable
                    if hasattr(self, 'stats_vars') and 'uncertainty' in self.stats_vars:
                        self.stats_vars['uncertainty'].set(f"Incertitude Prediction: N/A (manche 1-30)")
                else:
                    # Formule d'ajustement pour la session en cours
                    display_unc = 0.5 + (unc - 0.5) / 2.0  # Même formule que pour la confiance
                    if hasattr(self, 'stats_vars') and 'uncertainty' in self.stats_vars:
                        self.stats_vars['uncertainty'].set(f"Incertitude Prediction: {display_unc*100:.1f}%")
            except Exception as e:
                logger.debug(f"Erreur mise à jour affichage incertitude: {e}")
                # Continuer sans erreur

            # Mettre à jour les métriques détaillées d'incertitude
            if 'confidence_metrics' in pred:
                metrics = pred['confidence_metrics']

                if not is_target_round:
                    # Si nous ne sommes pas dans la plage cible (31-60), indiquer que les métriques détaillées ne sont pas applicables
                    if hasattr(self.stats_vars, 'uncertainty_details'):
                        self.stats_vars['uncertainty_details'].set(
                            f"Incertitude Détaillée: N/A (manche 1-30)"
                        )
                else:
                    # Afficher les métriques détaillées pour les manches 31-60
                    epistemic = metrics.get('epistemic_uncertainty', 0) * 100
                    aleatoric = metrics.get('aleatoric_uncertainty', 0) * 100
                    sensitivity = metrics.get('context_sensitivity', 0) * 100

                    if hasattr(self.stats_vars, 'uncertainty_details'):
                        self.stats_vars['uncertainty_details'].set(
                            f"Incertitude Détaillée: Épist({epistemic:.1f}%) | Aléa({aleatoric:.1f}%) | Sens({sensitivity:.1f}%)"
                        )

            # Mettre à jour le seuil adaptatif
            if 'adaptive_threshold' in pred:
                if not is_target_round:
                    # Si nous ne sommes pas dans la plage cible (31-60), indiquer que le seuil adaptatif n'est pas applicable
                    if hasattr(self.stats_vars, 'adaptive_threshold'):
                        self.stats_vars['adaptive_threshold'].set(f"Seuil Adaptatif: N/A (manche 1-30)")
                else:
                    threshold = pred['adaptive_threshold'] * 100
                    if hasattr(self.stats_vars, 'adaptive_threshold'):
                        self.stats_vars['adaptive_threshold'].set(f"Seuil Adaptatif: {threshold:.2f}%")

            # Mettre à jour les confiances des méthodes
            if 'methods' in pred:
                if not is_target_round:
                    # Si nous ne sommes pas dans la plage cible (31-60), indiquer que les confiances des méthodes ne sont pas applicables
                    if hasattr(self.stats_vars, 'method_conf'):
                        self.stats_vars['method_conf'].set(f"Confiance Méthodes: N/A (manche 1-30)")
                else:
                    method_conf_parts = []
                    for method, method_data in pred['methods'].items():
                        if 'confidence' in method_data:
                            method_conf = method_data['confidence'] * 100
                            method_conf_parts.append(f"{method.upper()}({method_conf:.1f}%)")

                    if hasattr(self.stats_vars, 'method_conf') and method_conf_parts:
                        self.stats_vars['method_conf'].set(f"Confiance Méthodes: {' | '.join(method_conf_parts)}")

            # Mettre à jour les poids bayésiens
            if 'bayesian_weights' in pred:
                if not is_target_round:
                    # Si nous ne sommes pas dans la plage cible (31-60), indiquer que les poids bayésiens ne sont pas applicables
                    if hasattr(self.stats_vars, 'bayesian_weights'):
                        self.stats_vars['bayesian_weights'].set(f"Poids Bayésiens: N/A (manche 1-30)")
                else:
                    bayesian_weights = pred['bayesian_weights']
                    weights_parts = []
                    for method, weight in bayesian_weights.items():
                        weights_parts.append(f"{method.upper()}({weight*100:.1f}%)")

                    if hasattr(self.stats_vars, 'bayesian_weights') and weights_parts:
                        self.stats_vars['bayesian_weights'].set(f"Poids Bayésiens: {' | '.join(weights_parts)}")

            # Pas besoin d'update_idletasks ici si appelé via root.after

        except Exception as e:
             logger.error(f"Erreur dans lightweight_update_display: {e}")