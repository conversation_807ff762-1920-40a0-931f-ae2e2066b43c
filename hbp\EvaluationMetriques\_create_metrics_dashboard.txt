# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 6955 à 7003
# Type: Méthode de la classe HybridBaccaratPredictor

    def _create_metrics_dashboard(self):
        """Crée un tableau de bord de métriques dans l'interface utilisateur."""
        # Vérifier si l'interface utilisateur est disponible
        if not self.is_ui_available():
            logger.warning("Interface utilisateur non disponible pour créer le tableau de bord de métriques.")
            return

        # Créer une nouvelle fenêtre pour le tableau de bord
        metrics_window = tk.Toplevel(self.root)
        metrics_window.title("Tableau de Bord des Métriques d'Entraînement")
        metrics_window.geometry("800x600")
        metrics_window.minsize(600, 400)

        # Créer un notebook (onglets) pour organiser les métriques
        notebook = ttk.Notebook(metrics_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Onglet pour les métriques LGBM
        lgbm_frame = ttk.Frame(notebook)
        notebook.add(lgbm_frame, text="LGBM")

        # Onglet pour les métriques LSTM
        lstm_frame = ttk.Frame(notebook)
        notebook.add(lstm_frame, text="LSTM")

        # Onglet pour les métriques combinées
        combined_frame = ttk.Frame(notebook)
        notebook.add(combined_frame, text="Combiné")

        # Onglet pour les graphiques
        plots_frame = ttk.Frame(notebook)
        notebook.add(plots_frame, text="Graphiques")

        # Remplir l'onglet LGBM
        self._fill_lgbm_metrics_tab(lgbm_frame)

        # Remplir l'onglet LSTM
        self._fill_lstm_metrics_tab(lstm_frame)

        # Remplir l'onglet des métriques combinées
        self._fill_combined_metrics_tab(combined_frame)

        # Remplir l'onglet des graphiques
        self._fill_plots_tab(plots_frame)

        # Bouton pour rafraîchir les métriques
        refresh_button = ttk.Button(metrics_window, text="Rafraîchir les Métriques",
                                   command=lambda: self._refresh_metrics_dashboard(lgbm_frame, lstm_frame, combined_frame, plots_frame))
        refresh_button.pack(pady=10)