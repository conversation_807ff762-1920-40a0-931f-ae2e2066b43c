# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 107 à 121
# Type: Méthode de la classe ConfigManager

    def set(self, key: str, value: Any, namespace: str = 'default') -> None:
        """
        Définit une valeur de configuration.

        Args:
            key: Clé de configuration
            value: Valeur de configuration
            namespace: Espace de noms pour la configuration
        """
        if namespace not in self._config:
            self._config[namespace] = {}

        self._config[namespace][key] = value

        logger.debug(f"Configuration '{key}' définie à '{value}' dans l'espace de noms '{namespace}'")