# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 4155 à 4172
# Type: Méthode de la classe WaitPlacementOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def _extract_pattern_key(self, features):
        """
        Extrait une clé de pattern à partir du vecteur de features.

        Args:
            features (List[float]): Vecteur de features

        Returns:
            str: Clé de pattern
        """
        # Simplifier les features en les arrondissant pour créer une clé
        if features is None or len(features) == 0:
            return "empty_features"

        # Utiliser seulement les 5 premières features pour la clé
        num_features = min(5, len(features))
        rounded_features = [round(float(f), 2) for f in features[:num_features]]
        return "_".join(map(str, rounded_features))