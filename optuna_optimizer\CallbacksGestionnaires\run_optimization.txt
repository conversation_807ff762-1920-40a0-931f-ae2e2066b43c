# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 13500 à 13587
# Type: Méthode de la classe OptunaThreadManager

    def run_optimization(self, optimizer_instance, n_trials,
                         callback=None, error_callback=None, progress_callback=None):
        """
        Lance l'optimisation Optuna dans un thread séparé.

        Args:
            optimizer_instance: Instance d'OptunaOptimizer à utiliser
            n_trials: Nombre d'essais à effectuer
            callback: Fonction à appeler lorsque l'optimisation est terminée
            error_callback: Fonction à appeler en cas d'erreur
            progress_callback: Fonction à appeler pour mettre à jour la progression

        Returns:
            bool: True si l'optimisation a été lancée, False sinon
        """
        if self.is_optimization_running():
            logger.error("Une optimisation est déjà en cours")
            return False

        self.callback = callback
        self.error_callback = error_callback
        self.progress_callback = progress_callback
        self.stop_requested = False
        self.start_time = time.time()

        # Créer une fonction de rappel pour le ThreadedOptimizer
        def on_optimization_complete(result):
            duration = time.time() - self.start_time
            logger.info("Optimisation terminée avec succès")
            self.result_queue.put(("success", result, duration))
            if self.callback:
                self.callback(result, duration)

        # Créer une fonction de rappel pour les erreurs
        def on_optimization_error(error):
            duration = time.time() - self.start_time
            logger.error(f"Erreur lors de l'optimisation: {error}")
            self.result_queue.put(("error", str(error), duration))
            if self.error_callback:
                self.error_callback(str(error), None, duration)

        # Créer une fonction de rappel pour la progression
        def on_optimization_progress(progress, message):
            if self.progress_callback:
                self.progress_callback(progress, message)

        # Créer une classe d'optimiseur adaptée pour ThreadedOptimizer
        class OptimizerAdapter:
            def __init__(self, optimizer_instance, n_trials, progress_callback=None):
                self.optimizer_instance = optimizer_instance
                self.n_trials = n_trials
                self.progress_callback = progress_callback

            def optimize(self, stop_event=None):
                # Ajouter un attribut pour indiquer que l'optimisation doit s'arrêter si demandé
                self.optimizer_instance.stop_requested = lambda: stop_event.is_set() if stop_event else False

                # Mettre à jour la progression initiale
                if self.progress_callback:
                    self.progress_callback(0, "Démarrage de l'optimisation...")

                # Exécuter l'optimisation
                logger.info(f"Début de l'optimisation avec {self.n_trials} essais")
                return self.optimizer_instance.optimize(n_trials=self.n_trials)

        # Créer l'adaptateur d'optimiseur
        optimizer_adapter = OptimizerAdapter(
            optimizer_instance=optimizer_instance,
            n_trials=n_trials,
            progress_callback=on_optimization_progress
        )

        # Créer et démarrer le ThreadedOptimizer
        self.threaded_optimizer = ThreadedOptimizer(
            optimizer_class=lambda: optimizer_adapter,
            callback=on_optimization_complete,
            error_callback=on_optimization_error
        )

        # Démarrer l'optimisation
        success = self.threaded_optimizer.start()
        if success:
            self.is_running = True
            logger.info(f"Thread d'optimisation démarré pour {n_trials} essais")
        else:
            logger.error("Impossible de démarrer l'optimisation")

        return success