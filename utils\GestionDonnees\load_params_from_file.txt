# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 3062 à 3106
# Type: Méthode

def load_params_from_file(file_path: str) -> Dict[str, Any]:
    """
    Charge les paramètres optimisés depuis un fichier JSON.
    Effectue des conversions de type pour assurer la compatibilité des paramètres booléens.

    Args:
        file_path (str): Chemin vers le fichier de paramètres

    Returns:
        Dict[str, Any]: Dictionnaire des paramètres chargés ou None en cas d'erreur
    """
    import json
    import logging
    logger = logging.getLogger(__name__)

    try:
        with open(file_path, "r", encoding="utf-8") as f:
            params = json.load(f)

        # Traitement simplifié pour les paramètres booléens
        if 'params' in params and isinstance(params['params'], dict):
            for param_name, value in params['params'].items():
                # Vérifier si c'est un paramètre booléen par son préfixe
                if param_name.startswith(('use_', 'lstm_use_', 'lgbm_use_')):
                    # Traitement simplifié pour les chaînes 'true'/'false'
                    if isinstance(value, str) and value.lower() in ('true', 'false'):
                        # Convertir les chaînes 'true'/'false' en booléens
                        params['params'][param_name] = value.lower() == 'true'
                        logger.info(f"Conversion de la chaîne '{value}' en booléen {params['params'][param_name]} pour '{param_name}'")
                # Convertir les chaînes 'true'/'false' en booléens pour les autres paramètres
                elif isinstance(value, str) and value.lower() in ('true', 'false'):
                    params['params'][param_name] = value.lower() == 'true'
                    logger.info(f"Conversion de la chaîne '{value}' en booléen {params['params'][param_name]} pour '{param_name}'")

        logger.info(f"Paramètres chargés depuis {file_path}: {len(params)} paramètres")
        return params
    except FileNotFoundError:
        logger.error(f"Fichier {file_path} non trouvé")
        return None
    except json.JSONDecodeError:
        logger.error(f"Erreur de décodage JSON dans {file_path}")
        return None
    except Exception as e:
        logger.error(f"Erreur lors du chargement des paramètres depuis {file_path}: {e}")
        return None