# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 53 à 118
# Type: Méthode

def isolated_consecutive_focused_metric(y_true, y_pred, **kwargs):
    """
    Métrique personnalisée pour LightGBM qui se concentre sur la capacité à produire
    des recommandations NON-WAIT valides consécutives.

    Cette métrique est conçue pour être utilisée avec LightGBM et est isolée dans un module
    séparé pour éviter les problèmes de pickling.

    Args:
        y_true: Valeurs réelles (0 pour banker, 1 pour player)
        y_pred: Probabilités prédites pour la classe positive (player)

    Returns:
        Tuple[str, float, bool]: Nom de la métrique, valeur, et si une valeur plus élevée est meilleure
    """
    try:
        # Convertir les prédictions en classes (0 ou 1)
        y_pred_class = (y_pred > 0.5).astype(int)

        # Calculer l'exactitude de base
        accuracy = np.mean(y_true == y_pred_class)

        # Identifier les prédictions correctes
        correct_predictions = (y_true == y_pred_class)

        # Calculer les séquences de prédictions correctes consécutives
        consecutive_correct = []
        current_streak = 0

        for is_correct in correct_predictions:
            if is_correct:
                current_streak += 1
            else:
                if current_streak > 0:
                    consecutive_correct.append(current_streak)
                    current_streak = 0

        # Ne pas oublier la dernière séquence
        if current_streak > 0:
            consecutive_correct.append(current_streak)

        # Calculer la longueur maximale des séquences correctes consécutives
        max_consecutive = max(consecutive_correct) if consecutive_correct else 0

        # Calculer la moyenne des séquences correctes consécutives
        avg_consecutive = np.mean(consecutive_correct) if consecutive_correct else 0

        # Calculer un score composite qui favorise à la fois l'exactitude et les séquences consécutives
        # Formule: (0.3 * accuracy + 0.4 * max_consecutive / len(y_true) + 0.3 * avg_consecutive / 5)
        # Cette formule donne plus de poids aux séquences consécutives longues
        composite_score = (
            0.3 * accuracy +
            0.4 * min(1.0, max_consecutive / (len(y_true) * 0.2)) +
            0.3 * min(1.0, avg_consecutive / 5)
        )

        # Retourner le score composite
        # Le nom 'consecutive_focused' indique que cette métrique se concentre sur les séquences consécutives
        # La valeur est le score composite
        # True indique qu'une valeur plus élevée est meilleure
        return 'consecutive_focused', composite_score, True

    except Exception as e:
        logger.error(f"Erreur dans isolated_consecutive_focused_metric: {e}", exc_info=True)
        # En cas d'erreur, retourner une métrique neutre
        return 'consecutive_focused', 0.5, True