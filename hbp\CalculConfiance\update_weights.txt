# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 10546 à 10740
# Type: Méthode de la classe HybridBaccaratPredictor

    def update_weights(self, last_prediction: Dict, actual_outcome: str) -> None:
        """
        Ajuste dynamiquement les poids des méthodes (Markov, LGBM, LSTM)
        en fonction de la performance récente.

        Args:
            last_prediction: Le dictionnaire de prédiction retourné par hybrid_prediction
                            pour la manche qui vient de se terminer.
            actual_outcome: Le résultat réel ('player' ou 'banker') de cette manche.
        """
        # Utiliser des verrous pour accéder/modifier les états partagés
        with self.sequence_lock, self.model_lock, self.weights_lock:
            # Conditions pour ajuster les poids (éviter ajustement au tout début)
            # Utiliser la longueur de l'historique de prédiction -1 car last_prediction est pour le coup N+1
            num_predictions_made = len(self.prediction_history) - 1
            min_predictions_for_adjust = 20 # Attendre un minimum de prédictions globales

            if num_predictions_made < min_predictions_for_adjust or 'methods' not in last_prediction:
                # logger.debug(f"Ajustement poids ignoré (preds={num_predictions_made} < {min_predictions_for_adjust} ou préd invalide).")
                return

            prediction_details = last_prediction['methods']
            adjust_weights = False # Flag pour savoir si on ajuste réellement

            # 1. Évaluer la performance de chaque méthode sur ce coup
            for method, performance_data in self.method_performance.items():
                if method not in prediction_details:
                    continue

                method_pred = prediction_details[method]
                pred_player = method_pred.get('player', 0.5)
                pred_banker = method_pred.get('banker', 0.5)

                # Déterminer si la *prédiction* de la méthode était correcte
                # (La proba la plus haute correspond au résultat réel)
                # Convertir en minuscules pour assurer la cohérence
                actual_outcome_lower = actual_outcome.lower()
                method_was_correct = (pred_player > pred_banker and actual_outcome_lower == 'player') or \
                                     (pred_banker > pred_player and actual_outcome_lower == 'banker')
                # Ne pas compter l'égalité (ou quasi-égalité) comme correcte ici

                # Mise à jour compteurs
                performance_data['total'] += 1
                if method_was_correct:
                    performance_data['correct'] += 1

                # Mise à jour historique de précision (fenêtre glissante, ex: 100 points)
                current_acc = performance_data['correct'] / performance_data['total'] if performance_data['total'] > 0 else 0.0
                acc_history = performance_data.get('accuracy_history') # Peut être None si mal initialisé
                if isinstance(acc_history, deque): # Utiliser deque pour efficacité
                     acc_history.append(current_acc) # append gère automatiquement maxlen
                elif isinstance(acc_history, list): # Fallback si c'est une liste
                    acc_history.append(current_acc)
                    if len(acc_history) > 100: acc_history.pop(0)
                else: # Initialiser si manquant
                     performance_data['accuracy_history'] = deque([current_acc], maxlen=100)

                # Vérifier si Assez de données par méthode pour ajustement global
                if performance_data['total'] >= min_predictions_for_adjust:
                    adjust_weights = True # Suffit qu'une méthode ait assez de données

            # 2. Ajuster les poids si au moins une méthode a été évaluée suffisamment
            if adjust_weights:
                # logger.debug("Ajustement des poids basé sur la performance récente...")
                new_weights = {}
                perf_scores = {}
                epsilon = 1e-9

                # Calculer score de performance récent (moyenne sur historique glissant)
                for method, perf_data in self.method_performance.items():
                    acc_history = perf_data.get('accuracy_history')
                    recent_acc = np.mean(acc_history) if acc_history else 0.5 # Utiliser 0.5 si historique vide
                    # Donner un léger bonus si le total est encore faible ? Non, risque d'instabilité.
                    perf_scores[method] = recent_acc
                    # logger.debug(f"  Méthode: {method}, Perf récente: {recent_acc:.3f} (sur {perf_data.get('total',0)} coups)")

                # Normaliser les scores pour qu'ils somment à 1
                total_score = sum(perf_scores.values())
                if total_score > epsilon:
                    normalized_scores = {m: max(0, s / total_score) for m, s in perf_scores.items()} # Assurer >= 0
                    # Re-normaliser après max(0, ...) au cas où un score était négatif (improbable)
                    final_total = sum(normalized_scores.values())
                    if final_total > epsilon: normalized_scores = {m: s / final_total for m, s in normalized_scores.items()}
                    else: normalized_scores = {m: 1.0 / len(perf_scores) for m in perf_scores}
                else:
                    normalized_scores = {m: 1.0 / len(perf_scores) for m in perf_scores} # Répartition égale

                # Ajustement lissé vers les scores normalisés
                alpha = 0.1  # Facteur de lissage (plus petit = plus lisse)
                current_weights = self.weights # Poids avant ajustement

                for method in current_weights:
                    target_weight = normalized_scores.get(method, 0) # Poids cible basé sur perf relative
                    adjusted_weight = (1 - alpha) * current_weights[method] + alpha * target_weight
                    new_weights[method] = max(0, adjusted_weight) # Assurer poids >= 0

                # Re-normaliser les nouveaux poids pour assurer somme = 1
                sum_new_weights = sum(new_weights.values())
                if sum_new_weights > epsilon:
                    self.weights = {m: w / sum_new_weights for m, w in new_weights.items()}
                else:
                    logger.warning("Ajustement poids: Somme des nouveaux poids quasi nulle. Retour aux poids initiaux.")
                    self.weights = self.config.initial_weights.copy()

                # logger.info(f"Nouveaux poids ajustés: { {m: f'{w:.3f}' for m, w in self.weights.items()} }")

                # --- Early Stopping / Gestion de la meilleure performance ---
                # Utiliser la précision de la session (basée sur RECOMMANDATIONS correct/total)

                correct_global = 0
                total_global_recommended = 0 # Compter seulement les recommandations non-'wait'
                # Itérer sur les prédictions faites (jusqu'à l'avant-dernière) et les résultats réels
                hist_len = len(self.prediction_history)
                seq_len = len(self.sequence)
                eval_len = min(hist_len - 1, seq_len - 1) # Nbre de paires (pred, outcome) évaluables

                if eval_len > 0:
                    for i in range(eval_len):
                          hist_pred = self.prediction_history[i]   # Préd pour coup i+1
                          hist_actual = self.sequence[i+1]          # Résultat réel coup i+1
                          rec = hist_pred.get('recommendation')
                          if rec != 'wait':
                               total_global_recommended += 1
                               if (rec == 'player' and hist_actual=='player') or (rec=='banker' and hist_actual=='banker'):
                                    correct_global += 1
                    session_accuracy = correct_global / total_global_recommended if total_global_recommended > 0 else 0.0
                else:
                    session_accuracy = 0.0 # Ou 0.5 ? 0.0 plus prudent

                # Comparer avec la meilleure performance enregistrée
                if session_accuracy > self.best_accuracy:
                    logger.info(f"Nouvelle meilleure précision session: {session_accuracy:.3f} ({correct_global}/{total_global_recommended}) > préc: {self.best_accuracy:.3f}. Sauvegarde poids.")
                    self.best_accuracy = session_accuracy
                    self.best_weights = self.weights.copy()
                    self.early_stopping_counter = 0
                else:
                    self.early_stopping_counter += 1
                    # logger.debug(f"Précision session ({session_accuracy:.3f}) <= meilleure ({self.best_accuracy:.3f}). Compteur Early Stop: {self.early_stopping_counter}")

                    # Revenir aux meilleurs poids si stagnation/déclin
                    if self.early_stopping_counter >= self.config.early_stopping_patience:
                        logger.warning(f"Performance n'augmente plus depuis {self.config.early_stopping_patience} ajustements. Retour aux meilleurs poids (Acc: {self.best_accuracy:.3f}).")
                        self.weights = self.best_weights.copy()
                        self.early_stopping_counter = 0 # Réinitialiser

                        # Optionnel : Réinitialiser aussi les historiques de perf individuelles?
                        # for method_perf in self.method_performance.values():
                        #     method_perf['accuracy_history'].clear()
                        # logger.warning("Historiques de précision individuels réinitialisés suite au revert.")


                # Mise à jour affichage poids UI (thread-safe)
                if self.is_ui_available():
                    self.root.after(0, self._update_weights_display)

            # else: logger.debug("Ajustement poids reporté (pas assez d'évaluations méthode >= {min_predictions_for_adjust}).")

            # 3. Mettre à jour l'optimiseur de placement des WAIT
            try:
                if hasattr(self, 'wait_placement_optimizer') and self.wait_placement_optimizer:
                    # Vérifier si l'optimiseur est activé
                    use_wait_placement_optimizer = getattr(self.config, 'use_wait_placement_optimizer', True)
                    if use_wait_placement_optimizer:
                        # Extraire les features pour l'optimiseur
                        lgbm_feat, _ = self.create_hybrid_features(self.sequence[:-1])  # Features pour la position précédente
                        features_vector = lgbm_feat if lgbm_feat is not None else []

                        # Déterminer la prédiction brute (sans WAIT)
                        raw_prediction = prediction_details.get('lgbm', {})
                        if not raw_prediction:
                            raw_prediction = {}

                        pred_player = raw_prediction.get('player', 0.5)
                        pred_banker = raw_prediction.get('banker', 0.5)
                        raw_outcome = 'player' if pred_player > pred_banker else 'banker'

                        # Récupérer la confiance et l'incertitude
                        confidence = max(pred_player, pred_banker)
                        uncertainty = last_prediction.get('uncertainty', 0.5)

                        # Mettre à jour l'optimiseur avec les résultats
                        self.wait_placement_optimizer.update(
                            features_vector,
                            raw_outcome,
                            last_prediction.get('recommendation', 'wait'),
                            actual_outcome,
                            confidence,
                            uncertainty
                        )

                        # Journaliser les statistiques de l'optimiseur
                        stats = self.wait_placement_optimizer.get_stats()
                        logger.debug(f"Statistiques de l'optimiseur de placement des WAIT: {stats}")
            except Exception as e:
                logger.error(f"Erreur lors de la mise à jour de l'optimiseur de placement des WAIT: {e}", exc_info=True)