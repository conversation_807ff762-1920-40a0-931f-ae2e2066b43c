# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 13004 à 13010
# Type: Méthode de la classe HybridBaccaratPredictor

            def error_callback(error_msg, best_params, duration):
                if ui_available:
                    self.root.after(0, lambda: self._finalize_optuna_optimization(
                        False, best_params, duration, error_msg
                    ))
                else:
                    logger_instance.error(f"Erreur lors de l'optimisation: {error_msg}")