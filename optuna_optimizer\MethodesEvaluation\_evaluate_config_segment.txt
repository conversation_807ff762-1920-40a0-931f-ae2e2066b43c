# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 11070 à 11250
# Type: Méthode de la classe OptunaOptimizer

    def _evaluate_config_segment(self, config, segment_indices, **kwargs):
        """
        Évalue une configuration sur un segment spécifique des données.

        Args:
            config: Configuration à évaluer
            segment_indices: Indices du segment de données à utiliser
            **kwargs: Arguments supplémentaires pour l'évaluation
                - is_viability_check: Indique si c'est une vérification de viabilité
                - force_lstm_training: Force l'entraînement LSTM même si is_viability_check est True
                - force_markov_training: Force l'utilisation de Markov même si is_viability_check est True
                - enable_cv: Activer la validation croisée
                - n_folds: Nombre de plis pour la validation croisée

        Returns:
            Dict: Résultats de l'évaluation
        """
        # Vérifier que les indices sont valides
        if segment_indices is None or len(segment_indices) == 0:
            logger.error("Erreur: segment_indices est None ou vide dans _evaluate_config_segment")
            # Retourner un résultat par défaut pour éviter les erreurs
            return {
                'score': 0.0,
                'non_wait_accuracy': 0.0,
                'wait_accuracy': 0.0,
                'wait_ratio': 0.5,
                'recommendation_rate': 0.5,
                'metrics': {'error': 'Indices vides'}
            }

        # Vérifier que les données sont disponibles
        if not hasattr(self, 'X_lgbm_full') or self.X_lgbm_full is None or not hasattr(self, 'y_full') or self.y_full is None:
            logger.error("Erreur: Données manquantes (X_lgbm_full ou y_full) dans _evaluate_config_segment")
            # Retourner un résultat par défaut pour éviter les erreurs
            return {
                'score': 0.0,
                'non_wait_accuracy': 0.0,
                'wait_accuracy': 0.0,
                'wait_ratio': 0.5,
                'recommendation_rate': 0.5,
                'metrics': {'error': 'Données manquantes'}
            }

        # Vérifier que les indices sont dans les limites des données
        if np.max(segment_indices) >= len(self.y_full):
            logger.error(f"Erreur: Indices hors limites dans _evaluate_config_segment. Max index: {np.max(segment_indices)}, Data size: {len(self.y_full)}")
            # Filtrer les indices valides
            valid_indices = segment_indices[segment_indices < len(self.y_full)]
            if len(valid_indices) == 0:
                # Retourner un résultat par défaut si aucun indice n'est valide
                return {
                    'score': 0.0,
                    'non_wait_accuracy': 0.0,
                    'wait_accuracy': 0.0,
                    'wait_ratio': 0.5,
                    'recommendation_rate': 0.5,
                    'metrics': {'error': 'Indices hors limites'}
                }
            # Utiliser uniquement les indices valides
            segment_indices = valid_indices
            logger.warning(f"Utilisation de {len(valid_indices)} indices valides sur {len(segment_indices)} demandés")

        # Extraire les données pour ce segment
        try:
            X_lgbm = self.X_lgbm_full[segment_indices]
            y = self.y_full[segment_indices]
            X_lstm = self.X_lstm_full[segment_indices] if self.X_lstm_full is not None else None
        except Exception as e:
            logger.error(f"Erreur lors de l'extraction des données pour le segment: {e}")
            # Retourner un résultat par défaut pour éviter les erreurs
            return {
                'score': 0.0,
                'non_wait_accuracy': 0.0,
                'wait_accuracy': 0.0,
                'wait_ratio': 0.5,
                'recommendation_rate': 0.5,
                'metrics': {'error': f'Erreur extraction: {str(e)}'}
            }

        # Utiliser la méthode d'évaluation existante avec ces données
        score, metrics = self._evaluate_config(
            config,
            is_viability_check=kwargs.get('is_viability_check', False),
            force_lstm_training=kwargs.get('force_lstm_training', False),
            force_markov_training=kwargs.get('force_markov_training', False),
            subset_indices=segment_indices,
            enable_cv=kwargs.get('enable_cv', False),
            n_folds=kwargs.get('n_folds', 3)
        )

        # Créer un dictionnaire de résultats
        result = {
            'score': score,
            'non_wait_accuracy': metrics.get('precision_non_wait', 0.0),
            'wait_accuracy': metrics.get('precision_wait', 0.0),
            'wait_ratio': metrics.get('wait_ratio', 0.0),
            'recommendation_rate': metrics.get('recommendation_rate', 0.0),
            'metrics': metrics
        }

        # Journalisation détaillée pour comprendre le comportement de l'optimisation
        segment_size = len(segment_indices)

        # Vérifier si score est None avant de le formater
        if score is not None:
            logger.debug(f"Évaluation segment ({segment_size} séquences) - Score: {score:.6f}")
        else:
            logger.debug(f"Évaluation segment ({segment_size} séquences) - Score: None")

        # Vérifier que les valeurs dans result sont non-None avant de les formater
        non_wait_accuracy = result.get('non_wait_accuracy', 0.0)
        wait_accuracy = result.get('wait_accuracy', 0.0)
        wait_ratio = result.get('wait_ratio', 0.0)
        recommendation_rate = result.get('recommendation_rate', 0.0)

        logger.debug(f"  Précision NON-WAIT: {non_wait_accuracy:.4f}, Précision WAIT: {wait_accuracy:.4f}")
        logger.debug(f"  Ratio WAIT: {wait_ratio:.4f}, Taux recommandation: {recommendation_rate:.4f}")

        # Journaliser les métriques de viabilité
        # Vérifier si metrics est None avant d'y accéder
        if metrics is not None:
            # Présence de recommandations dans les données cibles
            has_wait_in_target = metrics.get('has_wait_in_target', False)
            has_non_wait_in_target = metrics.get('has_non_wait_in_target', False)

            # Présence de recommandations dans les prédictions
            wait_count = metrics.get('wait_count', 0)
            non_wait_count = metrics.get('non_wait_count', 0)
            has_wait_in_predictions = wait_count > 0
            has_non_wait_in_predictions = non_wait_count > 0

            # Vérifier également les précisions
            wait_accuracy = metrics.get('precision_wait', 0.0)
            non_wait_accuracy = metrics.get('precision_non_wait', 0.0)

            # Seuils minimaux de précision pour considérer une configuration comme viable
            min_wait_accuracy_threshold = 0.15  # Seuil minimal pour la précision WAIT
            min_non_wait_accuracy_threshold = 0.30  # Seuil minimal pour la précision NON-WAIT

            # Une configuration est viable si:
            # 1. Les données cibles contiennent à la fois des recommandations WAIT et NON-WAIT
            # 2. Les prédictions contiennent à la fois des recommandations WAIT et NON-WAIT
            # 3. Les précisions sont au-dessus des seuils minimaux
            is_viable = metrics.get('viable', False)

            # Si viable n'est pas défini dans metrics, calculer en fonction des critères
            if 'viable' not in metrics:
                has_target_recommendations = has_wait_in_target and has_non_wait_in_target
                has_prediction_recommendations = has_wait_in_predictions and has_non_wait_in_predictions
                has_minimum_accuracy = (wait_accuracy >= min_wait_accuracy_threshold or not has_wait_in_target) and \
                                      (non_wait_accuracy >= min_non_wait_accuracy_threshold or not has_non_wait_in_target)

                is_viable = has_target_recommendations and has_prediction_recommendations and has_minimum_accuracy

                # Mettre à jour metrics avec la valeur calculée et les détails
                metrics['viable'] = is_viable
                metrics['has_target_recommendations'] = has_target_recommendations
                metrics['has_prediction_recommendations'] = has_prediction_recommendations
                metrics['has_minimum_accuracy'] = has_minimum_accuracy
        else:
            has_wait_in_target = False
            has_non_wait_in_target = False
            has_wait_in_predictions = False
            has_non_wait_in_predictions = False
            wait_accuracy = 0.0
            non_wait_accuracy = 0.0
            is_viable = False

        # Journaliser les détails de viabilité
        logger.debug(f"  Viabilité: {is_viable}")
        logger.debug(f"    Données cibles: WAIT={has_wait_in_target}, NON-WAIT={has_non_wait_in_target}")
        logger.debug(f"    Prédictions: WAIT={has_wait_in_predictions}, NON-WAIT={has_non_wait_in_predictions}")
        logger.debug(f"    Précisions: WAIT={wait_accuracy:.4f}, NON-WAIT={non_wait_accuracy:.4f}")

        # Journaliser les paramètres clés qui influencent la viabilité
        min_confidence = getattr(config, 'min_confidence_for_recommendation', 0.5)
        error_threshold = getattr(config, 'error_pattern_threshold', 0.5)
        transition_threshold = getattr(config, 'transition_uncertainty_threshold', 0.4)
        logger.debug(f"  Paramètres: min_confidence={min_confidence:.4f}, error_threshold={error_threshold:.4f}, transition_threshold={transition_threshold:.4f}")

        return result