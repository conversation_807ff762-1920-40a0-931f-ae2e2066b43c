# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 12488 à 12559
# Type: Méthode de la classe HybridBaccaratPredictor

    def _run_fast_update_async(self, save_after_update: bool = False, is_auto_trigger: bool = False) -> None:
        """
        Exécute la mise à jour rapide des modèles de manière asynchrone.

        Args:
            save_after_update: Si True, sauvegarde l'état après la mise à jour.
            is_auto_trigger: Si True, indique que la mise à jour a été déclenchée automatiquement.
        """
        logger_instance = getattr(self, 'logger', logging.getLogger(__name__))
        ui_available = self.is_ui_available()
        start_time = time.time()
        success = False
        summary = []

        try:
            logger_instance.info(f"Début mise à jour rapide asynchrone (auto={is_auto_trigger})...")
            if ui_available:
                self.root.after(0, lambda: self._update_progress(10, "Préparation mise à jour rapide..."))

            # Copie de la séquence actuelle pour traitement
            with self.sequence_lock:
                current_sequence = self.sequence[:]
                current_round_num = len(current_sequence)

                # Vérifier si la séquence est suffisamment longue
                if current_round_num < 10:
                    logger_instance.warning(f"Séquence trop courte pour mise à jour rapide ({current_round_num} < 10)")
                    if ui_available:
                        self.root.after(0, lambda: self._update_progress(0, "Séquence trop courte pour MàJ"))
                    return

                # Mettre à jour l'index de la dernière mise à jour incrémentale
                self.last_incremental_update_index = current_round_num

            # Mise à jour du modèle Markov (si disponible)
            if hasattr(self, 'markov') and self.markov:
                try:
                    with self.markov_lock:
                        self.markov.update_session(current_sequence)
                    logger_instance.info("Modèle Markov de session mis à jour")
                    summary.append("Markov")
                except Exception as e_markov:
                    logger_instance.error(f"Erreur mise à jour Markov: {e_markov}")

            # Mise à jour des autres modèles (LGBM, LSTM, etc.)
            # Cette partie dépend de l'implémentation spécifique de votre application
            # Ici, nous simulons simplement une mise à jour réussie

            if ui_available:
                self.root.after(0, lambda: self._update_progress(50, "Mise à jour des modèles..."))

            # Simuler une mise à jour réussie
            time.sleep(1)  # Simuler un traitement

            success = True
            summary.append("Modèles")

            logger_instance.info(f"Mise à jour rapide asynchrone terminée avec succès: {', '.join(summary)}")

        except Exception as e:
            logger_instance.error(f"Erreur pendant la mise à jour rapide asynchrone: {e}", exc_info=True)
            success = False
        finally:
            # Réinitialiser le flag de mise à jour rapide
            with self.training_lock:
                self.is_fast_updating = False

            # Finaliser la mise à jour (dans le thread UI)
            if ui_available:
                self.root.after(0, lambda: self._finalize_fast_update(success, start_time, summary, is_auto_trigger))
            else:
                logger_instance.info(f"Finalisation mise à jour rapide (UI non disponible): Succès={success}, Durée={time.time()-start_time:.1f}s")