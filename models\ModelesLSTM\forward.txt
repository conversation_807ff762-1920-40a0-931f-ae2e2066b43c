# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\models.py
# Lignes: 120 à 188
# Type: Méthode de la classe EnhancedLSTMModel

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Effectue la passe avant du modèle avec attention et connexions résiduelles.

        IMPORTANT: Cette fonction retourne des logits (non normalisés) qui doivent être
        traités avec softmax pour obtenir des probabilités. Les indices de sortie suivent
        le système zero-based standard de PyTorch:
        - Indice 0 = Player
        - Indice 1 = Banker
        """
        # Gérer les entrées 2D (batch_size=1 non explicite)
        if x.dim() == 2:
            # Ajoute la dimension batch: (seq_len, features) -> (1, seq_len, features)
            x = x.unsqueeze(0)
        elif x.dim() != 3:
            raise ValueError(f"Attendu input 2D ou 3D, reçu {x.dim()}D tensor.")

        batch_size = x.size(0)
        seq_len = x.size(1)
        device = x.device
        num_directions = 2 if self.bidirectional else 1

        # Normaliser l'entrée pour stabiliser l'entraînement et réduire la val_loss
        x = self.input_norm(x)

        # Appliquer le dropout à l'entrée
        x = self.input_dropout(x)

        # Initialiser les états cachés
        h0 = torch.zeros(self.num_layers * num_directions, batch_size, self.hidden_dim, device=device)
        c0 = torch.zeros(self.num_layers * num_directions, batch_size, self.hidden_dim, device=device)

        # LSTM - obtenir toutes les sorties de séquence
        lstm_out, _ = self.lstm(x, (h0, c0))  # lstm_out: (batch, seq_len, hidden*directions)

        # Appliquer l'attention si activée
        if self.use_attention:
            # Calculer les scores d'attention pour chaque pas de temps
            attention_scores = self.attention(lstm_out).squeeze(-1)  # (batch, seq_len)
            attention_weights = torch.softmax(attention_scores, dim=1)  # (batch, seq_len)

            # Appliquer les poids d'attention à la sortie LSTM
            attention_weights = attention_weights.unsqueeze(-1)  # (batch, seq_len, 1)
            weighted_output = lstm_out * attention_weights  # (batch, seq_len, hidden*directions)
            context_vector = weighted_output.sum(dim=1)  # (batch, hidden*directions)
        else:
            # Sans attention, utiliser simplement la dernière sortie
            context_vector = lstm_out[:, -1, :]  # (batch, hidden*directions)

        # Normalisation de couche
        normalized_out = self.layer_norm(context_vector)

        # Couche dense intermédiaire avec activation ReLU
        dense_out = self.fc1(normalized_out)
        activated_out = self.relu(dense_out)

        # Connexion résiduelle si activée
        if self.use_residual:
            combined_out = activated_out + normalized_out
        else:
            combined_out = activated_out

        # Dropout final
        dropped_out = self.dropout(combined_out)

        # Couche de sortie
        logits = self.fc2(dropped_out)  # (batch, output_size)

        return logits