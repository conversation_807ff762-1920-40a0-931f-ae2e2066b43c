DESCRIPTIF DÉTAILLÉ DES MÉTHODES - CALCUL DE CONFIANCE
================================================================================

Ce fichier contient la description détaillée des méthodes de calcul de confiance
pour les séquences consécutives et l'optimisation des recommandations.

DOMAINE FONCTIONNEL : Calcul de confiance, séquences consécutives et optimisation recommandations

TOTAL : 25 MÉTHODES ANALYSÉES

================================================================================

1. calculate_confidence.txt (ConsecutiveConfidenceCalculator.calculate_confidence - M<PERSON>THODE CALCUL CONFIANCE AVANCÉ)
   - Lignes 1665-2027 dans utils.py (363 lignes)
   - FONCTION : Calcule confiance pour recommandation basée sur données historiques avec facteurs multiples optimisés pour objectif 1
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * features (List[float]) - Vecteur features pour position actuelle
     * game_round (int) - Numéro manche actuelle (1-indexé)
     * config (optionnel) - Configuration prédicteur pour mise à jour paramètres
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MISE À JOUR CONFIG :** Met à jour paramètres depuis config si fourni (target_round_min/max, facteurs)
     * **VALIDATION MANCHES :** Vérifie si dans manches cibles (31-60), retourne confiance neutre sinon
     * **RECHERCHE PATTERNS :** find_similar_patterns() avec seuil similarité configurable
     * **LIMITATION PATTERNS :** Limite à max_similar_patterns pour éviter bruit
     * **STATISTIQUES AGRÉGÉES :** Calcule total_occurrences, total_success pondérés par similarité
     * **SÉQUENCES CONSÉCUTIVES :** Analyse consecutive_lengths avec moyenne et médiane pondérées
     * **POSITION PLAGE :** Calcule position_in_range dans manches cibles pour bonus adaptatif
     * **BONUS CLOCHE :** Applique bell_curve_bonus maximal au milieu de plage cible
     * **FACTEURS MULTIPLICATIFS :** occurrence_factor, consecutive_factor, sequence_bonus selon performances
     * **CONFIANCE PONDÉRÉE :** Combine success_rate_weight, consecutive_length_weight, pattern_frequency_weight
     * **RECOMMANDATION WAIT :** Analyse conditions pour recommander WAIT avec seuils adaptatifs
     * **ÉQUILIBRAGE RATIO :** Ajuste selon current_wait_ratio vs optimal_wait_ratio
     * **FOCUS CONSÉCUTIF :** Priorité absolue aux séquences consécutives en milieu de plage
     * **LOGGING DÉTAILLÉ :** Journalise décisions avec métriques complètes pour débogage
   - RETOUR : Dict[str, Any] - Dictionnaire complet avec confidence, expected_consecutive, similar_patterns_count, success_rate, wait_recommendation, etc.
   - UTILITÉ : Cœur du système de confiance avec optimisations spécifiques pour maximiser séquences consécutives NON-WAIT

2. calculate_performance_based_confidence.txt (ConsecutiveConfidenceCalculator.calculate_performance_based_confidence - CONFIANCE BASÉE PERFORMANCE)
   - Lignes 557-620 dans utils.py (64 lignes)
   - FONCTION : Calcule score confiance basé performances entraînement avec facteurs multiples optimisés
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * features (List[float]) - Vecteur features position actuelle
     * round_num (int) - Numéro manche actuelle
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION PATTERN :** Utilise _extract_pattern_key pour identifier pattern
     * **STATISTIQUES PATTERN :** Récupère total, success, consecutive_lengths depuis pattern_stats
     * **TAUX SUCCÈS :** success_rate = success/total si total >= min_occurrences, sinon 0.5
     * **FACTEUR OCCURRENCE :** occurrence_factor = 1.0 + (total/occurrence_factor_divisor) limité à max_occurrence_factor
     * **FACTEUR CONSÉCUTIF :** Combine moyenne et maximum consecutive_lengths avec consecutive_factor_divisor
     * **FACTEUR FIN PARTIE :** late_game_factor pour manches 31-60 (target_round_min/max)
     * **BONUS CLOCHE :** bell_curve_bonus = 1.0 + 0.2 * (1.0 - 4.0 * (relative_pos - 0.5)²) au milieu plage
     * **BONUS SÉQUENCE :** sequence_bonus si max_consecutive_length >= sequence_bonus_threshold
     * **PONDÉRATION FINALE :** Combine success_rate_weight, consecutive_length_weight, pattern_frequency_weight
     * **LIMITATION 0-1 :** min(1.0, max(0.0, confidence)) pour borner résultat
   - RETOUR : float - Score confiance entre 0.0 et 1.0
   - UTILITÉ : Calcul confiance sophistiqué avec optimisations spécifiques objectif 1 et facteurs multiples

3. find_similar_patterns.txt (ConsecutiveConfidenceCalculator.find_similar_patterns - RECHERCHE PATTERNS SIMILAIRES)
   - Lignes 1665-1727 dans utils.py (63 lignes)
   - FONCTION : Recherche patterns historiques similaires au pattern actuel avec calcul similarité
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * current_pattern (List[float]) - Pattern actuel à comparer
     * similarity_threshold (float, défaut=0.8) - Seuil minimum similarité
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION CLÉ :** Utilise _extract_pattern_key pour normalisation pattern
     * **PARCOURS HISTORIQUE :** Itère sur historical_data pour comparaisons
     * **CALCUL SIMILARITÉ :** Distance euclidienne normalisée entre patterns
     * **FILTRAGE SEUIL :** Retient uniquement patterns avec similarité >= threshold
     * **LIMITATION RÉSULTATS :** Limite à max_similar_patterns pour performance
     * **TRI PERTINENCE :** Ordonne par similarité décroissante
   - RETOUR : List[Dict] - Liste patterns similaires avec métadonnées (similarity, success, consecutive_length)
   - UTILITÉ : Base de l'analyse prédictive par comparaison patterns historiques

4. find_similar_patterns_1.txt (ConsecutiveConfidenceCalculator.find_similar_patterns - RECHERCHE PATTERNS SIMILAIRES - DOUBLON 1)
   - Lignes 1665-1727 dans utils.py (63 lignes)
   - FONCTION : Recherche patterns historiques similaires au pattern actuel avec calcul similarité - Version identique
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * current_pattern (List[float]) - Pattern actuel à comparer
     * similarity_threshold (float, défaut=0.8) - Seuil minimum similarité
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION CLÉ :** Utilise _extract_pattern_key pour normalisation pattern
     * **PARCOURS HISTORIQUE :** Itère sur historical_data pour comparaisons
     * **CALCUL SIMILARITÉ :** Distance euclidienne normalisée entre patterns
     * **FILTRAGE SEUIL :** Retient uniquement patterns avec similarité >= threshold
     * **LIMITATION RÉSULTATS :** Limite à max_similar_patterns pour performance
     * **TRI PERTINENCE :** Ordonne par similarité décroissante
   - RETOUR : List[Dict] - Liste patterns similaires avec métadonnées (similarity, success, consecutive_length)
   - UTILITÉ : Base de l'analyse prédictive par comparaison patterns historiques

5. get_confidence_adjustment.txt (ConsecutiveConfidenceCalculator.get_confidence_adjustment - AJUSTEMENT CONFIANCE ADAPTATIF)
   - Lignes 1226-1275 dans utils.py (50 lignes)
   - FONCTION : Calcule ajustement confiance basé performances récentes avec interpolation linéaire et équilibrage ratio WAIT
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION DONNÉES :** Retourne 0.0 si recent_outcomes absent ou < 5 éléments
     * **TAUX SUCCÈS RÉCENT :** Appelle _calculate_recent_success_rate() pour performance actuelle
     * **AJUSTEMENT BASE :** Si recent_success_rate >= 0.7, base_adjustment = -0.03 (agressif)
     * **AJUSTEMENT CONSERVATEUR :** Si recent_success_rate <= 0.3, base_adjustment = 0.03 (prudent)
     * **INTERPOLATION LINÉAIRE :** normalized_rate = (recent_success_rate - 0.3) / 0.4 pour zone 0.3-0.7
     * **CALCUL INTERPOLÉ :** base_adjustment = 0.03 - (normalized_rate * 0.06) pour transition douce
     * **RATIO WAIT ACTUEL :** wait_ratio = get_current_wait_ratio() pour équilibrage
     * **RATIO OPTIMAL :** optimal_wait_ratio depuis attribut ou 0.4 par défaut
     * **AJUSTEMENT RATIO :** Si abs(wait_ratio - optimal_wait_ratio) > 0.1, ratio_adjustment ±0.02
     * **COMBINAISON :** total_adjustment = base_adjustment + ratio_adjustment
     * **LIMITATION :** np.clip(total_adjustment, -0.05, 0.05) pour plage sécurisée
   - RETOUR : float - Ajustement confiance entre -0.05 et 0.05
   - UTILITÉ : Adaptation fine confiance avec équilibrage performance/prudence et ratio WAIT optimal

6. get_current_wait_ratio.txt (ConsecutiveConfidenceCalculator.get_current_wait_ratio - CALCUL RATIO WAIT ACTUEL)
   - Lignes 841-880 dans utils.py (40 lignes)
   - FONCTION : Calcule ratio WAIT actuel depuis recommandations récentes avec gestion robuste et bornage epsilon
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * config (optionnel) - Configuration pour optimal_wait_ratio alternatif
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EPSILON SÉCURITÉ :** epsilon = 1e-6 pour éviter divisions par zéro
     * **RATIO OPTIMAL :** optimal_ratio_candidate depuis config.optimal_wait_ratio ou self.optimal_wait_ratio
     * **FALLBACK :** Si optimal_ratio_candidate None, utilise 0.4 avec warning
     * **BORNAGE OPTIMAL :** bounded_optimal_ratio entre epsilon et 1.0-epsilon
     * **VALIDATION DONNÉES :** Retourne bounded_optimal_ratio si recent_recommendations vide
     * **COMPTAGE WAIT :** wait_count = sum(1 for rec if rec.lower() == 'wait') avec isinstance(rec, str)
     * **CALCUL RATIO :** ratio = wait_count / total_count avec vérification total_count > 0
     * **BORNAGE FINAL :** final_ratio entre epsilon et 1.0-epsilon avec logging détaillé
     * **LOGGING DEBUG :** Messages détaillés pour chaque étape et ajustement
   - RETOUR : float - Ratio WAIT entre epsilon et 1.0-epsilon
   - UTILITÉ : Mesure robuste ratio WAIT actuel pour équilibrage et adaptation dynamique

7. get_current_wait_ratio_1.txt (ConsecutiveConfidenceCalculator.get_current_wait_ratio - CALCUL RATIO WAIT SIMPLE - DOUBLON 1)
   - Lignes 1200-1224 dans utils.py (25 lignes)
   - FONCTION : Calcule ratio WAIT actuel version simplifiée sans gestion epsilon ni config
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SIMPLE :** Retourne 0.4 si recent_recommendations absent
     * **COMPTAGE WAIT :** wait_count = sum(1 for rec if rec.lower() == 'wait') avec isinstance(rec, str)
     * **CALCUL DIRECT :** ratio = wait_count / total_count sans bornage epsilon
     * **LOGGING DEBUG :** "Ratio WAIT actuel (méthode 2): X (Y/Z)" pour identification version
     * **FALLBACK :** Retourne 0.4 si total_count == 0
   - RETOUR : float - Ratio WAIT entre 0.0 et 1.0
   - UTILITÉ : Version simplifiée calcul ratio WAIT sans complexité epsilon

8. get_current_wait_ratio_2.txt (ConsecutiveConfidenceCalculator.get_current_wait_ratio - CALCUL RATIO WAIT OPTIMAL - DOUBLON 2)
   - Lignes 1401-1422 dans utils.py (22 lignes)
   - FONCTION : Calcule ratio WAIT actuel avec fallback sur optimal_ratio depuis config
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * config (optionnel) - Configuration pour optimal_wait_ratio
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RATIO OPTIMAL :** optimal_ratio = getattr(config, 'optimal_wait_ratio', self.optimal_wait_ratio)
     * **FALLBACK OPTIMAL :** Retourne optimal_ratio si recent_recommendations vide
     * **COMPTAGE STANDARD :** wait_count avec isinstance(rec, str) et rec.lower() == 'wait'
     * **CALCUL SIMPLE :** wait_count / total_count si total_count > 0 sinon optimal_ratio
     * **SANS LOGGING :** Version épurée sans debug logging
   - RETOUR : float - Ratio WAIT ou optimal_ratio
   - UTILITÉ : Version optimisée calcul ratio WAIT avec fallback intelligent sur optimal_ratio

9. should_wait.txt (WaitPlacementOptimizer.should_wait - DÉCISION RECOMMANDATION WAIT)
   - Lignes 3529-3642 dans utils.py (114 lignes)
   - FONCTION : Détermine si recommandation WAIT devrait être faite pour position actuelle avec analyse multi-facteurs
   - PARAMÈTRES :
     * self - Instance de la classe WaitPlacementOptimizer
     * features (List[float]) - Vecteur features pour position actuelle
     * current_consecutive_valid (int, défaut=0) - Nombre recommandations NON-WAIT valides consécutives
     * round_num (int, défaut=0) - Numéro manche actuelle
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MANCHES :** Vérifie si dans manches cibles (target_round_min/max), logique simplifiée sinon
     * **CRÉATION PATTERN :** Utilise _create_pattern_key pour identifier pattern actuel
     * **PROBABILITÉ ERREUR :** Calcule error_probability depuis error_patterns avec seuil min_pattern_occurrences
     * **DÉTECTION TRANSITION :** Analyse outcome_history pour identifier transitions et probabilité erreur associée
     * **EFFICACITÉ WAIT :** Calcule recent_wait_efficiency depuis wait_efficiency_history sur fenêtre récente
     * **FACTEUR CONSÉCUTIF :** consecutive_factor = 1.0 + (current_consecutive_valid * 0.1 * consecutive_priority_factor)
     * **SCORE WAIT :** Combine error_component et transition_component pondérés par poids respectifs
     * **AJUSTEMENT CONSÉCUTIF :** Divise wait_score par consecutive_factor pour favoriser séquences
     * **AJUSTEMENT EFFICACITÉ :** Réduit score si recent_wait_efficiency < wait_efficiency_threshold
     * **ÉQUILIBRAGE RATIO :** Applique pénalité/bonus selon current_wait_ratio vs wait_ratio_min/max
     * **DÉCISION FINALE :** should_wait = wait_score > error_pattern_threshold
     * **RAISON DÉTAILLÉE :** Identifie cause principale décision (pattern erreur, transition, ratio, séquence)
   - RETOUR : Dict[str, Any] - Dictionnaire complet avec should_wait, wait_score, probabilités, facteurs, raison
   - UTILITÉ : Décision intelligente WAIT avec optimisation séquences consécutives et équilibrage performance

10. should_wait_1.txt (WaitPlacementOptimizer.should_wait - DÉCISION WAIT AVANCÉE - DOUBLON 1)
   - Lignes 3897-4056 dans utils.py (160 lignes)
   - FONCTION : Détermine recommandation WAIT avec algorithme sophistiqué multi-facteurs et seuils adaptatifs
   - PARAMÈTRES :
     * self - Instance de la classe WaitPlacementOptimizer
     * features (List[float]) - Vecteur features position actuelle
     * prediction (str) - Prédiction actuelle ('player' ou 'banker')
     * confidence (float) - Niveau confiance prédiction
     * uncertainty (float) - Niveau incertitude prédiction
     * round_num (int) - Numéro manche actuelle
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CLÉ PATTERN :** pattern_key = _extract_pattern_key(features) pour identification
     * **SCORE DÉCISION :** decision_score = 0.0 et decision_factors = {} pour traçabilité
     * **FACTEUR ERREUR :** Si pattern_key in error_patterns, error_rate avec adjusted_error_threshold * 0.9
     * **AMPLIFICATION ERREUR :** error_factor = error_rate * 1.2 pour impact renforcé
     * **PRUDENCE PATTERN :** caution_factor = 0.2 pour patterns inconnus
     * **FACTEUR TRANSITION :** Analyse outcome_history[-2:] pour transitions avec adjusted_transition_threshold * 0.9
     * **AMPLIFICATION TRANSITION :** transition_factor = transition_error_rate * 1.2
     * **FACTEUR CONFIANCE :** adjusted_confidence_threshold * 1.1 avec confidence_factor * 1.2
     * **FACTEUR INCERTITUDE :** adjusted_uncertainty_threshold * 0.9 avec uncertainty_factor * 1.2
     * **PROTECTION SÉQUENCES :** Si current_consecutive_valid >= 2, streak_protection_factor = 0.15 + (consecutive * 0.07)
     * **SÉQUENCES LONGUES :** Si consecutive >= 4, long_streak_factor = 0.2 supplémentaire
     * **PRÉCISION RÉCENTE :** recent_accuracy sur 5 dernières avec recent_accuracy_factor si < 0.6
     * **EFFICACITÉ WAIT :** Si wait_efficiency > 0.7, efficiency_factor = (efficiency - 0.7) * 0.5
     * **SEUIL ADAPTATIF :** adaptive_threshold = 0.45-0.55 selon recent_accuracy
     * **DÉCISION FINALE :** should_make_wait = decision_score >= adaptive_threshold
   - RETOUR : Tuple[bool, float, Dict] - (should_wait, decision_score, decision_factors)
   - UTILITÉ : Décision WAIT sophistiquée avec protection séquences et adaptation dynamique

11. train.txt (ConsecutiveConfidenceCalculator.train - ENTRAÎNEMENT CALCULATEUR CONFIANCE)
   - Lignes 668-737 dans utils.py (70 lignes)
   - FONCTION : Entraîne calculateur confiance avec données historiques pour construction pattern_stats optimisé
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * training_data (List[Dict[str, Any]]) - Données entraînement avec round_num, features, outcome, recommendation, is_valid, confidence
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION DONNÉES :** Retourne avec warning si training_data vide
     * **RÉINITIALISATION :** pattern_stats = defaultdict avec total, success, consecutive_lengths, max_consecutive
     * **COMPTEUR SÉQUENCES :** consecutive_valid_count = 0 pour suivi séquences consécutives
     * **PARCOURS DONNÉES :** Pour chaque sample, extrait features, round_num, recommendation, outcome, is_valid
     * **CLÉ PATTERN :** pattern_key = _extract_pattern_key(features) pour identification
     * **FILTRAGE NON-WAIT :** Traite uniquement recommendation != 'WAIT'
     * **STATS PATTERN :** Incrémente pattern_stats[pattern_key]["total"]
     * **SUCCÈS CONSÉCUTIFS :** Si is_valid, incrémente success et consecutive_valid_count
     * **LONGUEURS SÉQUENCES :** Ajoute consecutive_valid_count à consecutive_lengths
     * **MAXIMUM CONSÉCUTIF :** Met à jour max_consecutive pour pattern
     * **RÉINITIALISATION :** consecutive_valid_count = 0 si not is_valid
     * **STATS GLOBALES :** Calcule total_patterns, total_samples, success_rate, max_consecutive
   - RETOUR : None (mise à jour interne pattern_stats)
   - UTILITÉ : Construction base données patterns pour calcul confiance basé historique performance

12. train_1.txt (WaitPlacementOptimizer.train - ENTRAÎNEMENT OPTIMISEUR WAIT - DOUBLON 1)
   - Lignes 3412-3527 dans utils.py (116 lignes)
   - FONCTION : Entraîne optimiseur WAIT avec données historiques pour identification patterns erreur et transitions
   - PARAMÈTRES :
     * self - Instance de la classe WaitPlacementOptimizer
     * training_data (List[Dict]) - Données entraînement avec features, outcome, recommendation, prediction, round_num
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION DONNÉES :** Retourne avec warning si training_data vide
     * **RÉINITIALISATION :** error_patterns = {}, transition_patterns = {}, historiques = []
     * **ANALYSE SÉQUENCES :** Parcours training_data[i] et training_data[i+1] pour patterns
     * **CLÉ PATTERN :** pattern_key = _create_pattern_key(features) pour identification
     * **HISTORIQUES :** Ajoute à pattern_history, outcome_history, recommendation_history
     * **DÉTECTION ERREUR :** would_be_error = (next_prediction != next_outcome et != 'WAIT')
     * **STATS PATTERNS :** Met à jour error_patterns[pattern_key] avec total et errors
     * **ANALYSE TRANSITIONS :** Si prev_outcome != current_outcome, transition_key = "prev_current"
     * **STATS TRANSITIONS :** Met à jour transition_patterns[transition_key] avec total et errors
     * **LIMITATION HISTORIQUES :** Maintient à max_pattern_history avec slice [-max:]
     * **EFFICACITÉ WAIT :** Analyse wait_indices pour effective_waits vs missed_opportunities
     * **CALCUL EFFICACITÉ :** wait_efficiency = effective_waits / total_waits
     * **RATIO WAIT :** current_wait_ratio = wait_count / total_count
     * **ADAPTATION SEUILS :** Appelle _adapt_thresholds() si adaptive_thresholds et len > 50
   - RETOUR : None (mise à jour interne patterns et statistiques)
   - UTILITÉ : Construction base patterns erreur et transitions pour optimisation décisions WAIT

13. train_consecutive_confidence_calculator.txt (ConsecutiveConfidenceCalculator.train_consecutive_confidence_calculator - ENTRAÎNEMENT CALCULATEUR COMPLET)
   - Lignes 955-1081 dans utils.py (127 lignes)
   - FONCTION : Entraîne calculateur confiance avec données LGBM ou historiques avec génération automatique échantillons
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * X_lgbm (array, optionnel) - Features LGBM (ignoré si training_data fourni)
     * y_lgbm (array, optionnel) - Labels LGBM (ignoré si training_data fourni)
     * train_indices (array, optionnel) - Indices entraînement (ignoré si training_data fourni)
     * val_indices (array, optionnel) - Indices validation (ignoré si training_data fourni)
     * training_data (List[Dict], optionnel) - Données entraînement directes
   - FONCTIONNEMENT DÉTAILLÉ :
     * **GÉNÉRATION DONNÉES :** Si training_data None et X_lgbm fourni, génère training_data depuis X_lgbm/y_lgbm
     * **INDICES ENTRAÎNEMENT :** Utilise train_indices si fourni, sinon range(len(X_lgbm))
     * **CONVERSION LABELS :** actual_outcome = 'banker' si y_lgbm[i] == 0 sinon 'player'
     * **SIMULATION PRÉDICTION :** prediction = actual_outcome, confidence = 0.7 fixe pour entraînement
     * **SEUIL NON-WAIT :** min_confidence depuis config.min_confidence_for_recommendation ou 0.6
     * **DÉTERMINATION WAIT :** is_non_wait = confidence >= min_confidence
     * **ÉCHANTILLON :** sample avec round_num, features, outcome, recommendation, is_valid, confidence
     * **DONNÉES HISTORIQUES :** Si X_lgbm absent, utilise self.historical_data avec _extract_lgbm_features
     * **PRÉDICTION LGBM :** Si lgbm_base disponible, proba = lgbm_base.predict_proba([features])[0]
     * **PRÉDICTION ALÉATOIRE :** Si LGBM échoue, random.choice(['player', 'banker']) avec confidence 0.5-0.7
     * **VALIDATION :** is_valid = prediction == actual_outcome pour chaque échantillon
     * **ENTRAÎNEMENT FINAL :** consecutive_confidence_calculator.train(training_data) si initialisé
   - RETOUR : bool - True si entraînement réussi, False sinon
   - UTILITÉ : Entraînement complet calculateur avec génération automatique données depuis LGBM ou historique

14. update.txt (WaitPlacementOptimizer.update - MISE À JOUR OPTIMISEUR WAIT)
   - Lignes 4058-4153 dans utils.py (96 lignes)
   - FONCTION : Met à jour optimiseur WAIT avec résultats décision pour apprentissage adaptatif complet
   - PARAMÈTRES :
     * self - Instance de la classe WaitPlacementOptimizer
     * features (List[float]) - Vecteur features utilisé pour prédiction
     * prediction (str) - Prédiction brute ('player' ou 'banker')
     * recommendation (str) - Recommandation finale ('player', 'banker' ou 'wait')
     * outcome (str) - Résultat réel ('player' ou 'banker')
     * confidence (float) - Niveau confiance prédiction
     * uncertainty (float) - Niveau incertitude prédiction
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CLÉ PATTERN :** pattern_key = _extract_pattern_key(features) pour identification
     * **MISE À JOUR HISTORIQUES :** Ajoute à recommendation_history, prediction_history, outcome_history, confidence_history, uncertainty_history
     * **LIMITATION TAILLE :** Maintient historiques à max_history_size avec pop(0)
     * **COMPTEUR DÉCISIONS :** Incrémente total_decisions
     * **SÉQUENCES NON-WAIT :** Si recommendation != 'wait', vérifie is_correct = (recommendation == outcome)
     * **CONSÉCUTIVES VALIDES :** Si correct, incrémente current_consecutive_valid et correct_non_wait_decisions
     * **MAXIMUM CONSÉCUTIF :** Met à jour max_consecutive_valid si dépassé
     * **RÉINITIALISATION :** current_consecutive_valid = 0 si incorrect
     * **GESTION WAIT :** Si recommendation == 'wait', incrémente total_waits
     * **EFFICACITÉ WAIT :** Si prediction != outcome, incrémente effective_waits et correct_wait_decisions
     * **OPPORTUNITÉS MANQUÉES :** Si prediction == outcome, incrémente missed_opportunities
     * **PATTERNS ERREUR :** Met à jour error_patterns[pattern_key] avec total et errors
     * **PATTERNS TRANSITION :** Analyse outcome_history[-2:] pour transitions et erreurs associées
     * **ADAPTATION SEUILS :** Appelle _adapt_thresholds() tous les 50 décisions si adaptive_thresholds
   - RETOUR : None (mise à jour complète état interne)
   - UTILITÉ : Apprentissage continu optimiseur WAIT avec suivi performance et adaptation automatique

15. update_with_result.txt (WaitPlacementOptimizer.update_with_result - MISE À JOUR RÉSULTAT)
   - Lignes 3644-3720 dans utils.py (77 lignes)
   - FONCTION : Met à jour optimiseur avec résultat recommandation pour apprentissage adaptatif
   - PARAMÈTRES :
     * self - Instance de la classe WaitPlacementOptimizer
     * features (List[float]) - Vecteur features utilisé pour recommandation
     * was_wait (bool) - Si recommandation était WAIT
     * was_correct (bool) - Si recommandation était correcte
     * actual_outcome (str) - Résultat réel ('banker', 'player', 'tie')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MISE À JOUR HISTORIQUE :** Ajoute actual_outcome à outcome_history avec limitation taille
     * **PATTERN ERREUR :** Met à jour error_patterns si was_wait=False et was_correct=False
     * **PATTERN TRANSITION :** Analyse transitions dans outcome_history pour mise à jour transition_patterns
     * **EFFICACITÉ WAIT :** Calcule et stocke efficacité si was_wait=True dans wait_efficiency_history
     * **RATIO WAIT :** Recalcule current_wait_ratio basé sur historique récent
     * **ADAPTATION SEUILS :** Appelle _adapt_thresholds pour ajustement dynamique paramètres
   - RETOUR : None (mise à jour interne)
   - UTILITÉ : Apprentissage continu pour optimisation adaptative placement WAIT

16. _adapt_thresholds.txt (WaitPlacementOptimizer._adapt_thresholds - ADAPTATION SEUILS DYNAMIQUE)
   - Lignes 3766-3795 dans utils.py (30 lignes)
   - FONCTION : Adapte seuils décision WAIT en fonction performances récentes avec apprentissage automatique
   - PARAMÈTRES :
     * self - Instance de la classe WaitPlacementOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CALCUL TAUX SUCCÈS :** wait_success_rate et non_wait_success_rate depuis statistiques
     * **RATIO OPTIMAL :** Augmente current_wait_ratio si WAIT plus efficaces, diminue sinon
     * **AJUSTEMENT LEARNING_RATE :** Utilise learning_rate pour modifications graduelles
     * **ADAPTATION ERROR_THRESHOLD :** Réduit seuil si besoin plus WAIT, augmente si moins
     * **SYNCHRONISATION SEUILS :** transition_uncertainty_threshold = error_pattern_threshold
     * **CONTRAINTES LIMITES :** Respecte wait_ratio_min/max et seuils 0.3-0.9
     * **LOGGING DÉTAILLÉ :** Debug avec valeurs précises pour monitoring
   - RETOUR : None (modification interne seuils)
   - UTILITÉ : Apprentissage adaptatif pour optimisation continue performance WAIT

17. _adapt_thresholds_1.txt (WaitPlacementOptimizer._adapt_thresholds - ADAPTATION SEUILS AVANCÉE - DOUBLON 1)
   - Lignes 4174-4253 dans utils.py (80 lignes)
   - FONCTION : Adapte seuils décision WAIT avec algorithme sophistiqué multi-facteurs pour optimisation performance
   - PARAMÈTRES :
     * self - Instance de la classe WaitPlacementOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CALCUL TAUX SUCCÈS :** wait_success_rate = correct_wait_decisions/total_waits, non_wait_success_rate similaire
     * **EFFICACITÉ WAIT :** wait_efficiency = effective_waits/total_waits pour mesurer évitement erreurs
     * **SÉQUENCES MOYENNES :** avg_consecutive_valid = max_consecutive_valid/2 pour analyse longueur
     * **AJUSTEMENT EFFICACITÉ :** Si wait_efficiency > 0.7, efficiency_adjustment = (wait_efficiency - 0.7) * 0.3
     * **AJUSTEMENT SUCCÈS :** Si wait_success_rate > non_wait_success_rate * 1.2, success_adjustment = 0.05
     * **AJUSTEMENT CONSÉCUTIF :** Si avg_consecutive_valid < 3, consecutive_adjustment = 0.05 (prudence)
     * **RATIO OPTIMAL :** base_optimal_ratio + efficiency_adjustment + success_adjustment + consecutive_adjustment
     * **LEARNING RATE ADAPTATIF :** adaptive_learning_rate = learning_rate * (1 + 2 * abs(current_wait_ratio - optimal_ratio))
     * **AJUSTEMENT SEUILS :** Si current_wait_ratio < optimal_ratio, réduit error_pattern_threshold, confidence_threshold, uncertainty_threshold
     * **SYNCHRONISATION :** transition_uncertainty_threshold = error_pattern_threshold
     * **LOGGING DÉTAILLÉ :** Journalise tous seuils avec optimal_ratio, wait_efficiency, avg_consecutive_valid
   - RETOUR : None (modification interne seuils adaptatifs)
   - UTILITÉ : Adaptation intelligente seuils avec algorithme multi-facteurs pour optimisation continue performance WAIT

18. _calculate_recent_success_rate.txt (ConsecutiveConfidenceCalculator._calculate_recent_success_rate - CALCUL TAUX SUCCÈS RÉCENT)
   - Lignes 1277-1309 dans utils.py (33 lignes)
   - FONCTION : Calcule taux succès recommandations récentes avec gestion robuste erreurs et comparaisons insensibles casse
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ATTRIBUTS :** Vérifie existence recent_recommendations et recent_outcomes, retourne 0.5 si absent
     * **COHÉRENCE DIMENSIONS :** Vérifie len(recent_recommendations) == len(recent_outcomes), warning si incohérent
     * **FILTRAGE NON-WAIT :** non_wait_indices = [i for rec if rec.lower() != 'wait'] avec isinstance(rec, str)
     * **GESTION VIDE :** Retourne 0.5 si aucune recommandation NON-WAIT trouvée
     * **COMPTAGE SUCCÈS :** success_count pour i in non_wait_indices avec comparaison insensible casse
     * **COMPARAISON ROBUSTE :** recent_recommendations[i].lower() == recent_outcomes[i].lower() avec isinstance vérifications
     * **LOGGING DEBUG :** "Taux succès récent: X/Y = Z" pour traçabilité calculs
     * **CALCUL FINAL :** success_count / len(non_wait_indices) pour taux précis
   - RETOUR : float - Taux succès entre 0.0 et 1.0 (0.5 par défaut si problème)
   - UTILITÉ : Mesure performance récente robuste pour adaptation dynamique paramètres confiance

19. _create_pattern_key.txt (WaitPlacementOptimizer._create_pattern_key - CRÉATION CLÉ PATTERN WAIT)
   - Lignes 3644-3670 dans utils.py (27 lignes)
   - FONCTION : Crée clé pattern simplifiée à partir features pour WaitPlacementOptimizer avec discrétisation
   - PARAMÈTRES :
     * self - Instance de la classe WaitPlacementOptimizer
     * features (List[float]) - Liste des features à convertir en clé
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION VIDE :** Retourne "empty_pattern" si features vide ou None
     * **LIMITATION FEATURES :** Utilise maximum 5 premières features pour éviter clés trop complexes
     * **DISCRÉTISATION INDEXÉE :** Pour chaque feature i, crée format "i_valeur_arrondie"
     * **ARRONDI DÉCIMAL :** round(value * 10) / 10 pour arrondir à 1 décimale
     * **CONSTRUCTION CLÉ :** Joint avec "_" pour créer clé unique pattern
     * **APPROCHE SIMPLIFIÉE :** Plus simple que ConsecutiveConfidenceCalculator pour performance
   - RETOUR : str - Clé pattern format "0_1.2_1_0.8_2_0.5..." ou "empty_pattern"
   - UTILITÉ : Identification rapide patterns pour WaitPlacementOptimizer avec performance optimisée

20. _extract_pattern_key.txt (ConsecutiveConfidenceCalculator._extract_pattern_key - EXTRACTION CLÉ PATTERN)
   - Lignes 622-666 dans utils.py (45 lignes)
   - FONCTION : Extrait clé pattern à partir vecteur features pour identification et comparaison patterns
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * features (List[float]) - Vecteur features à convertir en clé pattern
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉE :** Vérifie features non None, type valide (list/tuple/ndarray) et non vide
     * **LIMITATION FEATURES :** Limite à max_pattern_length pour éviter clés trop longues
     * **SÉLECTION FEATURES :** Prend features[:max_features] pour standardisation
     * **DISCRÉTISATION ADAPTATIVE :** Traitement différencié selon plage valeurs
     * **FEATURES NORMALISÉES :** Si 0≤feature≤1, discrétise en 5 niveaux (0.0, 0.25, 0.5, 0.75, 1.0)
     * **AUTRES FEATURES :** Arrondit à l'entier le plus proche pour réduction variabilité
     * **FEATURES NON-NUMÉRIQUES :** Conversion str() pour compatibilité
     * **CRÉATION CLÉ :** Joint discretized_features avec "_" comme séparateur
     * **GESTION ERREURS :** Try/catch avec retour "error_pattern" si exception
     * **LOGGING :** Journalise erreurs avec exc_info=True pour débogage
   - RETOUR : str - Clé pattern discrétisée ("empty_pattern" si vide, "error_pattern" si erreur)
   - UTILITÉ : Normalisation features en clés patterns pour recherche similarité et stockage efficace

21. _extract_pattern_key_1.txt (ConsecutiveConfidenceCalculator._extract_pattern_key - EXTRACTION CLÉ PATTERN - DOUBLON 1)
   - Lignes 622-666 dans utils.py (45 lignes)
   - FONCTION : Extrait clé pattern à partir vecteur features pour identification et comparaison patterns - Version identique
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * features (List[float]) - Vecteur features à convertir en clé pattern
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉE :** Vérifie features non None, type valide (list/tuple/ndarray) et non vide
     * **LIMITATION FEATURES :** Limite à max_pattern_length pour éviter clés trop longues
     * **SÉLECTION FEATURES :** Prend features[:max_features] pour standardisation
     * **DISCRÉTISATION ADAPTATIVE :** Traitement différencié selon plage valeurs
     * **FEATURES NORMALISÉES :** Si 0≤feature≤1, discrétise en 5 niveaux (0.0, 0.25, 0.5, 0.75, 1.0)
     * **AUTRES FEATURES :** Arrondit à l'entier le plus proche pour réduction variabilité
     * **FEATURES NON-NUMÉRIQUES :** Conversion str() pour compatibilité
     * **CRÉATION CLÉ :** Joint discretized_features avec "_" comme séparateur
     * **GESTION ERREURS :** Try/catch avec retour "error_pattern" si exception
     * **LOGGING :** Journalise erreurs avec exc_info=True pour débogage
   - RETOUR : str - Clé pattern discrétisée ("empty_pattern" si vide, "error_pattern" si erreur)
   - UTILITÉ : Normalisation features en clés patterns pour recherche similarité et stockage efficace
