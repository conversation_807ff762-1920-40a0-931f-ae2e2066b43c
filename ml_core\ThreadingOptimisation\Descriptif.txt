DESCRIPTIF DÉTAILLÉ DES MÉTHODES - THREADING ET OPTIMISATION
================================================================================

Ce fichier contient la description détaillée des méthodes liées au threading et à l'optimisation.

ÉTAT DOCUMENTATION : PREMIÈRE VAGUE EN COURS
- **Couverture** : En cours de documentation
- **Niveau** : Documentation fonctionnelle standardisée
- **Qualité** : 15-25 lignes par méthode minimum

DOMAINE FONCTIONNEL : THREADING ET OPTIMISATION
- Entraînement threadé et asynchrone
- Optimisation threadée
- Gestion des processus parallèles
- Contrôle d'exécution et monitoring

MÉTHODES DOCUMENTÉES :
================================================================================

1. start.txt (ThreadedTrainer.start - DÉMARRAGE ENTRAÎNEMENT THREADÉ)
   - Lignes 1388-1430 dans ml_core.py (43 lignes)
   - FONCTION : Démarre l'entraînement de modèles ML (LGBM et LSTM) dans un thread séparé pour permettre l'exécution asynchrone et non-bloquante avec gestion complète des états et erreurs
   - PARAMÈTRES :
     * self - Instance de ThreadedTrainer
     * X_lgbm - Features/données d'entrée pour le modèle LGBM
     * y_lgbm - Labels/cibles pour le modèle LGBM
     * X_lstm - Features/données d'entrée pour le modèle LSTM
     * y_lstm - Labels/cibles pour le modèle LSTM
     * config_override (optionnel) - Configuration personnalisée pour remplacer la configuration par défaut
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT** : Contrôle si un entraînement est déjà en cours avec self.is_running pour éviter les conflits
     * **RÉINITIALISATION** : Nettoie les événements (stop_event.clear()) et remet à zéro les résultats et erreurs
     * **HORODATAGE** : Enregistre l'heure de début avec time.time() pour suivi de durée
     * **VALIDATION INSTANCE** : Vérifie que trainer_instance possède la méthode '_train_models_async' requise
     * **GESTION ERREURS** : Appelle error_callback si l'instance est invalide et retourne False
     * **CRÉATION THREAD** : `self.thread = threading.Thread(target=self._run_training, args=(X_lgbm, y_lgbm, X_lstm, y_lstm, config_override))` instanciation
     * **CONFIG THREAD** : `name="ThreadedTrainer", daemon=True` configuration thread daemon
     * **DÉMARRAGE THREAD** : `self.thread.start(); self.is_running = True` lancement et mise à jour état
     * **LOGGING SUCCÈS** : `logger.info("Entraînement démarré avec succès")` traçabilité démarrage
     * **ERROR CALLBACK** : `if self.error_callback: self.error_callback(message)` notification erreurs validation
     * **RETOUR CONDITIONNEL** : `return True` si succès, `return False` si erreur ou déjà en cours
   - RETOUR : bool - True si l'entraînement a démarré avec succès, False en cas d'erreur ou si déjà en cours
   - UTILITÉ : Point d'entrée principal pour l'entraînement asynchrone de modèles ML. Essentiel pour les interfaces utilisateur non-bloquantes et les applications nécessitant un entraînement en arrière-plan. Permet la surveillance et le contrôle de l'entraînement.

2. start_1.txt (ThreadedOptimizer.start - DÉMARRAGE OPTIMISATION THREADÉE - HOMONYME)
   - Lignes 1616-1652 dans ml_core.py (37 lignes)
   - FONCTION : Démarre l'optimisation d'hyperparamètres dans un thread séparé pour permettre l'exécution asynchrone et non-bloquante avec création dynamique de l'instance d'optimiseur
   - PARAMÈTRES :
     * self - Instance de ThreadedOptimizer
     * *args - Arguments positionnels variables à transmettre au constructeur de l'optimiseur
     * **kwargs - Arguments nommés variables à transmettre au constructeur de l'optimiseur
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT** : Contrôle si une optimisation est déjà en cours avec self.is_running pour éviter les conflits
     * **RÉINITIALISATION** : Nettoie les événements (stop_event.clear()) et remet à zéro les résultats et erreurs
     * **CRÉATION DYNAMIQUE** : Instancie self.optimizer_class avec les arguments fournis (*args, **kwargs)
     * **GESTION ERREURS CRÉATION** : Capture les exceptions lors de l'instanciation et appelle error_callback
     * **LOGGING ERREURS** : Enregistre les erreurs de création d'optimiseur pour debugging
     * **CRÉATION THREAD** : Instancie threading.Thread avec target=_run_optimization (sans paramètres)
     * **CONFIGURATION DAEMON** : Définit daemon=True pour arrêt automatique avec le programme principal
     * **DÉMARRAGE ASYNCHRONE** : Lance thread.start() et marque is_running=True
     * **LOGGING SUCCÈS** : Enregistre le succès du démarrage pour traçabilité
   - RETOUR : bool - True si l'optimisation a démarré avec succès, False en cas d'erreur ou si déjà en cours
   - UTILITÉ : Point d'entrée principal pour l'optimisation asynchrone d'hyperparamètres. Essentiel pour les processus d'optimisation longs (Optuna, GridSearch) sans bloquer l'interface utilisateur. Permet la création flexible d'optimiseurs avec paramètres variables.

3. stop.txt (ThreadedTrainer.stop - ARRÊT ENTRAÎNEMENT THREADÉ)
   - Lignes 1533-1562 dans ml_core.py (30 lignes)
   - FONCTION : Arrête proprement l'entraînement en cours d'exécution dans le thread séparé avec gestion des timeouts et signalisation d'arrêt gracieux
   - PARAMÈTRES :
     * self - Instance de ThreadedTrainer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT** : Contrôle si un entraînement est en cours avec self.is_running avant tentative d'arrêt
     * **WARNING PRÉVENTIF** : Log un avertissement si aucun entraînement n'est en cours et retourne False
     * **SIGNALISATION ARRÊT** : Active l'événement d'arrêt avec self.stop_event.set() pour signaler au thread
     * **FLAG INSTANCE** : Définit trainer_instance.stop_training=True si l'attribut existe pour arrêt coopératif
     * **ATTENTE GRACIEUSE** : Utilise thread.join(timeout=5.0) pour attendre l'arrêt propre du thread
     * **VÉRIFICATION SURVIE** : Contrôle si le thread est encore vivant après le timeout avec thread.is_alive()
     * **ARRÊT FORCÉ** : Si le thread ne s'arrête pas, marque is_running=False et log un avertissement
     * **LIMITATION PYTHON** : Reconnaît qu'on ne peut pas forcer l'arrêt d'un thread en Python
     * **LOGGING SUCCÈS** : Enregistre l'arrêt réussi pour traçabilité
   - RETOUR : bool - True si l'arrêt a été initié avec succès, False si aucun entraînement en cours
   - UTILITÉ : Méthode essentielle pour l'arrêt contrôlé de l'entraînement asynchrone. Permet l'interruption propre des processus longs et la libération des ressources. Critique pour les interfaces utilisateur avec boutons d'arrêt.

4. stop_1.txt (ThreadedOptimizer.stop - ARRÊT OPTIMISATION THREADÉE - HOMONYME)
   - Lignes 1677-1702 dans ml_core.py (26 lignes)
   - FONCTION : Arrête proprement l'optimisation d'hyperparamètres en cours d'exécution dans le thread séparé avec gestion des timeouts et signalisation d'arrêt gracieux
   - PARAMÈTRES :
     * self - Instance de ThreadedOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT** : Contrôle si une optimisation est en cours avec self.is_running avant tentative d'arrêt
     * **WARNING PRÉVENTIF** : Log un avertissement si aucune optimisation n'est en cours et retourne False
     * **SIGNALISATION ARRÊT** : Active l'événement d'arrêt avec self.stop_event.set() pour signaler au thread d'optimisation
     * **ATTENTE GRACIEUSE** : Utilise thread.join(timeout=5.0) pour attendre l'arrêt propre du thread d'optimisation
     * **VÉRIFICATION SURVIE** : Contrôle si le thread est encore vivant après le timeout avec thread.is_alive()
     * **ARRÊT FORCÉ** : Si le thread ne s'arrête pas, marque is_running=False et log un avertissement
     * **LIMITATION PYTHON** : Reconnaît qu'on ne peut pas forcer l'arrêt d'un thread en Python
     * **LOGGING SUCCÈS** : Enregistre l'arrêt réussi de l'optimisation pour traçabilité
   - RETOUR : bool - True si l'arrêt a été initié avec succès, False si aucune optimisation en cours
   - UTILITÉ : Méthode essentielle pour l'arrêt contrôlé de l'optimisation asynchrone d'hyperparamètres. Permet l'interruption propre des processus d'optimisation longs (Optuna, GridSearch) et la libération des ressources. Critique pour les interfaces utilisateur avec contrôle d'arrêt.

5. is_training_running.txt (ThreadedTrainer.is_training_running - VÉRIFICATION ÉTAT ENTRAÎNEMENT)
   - Lignes 1582-1589 dans ml_core.py (8 lignes)
   - FONCTION : Vérifie l'état d'exécution de l'entraînement threadé en retournant la valeur du flag interne is_running pour surveillance et contrôle d'état
   - PARAMÈTRES :
     * self - Instance de ThreadedTrainer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **LECTURE ÉTAT** : Accède directement à l'attribut self.is_running sans modification
     * **RETOUR IMMÉDIAT** : Retourne la valeur booléenne sans traitement supplémentaire
     * **ÉTAT SYNCHRONISÉ** : Reflète l'état réel du thread d'entraînement (mis à jour par start/stop)
     * **VÉRIFICATION SIMPLE** : Méthode getter pure sans effets de bord
     * **THREAD-SAFE** : Lecture atomique d'un booléen, pas de synchronisation complexe requise
   - RETOUR : bool - True si l'entraînement est actuellement en cours d'exécution, False sinon
   - UTILITÉ : Méthode de surveillance essentielle pour les interfaces utilisateur et la logique de contrôle. Permet de vérifier l'état avant de démarrer/arrêter l'entraînement. Utile pour l'affichage d'indicateurs d'état et la validation des opérations.

6. is_optimization_running.txt (ThreadedOptimizer.is_optimization_running - VÉRIFICATION ÉTAT OPTIMISATION)
   - Lignes 1722-1729 dans ml_core.py (8 lignes)
   - FONCTION : Vérifie l'état d'exécution de l'optimisation threadée en retournant la valeur du flag interne is_running pour surveillance et contrôle d'état
   - PARAMÈTRES :
     * self - Instance de ThreadedOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **LECTURE ÉTAT** : Accède directement à l'attribut self.is_running sans modification
     * **RETOUR IMMÉDIAT** : Retourne la valeur booléenne sans traitement supplémentaire
     * **ÉTAT SYNCHRONISÉ** : Reflète l'état réel du thread d'optimisation (mis à jour par start/stop)
     * **VÉRIFICATION SIMPLE** : Méthode getter pure sans effets de bord
     * **THREAD-SAFE** : Lecture atomique d'un booléen, pas de synchronisation complexe requise
   - RETOUR : bool - True si l'optimisation est actuellement en cours d'exécution, False sinon
   - UTILITÉ : Méthode de surveillance essentielle pour les interfaces utilisateur et la logique de contrôle d'optimisation. Permet de vérifier l'état avant de démarrer/arrêter l'optimisation d'hyperparamètres. Utile pour l'affichage d'indicateurs d'état et la validation des opérations d'optimisation.

7. _run_training.txt (ThreadedTrainer._run_training - EXÉCUTION ENTRAÎNEMENT THREADÉ INTERNE)
   - Lignes 1432-1531 dans ml_core.py (100 lignes)
   - FONCTION : Méthode interne complexe qui exécute l'entraînement dans le thread séparé avec gestion avancée des callbacks, arrêt coopératif et restauration d'état
   - PARAMÈTRES :
     * self - Instance de ThreadedTrainer
     * X_lgbm - Features pour le modèle LGBM
     * y_lgbm - Labels pour le modèle LGBM
     * X_lstm - Features pour le modèle LSTM
     * y_lstm - Labels pour le modèle LSTM
     * config_override (optionnel) - Configuration personnalisée pour l'entraînement
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONFIGURATION ARRÊT** : Configure stop_training dynamiquement pour vérifier stop_event.is_set()
     * **SAUVEGARDE ORIGINALE** : Sauvegarde les méthodes originales avant modification temporaire
     * **PROPRIÉTÉ DYNAMIQUE** : Crée check_stop_training qui combine flag et événement d'arrêt
     * **CALLBACK PROGRESSION** : Configure observe_progress pour éviter la récursion infinie
     * **OBSERVATEUR PROGRESSION** : Ajoute _progress_observer à l'instance pour callbacks
     * **EXÉCUTION PRINCIPALE** : Appelle trainer_instance._train_models_async avec tous les paramètres
     * **VÉRIFICATION INTERRUPTION** : Contrôle stop_event.is_set() après entraînement
     * **RÉSULTAT STRUCTURÉ** : Crée dictionnaire avec success, message, duration
     * **GESTION ERREURS** : Capture exceptions avec logging détaillé et callbacks d'erreur
     * **RESTAURATION FINALE** : Restaure méthodes originales et supprime observateurs dans finally
     * **NETTOYAGE ÉTAT** : Marque is_running=False pour indiquer fin d'exécution
   - RETOUR : None (méthode interne, résultats stockés dans self.result)
   - UTILITÉ : Cœur de l'exécution threadée avec gestion sophistiquée des états et callbacks. Essentielle pour l'entraînement asynchrone robuste avec contrôle d'arrêt et surveillance de progression. Critique pour les applications nécessitant un entraînement interruptible.

8. observe_progress.txt (ThreadedTrainer.observe_progress - OBSERVATEUR PROGRESSION INTERNE)
   - Lignes 1473-1477 dans ml_core.py (5 lignes)
   - FONCTION : Fonction interne d'observation de progression créée dynamiquement pour éviter la récursion infinie lors des callbacks de progression dans l'entraînement threadé
   - PARAMÈTRES :
     * progress - Valeur de progression (pourcentage ou étape actuelle)
     * message - Message descriptif de l'état de progression
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION CALLBACK** : Contrôle si self.progress_callback existe avant appel
     * **DÉLÉGATION SÉCURISÉE** : Appelle progress_callback avec les mêmes paramètres sans modification
     * **ÉVITEMENT RÉCURSION** : Conçue pour éviter les boucles infinites de callbacks
     * **FONCTION INTERNE** : Créée dynamiquement dans _run_training pour contexte spécifique
     * **OBSERVATEUR PATTERN** : Implémente le pattern Observer pour surveillance de progression
   - RETOUR : None (fonction de callback, pas de retour)
   - UTILITÉ : Composant interne essentiel pour la surveillance de progression sans récursion. Permet les callbacks de progression sécurisés dans l'entraînement threadé. Critique pour les interfaces utilisateur avec barres de progression en temps réel.

9. _run_optimization.txt (ThreadedOptimizer._run_optimization - EXÉCUTION OPTIMISATION THREADÉE INTERNE)
   - Lignes 1654-1675 dans ml_core.py (22 lignes)
   - FONCTION : Méthode interne qui exécute l'optimisation d'hyperparamètres dans le thread séparé avec gestion des callbacks et des erreurs
   - PARAMÈTRES :
     * self - Instance de ThreadedOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXÉCUTION OPTIMISATION** : Appelle self.optimizer_instance.optimize(stop_event=self.stop_event) pour lancer l'optimisation
     * **TRANSMISSION STOP_EVENT** : Passe l'événement d'arrêt à l'optimiseur pour permettre l'interruption coopérative
     * **STOCKAGE RÉSULTAT** : Enregistre le résultat dans self.result pour récupération ultérieure
     * **VÉRIFICATION INTERRUPTION** : Contrôle si stop_event.is_set() pour détecter les arrêts demandés
     * **CALLBACK SUCCÈS** : Appelle self.callback(self.result) si l'optimisation se termine normalement
     * **GESTION ERREURS** : Capture les exceptions avec logging détaillé et stockage dans self.error
     * **CALLBACK ERREUR** : Appelle self.error_callback(e) si une erreur survient pendant l'optimisation
     * **NETTOYAGE FINAL** : Marque is_running=False dans le bloc finally pour indiquer la fin d'exécution
   - RETOUR : None (méthode interne, résultats stockés dans self.result)
   - UTILITÉ : Cœur de l'exécution d'optimisation threadée avec gestion robuste des états et callbacks. Essentielle pour l'optimisation asynchrone d'hyperparamètres (Optuna, GridSearch). Critique pour les processus d'optimisation longs avec contrôle d'arrêt.

10. check_stop_training.txt (ThreadedTrainer.check_stop_training - VÉRIFICATION ARRÊT ENTRAÎNEMENT INTERNE)
   - Lignes 1456-1457 dans ml_core.py (2 lignes)
   - FONCTION : Fonction interne créée dynamiquement pour vérifier si l'entraînement doit s'arrêter en combinant le flag local et l'événement d'arrêt threadé
   - PARAMÈTRES :
     * self_instance - Instance de l'entraîneur pour vérifier son flag stop_training
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DOUBLE VÉRIFICATION** : Combine self_instance.stop_training (flag local) et self.stop_event.is_set() (événement threadé)
     * **LOGIQUE OU** : Retourne True si l'une des deux conditions d'arrêt est activée
     * **ARRÊT LOCAL** : Vérifie self_instance.stop_training pour arrêt demandé par l'instance d'entraînement
     * **ARRÊT THREADÉ** : Vérifie self.stop_event.is_set() pour arrêt demandé par le thread manager
     * **FONCTION DYNAMIQUE** : Créée à la volée dans _run_training pour contexte spécifique
     * **ARRÊT COOPÉRATIF** : Permet l'arrêt gracieux depuis multiple sources
   - RETOUR : bool - True si l'entraînement doit s'arrêter, False sinon
   - UTILITÉ : Composant interne essentiel pour l'arrêt coopératif de l'entraînement threadé. Permet la vérification combinée des conditions d'arrêt depuis multiple sources. Critique pour l'arrêt gracieux et la responsivité des interfaces utilisateur.

11. get_error.txt (ThreadedTrainer.get_error - RÉCUPÉRATION ERREUR ENTRAÎNEMENT)
   - Lignes 1573-1580 dans ml_core.py (8 lignes)
   - FONCTION : Récupère l'erreur survenue pendant l'entraînement threadé pour diagnostic et gestion d'erreurs
   - PARAMÈTRES :
     * self - Instance de ThreadedTrainer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RETOUR DIRECT** : Retourne self.error sans traitement supplémentaire
     * **ÉTAT ERREUR** : Contient l'exception capturée pendant l'exécution ou None si pas d'erreur
     * **DIAGNOSTIC** : Permet l'inspection de l'erreur pour debugging et gestion
     * **GETTER SIMPLE** : Méthode getter pure sans effets de bord
     * **THREAD-SAFE** : Lecture atomique d'une référence d'objet
   - RETOUR : Exception ou None - L'erreur de l'entraînement ou None si l'entraînement n'a pas échoué
   - UTILITÉ : Méthode essentielle pour la gestion d'erreurs dans l'entraînement threadé. Permet le diagnostic des problèmes et la gestion appropriée des échecs. Critique pour les interfaces utilisateur affichant les erreurs et les systèmes de logging.

12. get_error_1.txt (ThreadedOptimizer.get_error - RÉCUPÉRATION ERREUR OPTIMISATION - HOMONYME)
   - Lignes 1713-1720 dans ml_core.py (8 lignes)
   - FONCTION : Récupère l'erreur survenue pendant l'optimisation threadée pour diagnostic et gestion d'erreurs
   - PARAMÈTRES :
     * self - Instance de ThreadedOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RETOUR DIRECT** : Retourne self.error sans traitement supplémentaire
     * **ÉTAT ERREUR** : Contient l'exception capturée pendant l'optimisation ou None si pas d'erreur
     * **DIAGNOSTIC** : Permet l'inspection de l'erreur pour debugging et gestion
     * **GETTER SIMPLE** : Méthode getter pure sans effets de bord
     * **THREAD-SAFE** : Lecture atomique d'une référence d'objet
   - RETOUR : Exception ou None - L'erreur de l'optimisation ou None si l'optimisation n'a pas échoué
   - UTILITÉ : Méthode essentielle pour la gestion d'erreurs dans l'optimisation threadée. Permet le diagnostic des problèmes d'optimisation et la gestion appropriée des échecs. Critique pour les interfaces utilisateur affichant les erreurs d'optimisation et les systèmes de logging.
