# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\liste\optuna_optimizer.py
# Lignes: 13706 à 13869
# Type: Méthode de la classe OptimizationStatsCollector

    def generate_report(self) -> str:
        """
        Génère un rapport récapitulatif des statistiques collectées.
        Sépare clairement les statistiques d'entraînement et d'évaluation.

        Returns:
            str: Rapport récapitulatif formaté
        """
        # Marquer que le rapport a été généré pour éviter le double comptage
        self.report_generated = True

        if self.total_predictions == 0:
            return "Aucune prédiction enregistrée."

        # Calculer les statistiques agrégées
        wait_ratio = self.wait_predictions / self.total_predictions if self.total_predictions > 0 else 0

        # Préparer le rapport
        report_lines = []

        # En-tête du rapport
        trial_info = f" pour l'essai {self.trial_id}" if self.trial_id is not None else ""
        report_lines.append(f"=== RAPPORT RÉCAPITULATIF DES STATISTIQUES D'OPTIMISATION{trial_info} ===")
        report_lines.append("")

        # Statistiques de l'essai actuel
        report_lines.append("1. STATISTIQUES DE L'ESSAI ACTUEL")
        report_lines.append(f"   Essai actuel: #{self.trial_id if self.trial_id is not None else 'N/A'}")
        report_lines.append(f"   Total des prédictions: {self.total_predictions}")
        report_lines.append(f"   Recommandations WAIT: {self.wait_predictions} ({wait_ratio:.2%})")
        report_lines.append(f"   Recommandations NON-WAIT: {self.non_wait_predictions} ({1-wait_ratio:.2%})")
        report_lines.append("")

        # Statistiques par phase
        report_lines.append("2. STATISTIQUES PAR PHASE")

        # Identifier les phases d'entraînement et d'évaluation
        training_phases = [k for k in self.phase_stats.keys() if 'train' in k.lower()]
        evaluation_phases = [k for k in self.phase_stats.keys() if 'eval' in k.lower() or 'test' in k.lower() or 'valid' in k.lower()]
        other_phases = [k for k in self.phase_stats.keys() if k not in training_phases and k not in evaluation_phases]

        # Statistiques d'entraînement agrégées
        if training_phases:
            report_lines.append("   2.1 PHASES D'ENTRAÎNEMENT")
            training_total = sum(self.phase_stats[k]['count'] for k in training_phases)
            training_wait = sum(self.phase_stats[k]['wait_count'] for k in training_phases)
            training_non_wait = sum(self.phase_stats[k]['non_wait_count'] for k in training_phases)
            training_wait_ratio = training_wait / training_total if training_total > 0 else 0

            report_lines.append(f"     Total des prédictions d'entraînement: {training_total}")
            report_lines.append(f"     Recommandations WAIT: {training_wait} ({training_wait_ratio:.2%})")
            report_lines.append(f"     Recommandations NON-WAIT: {training_non_wait} ({1-training_wait_ratio:.2%})")
            report_lines.append("")

            # Détails par phase d'entraînement
            for phase_key in training_phases:
                stats = self.phase_stats[phase_key]
                phase_wait_ratio = stats['wait_count'] / stats['count'] if stats['count'] > 0 else 0
                report_lines.append(f"     {phase_key}:")
                report_lines.append(f"       Total des prédictions: {stats['count']}")
                report_lines.append(f"       Recommandations WAIT: {stats['wait_count']} ({phase_wait_ratio:.2%})")
                report_lines.append(f"       Recommandations NON-WAIT: {stats['non_wait_count']} ({1-phase_wait_ratio:.2%})")

                if stats['decision_thresholds']:
                    report_lines.append(f"       Seuil de décision moyen: {statistics.mean(stats['decision_thresholds']):.4f}")
                if stats['confidence_values']:
                    report_lines.append(f"       Confiance moyenne: {statistics.mean(stats['confidence_values']):.4f}")
                if stats['uncertainty_values']:
                    report_lines.append(f"       Incertitude moyenne: {statistics.mean(stats['uncertainty_values']):.4f}")
                report_lines.append("")

        # Statistiques d'évaluation agrégées
        if evaluation_phases:
            report_lines.append("   2.2 PHASES D'ÉVALUATION")
            evaluation_total = sum(self.phase_stats[k]['count'] for k in evaluation_phases)
            evaluation_wait = sum(self.phase_stats[k]['wait_count'] for k in evaluation_phases)
            evaluation_non_wait = sum(self.phase_stats[k]['non_wait_count'] for k in evaluation_phases)
            evaluation_wait_ratio = evaluation_wait / evaluation_total if evaluation_total > 0 else 0

            report_lines.append(f"     Total des prédictions d'évaluation: {evaluation_total}")
            report_lines.append(f"     Recommandations WAIT: {evaluation_wait} ({evaluation_wait_ratio:.2%})")
            report_lines.append(f"     Recommandations NON-WAIT: {evaluation_non_wait} ({1-evaluation_wait_ratio:.2%})")
            report_lines.append("")

            # Détails par phase d'évaluation
            for phase_key in evaluation_phases:
                stats = self.phase_stats[phase_key]
                phase_wait_ratio = stats['wait_count'] / stats['count'] if stats['count'] > 0 else 0
                report_lines.append(f"     {phase_key}:")
                report_lines.append(f"       Total des prédictions: {stats['count']}")
                report_lines.append(f"       Recommandations WAIT: {stats['wait_count']} ({phase_wait_ratio:.2%})")
                report_lines.append(f"       Recommandations NON-WAIT: {stats['non_wait_count']} ({1-phase_wait_ratio:.2%})")

                if stats['decision_thresholds']:
                    report_lines.append(f"       Seuil de décision moyen: {statistics.mean(stats['decision_thresholds']):.4f}")
                if stats['confidence_values']:
                    report_lines.append(f"       Confiance moyenne: {statistics.mean(stats['confidence_values']):.4f}")
                if stats['uncertainty_values']:
                    report_lines.append(f"       Incertitude moyenne: {statistics.mean(stats['uncertainty_values']):.4f}")
                report_lines.append("")

        # Autres phases
        if other_phases:
            report_lines.append("   2.3 AUTRES PHASES")
            for phase_key in other_phases:
                stats = self.phase_stats[phase_key]
                phase_wait_ratio = stats['wait_count'] / stats['count'] if stats['count'] > 0 else 0
                report_lines.append(f"     {phase_key}:")
                report_lines.append(f"       Total des prédictions: {stats['count']}")
                report_lines.append(f"       Recommandations WAIT: {stats['wait_count']} ({phase_wait_ratio:.2%})")
                report_lines.append(f"       Recommandations NON-WAIT: {stats['non_wait_count']} ({1-phase_wait_ratio:.2%})")

                if stats['decision_thresholds']:
                    report_lines.append(f"       Seuil de décision moyen: {statistics.mean(stats['decision_thresholds']):.4f}")
                if stats['confidence_values']:
                    report_lines.append(f"       Confiance moyenne: {statistics.mean(stats['confidence_values']):.4f}")
                if stats['uncertainty_values']:
                    report_lines.append(f"       Incertitude moyenne: {statistics.mean(stats['uncertainty_values']):.4f}")
                report_lines.append("")

        # Alertes de diagnostic
        if self.diagnostic_alerts:
            report_lines.append("3. ALERTES DE DIAGNOSTIC")
            for alert_type, count in sorted(self.diagnostic_alerts.items(), key=lambda x: x[1], reverse=True):
                # Calculer le pourcentage par rapport au nombre de prédictions du type approprié
                # Éviter le doublon "ALERTE: ALERTE:"
                alert_display = alert_type
                if alert_type.startswith("ALERTE:"):
                    alert_display = alert_type
                else:
                    alert_display = f"ALERTE: {alert_type}"

                if "NON-WAIT" in alert_type:
                    # Pour les alertes NON-WAIT, calculer par rapport au nombre de NON-WAIT
                    base_count = max(1, self.non_wait_predictions)  # Éviter division par zéro
                    alert_ratio = min(1.0, count / base_count)  # Limiter à 100% maximum
                    report_lines.append(f"   {alert_display}: {count} occurrences ({alert_ratio:.2%} des prédictions NON-WAIT)")
                elif "WAIT" in alert_type:
                    # Pour les alertes WAIT, calculer par rapport au nombre de WAIT
                    base_count = max(1, self.wait_predictions)  # Éviter division par zéro
                    alert_ratio = min(1.0, count / base_count)  # Limiter à 100% maximum
                    report_lines.append(f"   {alert_display}: {count} occurrences ({alert_ratio:.2%} des prédictions WAIT)")
                else:
                    # Pour les autres alertes, calculer par rapport au nombre total
                    base_count = max(1, self.total_predictions)  # Éviter division par zéro
                    alert_ratio = min(1.0, count / base_count)  # Limiter à 100% maximum
                    report_lines.append(f"   {alert_display}: {count} occurrences ({alert_ratio:.2%} des prédictions)")
            report_lines.append("")

        # Statistiques détaillées
        report_lines.append("4. STATISTIQUES DÉTAILLÉES")
        if self.decision_thresholds:
            report_lines.append(f"   Seuil de décision: min={min(self.decision_thresholds):.4f}, max={max(self.decision_thresholds):.4f}, moy={statistics.mean(self.decision_thresholds):.4f}")
        if self.confidence_values:
            report_lines.append(f"   Confiance: min={min(self.confidence_values):.4f}, max={max(self.confidence_values):.4f}, moy={statistics.mean(self.confidence_values):.4f}")
        if self.uncertainty_values:
            report_lines.append(f"   Incertitude: min={min(self.uncertainty_values):.4f}, max={max(self.uncertainty_values):.4f}, moy={statistics.mean(self.uncertainty_values):.4f}")
        if self.wait_ratios:
            report_lines.append(f"   Ratio WAIT: min={min(self.wait_ratios):.4f}, max={max(self.wait_ratios):.4f}, moy={statistics.mean(self.wait_ratios):.4f}")
        if self.consecutive_wait_counts:
            report_lines.append(f"   WAIT consécutifs: min={min(self.consecutive_wait_counts)}, max={max(self.consecutive_wait_counts)}, moy={statistics.mean(self.consecutive_wait_counts):.2f}")

        # Joindre toutes les lignes du rapport
        return "\n".join(report_lines)