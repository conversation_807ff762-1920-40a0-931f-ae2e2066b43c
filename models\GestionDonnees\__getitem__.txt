# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\models.py
# Lignes: 58 à 68
# Type: Méthode de la classe BaccaratDataset

    def __getitem__(self, idx: int):
        # Retour ultra-optimisé des données sans vérifications supplémentaires
        # Utilisation d'indexation vectorisée pour un accès plus rapide
        if self.has_weights and self.has_sequence_positions:
            return self.sequences[idx], self.targets[idx], self.weights[idx], self.sequence_positions[idx]
        elif self.has_weights:
            return self.sequences[idx], self.targets[idx], self.weights[idx]
        elif self.has_sequence_positions:
            return self.sequences[idx], self.targets[idx], self.sequence_positions[idx]
        else:
            return self.sequences[idx], self.targets[idx]