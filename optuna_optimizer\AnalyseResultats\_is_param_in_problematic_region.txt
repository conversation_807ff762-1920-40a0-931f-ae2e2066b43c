# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 14088 à 14123
# Type: Méthode de la classe MetaOptimizer

    def _is_param_in_problematic_region(self, param_name, param_value):
        """
        Vérifie si un paramètre est dans une région problématique connue.

        Args:
            param_name: Nom du paramètre
            param_value: Valeur du paramètre

        Returns:
            bool: True si le paramètre est dans une région problématique
        """
        # Vérifier si le paramètre a des régions problématiques
        if param_name not in self.problematic_regions:
            return False

        # Récupérer les valeurs problématiques
        problematic_values = self.problematic_regions[param_name]

        # Pour les paramètres numériques, vérifier la proximité
        if isinstance(param_value, (int, float)):
            for prob_value in problematic_values:
                if isinstance(prob_value, (int, float)):
                    # Calculer la distance relative
                    param_range = self._get_param_range(param_name)
                    if param_range:
                        min_val, max_val = param_range
                        range_size = max_val - min_val
                        if range_size > 0:
                            distance = abs(param_value - prob_value) / range_size
                            if distance < 0.1:  # 10% de la plage
                                return True
        # Pour les paramètres catégoriels, vérifier l'égalité
        else:
            return param_value in problematic_values

        return False