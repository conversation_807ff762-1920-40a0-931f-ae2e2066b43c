# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 10966 à 10990
# Type: Méthode de la classe HybridBaccaratPredictor

    def _save_state_to_models_dir(self) -> bool:
        """
        Sauvegarde automatiquement l'état actuel dans MODEL_SAVE_DIR
        avec un timestamp, en utilisant .joblib. Utilise _perform_save.

        Returns:
            bool: True si la sauvegarde a réussi, False sinon.
        """
        save_dir = MODEL_SAVE_DIR
        try:
            os.makedirs(save_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"predictor_state_{timestamp}.joblib" # Toujours .joblib pour auto-save
            filepath = os.path.join(save_dir, filename)
            logger.info(f"Sauvegarde automatique de l'état vers: {filepath}")

            # Appel de la logique interne de sauvegarde
            return self._perform_save(filepath)

        except OSError as e:
            logger.error(f"Impossible de créer dossier sauvegarde '{save_dir}': {e}")
            return False
        except Exception as e:
            logger.error(f"Erreur inattendue pendant _save_state_to_models_dir: {e}", exc_info=True)
            return False