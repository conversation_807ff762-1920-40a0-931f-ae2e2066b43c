# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 4990 à 5051
# Type: Méthode de la classe OptunaOptimizer

    def _process_line_static(line_info):
        """
        Version statique de _process_line pour éviter les problèmes de sérialisation.
        Cette méthode est complètement indépendante de l'instance et n'utilise aucune dépendance externe.

        Args:
            line_info: Tuple (line_idx, line) contenant l'indice de la ligne et son contenu

        Returns:
            Dict or None: Dictionnaire contenant les données de la séquence, ou None si la ligne est invalide
        """
        import logging
        logger = logging.getLogger(__name__)

        line_idx, line = line_info

        # Ignorer les lignes vides ou les commentaires
        if not line.strip() or line.strip().startswith("#"):
            return None

        try:
            # Format attendu: séquence de résultats (P/B)
            full_sequence = line.strip()

            # Vérifier si la séquence est assez longue
            if len(full_sequence) >= 60:
                # Extraire les manches 1 à 30 pour l'entraînement
                training_sequence = full_sequence[:30]

                # Extraire les manches 31 à 60 pour l'évaluation
                target_sequence = full_sequence[30:60]

                # Convertir la séquence cible en liste de dictionnaires pour faciliter le traitement
                sequence_data = []
                for i, outcome in enumerate(target_sequence):
                    sequence_data.append({
                        'round_num': i + 31,  # Manche 31 à 60
                        'outcome': 'PLAYER' if outcome == 'P' else 'BANKER',
                        'line_idx': line_idx
                    })

                # Convertir les séquences en format standard (lowercase)
                training_sequence_std = []
                for outcome in training_sequence:  # Manches 1-30
                    training_sequence_std.append('player' if outcome == 'P' else 'banker')

                target_sequence_std = []
                for outcome in target_sequence:  # Manches 31-60
                    target_sequence_std.append('player' if outcome == 'P' else 'banker')

                # Retourner les données formatées avec séparation claire entre entraînement et évaluation
                return {
                    'training_sequence': training_sequence_std,  # Séquence d'entraînement (manches 1-30)
                    'target_sequence': target_sequence_std,      # Séquence cible (manches 31-60)
                    'sequence_data': sequence_data               # Données formatées pour l'évaluation
                }
            else:
                return None

        except Exception as e:
            logger.error(f"Erreur lors du traitement de la ligne {line_idx+1}: {e}")
            return None