# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1193 à 1206
# Type: Méthode de la classe TrainOptimizeInterface

    def set_result(self, model_name: str, metric_name: str, value: float) -> None:
        """
        Définit un résultat pour un modèle et une métrique.

        Args:
            model_name: Nom du modèle
            metric_name: Nom de la métrique
            value: Valeur du résultat
        """
        if model_name not in self.results:
            self.results[model_name] = {}

        self.results[model_name][metric_name] = value
        logger.debug(f"Résultat '{metric_name}' défini à {value} pour le modèle '{model_name}'")