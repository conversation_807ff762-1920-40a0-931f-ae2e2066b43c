# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 7061 à 7095
# Type: Méthode de la classe HybridBaccaratPredictor

    def _update_lgbm_metrics(self):
        """Met à jour les métriques LGBM dans le tableau de bord."""
        if not hasattr(self, 'lgbm_metrics') or not self.lgbm_metrics:
            return

        # Mettre à jour les métriques de base
        if 'precision' in self.lgbm_metrics:
            self.lgbm_metric_vars['precision'].set(f"Précision: {self.lgbm_metrics['precision']:.4f}")
        if 'recall' in self.lgbm_metrics:
            self.lgbm_metric_vars['recall'].set(f"Rappel: {self.lgbm_metrics['recall']:.4f}")
        if 'f1' in self.lgbm_metrics:
            self.lgbm_metric_vars['f1'].set(f"F1-Score: {self.lgbm_metrics['f1']:.4f}")
        if 'auc_roc' in self.lgbm_metrics:
            self.lgbm_metric_vars['auc_roc'].set(f"AUC-ROC: {self.lgbm_metrics['auc_roc']:.4f}")

        # Mettre à jour la matrice de confusion
        if 'confusion_matrix' in self.lgbm_metrics:
            cm = self.lgbm_metrics['confusion_matrix']
            for i in range(2):
                for j in range(2):
                    self.lgbm_cm_vars[i][j].set(str(cm[i, j]))

        # Mettre à jour l'importance des caractéristiques
        if 'feature_importance' in self.lgbm_metrics and hasattr(self, 'feature_names'):
            # Effacer les anciennes données
            for item in self.lgbm_feature_tree.get_children():
                self.lgbm_feature_tree.delete(item)

            # Ajouter les nouvelles données
            feature_importance = self.lgbm_metrics['feature_importance']
            if len(feature_importance) > 0 and len(self.feature_names) == len(feature_importance):
                # Trier les caractéristiques par importance
                sorted_features = sorted(zip(self.feature_names, feature_importance), key=lambda x: x[1], reverse=True)
                for feature, importance in sorted_features:
                    self.lgbm_feature_tree.insert('', 'end', values=(feature, f"{importance:.6f}"))