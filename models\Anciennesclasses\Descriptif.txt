DESCRIPTIF DÉTAILLÉ DES MÉTHODES - ANCIENNES CLASSES
================================================================================

Ce fichier contient la description détaillée des classes du système models.py.

ÉTAT DOCUMENTATION : PREMIÈRE VAGUE FONCTIONNELLE
- **Couverture** : Classes principales documentées
- **Niveau** : Documentation fonctionnelle standardisée
- **Qualité** : 15-25 lignes par classe minimum

DOMAINE FONCTIONNEL : ANCIENNES CLASSES
- Définitions de classes principales
- Architecture et responsabilités
- Structure et méthodes principales

================================================================================

1. class_BaccaratDataset.txt (BaccaratDataset - CLASSE DATASET PYTORCH BACCARAT)
   - Lignes 18-68 dans models.py (51 lignes)
   - FONCTION : Classe Dataset PyTorch optimisée pour les données de séquences Baccarat avec gestion mémoire avancée
   - RESPONSABILITÉS :
     * **HÉRITAGE DATASET** : Étend torch.utils.data.Dataset pour interface PyTorch standard
     * **GESTION SÉQUENCES** : Stockage optimisé des séquences d'entrée avec torch.from_numpy()
     * **VALIDATION ÉTIQUETTES** : Contrôle strict des targets (0=Player, 1=Banker) avec np.unique()
     * **OPTIMISATION MÉMOIRE** : Utilise share_memory_() pour partage efficace entre processus
     * **GESTION OPTIONNELLE** : Support weights et sequence_positions avec flags booléens
     * **ACCÈS INDEXÉ** : Implémentation __getitem__ avec retour conditionnel selon données
     * **INTERFACE STANDARD** : Méthodes __len__ et __getitem__ requises par PyTorch
   - MÉTHODES PRINCIPALES :
     * __init__(sequences, targets, weights, sequence_positions) - Initialisation avec validation
     * __getitem__(idx) - Accès indexé ultra-optimisé avec retour conditionnel
     * __len__() - Retourne taille dataset pour interface PyTorch
   - ARCHITECTURE :
     * **PERFORMANCE** : Préchargement mémoire avec tenseurs PyTorch partagés
     * **FLEXIBILITÉ** : Support données optionnelles (weights, positions)
     * **ROBUSTESSE** : Validation stricte des étiquettes et types
     * **COMPATIBILITÉ** : Interface PyTorch Dataset complète
   - UTILITÉ : Classe fondamentale pour l'entraînement des modèles ML. Essentielle pour l'interface PyTorch. Critique pour les performances de chargement des données.

2. class_EnhancedLSTMModel.txt (EnhancedLSTMModel - CLASSE MODÈLE LSTM AVANCÉ)
   - Lignes 71-188 dans models.py (118 lignes)
   - FONCTION : Classe modèle LSTM avancé avec mécanisme d'attention, connexions résiduelles et normalisation multicouche
   - RESPONSABILITÉS :
     * **HÉRITAGE NN.MODULE** : Étend torch.nn.Module pour modèle PyTorch standard
     * **ARCHITECTURE LSTM** : LSTM bidirectionnel optionnel avec dropout entre couches
     * **MÉCANISME ATTENTION** : Sequential avec Linear->Tanh->Linear pour focus séquences
     * **CONNEXIONS RÉSIDUELLES** : Combinaison activated_out + normalized_out optionnelle
     * **NORMALISATION MULTICOUCHE** : LayerNorm entrée et sortie pour stabilisation
     * **DROPOUT SPÉCIALISÉ** : Dropout différencié entrée/hidden/sortie pour régularisation
     * **FORWARD PASS** : Gestion dimensions 2D/3D avec attention et connexions résiduelles
   - MÉTHODES PRINCIPALES :
     * __init__(input_size, hidden_dim, output_size, ...) - Initialisation architecture complète
     * forward(x) - Passe avant avec attention, normalisation et connexions résiduelles
   - ARCHITECTURE :
     * **MODULARITÉ** : Composants optionnels (attention, résiduel) configurables
     * **PERFORMANCE** : Optimisations pour stabilité entraînement et réduction val_loss
     * **FLEXIBILITÉ** : Support entrées 2D/3D avec gestion automatique dimensions
     * **ROBUSTESSE** : Validation entrées et gestion erreurs dimensions
   - UTILITÉ : Classe centrale pour modèle LSTM state-of-the-art. Essentielle pour architecture neuronale avancée. Critique pour prédictions Baccarat sophistiquées.

3. class_PersistentMarkov.txt (PersistentMarkov - CLASSE GESTIONNAIRE MARKOV PERSISTANT)
   - Lignes 190-821 dans models.py (632 lignes)
   - FONCTION : Classe gestionnaire de modèles Markov persistants avec gestion global/session et analyse contextuelle sophistiquée
   - RESPONSABILITÉS :
     * **MODÈLES DUAUX** : Gestion séparée modèles globaux (historique) et session (temps réel)
     * **PERSISTANCE** : Export/import état complet avec sérialisation JSON-compatible
     * **THREAD-SAFETY** : Verrouillage RLock pour accès concurrent sécurisé
     * **ANALYSE CONTEXTUELLE** : Adaptation dynamique poids selon volatilité et streaks
     * **LISSAGE LAPLACE** : Gestion probabilités nulles avec facteur smoothing configurable
     * **ORDRES MULTIPLES** : Support chaînes Markov ordre 1 à 12 avec pondération
     * **PRÉDICTION COMBINÉE** : Fusion intelligente global/session avec paramètres adaptatifs
   - MÉTHODES PRINCIPALES :
     * __init__(max_order, smoothing) - Initialisation structures thread-safe
     * get_combined_probs(sequence, ...) - Prédiction combinée avec analyse contextuelle
     * update_global(sequences) - Apprentissage historique batch
     * update_session(sequence) - Apprentissage temps réel incrémental
     * export_models() / load_models(data) - Persistance état complet
     * reset(reset_type) - Réinitialisation soft/hard
     * _analyze_sequence_context(sequence) - Analyse volatilité et streaks
   - ARCHITECTURE :
     * **STRUCTURES OPTIMISÉES** : defaultdict imbriqués pour performance
     * **CONFIGURATION FLEXIBLE** : Paramètres adaptatifs depuis PredictorConfig
     * **ROBUSTESSE** : Validation extensive et gestion erreurs complète
     * **INTELLIGENCE** : Adaptation dynamique selon contexte de jeu
   - UTILITÉ : Classe centrale pour intelligence Markov du système. Essentielle pour prédictions sophistiquées. Critique pour adaptation temps réel et persistance.