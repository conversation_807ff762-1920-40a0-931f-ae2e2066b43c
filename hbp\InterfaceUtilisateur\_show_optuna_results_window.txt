# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 782 à 934
# Type: Méthode de la classe HybridBaccaratPredictor

    def _show_optuna_results_window(self, best_params: Dict[str, Any]):
        logger_instance = getattr(self, 'logger', logging.getLogger(__name__))
        if not self.is_ui_available():
            logger_instance.warning("Impossible d'afficher la fenêtre de résultats Optuna: UI non disponible.")
            return

        try:
            result_window = tk.Toplevel(self.root)
            result_window.title("Meilleurs Paramètres Optuna pour l'Objectif 1")
            result_window.geometry("600x500")
            result_window.transient(self.root) # Lie la fenêtre au parent
            result_window.grab_set() # Rend la fenêtre modale

            main_frame = ttk.Frame(result_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Titre avec explication de l'objectif 1
            ttk.Label(
                main_frame,
                text="Meilleurs paramètres pour l'Objectif 1 (Recommandations NON-WAIT valides consécutives)",
                font=('Segoe UI', 10, 'bold')
            ).pack(pady=(0, 5), anchor='w')

            ttk.Label(
                main_frame,
                text="Ces paramètres sont optimisés pour maximiser les séquences de recommandations NON-WAIT valides consécutives pour les manches 31-60.",
                wraplength=580
            ).pack(pady=(0, 10), anchor='w')

            # Créer un notebook avec des onglets pour organiser les paramètres
            notebook = ttk.Notebook(main_frame)
            notebook.pack(fill=tk.BOTH, expand=True, pady=5)

            # Onglet 1: Paramètres clés pour l'objectif 1
            key_params_frame = ttk.Frame(notebook, padding=10)
            notebook.add(key_params_frame, text="Paramètres Clés")

            # Filtrer les paramètres clés pour l'objectif 1
            key_param_names = [
                'base_confidence_threshold',
                'optimal_wait_ratio',
                'wait_ratio_tolerance',
                'consecutive_focus_factor',
                'objective1_weight',
                'target_round_min',
                'target_round_max',
                'late_game_weight_factor',
                'consecutive_confidence_adjustment_factor'
            ]

            key_params = {k: v for k, v in best_params.items() if k in key_param_names}

            # Afficher les paramètres clés dans un tableau
            for i, (param_name, value) in enumerate(key_params.items()):
                ttk.Label(key_params_frame, text=param_name, font=('Segoe UI', 9, 'bold')).grid(row=i, column=0, sticky='w', padx=5, pady=3)
                ttk.Label(key_params_frame, text=str(value)).grid(row=i, column=1, sticky='w', padx=5, pady=3)

            # Onglet 2: Poids des modèles
            weights_frame = ttk.Frame(notebook, padding=10)
            notebook.add(weights_frame, text="Poids des Modèles")

            # Filtrer les paramètres de poids
            weight_params = {k: v for k, v in best_params.items() if k.startswith('weight_')}

            # Calculer le total pour normalisation
            total_weight = sum(weight_params.values())
            normalized_weights = {k: v/total_weight for k, v in weight_params.items()} if total_weight > 0 else weight_params

            # Afficher les poids dans un tableau
            ttk.Label(weights_frame, text="Modèle", font=('Segoe UI', 9, 'bold')).grid(row=0, column=0, sticky='w', padx=5, pady=3)
            ttk.Label(weights_frame, text="Poids", font=('Segoe UI', 9, 'bold')).grid(row=0, column=1, sticky='w', padx=5, pady=3)
            ttk.Label(weights_frame, text="Poids Normalisé", font=('Segoe UI', 9, 'bold')).grid(row=0, column=2, sticky='w', padx=5, pady=3)

            for i, (param_name, value) in enumerate(weight_params.items()):
                model_name = param_name[7:]  # Enlever 'weight_'
                ttk.Label(weights_frame, text=model_name).grid(row=i+1, column=0, sticky='w', padx=5, pady=3)
                ttk.Label(weights_frame, text=f"{value:.4f}").grid(row=i+1, column=1, sticky='w', padx=5, pady=3)
                ttk.Label(weights_frame, text=f"{normalized_weights[param_name]:.4f}").grid(row=i+1, column=2, sticky='w', padx=5, pady=3)

            # Onglet 3: Tous les paramètres
            all_params_frame = ttk.Frame(notebook, padding=10)
            notebook.add(all_params_frame, text="Tous les Paramètres")

            all_params_text = scrolledtext.ScrolledText(all_params_frame, wrap=tk.WORD, height=15, width=60, font=('Consolas', 9))
            all_params_text.pack(fill=tk.BOTH, expand=True)

            try:
                params_str = json.dumps(best_params, indent=4, sort_keys=True)
            except Exception:
                params_str = str(best_params) # Fallback affichage brut

            all_params_text.insert(tk.END, params_str)
            all_params_text.configure(state=tk.DISABLED) # Lecture seule mais copie possible

            # Boutons d'action
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            apply_button = ttk.Button(
                button_frame,
                text="Sauvegarder les Paramètres",
                command=lambda: [
                    logger_instance.info("Sauvegarde des paramètres optimisés pour l'objectif 1 demandée depuis fenêtre résultats."),
                    self._save_params_to_file(best_params),
                    messagebox.showinfo("Paramètres Sauvegardés",
                                       "Paramètres optimisés pour l'objectif 1 sauvegardés dans params.txt.\n"
                                       "Vous pouvez maintenant les charger avec 'Charger Paramètres Optimisés'.",
                                       parent=result_window),
                    result_window.destroy()
                ],
                style='Accent.TButton'
            )
            apply_button.pack(side=tk.LEFT, padx=5, expand=True, fill=tk.X)

            # Ajouter un bouton pour générer un rapport détaillé
            if hasattr(self, 'current_optimizer_instance') and self.current_optimizer_instance is not None:
                optimizer = self.current_optimizer_instance
                if hasattr(optimizer, 'study') and optimizer.study is not None:
                    study = optimizer.study
                    if hasattr(study, 'best_trial') and study.best_trial is not None:
                        report_button = ttk.Button(
                            button_frame,
                            text="Générer Rapport Détaillé",
                            command=lambda: [
                                logger_instance.info("Génération d'un rapport détaillé demandée depuis fenêtre résultats."),
                                self.show_text_report(
                                    self.generate_optimization_report(study, study.best_trial),
                                    "Rapport d'optimisation détaillé"
                                )
                            ]
                        )
                        report_button.pack(side=tk.LEFT, padx=5, expand=True, fill=tk.X)

            close_button = ttk.Button(
                button_frame,
                text="Fermer sans Sauvegarder",
                command=lambda: [
                    logger_instance.info("Sauvegarde des paramètres optimisés pour l'objectif 1 annulée depuis fenêtre résultats."),
                    result_window.destroy()
                ]
            )
            close_button.pack(side=tk.RIGHT, padx=5, expand=True, fill=tk.X)

            # Ne pas utiliser wait_window() car cela bloque l'interface
            # lorsque les boutons détruisent déjà la fenêtre
            # result_window.wait_window() # Attend que cette fenêtre soit fermée

        except tk.TclError as e_tk:
            logger_instance.error(f"Erreur Tcl création fenêtre résultats Optuna: {e_tk}", exc_info=True)
            messagebox.showerror("Erreur UI", f"Impossible d'afficher la fenêtre de résultats:\n{e_tk}", parent=self.root)
        except Exception as e:
            logger_instance.error(f"Erreur inattendue création fenêtre résultats Optuna: {e}", exc_info=True)
            messagebox.showerror("Erreur", f"Erreur affichage résultats:\n{e}", parent=self.root)