DESCRIPTIF DÉTAILLÉ DES MÉTHODES - INTERFACES ET MODÈLES
================================================================================

Ce fichier contient la description détaillée des méthodes liées aux interfaces et modèles.

ÉTAT DOCUMENTATION : PREMIÈRE VAGUE EN COURS
- **Couverture** : En cours de documentation
- **Niveau** : Documentation fonctionnelle standardisée
- **Qualité** : 15-25 lignes par méthode minimum

DOMAINE FONCTIONNEL : INTERFACES ET MODÈLES
- Interfaces de modèles et modules
- Gestion et enregistrement des modèles
- Factory patterns et providers
- Prédiction et évaluation

MÉTHODES DOCUMENTÉES :
================================================================================

1. get_model.txt (TrainOptimizeInterface.get_model - RÉCUPÉRATION MODÈLE ENREGISTRÉ)
   - Lignes 1088-1104 dans ml_core.py (17 lignes)
   - FONCTION : Récupère un modèle enregistré dans l'interface par son nom avec validation d'existence et gestion d'erreurs appropriée
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * name (str) - Nom du modèle à récupérer depuis le registre des modèles
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION EXISTENCE** : `if name not in self.models:` contrôle existence directe dans dictionnaire
     * **EXCEPTION EXPLICITE** : `raise KeyError(f"Modèle '{name}' non enregistré dans l'interface")` avec message formaté
     * **RETOUR SÉCURISÉ** : `return self.models[name]` accès direct au dictionnaire après validation
     * **TYPE HINTS COMPLETS** : `name: str` et `-> Any` pour validation statique et documentation
     * **DOCSTRING COMPLÈTE** : Args, Returns, Raises avec descriptions détaillées
     * **THREAD-SAFETY** : Accès atomique au dictionnaire self.models (GIL protection)
     * **PERFORMANCE** : O(1) pour vérification et récupération dans dictionnaire hash
     * **GESTION ERREURS** : Message d'erreur explicite incluant nom modèle pour debugging
   - RETOUR : Any - Le modèle enregistré (type dépend du modèle stocké)
   - RAISES : KeyError - Si le modèle demandé n'est pas enregistré dans l'interface
   - UTILITÉ : Méthode essentielle pour accéder aux modèles enregistrés dans le système. Permet la récupération sécurisée avec validation d'existence. Critique pour les systèmes utilisant plusieurs modèles avec gestion centralisée.

2. register_model.txt (TrainOptimizeInterface.register_model - ENREGISTREMENT MODÈLE)
   - Lignes 1077-1086 dans ml_core.py (10 lignes)
   - FONCTION : Enregistre un modèle dans l'interface avec un nom unique pour permettre la récupération ultérieure et la gestion centralisée des modèles
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * name (str) - Nom unique du modèle pour identification et récupération
     * model (Any) - Instance du modèle à enregistrer (peut être n'importe quel type de modèle ML)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE DIRECT** : Enregistre le modèle dans self.models[name] avec le nom comme clé
     * **ÉCRASEMENT POSSIBLE** : Si un modèle avec le même nom existe, il sera remplacé silencieusement
     * **LOGGING DEBUG** : Enregistre un message de debug confirmant l'enregistrement avec le nom du modèle
     * **GESTION UNIVERSELLE** : Accepte tout type de modèle (PyTorch, scikit-learn, TensorFlow, etc.)
     * **RÉFÉRENCE DIRECTE** : Stocke une référence directe au modèle, pas une copie
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour construire un registre de modèles centralisé. Permet la gestion de multiples modèles avec accès par nom. Essentielle pour les systèmes complexes utilisant plusieurs algorithmes ML simultanément.

3. predict.txt (ModelInterface.predict - PRÉDICTION MODÈLE INTERFACE)
   - Lignes 999-1010 dans ml_core.py (12 lignes)
   - FONCTION : Méthode d'interface abstraite pour la prédiction de labels à partir de features, définissant le contrat standard que doivent implémenter toutes les classes de modèles
   - PARAMÈTRES :
     * self - Instance de ModelInterface
     * X - Features/données d'entrée pour la prédiction (format dépend de l'implémentation)
     * **kwargs - Arguments supplémentaires variables pour personnalisation de la prédiction
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTHODE ABSTRAITE** : Implémentation vide (pass) car c'est une interface à implémenter par les sous-classes
     * **CONTRAT STANDARD** : Définit la signature standard que doivent respecter toutes les implémentations
     * **FLEXIBILITÉ PARAMÈTRES** : Utilise **kwargs pour permettre des paramètres spécifiques à chaque type de modèle
     * **POLYMORPHISME** : Permet l'utilisation uniforme de différents types de modèles via la même interface
     * **DOCUMENTATION INTERFACE** : Fournit la documentation standard pour toutes les implémentations
   - RETOUR : Prédictions (format dépend de l'implémentation concrète)
   - UTILITÉ : Interface fondamentale pour standardiser les prédictions de modèles ML. Permet le polymorphisme et l'interchangeabilité des modèles. Essentielle pour les architectures modulaires où différents types de modèles doivent être utilisés de manière uniforme.

4. load.txt (ModelInterface.load - CHARGEMENT MODÈLE INTERFACE)
   - Lignes 1041-1052 dans ml_core.py (12 lignes)
   - FONCTION : Méthode d'interface abstraite pour le chargement de modèles depuis un chemin de fichier, définissant le contrat standard que doivent implémenter toutes les classes de modèles
   - PARAMÈTRES :
     * self - Instance de ModelInterface
     * path - Chemin de fichier depuis lequel charger le modèle (format dépend de l'implémentation)
     * **kwargs - Arguments supplémentaires variables pour personnalisation du chargement
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTHODE ABSTRAITE** : Implémentation vide (pass) car c'est une interface à implémenter par les sous-classes
     * **CONTRAT STANDARD** : Définit la signature standard que doivent respecter toutes les implémentations
     * **FLEXIBILITÉ PARAMÈTRES** : Utilise **kwargs pour permettre des paramètres spécifiques à chaque type de modèle
     * **POLYMORPHISME** : Permet le chargement uniforme de différents types de modèles via la même interface
     * **DOCUMENTATION INTERFACE** : Fournit la documentation standard pour toutes les implémentations
     * **PATTERN FLUENT** : Retourne self pour permettre le chaînage de méthodes
   - RETOUR : self - Instance du modèle pour permettre le chaînage de méthodes
   - UTILITÉ : Interface fondamentale pour standardiser le chargement de modèles ML. Permet le polymorphisme et l'interchangeabilité des modèles. Essentielle pour les architectures modulaires où différents types de modèles doivent être chargés de manière uniforme.

5. save.txt (ModelInterface.save - SAUVEGARDE MODÈLE INTERFACE)
   - Lignes 1027-1038 dans ml_core.py (12 lignes)
   - FONCTION : Méthode d'interface abstraite pour la sauvegarde de modèles vers un chemin de fichier, définissant le contrat standard que doivent implémenter toutes les classes de modèles
   - PARAMÈTRES :
     * self - Instance de ModelInterface
     * path - Chemin de fichier vers lequel sauvegarder le modèle (format dépend de l'implémentation)
     * **kwargs - Arguments supplémentaires variables pour personnalisation de la sauvegarde
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTHODE ABSTRAITE** : Implémentation vide (pass) car c'est une interface à implémenter par les sous-classes
     * **CONTRAT STANDARD** : Définit la signature standard que doivent respecter toutes les implémentations
     * **FLEXIBILITÉ PARAMÈTRES** : Utilise **kwargs pour permettre des paramètres spécifiques à chaque type de modèle
     * **POLYMORPHISME** : Permet la sauvegarde uniforme de différents types de modèles via la même interface
     * **DOCUMENTATION INTERFACE** : Fournit la documentation standard pour toutes les implémentations
     * **RETOUR BOOLÉEN** : Spécifie que l'implémentation doit retourner True/False pour indiquer le succès
   - RETOUR : bool - True si la sauvegarde a réussi, False sinon (selon l'implémentation concrète)
   - UTILITÉ : Interface fondamentale pour standardiser la sauvegarde de modèles ML. Permet le polymorphisme et l'interchangeabilité des modèles. Essentielle pour les architectures modulaires où différents types de modèles doivent être sauvegardés de manière uniforme.

6. fit.txt (ModelInterface.fit - ENTRAÎNEMENT MODÈLE INTERFACE)
   - Lignes 984-996 dans ml_core.py (13 lignes)
   - FONCTION : Méthode d'interface abstraite pour l'entraînement de modèles sur des données, définissant le contrat standard que doivent implémenter toutes les classes de modèles
   - PARAMÈTRES :
     * self - Instance de ModelInterface
     * X - Features/données d'entraînement (format dépend de l'implémentation)
     * y - Labels/cibles d'entraînement (format dépend de l'implémentation)
     * **kwargs - Arguments supplémentaires variables pour personnalisation de l'entraînement
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTHODE ABSTRAITE** : Implémentation vide (pass) car c'est une interface à implémenter par les sous-classes
     * **CONTRAT STANDARD** : Définit la signature standard que doivent respecter toutes les implémentations
     * **FLEXIBILITÉ PARAMÈTRES** : Utilise **kwargs pour permettre des paramètres spécifiques à chaque type de modèle
     * **POLYMORPHISME** : Permet l'entraînement uniforme de différents types de modèles via la même interface
     * **DOCUMENTATION INTERFACE** : Fournit la documentation standard pour toutes les implémentations
     * **PATTERN FLUENT** : Retourne self pour permettre le chaînage de méthodes
   - RETOUR : self - Instance du modèle pour permettre le chaînage de méthodes
   - UTILITÉ : Interface fondamentale pour standardiser l'entraînement de modèles ML. Permet le polymorphisme et l'interchangeabilité des modèles. Essentielle pour les architectures modulaires où différents types de modèles doivent être entraînés de manière uniforme.

7. factory.txt (ModelProvider.factory - FACTORY CRÉATION MODÈLE)
   - Lignes 782-785 dans ml_core.py (4 lignes)
   - FONCTION : Fonction factory interne qui crée des instances de modèles en combinant les paramètres par défaut avec les paramètres fournis, implémentant le pattern Factory pour la création flexible de modèles
   - PARAMÈTRES :
     * **kwargs - Arguments nommés variables à combiner avec les paramètres par défaut
   - FONCTIONNEMENT DÉTAILLÉ :
     * **FUSION PARAMÈTRES** : Combine default_params avec kwargs en utilisant {**default_params, **kwargs}
     * **PRIORITÉ KWARGS** : Les paramètres fournis dans kwargs écrasent les paramètres par défaut
     * **CRÉATION INSTANCE** : Appelle model_class(**params) pour instancier le modèle avec les paramètres fusionnés
     * **PATTERN FACTORY** : Implémente le design pattern Factory pour création standardisée
     * **FLEXIBILITÉ** : Permet la personnalisation tout en conservant des valeurs par défaut sensées
   - RETOUR : Instance du modèle créé avec les paramètres fusionnés
   - UTILITÉ : Fonction factory essentielle pour la création flexible de modèles avec paramètres par défaut. Permet la standardisation de la création tout en autorisant la personnalisation. Critique pour les systèmes de modèles configurables.

8. get_result.txt (TrainOptimizeInterface.get_result - RÉCUPÉRATION RÉSULTAT MÉTRIQUE)
   - Lignes 1208-1228 dans ml_core.py (21 lignes)
   - FONCTION : Récupère un résultat spécifique pour un modèle et une métrique donnés avec validation d'existence et gestion d'erreurs détaillée
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * model_name (str) - Nom du modèle pour lequel récupérer le résultat
     * metric_name (str) - Nom de la métrique à récupérer (ex: 'accuracy', 'loss', 'f1_score')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MODÈLE** : Vérifie si model_name existe dans self.results
     * **ERREUR MODÈLE** : Lève KeyError avec message explicite si le modèle n'est pas trouvé
     * **VALIDATION MÉTRIQUE** : Vérifie si metric_name existe dans self.results[model_name]
     * **ERREUR MÉTRIQUE** : Lève KeyError avec message détaillé incluant modèle et métrique si non trouvée
     * **RÉCUPÉRATION SÉCURISÉE** : Retourne self.results[model_name][metric_name] après validation complète
     * **MESSAGES EXPLICITES** : Inclut les noms de modèle et métrique dans les messages d'erreur pour debugging
   - RETOUR : float - Valeur numérique du résultat pour la métrique demandée
   - RAISES : KeyError - Si le modèle ou la métrique n'existe pas dans les résultats
   - UTILITÉ : Méthode essentielle pour accéder aux résultats d'évaluation de modèles. Permet la récupération sécurisée de métriques avec validation. Critique pour les systèmes d'analyse de performance et de comparaison de modèles.

9. set_result.txt (TrainOptimizeInterface.set_result - DÉFINITION RÉSULTAT MÉTRIQUE)
   - Lignes 1193-1206 dans ml_core.py (14 lignes)
   - FONCTION : Définit un résultat pour un modèle et une métrique spécifiques avec création automatique de la structure de données et logging
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * model_name (str) - Nom du modèle pour lequel définir le résultat
     * metric_name (str) - Nom de la métrique à définir (ex: 'accuracy', 'loss', 'f1_score')
     * value (float) - Valeur numérique du résultat à stocker
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CRÉATION MODÈLE** : Si model_name n'existe pas dans self.results, crée automatiquement self.results[model_name] = {}
     * **STOCKAGE VALEUR** : Enregistre la valeur avec self.results[model_name][metric_name] = value
     * **ÉCRASEMENT POSSIBLE** : Si la métrique existe déjà, la valeur est remplacée silencieusement
     * **LOGGING DEBUG** : Enregistre un message de debug avec métrique, valeur et modèle pour traçabilité
     * **STRUCTURE HIÉRARCHIQUE** : Maintient une structure modèle -> métrique -> valeur pour organisation
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour enregistrer les résultats d'évaluation de modèles. Permet le stockage organisé de métriques par modèle. Essentielle pour les systèmes de suivi de performance et d'analyse comparative de modèles.

10. get_best_model.txt (TrainOptimizeInterface.get_best_model - SÉLECTION MEILLEUR MODÈLE)
   - Lignes 1230-1263 dans ml_core.py (34 lignes)
   - FONCTION : Identifie et retourne le meilleur modèle selon une métrique spécifique avec support pour optimisation ascendante ou descendante
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * metric_name (str) - Nom de la métrique pour la comparaison (ex: 'accuracy', 'loss', 'f1_score')
     * higher_is_better (bool, défaut=True) - Si True, valeur plus élevée = meilleur; si False, valeur plus faible = meilleur
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION VARIABLES** : `best_model = None; best_value = float('-inf') if higher_is_better else float('inf')` initialisation comparaison
     * **PARCOURS RÉSULTATS** : `for model_name, metrics in self.results.items():` itération sur tous modèles
     * **VÉRIFICATION MÉTRIQUE** : `if metric_name in metrics:` contrôle existence métrique pour modèle
     * **EXTRACTION VALEUR** : `value = metrics[metric_name]` récupération valeur métrique
     * **COMPARAISON ASCENDANTE** : `if higher_is_better: if value > best_value:` logique optimisation maximisation
     * **COMPARAISON DESCENDANTE** : `else: if value < best_value:` logique optimisation minimisation
     * **MISE À JOUR MEILLEUR** : `best_value = value; best_model = model_name` assignation nouveau meilleur
     * **VALIDATION EXISTENCE** : `if best_model is None:` vérification qu'au moins un résultat existe
     * **EXCEPTION EXPLICITE** : `raise ValueError(f"Aucun résultat disponible pour la métrique '{metric_name}'")` erreur si aucun résultat
     * **RETOUR TUPLE** : `return best_model, best_value` retour nom modèle et valeur métrique
   - RETOUR : Tuple[str, float] - (nom du meilleur modèle, valeur de la métrique)
   - RAISES : ValueError - Si aucun résultat n'est disponible pour la métrique spécifiée
   - UTILITÉ : Méthode essentielle pour la sélection automatique du meilleur modèle selon des critères de performance. Critique pour les systèmes d'AutoML et la comparaison de modèles. Permet l'optimisation automatique de la sélection de modèles.

11. create_model_factory.txt (ModelProvider.create_model_factory - CRÉATION FACTORY MODÈLE)
   - Lignes 777-787 dans ml_core.py (11 lignes)
   - FONCTION : Crée une fonction factory pour instancier des modèles avec des paramètres par défaut prédéfinis, simplifiant la création répétée de modèles similaires
   - PARAMÈTRES :
     * model_class - Classe de modèle à instancier (ex: LGBMClassifier, LSTM, etc.)
     * **default_params - Paramètres par défaut à appliquer lors de chaque création de modèle
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉFINITION FACTORY** : `def factory(**kwargs): params = {**default_params, **kwargs}; return model_class(**params)` fonction interne
     * **FUSION PARAMÈTRES** : `{**default_params, **kwargs}` combine paramètres défaut avec kwargs fournis
     * **PRIORITÉ KWARGS** : Paramètres fournis dans kwargs écrasent paramètres par défaut
     * **INSTANCIATION** : `model_class(**params)` appel constructeur avec paramètres fusionnés
     * **RETOUR FACTORY** : `return factory` retourne fonction factory configurée pour réutilisation
     * **CLOSURE** : Utilise closure pour capturer model_class et default_params dans scope
     * **PATTERN FACTORY** : Implémente design pattern Factory Method avec paramètres par défaut
     * **TYPE HINTS** : `model_class: type`, `default_params: dict`, `-> function` validation statique
   - RETOUR : function - Fonction factory configurée pour créer des instances du modèle
   - UTILITÉ : Méthode avancée pour simplifier la création répétée de modèles avec configuration standard. Permet la standardisation des paramètres tout en autorisant la personnalisation. Essentielle pour les systèmes de modèles configurables et les pipelines ML.

12. predict_proba.txt (ModelInterface.predict_proba - PRÉDICTION PROBABILITÉS INTERFACE)
   - Lignes 1013-1024 dans ml_core.py (12 lignes)
   - FONCTION : Méthode d'interface abstraite pour la prédiction de probabilités à partir de features, définissant le contrat standard pour les prédictions probabilistes
   - PARAMÈTRES :
     * self - Instance de ModelInterface
     * X - Features/données d'entrée pour la prédiction de probabilités
     * **kwargs - Arguments supplémentaires variables pour personnalisation
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTHODE ABSTRAITE** : `pass` implémentation vide car interface à implémenter par sous-classes
     * **CONTRAT PROBABILISTE** : Définit signature standard pour prédictions probabilités
     * **FLEXIBILITÉ PARAMÈTRES** : `**kwargs` pour paramètres spécifiques à chaque type modèle
     * **POLYMORPHISME** : Permet utilisation uniforme différents modèles pour prédictions probabilistes
     * **DOCUMENTATION INTERFACE** : Fournit documentation standard pour toutes implémentations
     * **TYPE HINTS** : `X`, `**kwargs`, `-> probabilities` validation statique
     * **PATTERN TEMPLATE** : Définit template method pour implémentations concrètes
   - RETOUR : Probabilités (format dépend de l'implémentation concrète)
   - UTILITÉ : Interface fondamentale pour standardiser les prédictions probabilistes de modèles ML. Permet le polymorphisme pour les modèles nécessitant des probabilités. Essentielle pour les systèmes de confiance et d'incertitude.

13. get_calculate_uncertainty.txt (ModelProvider.get_calculate_uncertainty - RÉCUPÉRATION CALCUL INCERTITUDE)
   - Lignes 752-774 dans ml_core.py (23 lignes)
   - FONCTION : Importe et met en cache la méthode calculate_uncertainty depuis HybridBaccaratPredictor pour éviter les importations circulaires et optimiser les performances
   - PARAMÈTRES :
     * cls - Référence à la classe ModelProvider (méthode de classe)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION CACHE** : `if cls._calculate_uncertainty_func is not None: return cls._calculate_uncertainty_func` contrôle cache existant
     * **RETOUR CACHE** : Si fonction en cache, retourne immédiatement sans traitement supplémentaire
     * **RÉCUPÉRATION INSTANCE** : `temp_hbp = cls.get_hbp_instance()` obtient instance HybridBaccaratPredictor (singleton)
     * **MISE EN CACHE** : `cls._calculate_uncertainty_func = temp_hbp.calculate_uncertainty` stockage pour réutilisation
     * **RETOUR FONCTION** : `return cls._calculate_uncertainty_func` retourne méthode calculate_uncertainty
     * **ÉVITEMENT CIRCULAIRE** : Évite importations circulaires en important conditionnellement
     * **OPTIMISATION PERFORMANCE** : Cache pour éviter recréer instance HBP à chaque appel
     * **GARANTIE COHÉRENCE** : Utilise directement méthode instance HBP pour même logique production
     * **MÉTHODE CLASSE** : `@classmethod` pour accès variables classe
   - RETOUR : function - La méthode calculate_uncertainty de HybridBaccaratPredictor
   - UTILITÉ : Méthode avancée pour l'accès optimisé aux fonctions de calcul d'incertitude. Essentielle pour éviter les importations circulaires et optimiser les performances. Critique pour les systèmes nécessitant des calculs d'incertitude fréquents.
