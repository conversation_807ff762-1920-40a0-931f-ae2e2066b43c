import logging
import sys
import tkinter as tk
from tkinter import messagebox
from multiprocessing import freeze_support

from hbp import HybridBaccaratPredictor

# ===== Point d'Entrée Principal =====

if __name__ == "__main__":

    logger = logging.getLogger(__name__) # Obtenir logger configuré
    logger.info("=== Démarrage Application HybridBaccaratPredictor ===")

    # Support multiprocessing pour certains backends matplotlib ou si utilisé ailleurs
    freeze_support()

    root = None
    try:

        root = tk.Tk()

        # Créer l'instance de l'application
        logger.info("Initialisation de HybridBaccaratPredictor...")
        app = HybridBaccaratPredictor(root) # Passe la fenêtre racine
        logger.info("Initialisation HybridBaccaratPredictor terminée.")

        # Optionnel: Afficher la fenêtre après préparation
        # root.deiconify()

        # Lancer la boucle principale Tkinter
        logger.info("Démarrage de la boucle principale Tkinter (mainloop)...")
        root.mainloop()
        logger.info("Boucle principale Tkinter terminée.")

    except Exception as e:
        # Capturer les erreurs critiques au démarrage/exécution principale
        logging.critical(f"Erreur fatale application: {e}", exc_info=True)
        # Tenter d'afficher une erreur même si Tkinter a des problèmes
        try:
            # Créer une fenêtre d'erreur temporaire si root a échoué ou n'existe pas
            err_root = None
            if not root or not root.winfo_exists():
                 err_root = tk.Tk()
                 err_root.withdraw() # Cacher la fenêtre principale vide
            messagebox.showerror("Erreur Critique", f"Erreur fatale:\n{e}\n\nConsultez les logs.", parent=err_root if err_root else root)
            if err_root: err_root.destroy()
        except Exception as e_msgbox: # Si messagebox échoue aussi
            print(f"\nERREUR CRITIQUE (affichage messagebox impossible: {e_msgbox}): {e}\n")
        sys.exit(1) # Quitter

    finally:
        logger.info("=== Arrêt Application HybridBaccaratPredictor ===")
        logging.shutdown() # Nettoyer handlers logging
