# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 56 à 88
# Type: Méthode de la classe ConfigManager

    def load_from_python_file(self, file_path: str, namespace: str = 'default') -> bool:
        """
        Charge la configuration depuis un fichier Python.

        Args:
            file_path: Chemin vers le fichier Python
            namespace: Espace de noms pour la configuration

        Returns:
            True si le chargement a réussi, False sinon
        """
        try:
            # Charger le module Python
            spec = importlib.util.spec_from_file_location("config_module", file_path)
            config_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(config_module)

            # Extraire les attributs publics (non commençant par _)
            config_dict = {
                key: value for key, value in vars(config_module).items()
                if not key.startswith('_') and not callable(value)
            }

            # Stocker la configuration
            self._config[namespace] = config_dict
            self._config_sources[namespace] = file_path

            logger.info(f"Configuration chargée depuis {file_path} dans l'espace de noms '{namespace}'")
            return True

        except Exception as e:
            logger.error(f"Erreur lors du chargement de la configuration depuis {file_path}: {e}")
            return False