DESCRIPTIF DÉTAILLÉ DES MÉTHODES - DEUXIÈME VAGUE TECHNIQUE
================================================================================

ÉTAT DOCUMENTATION : DEUXIÈME VAGUE COMPLÈTE
- **Couverture** : 100% des méthodes enrichies techniquement
- **Niveau** : Code source réel intégré, détails d'implémentation
- **Qualité** : 25-40 lignes par méthode avec précision technique

STRUCTURE DU SYSTÈME :
- **CalculConfiance** : Méthodes de calcul et gestion de la confiance (23 méthodes)
- **EvaluationMetriques** : Métriques de performance et évaluation (19 méthodes)
- **GestionDonnees** : Gestion des données et préparation (27 méthodes)
- **InterfaceUtilisateur** : Interface utilisateur et affichage (29 méthodes)
- **OptimisationEntrainement** : Optimisation et entraînement des modèles (32 méthodes)
- **ReseauxNeuronaux** : Réseaux de neurones et prédictions (3 méthodes)
- **UtilitairesFonctions** : Fonctions utilitaires et helpers (27 méthodes)
- **anciennesclasses** : Définitions de classes du système (2 classes)

TOTAL : 162 MÉTHODES ANALYSÉES

================================================================================
SECTION 1 : CALCUL CONFIANCE
================================================================================

1. __init___1.txt (ConsecutiveConfidenceCalculator.__init__ - DOUBLON - Constructeur du calculateur de confiance)
   - Lignes 1423-1427 dans hbp.py (5 lignes)
   - FONCTION : Initialise une instance de ConsecutiveConfidenceCalculator pour le calcul de confiance basé sur les patterns consécutifs
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION STATISTIQUES :** Crée un dictionnaire par défaut pour stocker les statistiques de patterns
     * **STRUCTURE PATTERN_STATS :** Chaque pattern contient total, success, consecutive_lengths et max_consecutive
     * **HISTORIQUE RÉCENT :** Initialise les listes pour les recommandations et résultats récents
     * **CONFIGURATION LIMITE :** Définit la taille maximale de l'historique récent (défaut 50)
   - RETOUR : None - Constructeur ne retourne rien
   - UTILITÉ : Prépare le calculateur pour analyser les patterns de séquences consécutives et calculer la confiance des recommandations

2. calculate_confidence.txt (ConsecutiveConfidenceCalculator.calculate_confidence - Calcul de confiance avancé)
   - Lignes 1486-1579 dans hbp.py (94 lignes)
   - FONCTION : Calcule la confiance dans les recommandations NON-WAIT pour la manche actuelle en analysant les patterns et facteurs multiples
   - PARAMÈTRES :
     * self - Instance de la classe ConsecutiveConfidenceCalculator
     * features_vector (List[float]) - Vecteur de features pour la manche actuelle
     * current_round (int) - Numéro de la manche actuelle
     * config - Configuration du prédicteur
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION PLAGE :** Vérifie si la manche est dans la plage cible (target_round_min à target_round_max)
     * **EXTRACTION PATTERN :** Extrait la clé de pattern à partir du vecteur de features
     * **STATISTIQUES PATTERN :** Récupère les statistiques historiques pour ce pattern spécifique
     * **CALCUL CONFIANCE BASE :** Combine taux de succès (70%) et longueur moyenne consécutive (30%)
     * **BONUS COURBE CLOCHE :** Applique un bonus pour les manches au milieu de la plage cible
     * **FACTEURS MULTIPLICATIFS :** Calcule sequence_bonus, late_game_factor, occurrence_factor, consecutive_factor
     * **AJUSTEMENT FINAL :** Multiplie la confiance par tous les facteurs et limite entre 0 et 1
   - RETOUR : Dict - Dictionnaire contenant confidence, expected_consecutive, success_rate, et tous les facteurs calculés
   - UTILITÉ : Fournit une évaluation sophistiquée de la confiance pour optimiser les recommandations de mise

3. _analyze_sequence_context.txt (HybridBaccaratPredictor._analyze_sequence_context - Analyse contextuelle de séquence)
   - Lignes 13941-13979 dans hbp.py (39 lignes)
   - FONCTION : Analyse le contexte de la séquence pour adapter les poids des modèles en fonction de la volatilité et des patterns
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats à analyser
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION LONGUEUR :** Vérifie qu'il y a au moins 10 éléments pour une analyse fiable
     * **CALCUL VOLATILITÉ :** Analyse les alternances dans les 10 derniers coups pour mesurer l'instabilité
     * **DÉTECTION STREAKS :** Identifie la longueur du streak actuel en remontant la séquence
     * **NORMALISATION STREAK :** Normalise le facteur de streak entre 0 et 1 (max 10 coups)
     * **COMBINAISON FACTEURS :** Combine volatilité (70%) et facteur de streak (30%)
     * **INTERPRÉTATION :** Proche de 0 = séquence stable, proche de 1 = séquence volatile
   - RETOUR : float - Facteur contextuel entre 0 et 1 indiquant la pertinence du modèle de session vs global
   - UTILITÉ : Permet d'adapter dynamiquement les poids des modèles selon le contexte de jeu actuel

4. calculate_uncertainty.txt (HybridBaccaratPredictor.calculate_uncertainty - Calcul d'incertitude par variance)
   - Lignes 4386-4547 dans hbp.py (162 lignes)
   - FONCTION : Calcule un score d'incertitude basé sur la variance des prédictions des estimateurs du BaggingClassifier LGBM
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * features (Optional[List[float]]) - Liste des features d'entrée
     * predicted_class (Optional[int]) - Classe prédite (0=PLAYER, 1=BANKER), optionnel
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION FEATURES :** Vérifie que features n'est pas None et a la bonne longueur
     * **INITIALISATION MODÈLE :** Tente d'initialiser lgbm_uncertainty si nécessaire avec protection récursion
     * **VÉRIFICATIONS MODÈLE :** Contrôle que le modèle et scaler sont initialisés et fittés
     * **NORMALISATION :** Applique feature_scaler.transform avec gestion d'erreurs complète
     * **PRÉDICTIONS MULTIPLES :** Collecte les prédictions de chaque estimateur du BaggingClassifier
     * **GESTION CLASSES :** Trouve l'index de la classe Banker avec cache optimisé
     * **CALCUL VARIANCE :** Calcule la variance des probabilités Banker entre estimateurs
     * **NORMALISATION SCORE :** Applique facteur de normalisation réduit et clipping [0,1]
   - RETOUR : float - Score d'incertitude entre 0 et 1 (0.5 par défaut en cas d'erreur)
   - UTILITÉ : Fournit une mesure d'incertitude épistémique pour évaluer la fiabilité des prédictions

5. calculate_bayesian_weights.txt (HybridBaccaratPredictor.calculate_bayesian_weights - Calcul poids bayésiens)
   - Lignes 8589-8622 dans hbp.py (34 lignes)
   - FONCTION : Calcule les poids bayésiens des modèles en fonction de leur confiance selon P(M|D) = P(D|M) * P(M) / P(D)
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * current_weights (Dict[str, float]) - Poids actuels des modèles (priors)
     * method_confidences (Dict[str, float]) - Confiance calculée pour chaque modèle (likelihood)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CALCUL PRODUIT :** Multiplie poids actuels P(M) par confidences P(D|M) pour chaque méthode
     * **NORMALISATION BAYÉSIENNE :** Divise par somme totale pour obtenir probabilités postérieures P(M|D)
     * **GESTION EPSILON :** Utilise epsilon configuré pour éviter divisions par zéro
     * **FALLBACK SÉCURISÉ :** Retourne poids originaux si somme totale trop petite
   - RETOUR : Dict[str, float] - Poids bayésiens ajustés normalisés
   - UTILITÉ : Implémente mise à jour bayésienne des poids pour adaptation dynamique basée sur performance

6. update_weights.txt (HybridBaccaratPredictor.update_weights - Mise à jour poids méthodes)
   - Lignes 10546-10740 dans hbp.py (195 lignes)
   - FONCTION : Ajuste dynamiquement les poids des méthodes (Markov, LGBM, LSTM) en fonction de la performance récente avec protection thread-safe
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * last_prediction (Dict) - Dictionnaire de prédiction retourné par hybrid_prediction pour la manche terminée
     * actual_outcome (str) - Résultat réel ('player' ou 'banker') de cette manche
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise `with self.sequence_lock, self.model_lock, self.weights_lock:` pour accès sécurisé
     * **VALIDATION SEUIL :** Vérifie `num_predictions_made = len(self.prediction_history) - 1` et `min_predictions_for_adjust = 20`
     * **EXTRACTION DÉTAILS :** Récupère `prediction_details = last_prediction.get('methods', {})` pour analyse par méthode
     * **ÉVALUATION PERFORMANCE :** Pour chaque méthode, calcule `method_was_correct = (pred_player > pred_banker and actual_outcome_lower == 'player') or (pred_banker > pred_player and actual_outcome_lower == 'banker')`
     * **MISE À JOUR HISTORIQUE :** Ajoute résultat à `performance_data['accuracy_history'].append(1.0 if method_was_correct else 0.0)` avec limite `max_history_size = 50`
     * **CALCUL SCORES PERFORMANCE :** Calcule `recent_acc = np.mean(acc_history)` pour chaque méthode avec fallback `0.5` si vide
     * **AJUSTEMENT POIDS :** Applique `new_weights[method] = max(epsilon, perf_scores[method])` puis normalise avec `total_perf = sum(new_weights.values())`
     * **LISSAGE ADAPTATIF :** Combine anciens et nouveaux poids avec `learning_rate = 0.1` : `self.weights[method] = (1 - learning_rate) * old_weight + learning_rate * new_weight`
     * **CALCUL ACCURACY SESSION :** Évalue performance globale avec `session_accuracy = correct_global / total_global_recommended` sur historique complet
     * **MISE À JOUR OPTIMISEUR WAIT :** Si activé, appelle `self.wait_placement_optimizer.update()` avec features, confiance et incertitude
   - RETOUR : None - Met à jour directement les poids internes et l'optimiseur
   - UTILITÉ : Maintient adaptation dynamique des poids basée sur performance récente avec lissage et protection thread-safe

7. calculate_model_confidence.txt (HybridBaccaratPredictor.calculate_model_confidence - Confiance modèle)
   - Lignes 8426-8489 dans hbp.py (64 lignes)
   - FONCTION : Calcule la confiance d'un modèle en utilisant plusieurs méthodes avancées avec facteurs contextuels et performance historique
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * prob_banker (float) - Probabilité prédite pour 'banker' (entre 0 et 1)
     * method (str) - Nom du modèle ('markov', 'lgbm', 'lstm')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONFIANCE BASE :** Calcule `base_confidence = np.clip(confidence_normalization_factor * abs(prob_banker - 0.5), confidence_min_clip, confidence_max_clip)` avec facteur normalisation (défaut: 2.0)
     * **FACTEUR HISTORIQUE :** Utilise performance récente avec `recent_acc = np.mean(acc_history[-10:])` et `historical_factor = 1.0 + (recent_acc - 0.5) * 2.0`
     * **PROTECTION THREAD :** Accède aux performances avec `with self.weights_lock:` pour sécurité
     * **CLIPPING HISTORIQUE :** Limite facteur avec `max(historical_factor_min, min(historical_factor_max, historical_factor))` (défaut: 0.8-2.0)
     * **DÉTECTION STREAKS :** Analyse `last_3 = self.sequence[-3:]` et vérifie `all(x == last_3[0] for x in last_3)`
     * **BONUS SPÉCIFIQUES :** Applique `markov_streak_factor = 1.2` pour Markov, `lstm_streak_factor = 1.15` pour LSTM lors de streaks
     * **AJUSTEMENT LONGUEUR :** Pour LSTM si `seq_length > lstm_long_sequence_threshold`, multiplie par `min(lstm_long_sequence_factor, 1.0 + (seq_length - threshold) / 100)`
     * **PÉNALITÉ MARKOV :** Si `seq_length < markov_short_sequence_threshold`, applique `max(markov_short_sequence_factor, seq_length / threshold)`
     * **COMBINAISON FINALE :** Calcule `final_confidence = base_confidence * historical_factor * context_factor`
   - RETOUR : float - Score de confiance entre 0 et 1, combinant distance à 0.5, performance historique et facteurs contextuels
   - UTILITÉ : Évalue fiabilité prédiction selon méthode spécifique, performance passée et contexte séquence actuelle

8. analyze_context_sensitivity.txt (HybridBaccaratPredictor.analyze_context_sensitivity - Analyse sensibilité contextuelle)
   - Lignes 8491-8587 dans hbp.py (97 lignes)
   - FONCTION : Analyse la sensibilité contextuelle de la prédiction en évaluant comment elle changerait avec variations dans la séquence
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence actuelle de résultats ('player'/'banker')
     * prob_banker (float) - Probabilité prédite pour 'banker' (0.0-1.0)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SÉQUENCE :** Vérifie `if not isinstance(sequence, list): return 0.5` et `if len(sequence) < 5: return 0.5` pour données insuffisantes
     * **GÉNÉRATION VARIATIONS :** Crée 3 types de variations : `modified_sequences = []`
       - **Variation dernière position :** `var1 = sequence[:-1] + ['banker' if sequence[-1] == 'player' else 'player']`
       - **Variation avant-dernière :** `var2 = sequence[:-2] + ['banker' if sequence[-2] == 'player' else 'player'] + [sequence[-1]]` si `len(sequence) >= 2`
       - **Variation 3ème position :** `var3 = sequence[:-3] + ['banker' if sequence[-3] == 'player' else 'player'] + sequence[-2:]` si `len(sequence) >= 3`
     * **CALCUL PRÉDICTIONS VARIATIONS :** Pour chaque variation, appelle `lgbm_feat, lstm_feat = self.create_hybrid_features(var_seq)` puis `var_prediction = self.hybrid_prediction(lgbm_feat, lstm_feat)`
     * **EXTRACTION PROBABILITÉS :** Récupère `var_prob = var_prediction.get('banker', 0.5)` pour chaque variation
     * **GESTION ERREURS :** Capture exceptions avec `except Exception as e:` et continue avec variations suivantes
     * **CALCUL DIFFÉRENCES :** Calcule `diffs = [abs(prob_banker - var_prob) for var_prob in variation_probs]` et `avg_diff = np.mean(diffs)`
     * **NORMALISATION SENSIBILITÉ :** Applique `sensitivity_factor = getattr(self.config, 'sensitivity_normalization_factor', 5.0)` puis `sensitivity = np.clip(avg_diff * sensitivity_factor, uncertainty_min_clip, uncertainty_max_clip)`
     * **PARAMÈTRES CONFIG :** Utilise `uncertainty_min_clip=0.0`, `uncertainty_max_clip=1.0` depuis configuration
   - RETOUR : float - Score de sensibilité entre 0 (stable) et 1 (très sensible), basé sur variance des prédictions
   - UTILITÉ : Évalue robustesse de la prédiction face aux variations contextuelles pour mesurer incertitude épistémique

9. calculate_epistemic_uncertainty.txt (HybridBaccaratPredictor.calculate_epistemic_uncertainty - Incertitude épistémique)
   - Lignes 8624-8644 dans hbp.py (21 lignes)
   - FONCTION : Calcule l'incertitude épistémique (incertitude du modèle) basée sur la variance des prédictions entre différents modèles
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * prob_list (List[float]) - Liste des probabilités prédites par différents modèles (markov, lgbm, lstm)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CALCUL VARIANCE :** Calcule `variance = np.var(prob_list)` pour mesurer désaccord entre modèles
     * **RÉCUPÉRATION CONFIG :** Extrait `uncertainty_normalization_factor = getattr(self.config, 'uncertainty_normalization_factor', 4.0)` (variance max = 0.25 pour valeurs [0,1])
     * **PARAMÈTRES CLIPPING :** Récupère `uncertainty_min_clip = getattr(self.config, 'uncertainty_min_clip', 0.0)` et `uncertainty_max_clip = getattr(self.config, 'uncertainty_max_clip', 1.0)`
     * **NORMALISATION VARIANCE :** Applique `normalized_variance = np.clip(variance * uncertainty_normalization_factor, uncertainty_min_clip, uncertainty_max_clip)`
     * **INTERPRÉTATION :** Variance élevée = modèles en désaccord = forte incertitude épistémique (manque de connaissances)
   - RETOUR : float - Score d'incertitude épistémique entre 0 et 1, normalisé depuis variance des prédictions
   - UTILITÉ : Quantifie l'incertitude due au manque de connaissances du modèle via désaccord entre prédicteurs

10. calculate_aleatoric_uncertainty.txt (HybridBaccaratPredictor.calculate_aleatoric_uncertainty - Incertitude aléatoire)
    - Lignes 8646-8667 dans hbp.py (22 lignes)
    - FONCTION : Calcule l'incertitude aléatoire (incertitude inhérente) basée sur l'entropie de la prédiction
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * prob (float) - Probabilité prédite pour une classe (0.0-1.0)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **RÉCUPÉRATION EPSILON :** Extrait `epsilon = getattr(self.config, 'epsilon_value', 1e-9)` pour éviter divisions par zéro
      * **SÉCURISATION PROBABILITÉ :** Calcule `prob_safe = max(epsilon, min(1.0 - epsilon, prob))` pour assurer prob dans [epsilon, 1-epsilon]
      * **CALCUL ENTROPIE BINAIRE :** Applique `entropy = -(prob_safe * np.log2(prob_safe) + (1.0 - prob_safe) * np.log2(1.0 - prob_safe))`
      * **NORMALISATION AUTOMATIQUE :** L'entropie binaire max est 1.0 (quand prob=0.5), donc pas de normalisation supplémentaire nécessaire
      * **INTERPRÉTATION :** Entropie élevée = prédiction proche de 0.5 = forte incertitude aléatoire inhérente
    - RETOUR : float - Score d'incertitude aléatoire entre 0 et 1, basé sur entropie binaire
    - UTILITÉ : Quantifie l'incertitude irréductible inhérente aux données via entropie de la prédiction

11. get_weights.txt (HybridBaccaratPredictor.get_weights - Récupération poids actuels)
    - Lignes 8416-8424 dans hbp.py (9 lignes)
    - FONCTION : Retourne les poids actuels des méthodes de prédiction avec protection thread-safe complète
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **PROTECTION THREAD :** Utilise `with self.weights_lock:` pour protéger l'accès concurrent aux poids
      * **COPIE DÉFENSIVE :** Retourne `self.weights.copy()` pour éviter modifications externes accidentelles
      * **ACCÈS SÉCURISÉ :** Garantit lecture atomique des poids même en cas d'accès concurrent
      * **VALIDATION EXISTENCE :** Assure que l'attribut weights existe avant accès
      * **GESTION ERREURS :** Protection contre exceptions lors de l'accès aux poids
    - RETOUR : Dict[str, float] - Copie des poids actuels des méthodes (markov, lgbm, lstm)
    - UTILITÉ : Accès thread-safe aux poids pour affichage UI et calculs de prédiction hybride avec protection complète

12. consecutive_focused_metric.txt (HybridBaccaratPredictor.consecutive_focused_metric - Métrique focus consécutif)
    - Lignes 296-365 dans hbp.py (70 lignes)
    - FONCTION : Métrique personnalisée pour LGBM qui se concentre sur les recommandations NON-WAIT valides consécutives spécifiquement pour les manches 31-60
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * y_true - Labels réels (0=Player, 1=Banker)
      * y_pred - Probabilités prédites pour la classe positive (banker)
      * round_indices (optionnel) - Indices des manches (si None, données déjà filtrées)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CONVERSION CLASSES :** Calcule `y_pred_class = (y_pred > 0.5).astype(int)` pour obtenir prédictions binaires
      * **FILTRAGE MANCHES CIBLES :** Si `round_indices` fourni, filtre `target_indices = [i for i, r in enumerate(round_indices) if 31 <= r <= 60]`
      * **VALIDATION DONNÉES :** Retourne `1e-7, 'consecutive_focused_metric'` si aucune manche 31-60 présente
      * **CALCUL ACCURACY BASE :** Calcule `correct_predictions = (y_pred_class == y_true)` et `accuracy = np.mean(correct_predictions)`
      * **SIMULATION RECOMMANDATIONS :** Calcule `confidence = np.abs(y_pred - 0.5) * 2` et `non_wait_mask = confidence >= min_confidence`
      * **CALCUL SÉQUENCES CONSÉCUTIVES :** Itère pour détecter séquences NON-WAIT correctes consécutives avec `current_consecutive += 1` si correct, reset si incorrect
      * **MÉTRIQUES SÉQUENCES :** Calcule `max_consecutive = max(consecutive_sequences)` et moyenne pondérée avec poids quadratiques
      * **SCORE FINAL :** Combine `final_score = (max_consecutive**2 * 0.8 + weighted_mean * 0.15 + accuracy * 0.05)` privilégiant fortement les séquences
    - RETOUR : tuple - (score, nom_de_la_métrique) avec score optimisé pour recommandations consécutives
    - UTILITÉ : Optimise LGBM spécifiquement pour objectif recommandations NON-WAIT valides consécutives sur manches 31-60

13. calculate_consecutive_focused_weights.txt (HybridBaccaratPredictor.calculate_consecutive_focused_weights - Poids focus consécutif)
    - Lignes 172-294 dans hbp.py (123 lignes)
    - FONCTION : Calcule des poids d'échantillons qui favorisent les recommandations NON-WAIT valides consécutives pour les manches 31-60
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * X_features (np.ndarray) - Features d'entrée pour l'entraînement
      * y (np.ndarray) - Labels cibles (0=Player, 1=Banker)
      * sequence_positions (np.ndarray, optionnel) - Positions des échantillons dans la séquence
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION POIDS :** Crée `sample_weights = np.ones(len(y), dtype=np.float32)` comme base uniforme
      * **FILTRAGE MANCHES CIBLES :** Si `sequence_positions` fourni, identifie indices avec `31 <= pos <= 60`
      * **SIMULATION PRÉDICTIONS :** Pour chaque échantillon, simule prédiction et calcule confiance avec `confidence = abs(prob_banker - 0.5) * 2`
      * **DÉTECTION NON-WAIT :** Marque échantillons avec `confidence >= min_confidence_for_recommendation`
      * **CALCUL SÉQUENCES CONSÉCUTIVES :** Parcourt échantillons pour identifier séquences NON-WAIT correctes consécutives
      * **PONDÉRATION PROGRESSIVE :** Applique poids croissants selon position dans séquence : `weight = base_weight * (1 + consecutive_bonus * position_in_sequence)`
      * **BONUS MANCHES CIBLES :** Multiplie par `target_round_weight` (défaut: 2.0) pour manches 31-60
      * **PÉNALITÉ ERREURS :** Applique `error_penalty_weight` pour échantillons NON-WAIT incorrects qui brisent séquences
      * **NORMALISATION FINALE :** Normalise poids pour maintenir distribution équilibrée avec `sample_weights = sample_weights / np.mean(sample_weights)`
    - RETOUR : np.ndarray - Poids d'échantillons optimisés pour favoriser recommandations consécutives
    - UTILITÉ : Guide l'entraînement LGBM pour optimiser spécifiquement les recommandations NON-WAIT valides consécutives

14. evaluate_consecutive_focused.txt (HybridBaccaratPredictor.evaluate_consecutive_focused - Évaluation focus consécutif)
    - Lignes 367-500+ dans hbp.py (130+ lignes)
    - FONCTION : Évalue performance spécifique du système sur les recommandations NON-WAIT valides consécutives pour manches 31-60
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * num_simulations (int, optionnel) - Nombre de simulations à effectuer (défaut: 100)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **GÉNÉRATION SÉQUENCES :** Crée `num_simulations` séquences aléatoires de 60 manches avec `np.random.choice(['player', 'banker'])`
      * **EXTRACTION MANCHES CIBLES :** Isole `late_game_sequence = simulated_sequence[30:60]` pour focus sur manches 31-60
      * **PRÉDICTIONS SÉQUENTIELLES :** Pour chaque position i, utilise `current_seq = simulated_sequence[:30+i]` pour prédiction progressive
      * **EXTRACTION FEATURES :** Appelle `lgbm_features = self._extract_lgbm_features(current_seq)` et `lstm_features = self._extract_lstm_features(current_seq)`
      * **PRÉDICTION HYBRIDE :** Exécute `prediction = self.hybrid_prediction(lgbm_features, lstm_features)` pour chaque position
      * **CALCUL CONFIANCE :** Détermine `confidence = max(prediction['player'], prediction['banker'])` et compare avec `min_confidence_for_recommendation`
      * **DÉTECTION SÉQUENCES NON-WAIT :** Identifie recommandations NON-WAIT avec `confidence >= min_confidence` et vérifie exactitude
      * **CALCUL SÉQUENCES CONSÉCUTIVES :** Parcourt pour identifier séquences NON-WAIT correctes consécutives, reset si erreur
      * **MÉTRIQUES FINALES :** Calcule `max_consecutive = max(consecutive_sequences)`, moyenne pondérée avec poids quadratiques
      * **SCORE COMPOSITE :** Combine `consecutive_score = (max_consecutive**2 * CONSECUTIVE_SCORE_MAX_WEIGHT + weighted_mean * CONSECUTIVE_SCORE_WEIGHTED_MEAN_WEIGHT + precision_non_wait * CONSECUTIVE_SCORE_PRECISION_WEIGHT)`
    - RETOUR : Dict - Métriques incluant max_consecutive, precision_non_wait, consecutive_score
    - UTILITÉ : Évalue efficacité système sur objectif principal de recommandations NON-WAIT valides consécutives

15. consecutive_valid_recommendations_loss.txt (HybridBaccaratPredictor.consecutive_valid_recommendations_loss - Loss recommandations consécutives)
    - Lignes 84-170 dans hbp.py (87 lignes)
    - FONCTION : Fonction de perte personnalisée qui optimise pour les recommandations NON-WAIT valides consécutives avec pondération manches 31-60
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * outputs (torch.Tensor) - Sorties du modèle (logits)
      * targets (torch.Tensor) - Labels cibles (0=Player, 1=Banker)
      * weights (torch.Tensor, optionnel) - Poids des échantillons
      * sequence_positions (torch.Tensor, optionnel) - Positions des échantillons dans la séquence
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CALCUL PROBABILITÉS :** Utilise `probas = F.softmax(outputs, dim=1)` pour convertir logits en probabilités
      * **CALCUL CONFIANCE :** Extrait `confidence, predicted_classes = torch.max(probas, dim=1)` pour confiance et prédictions
      * **DÉTECTION NON-WAIT :** Crée `non_wait_mask = confidence >= min_confidence` avec `min_confidence = self.config.min_confidence_for_recommendation`
      * **VALIDATION PRÉDICTIONS :** Calcule `correct_predictions = (predicted_classes == targets)` pour exactitude
      * **INITIALISATION POIDS :** Crée `sample_weights = torch.ones_like(confidence, device=device)` comme base
      * **PONDÉRATION MANCHES CIBLES :** Si `sequence_positions` fourni, applique `target_round_weight` pour manches 31-60
      * **BONUS RECOMMANDATIONS VALIDES :** Multiplie par `consecutive_focus_factor` pour `valid_non_wait = non_wait_mask & correct_predictions`
      * **PÉNALITÉ ERREURS :** Applique `consecutive_focus_factor * 1.5` pour `invalid_non_wait = non_wait_mask & ~correct_predictions`
      * **CALCUL LOSS :** Utilise `per_sample_losses = F.cross_entropy(outputs, targets, reduction='none')` puis `weighted_losses = per_sample_losses * sample_weights`
      * **AGRÉGATION FINALE :** Retourne `weighted_losses.mean()` pour loss moyenne pondérée
    - RETOUR : torch.Tensor - Valeur de perte pondérée optimisée pour recommandations consécutives
    - UTILITÉ : Optimise spécifiquement les recommandations NON-WAIT valides consécutives avec focus manches 31-60

16. consecutive_valid_recommendations_loss_1.txt (HybridBaccaratPredictor.consecutive_valid_recommendations_loss_1 - Loss recommandations consécutives v1)
    - Lignes 10278-10348 dans hbp.py (71 lignes)
    - FONCTION : Version alternative de fonction de perte qui met l'accent sur les recommandations consécutives valides avec pénalité progressive
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * outputs (torch.Tensor) - Sorties du modèle (logits)
      * targets (torch.Tensor) - Labels cibles (0=Player, 1=Banker)
      * weights (torch.Tensor, optionnel) - Poids des échantillons
      * sequence_positions (torch.Tensor, optionnel) - Positions des échantillons dans la séquence
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION CIBLES :** Vérifie `if targets.min() < 0 or targets.max() > 1:` pour indices 0-based valides
      * **LOSS BASE :** Utilise `base_loss = self.uncertainty_weighted_loss(outputs, targets, weights, sequence_positions)` comme fondation
      * **VÉRIFICATION POSITIONS :** Si `sequence_positions is None: return base_loss` pour fallback sécurisé
      * **PARAMÈTRES CONFIGURABLES :** Extrait `target_round_min = getattr(self.config, 'target_round_min', 31)`, `target_round_max = getattr(self.config, 'target_round_max', 60)`, `consecutive_penalty_factor = getattr(self.config, 'consecutive_penalty_factor', 2.0)`
      * **CONVERSION POSITIONS :** Calcule `positions_1_indexed = sequence_positions + 1` pour positions 1-indexées
      * **MASQUE MANCHES CIBLES :** Crée `target_mask = (positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max)`
      * **VALIDATION BATCH :** Si `not torch.any(target_mask): return base_loss` pour éviter calculs inutiles
      * **CALCUL PRÉDICTIONS :** Utilise `_, predicted = torch.max(outputs, 1)` pour extraire classes prédites
      * **CALCUL ERREURS :** Détermine `errors = (predicted != targets).float()` pour identifier erreurs
      * **PÉNALITÉ PROGRESSIVE :** Pour chaque position dans manches cibles, calcule `relative_pos = (pos - target_round_min) / (target_round_max - target_round_min)` puis `penalty[i] = errors[i] * consecutive_penalty_factor * (1.0 + relative_pos)`
      * **LOSS FINALE :** Retourne `base_loss + penalty.mean()` pour combiner loss base et pénalités
    - RETOUR : torch.Tensor - Loss combinée avec pénalité progressive pour manches cibles
    - UTILITÉ : Version améliorée avec pénalité progressive selon position dans manches 31-60 pour optimiser recommandations consécutives

17. uncertainty_weighted_loss.txt (HybridBaccaratPredictor.uncertainty_weighted_loss - Loss pondérée par incertitude)
    - Lignes 10172-10276 dans hbp.py (105 lignes)
    - FONCTION : Fonction de perte personnalisée qui prend en compte l'incertitude et les positions de séquence avec focus manches 31-60
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * outputs (torch.Tensor) - Sorties du modèle (logits)
      * targets (torch.Tensor) - Labels cibles (0=Player, 1=Banker)
      * weights (torch.Tensor, optionnel) - Poids des échantillons
      * sequence_positions (torch.Tensor, optionnel) - Positions des échantillons dans la séquence
    - FONCTIONNEMENT DÉTAILLÉ :
      * **LOSS BASE :** Utilise `base_criterion = nn.CrossEntropyLoss(reduction='none')` avec système 0-based standard
      * **VALIDATION CIBLES :** Vérifie `if targets.min() < 0 or targets.max() > 1:` pour indices valides
      * **CALCUL LOSS ÉCHANTILLONS :** Calcule `per_sample_losses = base_criterion(outputs, targets)` pour chaque échantillon
      * **INITIALISATION POIDS :** Crée `combined_weights = torch.ones_like(per_sample_losses)` comme base
      * **APPLICATION POIDS :** Si `weights is not None`, applique `combined_weights = combined_weights * weights` avec ajustement forme
      * **PONDÉRATION MANCHES CIBLES :** Si `sequence_positions` fourni, extrait paramètres `late_game_weight_factor`, `target_round_min=31`, `target_round_max=60`
      * **CONVERSION POSITIONS :** Calcule `positions_1_indexed = sequence_positions + 1` pour correspondance numéros manches
      * **MASQUE MANCHES CIBLES :** Crée `late_game_mask = (positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max)`
      * **CALCUL ERREURS CONSÉCUTIVES :** Détermine `_, predicted = torch.max(outputs, 1)` puis `errors = (predicted != targets).float()`
      * **PÉNALITÉ CONSÉCUTIVE :** Pour manches cibles, calcule `consecutive_count` et applique `consecutive_penalty = 1.0 + consecutive_errors * 0.5`
      * **COMBINAISON POIDS :** Multiplie `combined_weights = combined_weights * position_factor` pour intégrer tous facteurs
      * **LOSS FINALE :** Calcule `weighted_losses = per_sample_losses * combined_weights` puis retourne `weighted_losses.mean()`
    - RETOUR : torch.Tensor - Loss moyenne pondérée par incertitude et positions séquence
    - UTILITÉ : Améliore apprentissage en tenant compte de l'incertitude avec focus spécial sur manches 31-60 et pénalité erreurs consécutives

18. get_confidence_adjustment.txt (HybridBaccaratPredictor.get_confidence_adjustment - Ajustement confiance)
    - FONCTION : Calcule ajustement de confiance basé sur contexte actuel
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * base_confidence (float) - Confiance de base calculée
      * current_round (int) - Numéro de manche actuelle
      * sequence_context (Dict) - Contexte de la séquence actuelle
    - FONCTIONNEMENT DÉTAILLÉ :
      * **ANALYSE CONTEXTE :** Évalue contexte de jeu actuel
      * **FACTEUR MANCHE :** Applique ajustement selon numéro de manche
      * **HISTORIQUE RÉCENT :** Considère performance récente
      * **AJUSTEMENT ADAPTATIF :** Modifie confiance selon conditions
      * **VALIDATION BORNES :** Assure confiance dans limites acceptables
    - RETOUR : float - Confiance ajustée selon contexte
    - UTILITÉ : Adapte confiance aux conditions spécifiques de jeu

19. update_consecutive_confidence_calculator.txt (HybridBaccaratPredictor.update_consecutive_confidence_calculator - MAJ calculateur confiance consécutive)
    - Lignes 8491-8587 dans hbp.py (97 lignes)
    - FONCTION : Met à jour le calculateur de confiance consécutive pour recommandations NON-WAIT avec suivi manches 31-61
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * round_num (int) - Numéro de la manche actuelle
      * recommendation (str) - Recommandation faite ('player', 'banker', 'wait')
      * actual_outcome (str) - Résultat réel ('player', 'banker')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION PLAGE :** Extrait `target_round_min = getattr(self.config, 'target_round_min', 31)`, `target_round_max = getattr(self.config, 'target_round_max', 61)` et vérifie `is_target_round = target_round_min <= round_num <= target_round_max`
      * **INITIALISATION COMPTEURS :** Si première utilisation, initialise `self.total_nonwait = 0`, `self.total_nonwait_valid = 0`, `self.total_wait = 0`, `self.consecutive_nonwait_valid = 0`, `self.max_consecutive_nonwait_valid = 0`
      * **MISE À JOUR CALCULATEUR :** Si `hasattr(self, 'consecutive_confidence_calculator'):`, appelle `self.consecutive_confidence_calculator.update_recent_data(recommendation, actual_outcome)` avec gestion d'erreurs
      * **TRAITEMENT NON-WAIT :** Si `recommendation != 'wait':`, incrémente `self.total_nonwait += 1` et vérifie validité avec `is_valid = (recommendation_lower == 'player' and actual_outcome_lower == 'player') or (recommendation_lower == 'banker' and actual_outcome_lower == 'banker')`
      * **SÉQUENCE VALIDE :** Si valide, incrémente `self.total_nonwait_valid += 1`, `self.consecutive_nonwait_valid += 1` et met à jour `self.max_consecutive_nonwait_valid = max(self.max_consecutive_nonwait_valid, self.consecutive_nonwait_valid)`
      * **SÉQUENCE INVALIDE :** Si invalide, reset `self.consecutive_nonwait_valid = 0` avec logging détaillé
      * **TRAITEMENT WAIT :** Si `recommendation == 'wait'`, incrémente `self.total_wait += 1` mais maintient séquence consécutive (WAIT n'interrompt pas)
      * **LOGGING DÉTAILLÉ :** Enregistre toutes transitions avec `logger.debug` pour debugging et suivi performance
    - RETOUR : None - Met à jour directement les compteurs internes et calculateur
    - UTILITÉ : Maintient statistiques précises des recommandations NON-WAIT consécutives avec focus manches 31-61

20. update_consecutive_confidence_calculator_1.txt (HybridBaccaratPredictor.update_consecutive_confidence_calculator_1 - MAJ calculateur v1)
    - FONCTION : Version alternative de mise à jour du calculateur de confiance
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * results_batch (List[str]) - Lot de résultats à traiter
      * predictions_batch (List[Dict]) - Lot de prédictions correspondantes
    - FONCTIONNEMENT DÉTAILLÉ :
      * **TRAITEMENT LOT :** Traite plusieurs résultats simultanément
      * **OPTIMISATION :** Calculs vectorisés pour efficacité
      * **VALIDATION BATCH :** Vérifie cohérence du lot complet
      * **MISE À JOUR ATOMIQUE :** Applique tous changements en une fois
      * **ROLLBACK :** Possibilité d'annuler si erreur détectée
    - RETOUR : bool - True si mise à jour réussie, False sinon
    - UTILITÉ : Version optimisée pour traitement par lots

21. init_consecutive_confidence_calculator.txt (HybridBaccaratPredictor.init_consecutive_confidence_calculator - Init calculateur confiance)
    - Lignes 1410-1587 dans hbp.py (178 lignes)
    - FONCTION : Initialise le calculateur de confiance consécutive pour recommandations NON-WAIT avec classe interne complète
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CAPTURE CONFIG :** Utilise `config_ref = self.config` pour accès dans classe interne
      * **DÉFINITION CLASSE :** Crée `class ConsecutiveConfidenceCalculator:` avec méthodes complètes :
        - `__init__()` : Initialise `pattern_stats = defaultdict(lambda: {"total": 0, "success": 0, "consecutive_lengths": [], "max_consecutive": 0})`, `recent_recommendations = []`, `recent_outcomes = []`, `max_recent_history = getattr(config_ref, 'max_recent_history', 50)`
        - `_extract_pattern_key()` : Extrait clé pattern avec arrondi intelligent (ratios: 1 décimale, autres: 0 décimale)
        - `update_recent_data()` : Met à jour historique avec rotation automatique
        - `get_current_wait_ratio()` : Calcule ratio WAIT/NON-WAIT avec `wait_count = sum(1 for rec in self.recent_recommendations if rec == 'wait')`
        - `get_recent_nonwait_success_rate()` : Taux succès NON-WAIT avec `valid_count = sum(1 for rec, out in zip(self.recent_recommendations, self.recent_outcomes) if rec != 'wait' and rec.lower() == out.lower())`
        - `calculate_confidence()` : Calcul confiance principal avec validation plage, extraction pattern, calcul taux succès, longueur moyenne séquences, facteurs bonus/occurrence/consécutif
      * **CALCULS AVANCÉS :** Dans `calculate_confidence()` :
        - Confiance base : `confidence = success_rate * 0.7 + min(1.0, avg_consecutive / 10.0) * 0.3`
        - Position relative : `relative_pos = (current_round - target_round_min) / (target_round_max - target_round_min)`
        - Bonus séquence : `sequence_bonus = 1.0 + getattr(config, 'sequence_bonus_factor', 0.1) * (pattern_stats["max_consecutive"] - getattr(config, 'sequence_bonus_threshold', 4))`
        - Facteur jeu tardif : `late_game_factor = 1.0 + getattr(config, 'late_game_factor', 1.0) * (relative_pos - 0.7) / 0.3`
        - Facteur occurrence : `occurrence_factor = min(1.0 + pattern_stats["total"] / getattr(config, 'occurrence_factor_divisor', 10.0), getattr(config, 'max_occurrence_factor', 2.0))`
      * **INSTANCIATION :** Crée `self.consecutive_confidence_calculator = ConsecutiveConfidenceCalculator()` avec gestion d'erreurs complète
    - RETOUR : bool - True si initialisation réussie, False si erreur
    - UTILITÉ : Configure calculateur spécialisé avec classe interne complète pour analyse patterns et confiance manches consécutives

22. init_wait_placement_optimizer.txt (HybridBaccaratPredictor.init_wait_placement_optimizer - Init optimiseur placement attente)
    - FONCTION : Initialise l'optimiseur de placement pour stratégies d'attente
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * optimizer_config (Dict) - Configuration de l'optimiseur
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CRÉATION OPTIMISEUR :** Instancie optimiseur de placement
      * **CONFIGURATION STRATÉGIES :** Configure stratégies d'attente disponibles
      * **PARAMÈTRES OPTIMISATION :** Définit critères d'optimisation
      * **VALIDATION :** Vérifie cohérence de la configuration
      * **INTÉGRATION :** Intègre dans système de décision principal
    - RETOUR : bool - True si initialisation réussie
    - UTILITÉ : Configure optimisation des stratégies d'attente

23. get_current_wait_ratio.txt (HybridBaccaratPredictor.get_current_wait_ratio - Ratio attente actuel)
    - FONCTION : Calcule le ratio d'attente actuel basé sur conditions de jeu
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * current_confidence (float) - Confiance actuelle du système
      * recent_performance (Dict) - Performance récente des méthodes
    - FONCTIONNEMENT DÉTAILLÉ :
      * **ANALYSE CONFIANCE :** Évalue niveau de confiance actuel
      * **PERFORMANCE RÉCENTE :** Analyse tendances de performance
      * **CALCUL RATIO :** Détermine ratio optimal d'attente
      * **AJUSTEMENT DYNAMIQUE :** Adapte selon conditions changeantes
      * **VALIDATION BORNES :** Assure ratio dans limites acceptables
    - RETOUR : float - Ratio d'attente optimal entre 0 et 1
    - UTILITÉ : Optimise stratégie d'attente selon conditions actuelles

================================================================================
SECTION 2 : EVALUATION METRIQUES
================================================================================

1. update_statistics.txt (HybridBaccaratPredictor.update_statistics - Mise à jour statistiques interface)
   - Lignes 11533-11664 dans hbp.py (132 lignes)
   - FONCTION : Met à jour les labels de statistiques dans l'interface utilisateur avec métriques complètes et adaptation aux manches cibles
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise sequence_lock et model_lock pour accès sécurisé aux données
     * **CALCUL SÉRIE :** Détermine la série actuelle (Player/Banker) et sa longueur
     * **PRÉCISION SESSION :** Calcule la précision basée sur les recommandations non-wait vs résultats réels
     * **DÉTECTION PLAGE CIBLE :** Vérifie si la manche actuelle est dans la plage 31-60 pour adaptation affichage
     * **MÉTRIQUES MÉTHODES :** Calcule précision et confiance pour chaque méthode (LGBM, LSTM, etc.)
     * **STATISTIQUES PARTIE :** Affiche répartition Player/Banker avec pourcentages
     * **INCERTITUDE DÉTAILLÉE :** Présente épistémique, aléatoire et sensibilité contextuelle
     * **SEUIL ADAPTATIF :** Affiche le seuil de confiance adaptatif actuel
     * **POIDS BAYÉSIENS :** Montre la pondération bayésienne des différentes méthodes
   - RETOUR : None - Méthode de mise à jour d'interface ne retourne rien
   - UTILITÉ : Fournit un tableau de bord complet des performances en temps réel avec adaptation contextuelle

2. _create_metrics_dashboard.txt (HybridBaccaratPredictor._create_metrics_dashboard - Création tableau de bord métriques)
   - Lignes 6955-7003 dans hbp.py (49 lignes)
   - FONCTION : Crée tableau de bord interactif pour visualisation métriques détaillées avec interface à onglets
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec warning et retour si interface indisponible
     * **CRÉATION FENÊTRE :** Instancie `metrics_window = tk.Toplevel(self.root)` avec configuration :
       - `metrics_window.title("Tableau de Bord des Métriques d'Entraînement")`
       - `metrics_window.geometry("800x600")` pour taille initiale
       - `metrics_window.minsize(600, 400)` pour taille minimale
     * **CRÉATION NOTEBOOK :** Instancie `notebook = ttk.Notebook(metrics_window)` avec `notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)`
     * **ONGLET LGBM :** Crée `lgbm_frame = ttk.Frame(notebook)` puis `notebook.add(lgbm_frame, text="LGBM")` pour métriques LGBM
     * **ONGLET LSTM :** Crée `lstm_frame = ttk.Frame(notebook)` puis `notebook.add(lstm_frame, text="LSTM")` pour métriques LSTM
     * **ONGLET COMBINÉ :** Crée `combined_frame = ttk.Frame(notebook)` puis `notebook.add(combined_frame, text="Combiné")` pour métriques hybrides
     * **ONGLET GRAPHIQUES :** Crée `plots_frame = ttk.Frame(notebook)` puis `notebook.add(plots_frame, text="Graphiques")` pour visualisations
     * **REMPLISSAGE ONGLETS :** Appelle méthodes spécialisées :
       - `self._fill_lgbm_metrics_tab(lgbm_frame)` pour métriques LGBM détaillées
       - `self._fill_lstm_metrics_tab(lstm_frame)` pour métriques LSTM détaillées
       - `self._fill_combined_metrics_tab(combined_frame)` pour métriques combinées
       - `self._fill_plots_tab(plots_frame)` pour graphiques et visualisations
     * **BOUTON RAFRAÎCHISSEMENT :** Crée `refresh_button = ttk.Button(metrics_window, text="Rafraîchir les Métriques", command=lambda: self._refresh_metrics_dashboard(lgbm_frame, lstm_frame, combined_frame, plots_frame))` avec `refresh_button.pack(pady=10)`
   - RETOUR : None - Affiche fenêtre modale avec tableau de bord
   - UTILITÉ : Interface complète pour analyse performance avec onglets spécialisés, métriques détaillées et rafraîchissement temps réel

3. _refresh_metrics_dashboard.txt (HybridBaccaratPredictor._refresh_metrics_dashboard - Rafraîchissement tableau de bord)
   - Lignes 8107-8111 dans hbp.py (5 lignes)
   - FONCTION : Rafraîchit toutes les métriques dans le tableau de bord en appelant les méthodes de mise à jour spécialisées
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * lgbm_frame - Frame Tkinter pour métriques LGBM
     * lstm_frame - Frame Tkinter pour métriques LSTM
     * combined_frame - Frame Tkinter pour métriques combinées
     * plots_frame - Frame Tkinter pour graphiques
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MISE À JOUR LGBM :** Appelle `self._update_lgbm_metrics()` pour rafraîchir métriques LGBM (précision, rappel, F1, AUC-ROC, matrice confusion, importance features)
     * **MISE À JOUR LSTM :** Appelle `self._update_lstm_metrics()` pour rafraîchir métriques LSTM (loss, accuracy, courbes d'apprentissage)
     * **MISE À JOUR COMBINÉES :** Appelle `self._update_combined_metrics()` pour rafraîchir métriques hybrides (incertitudes, performance globale, poids bayésiens)
     * **SYNCHRONISATION INTERFACE :** Assure cohérence entre toutes les vues du tableau de bord
   - RETOUR : None - Met à jour directement l'interface utilisateur
   - UTILITÉ : Synchronisation complète du tableau de bord avec données actuelles pour monitoring temps réel

4. _fill_combined_metrics_tab.txt (HybridBaccaratPredictor._fill_combined_metrics_tab - Remplissage onglet métriques combinées)
   - Lignes 7422-7471 dans hbp.py (50 lignes)
   - FONCTION : Remplit onglet avec métriques combinées incluant incertitudes, poids modèles et performance globale
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * parent_frame - Frame Tkinter parent pour l'onglet
   - FONCTIONNEMENT DÉTAILLÉ :
     * **SECTION INCERTITUDES :** Crée `uncertainty_frame = ttk.LabelFrame(parent_frame, text="Métriques d'Incertitude")` avec variables :
       - `'epistemic_uncertainty': tk.StringVar(value="Incertitude Épistémique: N/A")` pour désaccord entre modèles
       - `'aleatoric_uncertainty': tk.StringVar(value="Incertitude Aléatoire: N/A")` pour incertitude intrinsèque données
       - `'context_sensitivity': tk.StringVar(value="Sensibilité Contextuelle: N/A")` pour sensibilité au contexte
       - `'total_uncertainty': tk.StringVar(value="Incertitude Totale: N/A")` pour incertitude combinée
     * **DISPOSITION INCERTITUDES :** Place labels avec `ttk.Label(uncertainty_frame, textvariable=var).grid(row=i//2, column=i%2, padx=10, pady=5, sticky=tk.W)` en grille 2x2
     * **SECTION POIDS MODÈLES :** Crée `weights_frame = ttk.LabelFrame(parent_frame, text="Poids des Modèles")` avec variables :
       - `'markov_weight': tk.StringVar(value="Poids Markov: N/A")` pour pondération Markov
       - `'lgbm_weight': tk.StringVar(value="Poids LGBM: N/A")` pour pondération LGBM
       - `'lstm_weight': tk.StringVar(value="Poids LSTM: N/A")` pour pondération LSTM
     * **DISPOSITION POIDS :** Place labels avec `ttk.Label(weights_frame, textvariable=var).grid(row=0, column=i, padx=10, pady=5, sticky=tk.W)` en ligne horizontale
     * **SECTION PERFORMANCE :** Crée `performance_frame = ttk.LabelFrame(parent_frame, text="Performance Combinée")` avec variables :
       - `'combined_accuracy': tk.StringVar(value="Exactitude Combinée: N/A")` pour précision globale
       - `'recommendation_rate': tk.StringVar(value="Taux de Recommandation: N/A")` pour fréquence recommandations
       - `'average_confidence': tk.StringVar(value="Confiance Moyenne: N/A")` pour confiance moyenne
     * **DISPOSITION PERFORMANCE :** Place labels avec même système de grille pour affichage structuré
   - RETOUR : None - Configure directement l'interface utilisateur
   - UTILITÉ : Vue unifiée performance ensemble des modèles avec métriques d'incertitude, pondération et performance globale

5. _fill_lgbm_metrics_tab.txt (HybridBaccaratPredictor._fill_lgbm_metrics_tab - Remplissage onglet métriques LGBM)
   - Lignes 7005-7059 dans hbp.py (55 lignes)
   - FONCTION : Remplit onglet spécialisé pour métriques LGBM avec métriques base, matrice confusion et importance features
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * parent_frame - Frame Tkinter parent pour l'onglet
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTRIQUES BASE :** Crée `basic_metrics_frame = ttk.LabelFrame(parent_frame, text="Métriques de Base")` avec variables :
       - `'accuracy': tk.StringVar(value="Exactitude: N/A")` pour précision globale
       - `'precision': tk.StringVar(value="Précision: N/A")` pour précision classe positive
       - `'recall': tk.StringVar(value="Rappel: N/A")` pour sensibilité
       - `'f1': tk.StringVar(value="F1-Score: N/A")` pour moyenne harmonique
       - `'auc_roc': tk.StringVar(value="AUC-ROC: N/A")` pour aire sous courbe ROC
     * **DISPOSITION MÉTRIQUES :** Place labels avec `ttk.Label(basic_metrics_frame, textvariable=var).grid(row=i//2, column=i%2, padx=10, pady=5, sticky=tk.W)` en grille 2x3
     * **MATRICE CONFUSION :** Crée `confusion_matrix_frame = ttk.LabelFrame(parent_frame, text="Matrice de Confusion")` avec variables 2x2 :
       - `self.lgbm_cm_vars = [[tk.StringVar(value="0") for _ in range(2)] for _ in range(2)]` pour cellules matrice
       - Place avec `ttk.Label(confusion_matrix_frame, textvariable=self.lgbm_cm_vars[i][j]).grid(row=i+1, column=j+1, padx=5, pady=5)`
     * **LABELS MATRICE :** Ajoute `ttk.Label(confusion_matrix_frame, text="Prédiction").grid(row=0, column=1, columnspan=2)` et labels axes
     * **IMPORTANCE FEATURES :** Crée `feature_importance_frame = ttk.LabelFrame(parent_frame, text="Importance des Caractéristiques")` avec Treeview :
       - `self.lgbm_feature_tree = ttk.Treeview(feature_importance_frame, columns=('Feature', 'Importance'), show='headings', height=8)`
       - Configure colonnes avec `self.lgbm_feature_tree.heading('Feature', text='Caractéristique')` et `self.lgbm_feature_tree.heading('Importance', text='Importance')`
       - Ajoute scrollbar avec `scrollbar = ttk.Scrollbar(feature_importance_frame, orient=tk.VERTICAL, command=self.lgbm_feature_tree.yview)`
   - RETOUR : None - Configure directement l'interface utilisateur
   - UTILITÉ : Interface complète métriques LGBM avec visualisation matrice confusion et classement importance features

6. _fill_lstm_metrics_tab.txt (HybridBaccaratPredictor._fill_lstm_metrics_tab - Remplissage onglet métriques LSTM)
   - Lignes 7097-7151 dans hbp.py (55 lignes)
   - FONCTION : Remplit onglet spécialisé pour métriques LSTM avec métriques d'entraînement, validation et objectifs spécifiques
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * parent_frame - Frame Tkinter parent pour l'onglet
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTRIQUES BASE :** Crée `basic_metrics_frame = ttk.LabelFrame(parent_frame, text="Métriques de Base")` avec variables complètes :
       - `'train_loss': tk.StringVar(value="Perte d'entraînement: N/A")` pour loss entraînement
       - `'val_loss': tk.StringVar(value="Perte de validation: N/A")` pour loss validation
       - `'train_accuracy': tk.StringVar(value="Exactitude d'entraînement: N/A")` pour accuracy entraînement
       - `'val_accuracy': tk.StringVar(value="Exactitude de validation: N/A")` pour accuracy validation
       - `'precision': tk.StringVar(value="Précision: N/A")`, `'recall': tk.StringVar(value="Rappel: N/A")`, `'f1': tk.StringVar(value="F1-Score: N/A")` pour métriques classification
       - `'objective1': tk.StringVar(value="Obj1 (Consécutives 31-60): N/A")` pour objectif consécutives manches cibles
       - `'objective2': tk.StringVar(value="Obj2 (Précision 31-60): N/A")` pour objectif précision manches cibles
     * **DISPOSITION MÉTRIQUES :** Place labels avec `ttk.Label(basic_metrics_frame, textvariable=var).grid(row=i//3, column=i%3, padx=10, pady=5, sticky=tk.W)` en grille 3x3
     * **MATRICE CONFUSION :** Crée `confusion_frame = ttk.LabelFrame(parent_frame, text="Matrice de Confusion")` avec grille 2x2 :
       - `self.lstm_cm_vars = []` puis boucles `for i in range(2): for j in range(2):` pour créer `tk.StringVar(value="N/A")`
       - Place avec `ttk.Label(confusion_frame, textvariable=var, width=10, anchor='center', borderwidth=1, relief="solid").grid(row=i+1, column=j+1, padx=5, pady=5)`
     * **LABELS AXES :** Ajoute `ttk.Label(confusion_frame, text="Réel", font=('Arial', 10, 'bold')).grid(row=0, column=0)` et labels pour axes prédiction/réel
     * **CANVAS COURBES :** Crée `curves_frame = ttk.LabelFrame(parent_frame, text="Courbes d'Apprentissage")` avec `self.lstm_curves_canvas = tk.Canvas(curves_frame, width=600, height=400, bg='white')` pour visualisation courbes
   - RETOUR : None - Configure directement l'interface utilisateur
   - UTILITÉ : Interface complète métriques LSTM avec entraînement/validation, objectifs spécifiques manches 31-60 et visualisation courbes

7. _fill_plots_tab.txt (HybridBaccaratPredictor._fill_plots_tab - Remplissage onglet graphiques)
   - Lignes 7526-7547 dans hbp.py (22 lignes)
   - FONCTION : Remplit onglet avec boutons pour affichage graphiques et visualisations métriques avec zone d'affichage
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * parent_frame - Frame Tkinter parent pour l'onglet
   - FONCTIONNEMENT DÉTAILLÉ :
     * **FRAME BOUTONS :** Crée `buttons_frame = ttk.Frame(parent_frame)` avec `buttons_frame.pack(fill=tk.X, padx=10, pady=5)` pour barre boutons
     * **BOUTON COURBES LGBM :** Crée `ttk.Button(buttons_frame, text="Courbes d'Apprentissage LGBM", command=self._show_lgbm_learning_curves).pack(side=tk.LEFT, padx=5)` pour affichage courbes LGBM
     * **BOUTON COURBES LSTM :** Crée `ttk.Button(buttons_frame, text="Courbes d'Apprentissage LSTM", command=self._show_lstm_learning_curves).pack(side=tk.LEFT, padx=5)` pour affichage courbes LSTM
     * **BOUTON MATRICES CONFUSION :** Crée `ttk.Button(buttons_frame, text="Matrices de Confusion", command=self._show_confusion_matrices).pack(side=tk.LEFT, padx=5)` pour affichage matrices
     * **BOUTON IMPORTANCE FEATURES :** Crée `ttk.Button(buttons_frame, text="Importance des Features", command=self._show_feature_importance).pack(side=tk.LEFT, padx=5)` pour importance LGBM
     * **ZONE AFFICHAGE :** Crée `self.plot_display_frame = ttk.Frame(parent_frame)` avec `self.plot_display_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)` pour contenu graphiques
     * **LABEL INITIAL :** Ajoute `ttk.Label(self.plot_display_frame, text="Sélectionnez un type de graphique à afficher").pack(pady=50)` comme placeholder
   - RETOUR : None - Configure directement l'interface utilisateur
   - UTILITÉ : Interface navigation graphiques avec boutons spécialisés et zone affichage dynamique pour visualisations métriques

8. _update_combined_metrics.txt (HybridBaccaratPredictor._update_combined_metrics - MAJ métriques combinées)
   - Lignes 7097-7151 dans hbp.py (55 lignes)
   - FONCTION : Met à jour métriques combinées de tous les modèles avec calcul performance globale et incertitudes
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION VARIABLES :** Vérifie `if not hasattr(self, 'combined_metric_vars'): return` pour éviter erreurs si interface non initialisée
     * **MÉTRIQUES INCERTITUDE :** Si `hasattr(self, 'uncertainty_metrics') and self.uncertainty_metrics:` :
       - Met à jour `self.combined_metric_vars['epistemic_uncertainty'].set(f"Incertitude Épistémique: {metrics['epistemic_uncertainty']:.4f}")` pour incertitude modèle
       - Met à jour `self.combined_metric_vars['aleatoric_uncertainty'].set(f"Incertitude Aléatoire: {metrics['aleatoric_uncertainty']:.4f}")` pour incertitude données
       - Met à jour `self.combined_metric_vars['context_sensitivity'].set(f"Sensibilité Contextuelle: {metrics['context_sensitivity']:.4f}")` pour adaptation contexte
       - Met à jour `self.combined_metric_vars['total_uncertainty'].set(f"Incertitude Totale: {metrics['total_uncertainty']:.4f}")` pour incertitude globale
     * **POIDS MODÈLES :** Met à jour affichage poids avec `self.config.weight_markov`, `self.config.weight_lgbm`, `self.config.weight_lstm` formatés en pourcentages
     * **CALCUL PERFORMANCE COMBINÉE :** Si `hasattr(self, 'prediction_history') and self.prediction_history:` :
       - Initialise compteurs `correct_predictions = 0`, `total_predictions = len(self.prediction_history)`, `recommendations = 0`, `total_confidence = 0.0`
       - Parcourt `for pred in self.prediction_history:` pour analyser chaque prédiction :
         - Incrémente `correct_predictions` si `pred['actual_outcome'] == pred['combined_prediction']` pour exactitude
         - Incrémente `recommendations` si `pred['recommendation'] is not None` pour taux recommandation
         - Ajoute `total_confidence += pred['combined_confidence']` pour confiance moyenne
     * **MÉTRIQUES FINALES :** Calcule et affiche :
       - `accuracy = correct_predictions / total_predictions` pour exactitude combinée globale
       - `recommendation_rate = recommendations / len(self.prediction_history)` pour pourcentage recommandations émises
       - `avg_confidence = total_confidence / len(self.prediction_history)` pour confiance moyenne système
     * **MISE À JOUR AFFICHAGE :** Met à jour variables Tkinter avec formatage approprié pour chaque métrique calculée
   - RETOUR : None - Met à jour directement les variables d'affichage Tkinter
   - UTILITÉ : Affichage synthétique performance globale système avec métriques d'incertitude et statistiques combinées pour évaluation holistique

9. _update_lgbm_metrics.txt (HybridBaccaratPredictor._update_lgbm_metrics - MAJ métriques LGBM)
   - Lignes 7061-7095 dans hbp.py (35 lignes)
   - FONCTION : Met à jour les métriques LGBM dans le tableau de bord interface avec affichage formaté et importance features
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MÉTRIQUES :** Vérifie `if not hasattr(self, 'lgbm_metrics') or not self.lgbm_metrics: return` pour éviter erreurs si métriques non disponibles
     * **MISE À JOUR MÉTRIQUES BASE :** Met à jour variables Tkinter pour chaque métrique disponible avec formatage précis :
       - `self.lgbm_metric_vars['precision'].set(f"Précision: {self.lgbm_metrics['precision']:.4f}")` si 'precision' dans métriques
       - `self.lgbm_metric_vars['recall'].set(f"Rappel: {self.lgbm_metrics['recall']:.4f}")` si 'recall' disponible
       - `self.lgbm_metric_vars['f1'].set(f"F1-Score: {self.lgbm_metrics['f1']:.4f}")` si 'f1' disponible
       - `self.lgbm_metric_vars['auc_roc'].set(f"AUC-ROC: {self.lgbm_metrics['auc_roc']:.4f}")` si 'auc_roc' disponible
       - `self.lgbm_metric_vars['accuracy'].set(f"Exactitude: {self.lgbm_metrics['accuracy']:.4f}")` si 'accuracy' disponible
     * **MISE À JOUR IMPORTANCE FEATURES :** Si `'feature_importance' in self.lgbm_metrics and hasattr(self, 'feature_names'):` :
       - Efface anciennes données avec `for item in self.lgbm_feature_tree.get_children(): self.lgbm_feature_tree.delete(item)`
       - Récupère `feature_importance = self.lgbm_metrics['feature_importance']` pour valeurs importance
       - Valide cohérence avec `if len(feature_importance) > 0 and len(self.feature_names) == len(feature_importance):`
       - Trie features par importance avec `sorted_features = sorted(zip(self.feature_names, feature_importance), key=lambda x: x[1], reverse=True)`
       - Insère dans Treeview avec `self.lgbm_feature_tree.insert('', 'end', values=(feature, f"{importance:.6f}"))` pour chaque feature
     * **PROTECTION ERREURS :** Utilise conditions pour éviter erreurs si variables UI non initialisées ou métriques manquantes
     * **FORMATAGE PRÉCISION :** Utilise formatage `.4f` pour métriques et `.6f` pour importance features pour lisibilité optimale
   - RETOUR : None - Méthode de mise à jour interface ne retourne rien
   - UTILITÉ : Synchronisation complète métriques LGBM avec interface utilisateur incluant importance features triées

10. _update_lstm_metrics.txt (HybridBaccaratPredictor._update_lstm_metrics - MAJ métriques LSTM)
    - Lignes 7153-7193 dans hbp.py (41 lignes)
    - FONCTION : Met à jour métriques LSTM dans tableau de bord avec loss, accuracy, métriques classification et matrice confusion
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION MÉTRIQUES :** Vérifie `if not hasattr(self, 'lstm_metrics') or not self.lstm_metrics: return` pour éviter erreurs
      * **MÉTRIQUES CLASSIFICATION :** Met à jour variables Tkinter pour métriques disponibles :
        - `self.lstm_metric_vars['precision'].set(f"Précision: {self.lstm_metrics['precision']:.4f}")` si 'precision' dans métriques
        - `self.lstm_metric_vars['recall'].set(f"Rappel: {self.lstm_metrics['recall']:.4f}")` si 'recall' dans métriques
        - `self.lstm_metric_vars['f1'].set(f"F1-Score: {self.lstm_metrics['f1']:.4f}")` si 'f1' dans métriques
      * **OBJECTIFS SPÉCIFIQUES :** Met à jour objectifs manches cibles 31-60 :
        - `self.lstm_metric_vars['objective1'].set(f"Obj1 (Consécutives 31-60): {self.lstm_metrics['objective1']:.4f}")` si disponible
        - `self.lstm_metric_vars['objective2'].set(f"Obj2 (Précision 31-60): {self.lstm_metrics['objective2']:.4f}")` si disponible
      * **LOSS ET ACCURACY :** Met à jour métriques d'entraînement :
        - Si `'train_losses' in self.lstm_metrics and self.lstm_metrics['train_losses']:`, récupère `last_train_loss = self.lstm_metrics['train_losses'][-1]` et met à jour
        - Si `'train_accuracies' in self.lstm_metrics and self.lstm_metrics['train_accuracies']:`, récupère `last_train_acc = self.lstm_metrics['train_accuracies'][-1]` et met à jour
      * **MATRICE CONFUSION :** Si `'confusion_matrix' in self.lstm_metrics:`, itère `for i in range(2): for j in range(2):` et met à jour `self.lstm_cm_vars[i][j].set(str(cm[i, j]))`
    - RETOUR : None - Met à jour directement les variables d'affichage Tkinter
    - UTILITÉ : Affichage temps réel performance LSTM avec loss/accuracy, métriques classification et objectifs spécifiques manches cibles

10.5. _update_combined_metrics.txt (HybridBaccaratPredictor._update_combined_metrics - MAJ métriques combinées)
    - Lignes 7473-7524 dans hbp.py (52 lignes)
    - FONCTION : Met à jour les métriques combinées dans le tableau de bord avec calculs de performance globale
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **MISE À JOUR INCERTITUDES :** Si `hasattr(self, 'prediction_history') and self.prediction_history:`, récupère `last_prediction = self.prediction_history[-1]`
      * **MÉTRIQUES CONFIANCE :** Extrait `metrics = last_prediction['confidence_metrics']` et met à jour variables Tkinter :
        - `self.combined_metric_vars['epistemic_uncertainty'].set(f"Incertitude Épistémique: {metrics['epistemic_uncertainty']:.4f}")`
        - `self.combined_metric_vars['aleatoric_uncertainty'].set(f"Incertitude Aléatoire: {metrics['aleatoric_uncertainty']:.4f}")`
        - `self.combined_metric_vars['context_sensitivity'].set(f"Sensibilité Contextuelle: {metrics['context_sensitivity']:.4f}")`
        - `self.combined_metric_vars['total_uncertainty'].set(f"Incertitude Totale: {metrics['total_uncertainty']:.4f}")`
      * **POIDS MODÈLES :** Met à jour affichage poids avec `self.config.weight_markov`, `self.config.weight_lgbm`, `self.config.weight_lstm`
      * **CALCUL PERFORMANCE COMBINÉE :** Parcourt `self.prediction_history` pour calculer :
        - `correct_predictions` et `total_predictions` avec comparaison `pred['actual_outcome'] == pred['combined_prediction']`
        - `recommendations` en comptant `pred['recommendation'] is not None`
        - `total_confidence` en sommant `pred['combined_confidence']`
      * **MÉTRIQUES FINALES :** Calcule et affiche :
        - `accuracy = correct_predictions / total_predictions` pour exactitude combinée
        - `recommendation_rate = recommendations / len(self.prediction_history)` pour taux de recommandation
        - `avg_confidence = total_confidence / len(self.prediction_history)` pour confiance moyenne
    - RETOUR : None - Met à jour directement les variables d'affichage Tkinter
    - UTILITÉ : Affichage synthétique performance globale système avec métriques d'incertitude et statistiques combinées

11. _draw_confusion_matrix.txt (HybridBaccaratPredictor._draw_confusion_matrix - Dessin matrice confusion)
    - Lignes 8031-8099 dans hbp.py (69 lignes)
    - FONCTION : Dessine matrice de confusion sur canvas Tkinter avec couleurs graduées et valeurs pour visualisation performance classification
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * parent_frame - Frame Tkinter parent pour intégration canvas
      * cm (numpy.ndarray) - Matrice de confusion 2x2 avec valeurs entières
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CRÉATION CANVAS :** Instancie `canvas = tk.Canvas(parent_frame, bg='white')` avec `canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)` pour intégration
      * **CALCUL DIMENSIONS :** Utilise `canvas.update()` puis `canvas_width = canvas.winfo_width()` et `canvas_height = canvas.winfo_height()` avec fallback 300x300
      * **CALCUL CELLULES :** Détermine `margin = 20`, `available_size = min(canvas_width, canvas_height) - 2 * margin`, `cell_size = available_size // 2` pour cellules carrées
      * **NORMALISATION COULEURS :** Calcule `max_val = max(cm[0, 0], cm[0, 1], cm[1, 0], cm[1, 1])` pour intensité maximale
      * **DESSIN CELLULES :** Double boucle `for i in range(2): for j in range(2):` pour chaque cellule matrice :
        - Calcule position `x1 = margin + j * cell_size`, `y1 = margin + i * cell_size`, `x2 = x1 + cell_size`, `y2 = y1 + cell_size`
        - Détermine intensité `intensity = cm[i, j] / max_val if max_val > 0 else 0` pour gradient couleur
        - Appelle `color = self._get_color_for_intensity(intensity)` pour couleur appropriée
        - Dessine rectangle `canvas.create_rectangle(x1, y1, x2, y2, fill=color, outline="black")` avec bordure
      * **AJOUT VALEURS :** Pour chaque cellule, calcule centre `(x1 + x2) / 2`, `(y1 + y2) / 2` puis :
        - Ajoute texte `canvas.create_text(center_x, center_y, text=str(cm[i, j]), fill="white" if intensity > 0.5 else "black", font=("Arial", 12, "bold"))` avec contraste adaptatif
      * **LABELS AXES :** Ajoute labels classes avec `canvas.create_text()` pour "Banker" et "Player" sur axes appropriés
      * **MÉTHODE COULEUR :** `_get_color_for_intensity()` retourne couleur hexadécimale basée sur intensité (bleu clair à bleu foncé)
    - RETOUR : None - Dessine directement sur canvas fourni
    - UTILITÉ : Visualisation intuitive performance classification avec couleurs graduées et valeurs numériques pour analyse rapide confusion

12. _draw_curve.txt (HybridBaccaratPredictor._draw_curve - Dessin courbes performance)
    - Lignes 7405-7420 dans hbp.py (16 lignes)
    - FONCTION : Dessine une courbe de données sur canvas Tkinter avec normalisation automatique et lissage
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * canvas - Canvas Tkinter où dessiner la courbe
      * data (List[float]) - Données à tracer (valeurs Y)
      * min_val (float) - Valeur minimale pour normalisation Y
      * max_val (float) - Valeur maximale pour normalisation Y
      * x_offset (int) - Décalage horizontal de départ
      * y_offset (int) - Décalage vertical de départ
      * width (int) - Largeur de la zone de tracé
      * height (int) - Hauteur de la zone de tracé
      * color (str, optionnel) - Couleur de la courbe (défaut: "blue")
      * label (str, optionnel) - Label pour légende (défaut: "")
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION DONNÉES :** Vérifie `if not data: return` pour éviter tracé vide
      * **CALCUL POINTS :** Itère sur données avec `for i, val in enumerate(data):`
      * **NORMALISATION X :** Calcule `x = x_offset + (i / (len(data) - 1 if len(data) > 1 else 1)) * width` pour répartition uniforme
      * **NORMALISATION Y :** Calcule `y = y_offset + height - ((val - min_val) / (max_val - min_val if max_val > min_val else 1)) * height` avec inversion axe Y
      * **CONSTRUCTION LISTE :** Ajoute `points.append(x)` puis `points.append(y)` pour format Tkinter
      * **TRACÉ COURBE :** Si `len(points) >= 4:`, appelle `canvas.create_line(points, fill=color, width=2, smooth=True)` avec lissage
    - RETOUR : None - Dessine directement sur le canvas
    - UTILITÉ : Visualisation courbes métriques d'entraînement avec normalisation automatique et rendu lisse

13. _draw_lstm_learning_curves.txt (HybridBaccaratPredictor._draw_lstm_learning_curves - Courbes apprentissage LSTM)
    - Lignes 7195-7403 dans hbp.py (209 lignes)
    - FONCTION : Dessine courbes d'apprentissage LSTM sur canvas avec gestion automatique des échelles et légendes
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION PRÉREQUIS :** Vérifie `if not hasattr(self, 'lstm_metrics') or not self.lstm_metrics or not hasattr(self, 'lstm_curves_canvas'): return`
      * **NETTOYAGE CANVAS :** Appelle `self.lstm_curves_canvas.delete("all")` pour effacer contenu précédent
      * **CALCUL DIMENSIONS :** Détermine `canvas_width = self.lstm_curves_canvas.winfo_width()`, `canvas_height = self.lstm_curves_canvas.winfo_height()` avec fallback 400x300
      * **DÉFINITION MARGES :** Configure `margin_left = 50`, `margin_top = 30`, `margin_right = 30`, `margin_bottom = 50` pour espacement
      * **CALCUL ZONE TRACÉ :** Calcule `plot_width = canvas_width - margin_left - margin_right`, `plot_height = canvas_height - margin_top - margin_bottom`
      * **VÉRIFICATION MÉTRIQUES :** Teste existence `has_train_loss = 'train_losses' in self.lstm_metrics`, `has_val_loss = 'val_losses' in self.lstm_metrics`, etc.
      * **TRACÉ COURBES PERTE :** Si données disponibles, calcule `min_loss = min(all_losses)`, `max_loss = max(all_losses)` avec marge 10%
      * **APPEL _draw_curve PERTE :** Utilise `self._draw_curve(self.lstm_curves_canvas, train_losses, min_loss, max_loss, margin_left, margin_top, plot_width, plot_height, color="blue")`
      * **TRACÉ COURBES EXACTITUDE :** Même processus pour accuracy avec `min_acc`, `max_acc` et position `margin_top + plot_height // 2`
      * **GÉNÉRATION LÉGENDE :** Crée légende avec `legend_x = margin_left + plot_width - 150`, `legend_y = margin_top + 10`
      * **ÉLÉMENTS LÉGENDE :** Pour chaque courbe, dessine ligne avec `canvas.create_line()` et texte avec `canvas.create_text()`
      * **AXES ET LABELS :** Dessine axes avec `canvas.create_line()` et labels avec `canvas.create_text()` pour graduation
    - RETOUR : None - Dessine directement sur le canvas LSTM
    - UTILITÉ : Visualisation complète convergence LSTM avec perte et exactitude, échelles automatiques et légendes

14. _show_confusion_matrices.txt (HybridBaccaratPredictor._show_confusion_matrices - Affichage matrices confusion)
    - Lignes 8006-8029 dans hbp.py (24 lignes)
    - FONCTION : Affiche matrices de confusion pour tous les modèles entraînés dans l'interface graphique
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **NETTOYAGE INTERFACE :** Itère `for widget in self.plot_display_frame.winfo_children():` puis `widget.destroy()` pour effacer contenu précédent
      * **VÉRIFICATION LGBM :** Teste `has_lgbm_cm = hasattr(self, 'lgbm_metrics') and self.lgbm_metrics and 'confusion_matrix' in self.lgbm_metrics`
      * **VÉRIFICATION LSTM :** Teste `has_lstm_cm = hasattr(self, 'lstm_metrics') and self.lstm_metrics and 'confusion_matrix' in self.lstm_metrics`
      * **VALIDATION DONNÉES :** Si `not (has_lgbm_cm or has_lstm_cm):`, affiche `ttk.Label(self.plot_display_frame, text="Aucune matrice de confusion disponible").pack(pady=50)` et retourne
      * **CRÉATION FRAME LGBM :** Si `has_lgbm_cm:`, crée `lgbm_frame = ttk.LabelFrame(self.plot_display_frame, text="Matrice de Confusion LGBM")` avec `pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=5)`
      * **AFFICHAGE LGBM :** Appelle `self._draw_confusion_matrix(lgbm_frame, self.lgbm_metrics['confusion_matrix'])` pour rendu visuel
      * **CRÉATION FRAME LSTM :** Si `has_lstm_cm:`, crée `lstm_frame = ttk.LabelFrame(self.plot_display_frame, text="Matrice de Confusion LSTM")` avec même configuration
      * **AFFICHAGE LSTM :** Appelle `self._draw_confusion_matrix(lstm_frame, self.lstm_metrics['confusion_matrix'])` pour rendu visuel
    - RETOUR : None - Affiche directement dans l'interface utilisateur
    - UTILITÉ : Comparaison visuelle côte-à-côte des performances de classification LGBM et LSTM avec matrices colorées

15. _show_feature_importance.txt (HybridBaccaratPredictor._show_feature_importance - Affichage importance features)
    - Lignes 7916-8004 dans hbp.py (89 lignes)
    - FONCTION : Affiche graphique barres horizontales importance features LGBM avec tri et limitation top 15
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **NETTOYAGE INTERFACE :** Itère `for widget in self.plot_display_frame.winfo_children():` puis `widget.destroy()` pour effacer contenu précédent
      * **VALIDATION DONNÉES :** Vérifie `if not hasattr(self, 'lgbm_metrics') or not self.lgbm_metrics or 'feature_importance' not in self.lgbm_metrics:` avec message d'erreur
      * **VALIDATION NOMS :** Contrôle `if not hasattr(self, 'feature_names') or not self.feature_names:` avec message "Noms des caractéristiques non disponibles"
      * **VALIDATION COHÉRENCE :** Teste `if len(feature_importance) == 0 or len(self.feature_names) != len(feature_importance):` avec message "Données d'importance des caractéristiques invalides"
      * **CRÉATION CANVAS :** Instancie `canvas = tk.Canvas(self.plot_display_frame, bg='white')` avec `canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)`
      * **CALCUL DIMENSIONS :** Détermine `canvas_width = canvas.winfo_width()`, `canvas_height = canvas.winfo_height()` avec fallback 600x400
      * **DÉFINITION MARGES :** Configure `margin_left = 150`, `margin_right = 20`, `margin_top = 20`, `margin_bottom = 20` pour espacement
      * **TRI FEATURES :** Utilise `sorted_features = sorted(zip(self.feature_names, feature_importance), key=lambda x: x[1], reverse=True)` pour classement décroissant
      * **LIMITATION TOP 15 :** Applique `top_n = min(15, len(sorted_features))` puis `sorted_features = sorted_features[:top_n]` pour lisibilité
      * **CALCUL BARRES :** Détermine `bar_height = plot_height / top_n`, `max_importance = max(importance for _, importance in sorted_features)` pour normalisation
      * **DESSIN BARRES :** Pour chaque feature, calcule coordonnées `x1 = margin_left`, `y1 = margin_top + i * bar_height`, `x2 = margin_left + (importance / max_importance) * plot_width`, `y2 = y1 + bar_height * 0.8`
      * **RENDU VISUEL :** Dessine avec `canvas.create_rectangle(x1, y1, x2, y2, fill="blue")` pour barre, `canvas.create_text()` pour nom feature (ancré à droite) et valeur importance (ancré à gauche)
      * **TITRE GRAPHIQUE :** Ajoute `canvas.create_text(canvas_width // 2, margin_top // 2, text="Importance des Caractéristiques LGBM", fill="black", font=("Arial", 12, "bold"))`
    - RETOUR : None - Affiche directement dans l'interface utilisateur
    - UTILITÉ : Visualisation graphique importance features LGBM avec barres horizontales, tri décroissant et limitation top 15 pour interprétabilité

16. _show_lgbm_learning_curves.txt (HybridBaccaratPredictor._show_lgbm_learning_curves - Courbes apprentissage LGBM)
    - Lignes 7549-7680 dans hbp.py (132 lignes)
    - FONCTION : Affiche courbes d'apprentissage LGBM avec canvas Tkinter et échelles automatiques pour métriques d'entraînement
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **NETTOYAGE INTERFACE :** Itère `for widget in self.plot_display_frame.winfo_children():` puis `widget.destroy()` pour effacer contenu précédent
      * **VALIDATION DONNÉES :** Vérifie `if not hasattr(self, 'lgbm_metrics') or not self.lgbm_metrics or 'eval_result' not in self.lgbm_metrics:` avec message "Aucune donnée de courbe d'apprentissage LGBM disponible"
      * **CRÉATION CANVAS :** Instancie `canvas = tk.Canvas(self.plot_display_frame, width=800, height=600, bg="white")` avec `canvas.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)`
      * **CONFIGURATION DIMENSIONS :** Définit `canvas_width = 800`, `canvas_height = 600`, `margin_left = 80`, `margin_right = 50`, `margin_top = 50`, `margin_bottom = 80`, `plot_width = canvas_width - margin_left - margin_right`, `plot_height = canvas_height - margin_top - margin_bottom`
      * **EXTRACTION DONNÉES :** Récupère `eval_result = self.lgbm_metrics['eval_result']` puis extrait métriques disponibles (train/valid loss, accuracy, etc.)
      * **AXES PRINCIPAUX :** Dessine axes avec `canvas.create_line(margin_left, margin_top, margin_left, canvas_height - margin_bottom, fill="black", width=2)` et `canvas.create_line(margin_left, canvas_height - margin_bottom, canvas_width - margin_right, canvas_height - margin_bottom, fill="black", width=2)`
      * **CALCUL ÉCHELLES :** Détermine `min_val = min(all_values)`, `max_val = max(all_values)` avec marge 10% pour normalisation
      * **TRACÉ COURBES :** Pour chaque métrique disponible, appelle `self._draw_curve()` avec couleurs spécifiques (bleu train, rouge validation)
      * **LÉGENDES :** Ajoute légendes avec `canvas.create_line()` et `canvas.create_text()` pour chaque courbe tracée
      * **ÉTIQUETTES AXES :** Ajoute "Itération" en bas et "Valeur" à gauche avec `canvas.create_text()`
      * **TITRE GRAPHIQUE :** Ajoute titre centré "Courbes d'Apprentissage LGBM" avec police bold
    - RETOUR : None - Affiche directement dans l'interface utilisateur
    - UTILITÉ : Visualisation convergence LGBM avec courbes train/validation, échelles automatiques et interface Tkinter native

17. _show_lstm_learning_curves.txt (HybridBaccaratPredictor._show_lstm_learning_curves - Affichage courbes LSTM)
    - Lignes 7682-7914 dans hbp.py (233 lignes)
    - FONCTION : Affiche courbes d'apprentissage LSTM dans interface graphique avec canvas Tkinter et échelles automatiques pour perte et exactitude
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **NETTOYAGE INTERFACE :** Itère `for widget in self.plot_display_frame.winfo_children():` puis `widget.destroy()` pour effacer contenu précédent
      * **VALIDATION DONNÉES :** Vérifie `if not hasattr(self, 'lstm_metrics') or not self.lstm_metrics:` avec affichage `ttk.Label(self.plot_display_frame, text="Aucune donnée de courbe d'apprentissage LSTM disponible").pack(pady=50)` et retour
      * **CRÉATION CANVAS :** Instancie `canvas = tk.Canvas(self.plot_display_frame, width=800, height=600, bg='white')` avec `canvas.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)`
      * **CONFIGURATION LAYOUT :** Définit `margin_left = 80`, `margin_top = 50`, `margin_right = 50`, `margin_bottom = 80` pour espacement et `plot_width = 800 - margin_left - margin_right`, `plot_height = 250` pour zone tracé
      * **VÉRIFICATION DONNÉES PERTE :** Teste `has_train_loss = 'train_losses' in self.lstm_metrics and self.lstm_metrics['train_losses']` et `has_val_loss = 'val_losses' in self.lstm_metrics and self.lstm_metrics['val_losses']`
      * **TRACÉ COURBES PERTE :** Si `has_train_loss or has_val_loss:` :
        - Calcule échelles avec `all_losses = []`, ajoute données disponibles et détermine `min_loss = min(all_losses)`, `max_loss = max(all_losses)`
        - Appelle `self._draw_curve(canvas, self.lstm_metrics['train_losses'], min_loss, max_loss, margin_left, margin_top, plot_width, plot_height, color="blue")` pour perte entraînement
        - Appelle `self._draw_curve(canvas, self.lstm_metrics['val_losses'], min_loss, max_loss, margin_left, margin_top, plot_width, plot_height, color="red")` pour perte validation si disponible
      * **VÉRIFICATION DONNÉES EXACTITUDE :** Teste `has_train_acc = 'train_accuracies' in self.lstm_metrics and self.lstm_metrics['train_accuracies']` et `has_val_acc = 'val_accuracies' in self.lstm_metrics and self.lstm_metrics['val_accuracies']`
      * **TRACÉ COURBES EXACTITUDE :** Si `has_train_acc or has_val_acc:` :
        - Même processus avec position décalée `margin_top + plot_height + 40` pour séparation visuelle
        - Utilise couleurs vert pour entraînement et violet pour validation
      * **GÉNÉRATION LÉGENDES :** Ajoute légendes avec `canvas.create_line()` et `canvas.create_text()` pour chaque courbe disponible avec couleurs correspondantes
      * **AXES ET LABELS :** Dessine axes avec `canvas.create_line()` et ajoute labels "Époque" en bas et "Valeur" à gauche avec rotation si supportée
      * **TITRE GRAPHIQUE :** Ajoute titre centré "Courbes d'Apprentissage LSTM" avec police bold
    - RETOUR : None - Affiche directement dans l'interface utilisateur
    - UTILITÉ : Visualisation complète convergence LSTM avec perte et exactitude, échelles automatiques, légendes et interface Tkinter native pour analyse performance

18. _save_lgbm_training_plots.txt (HybridBaccaratPredictor._save_lgbm_training_plots - Sauvegarde graphiques LGBM)
    - Lignes 6716-6845 dans hbp.py (130 lignes)
    - FONCTION : Crée et sauvegarde graphiques matplotlib pour métriques LGBM avec courbes, importance features, matrice confusion et ROC
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * eval_result (Dict) - Dictionnaire résultats évaluation LGBM avec métriques train/validation
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CONFIGURATION MATPLOTLIB :** Force `matplotlib.use('Agg', force=True)` pour backend non-interactif et éviter problèmes thread
      * **CRÉATION DOSSIER :** Utilise `plots_dir = os.path.join("logs", "training_plots")` puis `os.makedirs(plots_dir, exist_ok=True)` pour structure
      * **TIMESTAMP :** Génère `timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")` pour noms fichiers uniques
      * **EXTRACTION MÉTRIQUES :** Récupère `metrics = set()` puis itère `for split in ['train', 'val']:` et `for metric in eval_result[split].keys():` pour collecter métriques disponibles
      * **GRAPHIQUES MÉTRIQUES :** Pour chaque métrique, crée `plt.figure(figsize=(10, 5))` puis trace :
        - `plt.plot(eval_result['train'][metric], label=f'Train {metric}')` si disponible
        - `plt.plot(eval_result['val'][metric], label=f'Validation {metric}')` si disponible
        - Configure titre, axes, légende et grille puis sauvegarde avec `plt.savefig(metric_plot_path)` et `plt.close()`
      * **IMPORTANCE FEATURES :** Si `'feature_importance' in self.lgbm_metrics and hasattr(self, 'feature_names'):` :
        - Trie avec `indices = np.argsort(feature_importance)[::-1]` et limite `top_n = min(20, len(indices))`
        - Crée `plt.figure(figsize=(12, 8))` avec `plt.barh()` pour barres horizontales
        - Configure `plt.yticks(range(top_n), [self.feature_names[i] for i in indices[:top_n]])` pour labels
      * **MATRICE CONFUSION :** Si `'confusion_matrix' in self.lgbm_metrics:` :
        - Crée `plt.figure(figsize=(8, 6))` avec `plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)`
        - Ajoute valeurs cellules avec boucles `for i in range(cm.shape[0]): for j in range(cm.shape[1]):` et `plt.text()`
      * **COURBE ROC :** Si `'auc_roc' in self.lgbm_metrics:` :
        - Trace ligne diagonale `plt.plot([0, 1], [0, 1], 'k--')` et configure axes `plt.xlim([0.0, 1.0])`, `plt.ylim([0.0, 1.05])`
        - Ajoute titre avec AUC `plt.title(f'Courbe ROC (AUC = {auc_roc:.4f})')`
      * **GESTION ERREURS :** Capture exceptions avec logging détaillé pour chaque étape de création graphique
    - RETOUR : None - Sauvegarde fichiers PNG sur disque
    - UTILITÉ : Persistance complète visualisations LGBM avec matplotlib pour analyse post-entraînement et documentation

19. _save_lstm_training_plots.txt (HybridBaccaratPredictor._save_lstm_training_plots - Sauvegarde graphiques LSTM)
    - Lignes 6847-6953 dans hbp.py (107 lignes)
    - FONCTION : Crée et sauvegarde graphiques matplotlib pour métriques LSTM avec courbes loss/accuracy et matrice confusion
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CONFIGURATION MATPLOTLIB :** Force `matplotlib.use('Agg', force=True)` pour backend non-interactif et éviter problèmes thread
      * **VALIDATION MÉTRIQUES :** Vérifie `if not hasattr(self, 'lstm_metrics') or not isinstance(self.lstm_metrics, dict) or not self.lstm_metrics:` avec warning et retour
      * **CRÉATION DOSSIER :** Utilise `plots_dir = os.path.join("logs", "training_plots")` puis `os.makedirs(plots_dir, exist_ok=True)` pour structure
      * **TIMESTAMP :** Génère `timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")` pour noms fichiers uniques
      * **GRAPHIQUE PERTES :** Si `'train_losses' in self.lstm_metrics:` :
        - Crée `plt.figure(figsize=(10, 5))` avec `plt.plot(self.lstm_metrics['train_losses'], label='Train Loss')`
        - Ajoute validation si `'val_losses' in self.lstm_metrics:` avec `plt.plot(self.lstm_metrics['val_losses'], label='Validation Loss')`
        - Configure titre "Évolution de la perte pendant l'entraînement LSTM", axes "Époque"/"Perte", légende et grille
        - Sauvegarde avec `loss_plot_path = os.path.join(plots_dir, f"lstm_loss_plot_{timestamp}.png")` et `plt.savefig(loss_plot_path)`
      * **GRAPHIQUE EXACTITUDES :** Si `'train_accuracies' in self.lstm_metrics:` :
        - Crée `plt.figure(figsize=(10, 5))` avec `plt.plot(self.lstm_metrics['train_accuracies'], label='Train Accuracy')`
        - Ajoute validation si `'val_accuracies' in self.lstm_metrics:` avec `plt.plot(self.lstm_metrics['val_accuracies'], label='Validation Accuracy')`
        - Configure titre "Évolution de l'exactitude pendant l'entraînement LSTM", axes "Époque"/"Exactitude"
      * **MATRICE CONFUSION :** Si `'confusion_matrix' in self.lstm_metrics:` :
        - Crée `plt.figure(figsize=(8, 6))` avec `plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)`
        - Configure classes `['Banker', 'Player']` avec `plt.xticks()` et `plt.yticks()`
        - Ajoute valeurs cellules avec boucles `for i, j in range(cm.shape):` et `plt.text(j, i, format(cm[i, j], 'd'))`
      * **GESTION ERREURS :** Capture `except Exception as e:` avec `logger.error(f"Erreur lors de la création des graphiques LSTM: {e}", exc_info=True)`
    - RETOUR : None - Sauvegarde fichiers PNG sur disque
    - UTILITÉ : Persistance visualisations LSTM avec matplotlib pour analyse convergence et performance post-entraînement

================================================================================
SECTION 3 : GESTION DONNEES
================================================================================

1. _append_session_to_historical_txt.txt (HybridBaccaratPredictor._append_session_to_historical_txt - Sauvegarde session historique)
   - Lignes 12746-12806 dans hbp.py (61 lignes)
   - FONCTION : Ajoute la séquence de session actuelle au fichier historique en format 0/1 avec gestion intelligente des sauts de ligne et validation format
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * filepath (str, optionnel) - Chemin vers le fichier historical_data.txt (défaut: "historical_data.txt")
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise `with self.sequence_lock:` puis `session_to_save = self.sequence[:]` pour copie thread-safe
     * **VALIDATION SÉQUENCE :** Vérifie `if not session_to_save:` avec `logger.info("Séquence de session vide, rien à ajouter.")` et retour `True`
     * **CONVERSION FORMAT :** Transforme séquence avec `converted_sequence = []` puis boucle `for outcome in session_to_save:` :
       - `if outcome == 'player': converted_sequence.append('0')` pour Player → 0
       - `elif outcome == 'banker': converted_sequence.append('1')` pour Banker → 1
       - `else: logger.warning(f"Outcome invalide ignoré: {outcome}")` pour valeurs inconnues
     * **VALIDATION CONVERSION :** Teste `if not converted_sequence:` avec message "Aucun outcome valide trouvé" et retour `True`
     * **GESTION FICHIER :** Ouvre `with open(filepath, 'a+', encoding='utf-8') as f:` en mode append+read pour vérification
     * **VÉRIFICATION SAUT LIGNE :** Utilise `f.seek(0, 2)` pour fin fichier, `file_size = f.tell()` puis si `file_size > 0:` :
       - `f.seek(file_size - 1)` pour dernier caractère
       - `last_char = f.read(1)` et `if last_char != '\n': f.write('\n')` pour assurer saut ligne
     * **ÉCRITURE DONNÉES :** Écrit `line_to_write = ''.join(converted_sequence) + '\n'` puis `f.write(line_to_write)`
     * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Session ajoutée au fichier historique: {len(converted_sequence)} manches")`
     * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de l'ajout de la session: {e}", exc_info=True)` et retour `False`
   - RETOUR : bool - True si ajout réussi ou séquence vide, False en cas d'erreur
   - UTILITÉ : Persistance automatique sessions avec format standardisé 0/1, gestion robuste fichiers et validation complète données

2. load_historical_data.txt (HybridBaccaratPredictor.load_historical_data - Chargement données historiques)
   - Lignes 4831-4886 dans hbp.py (56 lignes)
   - FONCTION : Charge les données historiques depuis un fichier .txt avec interface utilisateur et validation complète
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT :** Contrôle qu'aucune tâche ML n'est en cours avant chargement
     * **SÉLECTION FICHIER :** Ouvre dialogue de sélection avec filtres pour fichiers .txt
     * **CHARGEMENT INTERNE :** Utilise _load_historical_txt pour traitement du fichier
     * **CALCUL STATISTIQUES :** Détermine nombre de parties, longueur moyenne, total coups
     * **AFFICHAGE RÉSULTATS :** Présente statistiques détaillées dans messagebox
     * **GESTION SESSION :** Propose réinitialisation de la session en cours si applicable
     * **MISE À JOUR MODÈLES :** Met à jour automatiquement les modèles Markov globaux
     * **GESTION ERREURS :** Affiche messages d'erreur spécifiques selon le type de problème
   - RETOUR : None - Méthode d'interface utilisateur ne retourne rien
   - UTILITÉ : Interface conviviale pour charger l'historique avec validation et feedback utilisateur complet

3. _create_lgbm_features.txt (HybridBaccaratPredictor._create_lgbm_features - Création features LGBM optimisées)
   - Lignes 13748-13801 dans hbp.py (54 lignes)
   - FONCTION : Crée un vecteur de features optimisé pour le modèle LGBM à partir d'une séquence de résultats avec assemblage structuré
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **COMPTEURS BASIQUES :** Calcule `banker_count = sum(1 for outcome in sequence if outcome == 'banker')` et `player_count = sum(1 for outcome in sequence if outcome == 'player')`
     * **CALCUL STREAKS :** Appelle `streak_counts = self._calculate_streaks(sequence)` pour obtenir dictionnaire complet statistiques séries
     * **CALCUL ALTERNANCES :** Appelle `alternate_info = self._calculate_alternates(sequence)` pour patterns d'alternance avec compteurs spécifiques
     * **FEATURES DECAY :** Calcule `banker_decay = self._calculate_decay_feature(sequence, 'banker')` et `player_decay = self._calculate_decay_feature(sequence, 'player')` avec pondération temporelle
     * **ASSEMBLAGE FEATURES BASIQUES :** Initialise `features = [banker_count, player_count, banker_count / max(1, len(sequence)), player_count / max(1, len(sequence)), streak_counts['banker_streaks'], streak_counts['player_streaks'], banker_decay, player_decay]`
     * **STREAKS SPÉCIFIQUES :** Boucle `for length in range(2, 8):` pour ajouter `features.append(streak_counts[f'banker_streak_{length}'])` et `features.append(streak_counts[f'player_streak_{length}'])` (12 features)
     * **INFOS ALTERNANCE :** Étend avec `features.extend([alternate_info['alternate_count_2'], alternate_info['alternate_count_3'], alternate_info['alternate_ratio'], streak_counts['max_banker_streak'], streak_counts['max_player_streak']])`
     * **OPTIMISATION OPTUNA :** Réduit de 28 à 25 features en supprimant 3 features spécifiques selon optimisation hyperparamètres
     * **VALIDATION FINALE :** Assure que `len(features) == 25` pour cohérence avec modèle entraîné
     * **ORDRE FEATURES :** Structure précise : [compteurs(2), ratios(2), streaks_totaux(2), decay(2), streaks_2-7(12), alternances(3), max_streaks(2)] = 25 total
   - RETOUR : List[float] - Vecteur de 25 features normalisées et structurées pour LGBM
   - UTILITÉ : Pipeline complet d'extraction features LGBM avec ordre standardisé et optimisation Optuna intégrée

4. handle_short_sequence.txt (HybridBaccaratPredictor.handle_short_sequence - Gestion séquences courtes LSTM)
   - Lignes 5107-5233 dans hbp.py (127 lignes)
   - FONCTION : Gère séquences trop courtes pour LSTM avec padding intelligent et génération features step-by-step
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (Optional[List[str]]) - Séquence résultats ('player'/'banker') potentiellement None ou courte
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONFIGURATION CIBLE :** Récupère `target_length = self.config.lstm_sequence_length` et `num_features = self.config.lstm_input_size`
     * **VALIDATION MODÈLE :** Vérifie cohérence `self.lstm.input_size != num_features` avec warning si incohérence
     * **CAS SÉQUENCE VIDE :** Si `not sequence`, retourne `np.zeros((target_length, num_features), dtype=np.float32)`
     * **CAS SÉQUENCE LONGUE :** Si `current_length >= target_length`, appelle `self.create_hybrid_features(sequence)` et retourne `lstm_features`
     * **CAS SÉQUENCE COURTE :** Calcule `num_padding = target_length - current_length` et crée `padding_array = np.zeros((num_padding, num_features))`
     * **GÉNÉRATION FEATURES STEP :** Pour chaque `idx in range(current_length)`, calcule 12 features :
       - `pos_norm = (num_padding + idx) / float(target_length)` (position globale normalisée)
       - `is_banker = 1.0 if outcome == 'banker' else 0.0` (encodage binaire)
       - `ratio_banker = sub_sequence.count('banker') / sub_len` (ratio cumulatif)
       - `ratio_player = 1.0 - ratio_banker` (ratio complémentaire)
       - `is_repeat = 1.0 if idx > 0 and sequence[idx] == sequence[idx-1] else 0.0` (détection répétition)
       - `recent_3_count_banker` (comptage banker sur 3 derniers coups)
       - `imbalance = ratio_banker - 0.5` (déséquilibre par rapport à 50/50)
       - `streak_length` (longueur streak actuel avec calcul lookback)
       - `seq_odd_even = (num_padding + idx) % 2` (position paire/impaire)
       - 3 features Optuna par défaut : `0.5, 0.5, 0.5` (confidence, error_pattern_threshold, transition_uncertainty_threshold)
     * **FEATURES CONFIGURABLES :** Utilise `base_features_count = getattr(self.config, 'lstm_base_features_count', 9)` pour limiter features
     * **PADDING FEATURES :** Ajoute `zeros_to_add = num_features - len(current_step_features)` pour compléter à `num_features`
     * **CONCATÉNATION :** Combine `np.concatenate((padding_array, actual_features_array), axis=0)` avec padding au début
     * **VALIDATION FINALE :** Vérifie `padded_sequence_features.shape == (target_length, num_features)` avant retour
   - RETOUR : np.ndarray - Array shape (lstm_sequence_length, lstm_input_size) avec padding zeros au début et features réelles à la fin
   - UTILITÉ : Permet utilisation LSTM même avec séquences insuffisantes via padding intelligent et génération features détaillées

5. create_lstm_sequence_features.txt (HybridBaccaratPredictor.create_lstm_sequence_features - Création features LSTM)
   - Lignes 13570-13745 dans hbp.py (176 lignes)
   - FONCTION : Crée une matrice de features pour le modèle LSTM à partir d'une séquence avec fenêtre adaptative et taille de sortie fixe
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
     * keep_history_length (int, optionnel) - Taille maximale de la matrice de sortie (défaut: config.lstm_sequence_length)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SÉQUENCE :** Vérifie `if not sequence: return None` pour éviter erreurs
     * **DÉTERMINATION LONGUEUR :** Utilise `keep_history_length = self.config.lstm_sequence_length` si None fourni
     * **FENÊTRE ADAPTATIVE :** Utilise `effective_sequence = sequence[:]` (toute la séquence disponible)
     * **INITIALISATION MATRICE :** Crée `sequence_features = np.zeros((keep_history_length, features_count), dtype=np.float32)`
     * **GESTION LONGUEUR :** Si `seq_length <= keep_history_length`, utilise toute séquence avec padding au début ; sinon prend les derniers éléments
     * **CALCUL INDICES :** Détermine `matrix_indices` et `indices_to_use` pour mapping séquence → matrice
     * **GÉNÉRATION FEATURES (12 features par position) :**
       - **Feature 1 :** `(seq_idx + 1) / seq_length` (position relative normalisée)
       - **Feature 2 :** `1 if outcome == 'banker' else 0` (encodage binaire banker/player)
       - **Features 3-4 :** `banker_count / total` et `player_count / total` (ratios cumulatifs)
       - **Feature 5 :** `1 if outcome == previous else 0` (détection répétition)
       - **Feature 6 :** Comptage banker sur 3 derniers coups
       - **Feature 7 :** `banker_count / total - 0.5` (déséquilibre par rapport à 50/50)
       - **Feature 8 :** `alternance_count / seq_idx` (fréquence alternances)
       - **Feature 9 :** Longueur streak actuel avec calcul lookback
       - **Feature 10 :** `(seq_idx + 1) % 2` (position paire/impaire)
       - **Feature 11 :** Proximité du dernier changement de streak normalisée
       - **Feature 12 :** Réservée pour extensions futures
     * **OPTIMISATION MÉMOIRE :** Utilise dtype=np.float32 pour réduire empreinte mémoire
   - RETOUR : Optional[np.ndarray] - Matrice (keep_history_length, lstm_input_size) ou None si erreur
   - UTILITÉ : Génère représentation séquentielle riche avec fenêtre adaptative pour architecture LSTM

6. create_hybrid_features.txt (HybridBaccaratPredictor.create_hybrid_features - Création features hybrides)
   - Lignes 13803-13857 dans hbp.py (55 lignes)
   - FONCTION : Fonction centralisée pour la création de features hybrides (LGBM et LSTM) avec fenêtre adaptative
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SÉQUENCE :** Vérifie `if not sequence or len(sequence) < 2:` avec warning et retour `(None, None)`
     * **INITIALISATION VARIABLES :** Définit `lgbm_features = None` et `lstm_features = None`
     * **CRÉATION FEATURES LGBM :** Appelle `lgbm_features = self._create_lgbm_features(sequence)` avec gestion exception
     * **CRÉATION FEATURES LSTM :**
       - Récupère `lstm_sequence_length = self.config.lstm_sequence_length`
       - Appelle `lstm_features = self.create_lstm_sequence_features(sequence, lstm_sequence_length)`
       - Gestion logging conditionnel pour éviter spam pendant optimisation Optuna
     * **GESTION ERREURS :** Capture exceptions séparément pour LGBM et LSTM avec `logger.error` et `exc_info=True`
     * **VALIDATION FINALE :** Vérifie `if lgbm_features is None and lstm_features is None:` avec warning
     * **FENÊTRE ADAPTATIVE :** Utilise toute la séquence disponible pour calculer probabilités manche N depuis N-1 manches précédentes
   - RETOUR : Tuple[Optional[List[float]], Optional[np.ndarray]] - (features LGBM, features LSTM) ou None pour type en erreur
   - UTILITÉ : Point d'entrée unifié pour génération de features multi-modèles avec gestion robuste des erreurs

7. _extract_lstm_features.txt (HybridBaccaratPredictor._extract_lstm_features - Extraction features LSTM)
   - Lignes 13994-14024 dans hbp.py (31 lignes)
   - FONCTION : Méthode interne wrapper pour extraire les features LSTM avec gestion d'erreurs robuste
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RÉCUPÉRATION CONFIG :** Utilise `lstm_sequence_length = self.config.lstm_sequence_length` pour longueur cible
     * **LOGGING DEBUG :** Enregistre `logger.info(f"_extract_lstm_features: Séquence de longueur {len(sequence)}, lstm_sequence_length={lstm_sequence_length}")`
     * **APPEL PRINCIPAL :** Exécute `features = self.create_lstm_sequence_features(sequence, lstm_sequence_length)` avec gestion exception
     * **VALIDATION RETOUR :** Vérifie `if features is None:` avec logging erreur et création fallback
     * **FALLBACK SÉCURISÉ :** Crée `features = np.zeros((lstm_sequence_length, self.config.lstm_input_size), dtype=np.float32)` si échec
     * **LOGGING SUCCÈS :** Enregistre `logger.info(f"_extract_lstm_features: Features créées avec succès, shape={features.shape}")` si réussite
     * **GESTION EXCEPTION :** Capture toutes exceptions avec `logger.error(f"_extract_lstm_features: Erreur lors de la création des features LSTM: {e}", exc_info=True)`
   - RETOUR : np.ndarray - Matrice features (lstm_sequence_length, lstm_input_size) ou matrice zéros si erreur
   - UTILITÉ : Wrapper sécurisé pour extraction features LSTM avec fallback robuste et logging détaillé

8. _extract_lgbm_features.txt (HybridBaccaratPredictor._extract_lgbm_features - Extraction features LGBM)
   - Lignes 13981-13992 dans hbp.py (12 lignes)
   - FONCTION : Méthode interne wrapper simple pour extraire les features LGBM
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **APPEL DIRECT :** Exécute simplement `return self._create_lgbm_features(sequence)` sans traitement supplémentaire
     * **WRAPPER SIMPLE :** Fournit interface cohérente avec _extract_lstm_features pour uniformité API
   - RETOUR : List[float] - Liste des 25 features LGBM normalisées
   - UTILITÉ : Wrapper simple pour extraction features LGBM avec interface uniforme

9. _calculate_sample_weights.txt (HybridBaccaratPredictor._calculate_sample_weights - Calcul poids échantillons)
   - Lignes 4305-4329 dans hbp.py (25 lignes)
   - FONCTION : Calcule poids d'échantillons avec facteur de décroissance temporelle pour privilégier données récentes avec validation robuste
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * list_of_all_origins (List[int]) - Liste des indices d'origine des échantillons dans données historiques
     * final_num_samples (int) - Nombre total d'échantillons finaux après préparation
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION LOGGER :** Récupère `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))` pour logging adaptatif
     * **INITIALISATION POIDS :** Crée `sample_weights_all = np.ones(final_num_samples, dtype=np.float64)` avec poids uniformes par défaut
     * **RÉCUPÉRATION CONFIG :** Utilise `decay_factor = getattr(self.config, 'sample_weight_decay_factor', 0.9995)` pour facteur décroissance (proche de 1)
     * **CALCUL INDEX MAX :** Détermine `max_origin_index_used = max(list_of_all_origins) if list_of_all_origins else 0` pour référence temporelle
     * **VALIDATION CONDITIONS :** Vérifie `if 0 < decay_factor < 1 and max_origin_index_used > 0 and final_num_samples > 0:` pour application decay
     * **CONSTANTE EPSILON :** Définit `epsilon_decay = 1e-8` pour éviter poids nuls et problèmes numériques
     * **CALCUL POIDS DÉCROISSANTS :** Pour chaque `i, sample_origin_index in enumerate(list_of_all_origins):` :
       - Calcule `time_lag = max(0, max_origin_index_used - sample_origin_index)` pour distance temporelle
       - Applique `weight_raw = decay_factor ** time_lag` pour pondération exponentielle décroissante
       - Sécurise avec `sample_weights_all[i] = max(epsilon_decay, weight_raw) if np.isfinite(weight_raw) else epsilon_decay`
     * **LOGGING SUCCÈS :** Enregistre `logger_instance.info(f"Poids temporels appliqués (decay={decay_factor:.4f}).")` si conditions remplies
     * **FALLBACK UNIFORME :** Si conditions non remplies, log `"Poids temporels non appliqués (decay <= 0, >= 1, ou données insuffisantes)."`
     * **PROTECTION NUMÉRIQUE :** Assure que tous poids sont finis et positifs avec epsilon comme minimum
   - RETOUR : np.ndarray - Array de poids avec dtype=np.float64, décroissance temporelle ou uniformes selon conditions
   - UTILITÉ : Pondération temporelle robuste pour privilégier données récentes dans entraînement avec protection numérique complète

10. _calculate_streaks.txt (HybridBaccaratPredictor._calculate_streaks - Calcul séries consécutives)
    - Lignes 13803-13869 dans hbp.py (67 lignes)
    - FONCTION : Analyse et calcule statistiques complètes des séries consécutives (streaks) pour une séquence
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * sequence (List[str]) - Séquence de résultats ('player', 'banker')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SÉQUENCE :** Retourne dictionnaire vide avec toutes clés à 0 si `not sequence`
      * **INITIALISATION COMPTEURS :** Crée `streak_counts` avec 16 clés : banker_streaks, player_streaks, banker_streak_2 à 7, player_streak_2 à 7, max_banker_streak, max_player_streak
      * **VARIABLES ÉTAT :** Initialise `current_type = None` et `current_length = 0` pour suivi streak actuel
      * **PARCOURS SÉQUENCE :** Pour chaque `outcome` dans séquence :
        - **VALIDATION OUTCOME :** Si `outcome not in ('banker', 'player')`, reset `current_type = None` et `current_length = 0`
        - **DÉTECTION CHANGEMENT :** Si `outcome != current_type`, traite streak précédent et démarre nouveau avec `current_type = outcome` et `current_length = 1`
        - **CONTINUATION STREAK :** Si même type, incrémente `current_length += 1`
      * **TRAITEMENT STREAK PRÉCÉDENT :** Quand changement détecté :
        - Incrémente `streak_counts[f'{current_type}_streaks'] += 1` pour comptage total
        - Met à jour `streak_counts[f'max_{current_type}_streak'] = max(streak_counts[f'max_{current_type}_streak'], current_length)` pour maximum
        - Si `2 <= current_length <= 7`, incrémente `streak_counts[f'{current_type}_streak_{current_length}'] += 1` pour comptage spécifique
      * **TRAITEMENT STREAK FINAL :** Après boucle, traite dernière streak en cours avec même logique
      * **CALCUL MOYENNES :** Calcule moyennes des streaks si compteurs > 0, sinon 0.0
      * **DÉTECTION STREAK ACTUELLE :** Analyse fin séquence pour déterminer `current_streak_length` et `current_streak_type`
    - RETOUR : Dict[str, Union[int, float]] - Dictionnaire avec 16 clés de statistiques streaks complètes
    - UTILITÉ : Analyse patterns de séries consécutives pour features ML avec comptage détaillé par longueur et type

11. _calculate_alternates.txt (HybridBaccaratPredictor._calculate_alternates - Calcul alternances)
    - Lignes 13871-13911 dans hbp.py (41 lignes)
    - FONCTION : Analyse et calcule statistiques des patterns d'alternance dans une séquence avec détection motifs spécifiques
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * sequence (List[str]) - Séquence de résultats ('player', 'banker')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SÉQUENCE :** Retourne `{'alternate_count_2': 0, 'alternate_count_3': 0, 'alternate_ratio': 0.5}` si `not sequence or len(sequence) < 2`
      * **INITIALISATION COMPTEURS :** Définit `alternates = 0` et `last_outcome = sequence[0]` pour suivi alternances basiques
      * **PARCOURS ALTERNANCES :** Pour chaque `outcome` dans `sequence[1:]` :
        - Teste `if outcome != last_outcome and outcome in ('banker', 'player'):` puis `alternates += 1` pour comptage
        - Met à jour `last_outcome = outcome if outcome in ('banker', 'player') else last_outcome` avec validation
      * **DÉFINITION MOTIFS :** Crée patterns spécifiques :
        - `alt_2_pattern = ['banker', 'player', 'banker', 'player']` pour alternance double
        - `alt_3_pattern = ['banker', 'player', 'banker', 'player', 'banker', 'player']` pour alternance triple
      * **INITIALISATION COMPTEURS MOTIFS :** Définit `alt_count_2 = 0` et `alt_count_3 = 0` pour patterns
      * **DÉTECTION MOTIF 2 :** Si `len(sequence) >= 4:`, parcourt `for i in range(len(sequence) - 3):` :
        - Extrait `window = sequence[i:i+4]` pour fenêtre glissante
        - Teste `if window == alt_2_pattern:` puis `alt_count_2 += 1` pour comptage motif
      * **DÉTECTION MOTIF 3 :** Si `len(sequence) >= 6:`, parcourt `for i in range(len(sequence) - 5):` :
        - Extrait `window = sequence[i:i+6]` pour fenêtre glissante
        - Teste `if window == alt_3_pattern:` puis `alt_count_3 += 1` pour comptage motif
      * **CALCUL RATIO :** Calcule `alt_ratio = alternates / (len(sequence) - 1) if len(sequence) > 1 else 0.5` pour ratio global
      * **CONSTRUCTION RETOUR :** Retourne dictionnaire `{'alternate_count_2': alt_count_2, 'alternate_count_3': alt_count_3, 'alternate_ratio': alt_ratio}`
    - RETOUR : Dict[str, float] - Dictionnaire avec compteurs motifs et ratio alternance global
    - UTILITÉ : Détection patterns d'alternance pour features LGBM avec analyse motifs spécifiques et ratio global

12. _load_historical_txt.txt (HybridBaccaratPredictor._load_historical_txt - Chargement historique TXT)
    - Lignes 11157-11238 dans hbp.py (82 lignes)
    - FONCTION : Charge et parse fichiers historiques au format texte avec validation complète et mise à jour Markov global
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * filepath (str) - Chemin vers le fichier historical_data.txt à charger
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION FICHIER :** Vérifie `if not os.path.exists(filepath):` avec logging erreur et mise à jour progress si UI disponible
      * **LECTURE SÉCURISÉE :** Ouvre `with open(filepath, 'r', encoding='utf-8') as f:` pour lecture UTF-8
      * **PARSING LIGNES :** Itère sur `for line_num, line in enumerate(f, 1):` avec nettoyage `line = line.strip()`
      * **VALIDATION FORMAT :** Vérifie que ligne contient uniquement '0', '1', 'B', 'P' avec `if not all(c in '01BP' for c in line):`
      * **CONVERSION STANDARDISÉE :** Convertit format avec `line = line.replace('B', '1').replace('P', '0')` puis `game_sequence = ['banker' if c == '1' else 'player' for c in line]`
      * **FILTRAGE LONGUEUR :** Ignore parties trop courtes avec `if len(game_sequence) < 2:` et logging warning
      * **ACCUMULATION DONNÉES :** Ajoute à `new_historical_data.append(game_sequence)` pour construction dataset
      * **MISE À JOUR THREAD-SAFE :** Utilise `with self.sequence_lock, self.markov_lock:` pour modifier état partagé
      * **ASSIGNATION ÉTAT :** Met à jour `self.historical_data = new_historical_data`, `self.loaded_historical = True`, `self.historical_games_at_startup_or_reset = num_games_loaded`
      * **CALCUL STATISTIQUES :** Détermine `total_rounds = sum(len(g) for g in new_historical_data)` pour logging
      * **MISE À JOUR MARKOV :** Si `self.markov:`, appelle `self.markov.update_global(self.historical_data)` avec gestion d'erreurs
      * **GESTION ERREURS :** Capture `UnicodeDecodeError` pour problèmes encodage et `Exception` générale avec reset compteur
      * **LOGGING DÉTAILLÉ :** Enregistre succès avec `logger.info(f"_load_historical_txt: Succès ({num_games_loaded} parties chargées, {total_rounds} coups totaux).")`
    - RETOUR : bool - True si chargement réussi, False en cas d'erreur
    - UTILITÉ : Import robuste données historiques avec validation format, conversion standardisée et mise à jour Markov global

13. _apply_data_sampling.txt (HybridBaccaratPredictor._apply_data_sampling - Application échantillonnage données)
    - Lignes 4271-4303 dans hbp.py (33 lignes)
    - FONCTION : Applique techniques d'échantillonnage pour équilibrage dataset avec désactivation pour garantir manches 31-60 et optimisation mémoire
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * original_historical_data (List[List[str]]) - Données historiques originales par parties
      * max_games (Optional[int]) - Limite maximale de jeux à traiter (None = pas de limite)
      * sampling_fraction (Optional[float]) - Fraction d'échantillonnage entre 0.0 et 1.0 (None = pas d'échantillonnage)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION LOGGER :** Récupère `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))` pour logging adaptatif
      * **COMPTAGE ORIGINAL :** Calcule `num_original_games = len(original_historical_data)` pour statistiques initiales
      * **COPIE DONNÉES :** Initialise `data_to_process = original_historical_data` comme base de travail
      * **DÉSACTIVATION ÉCHANTILLONNAGE :** Commentaire explicite "MODIFIÉ: Désactive l'échantillonnage pour garantir que toutes les manches 31-60 soient utilisées"
      * **VALIDATION MAX_GAMES :** Si `max_games is not None and max_games > 0 and len(data_to_process) > max_games:` :
        - Applique `data_to_process = data_to_process[:max_games]` pour limitation
        - Log `logger_instance.info(f"Limitation à {max_games} jeux (sur {num_original_games} disponibles)")`
      * **VALIDATION SAMPLING :** Si `sampling_fraction is not None and 0 < sampling_fraction < 1:` :
        - Calcule `target_size = int(len(data_to_process) * sampling_fraction)` pour taille cible
        - Applique `data_to_process = random.sample(data_to_process, target_size)` pour échantillonnage aléatoire
        - Log `logger_instance.info(f"Échantillonnage appliqué: {target_size} jeux sélectionnés")`
      * **STATISTIQUES FINALES :** Calcule `final_num_games = len(data_to_process)` pour comptage final
      * **GÉNÉRATION RAPPORT :** Crée `sampling_info = f"Données traitées: {final_num_games}/{num_original_games} jeux"` avec ratio
      * **LOGGING RÉSULTAT :** Enregistre `logger_instance.info(f"Échantillonnage terminé: {sampling_info}")`
    - RETOUR : Tuple[List[List[str]], str] - (données_échantillonnées, rapport_échantillonnage)
    - UTILITÉ : Optimisation mémoire et performance avec contrôle précis taille dataset tout en préservant intégrité manches cibles

14. _calculate_decay_feature.txt (HybridBaccaratPredictor._calculate_decay_feature - Calcul feature decay)
    - Lignes 13913-13939 dans hbp.py (27 lignes)
    - FONCTION : Calcule feature avec pondération temporelle décroissante pour donner plus de poids aux résultats récents avec normalisation
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * sequence (List[str]) - Séquence de résultats ('banker' ou 'player')
      * target_outcome (str) - Résultat cible à analyser ('banker' ou 'player')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SÉQUENCE :** Vérifie `if not sequence: return 0.0` pour éviter division par zéro et erreurs
      * **RÉCUPÉRATION CONFIG :** Utilise `decay_factor = self.config.decay_factor` pour facteur de décroissance temporelle (typiquement 0.95-0.99)
      * **INITIALISATION CALCULS :** Définit `weighted_sum = 0.0` et `total_weight = 0.0` pour accumulation pondérée
      * **PARCOURS INVERSE :** Itère `for i, outcome in enumerate(sequence):` pour traitement chronologique
      * **CALCUL POIDS :** Calcule `weight = decay_factor ** (len(sequence) - 1 - i)` pour pondération décroissante (plus récent = poids plus élevé)
      * **ACCUMULATION PONDÉRÉE :** Ajoute `total_weight += weight` pour normalisation puis :
        - Si `outcome == target_outcome:`, ajoute `weighted_sum += weight` pour contribution positive
      * **VALIDATION NORMALISATION :** Vérifie `if total_weight == 0.0: return 0.0` pour éviter division par zéro
      * **CALCUL FINAL :** Retourne `weighted_sum / total_weight` pour feature normalisée entre 0 et 1
      * **INTERPRÉTATION :** Valeur proche de 1 = target_outcome fréquent récemment, proche de 0 = rare récemment
    - RETOUR : float - Feature decay normalisée entre 0.0 et 1.0
    - UTILITÉ : Génère feature temporelle pour modèles ML avec emphasis sur données récentes et normalisation robuste

15. _create_temporal_split.txt (HybridBaccaratPredictor._create_temporal_split - Division temporelle)
    - Lignes 4331-4349 dans hbp.py (19 lignes)
    - FONCTION : Crée division temporelle des données pour validation avec TimeSeriesSplit et sélection du dernier split
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * X_lgbm_all (np.ndarray) - Données features LGBM pour déterminer taille dataset
    - FONCTIONNEMENT DÉTAILLÉ :
      * **RÉCUPÉRATION CONFIG :** Utilise `n_splits = max(2, getattr(self.config, 'lgbm_cv_splits', 5))` pour nombre de splits
      * **INITIALISATION SPLITTER :** Crée `tscv = TimeSeriesSplit(n_splits=n_splits)` pour division temporelle
      * **INITIALISATION LISTES :** Définit `train_indices_list = []` et `val_indices_list = []` pour accumulation
      * **GÉNÉRATION SPLITS :** Itère `for train_idx, val_idx in tscv.split(X_lgbm_all):` pour créer tous les splits possibles
      * **ACCUMULATION INDICES :** Ajoute `train_indices_list.append(train_idx)` et `val_indices_list.append(val_idx)` pour chaque split
      * **VALIDATION GÉNÉRATION :** Vérifie `if not train_indices_list or not val_indices_list:` avec logging erreur et retour `(None, None)`
      * **SÉLECTION DERNIER SPLIT :** Utilise `train_indices = train_indices_list[-1]` et `val_indices = val_indices_list[-1]` pour split le plus récent
      * **LOGIQUE TEMPORELLE :** TimeSeriesSplit assure que validation utilise toujours données plus récentes que entraînement
    - RETOUR : Tuple[np.ndarray, np.ndarray] - (indices_train, indices_validation) ou (None, None) si erreur
    - UTILITÉ : Assure validation temporellement cohérente avec données futures pour test réaliste des modèles

16. _validate_data_shapes.txt (HybridBaccaratPredictor._validate_data_shapes - Validation formes données)
    - Lignes 4351-4384 dans hbp.py (34 lignes)
    - FONCTION : Valide cohérence des dimensions de tous les arrays de données d'entraînement avec vérifications exhaustives
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * X_lgbm_all (np.ndarray) - Features LGBM pour tous échantillons
      * y_labels_all (np.ndarray) - Labels pour tous échantillons
      * X_lstm_all (np.ndarray) - Features LSTM pour tous échantillons
      * sample_weights_all (np.ndarray) - Poids échantillons pour tous échantillons
      * list_of_all_prefix_sequences (List[List[str]]) - Séquences préfixes pour tous échantillons
      * list_of_all_origins (List[int]) - Indices origine pour tous échantillons
      * final_num_samples (int) - Nombre attendu d'échantillons total
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `is_valid = True` et `message = ""` pour accumulation erreurs
      * **VALIDATION X_LGBM :** Vérifie `if X_lgbm_all.shape[0] != final_num_samples:` avec message `f"Incohérence taille X_lgbm (attendu: {final_num_samples}, obtenu: {X_lgbm_all.shape[0]})"`
      * **VALIDATION Y_LABELS :** Vérifie `if y_labels_all.shape[0] != final_num_samples:` avec message `f"Incohérence taille y_labels (attendu: {final_num_samples}, obtenu: {y_labels_all.shape[0]})"`
      * **VALIDATION X_LSTM :** Vérifie `if X_lstm_all.shape[0] != final_num_samples:` avec message `f"Incohérence taille X_lstm (attendu: {final_num_samples}, obtenu: {X_lstm_all.shape[0]})"`
      * **VALIDATION POIDS :** Vérifie `if sample_weights_all.shape[0] != final_num_samples:` avec message `f"Incohérence taille sample_weights (attendu: {final_num_samples}, obtenu: {sample_weights_all.shape[0]})"`
      * **VALIDATION SÉQUENCES :** Vérifie `if len(list_of_all_prefix_sequences) != final_num_samples:` avec message `f"Incohérence taille list_of_all_prefix_sequences (attendu: {final_num_samples}, obtenu: {len(list_of_all_prefix_sequences)})"`
      * **VALIDATION ORIGINES :** Vérifie `if len(list_of_all_origins) != final_num_samples:` avec message `f"Incohérence taille list_of_all_origins (attendu: {final_num_samples}, obtenu: {len(list_of_all_origins)})"`
      * **ACCUMULATION ERREURS :** Chaque erreur ajoute à `message` et met `is_valid = False`
      * **RETOUR CONDITIONNEL :** Si `not is_valid:`, retourne `(False, f"Erreur de validation des shapes: {message}")`, sinon `(True, "")`
    - RETOUR : Tuple[bool, str] - (validation_réussie, message_erreur_ou_vide)
    - UTILITÉ : Prévient erreurs de compatibilité entre modèles avec validation exhaustive dimensions avant entraînement

17. _get_cumulative_new_data.txt (HybridBaccaratPredictor._get_cumulative_new_data - Données nouvelles cumulatives)
    - Lignes 2772-2910 dans hbp.py (139 lignes)
    - FONCTION : Extrait données cumulatives pour entraînement incrémental incluant nouvelles parties historiques et session actuelle
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **PROTECTION THREAD :** Utilise `with self.sequence_lock:` pour accès sécurisé aux données
      * **IDENTIFICATION NOUVELLES PARTIES :** Calcule `current_total_games_in_history = len(self.historical_data)`, `start_index_new_history = self.historical_games_at_startup_or_reset`, `num_new_games_in_history = current_total_games_in_history - start_index_new_history`
      * **AJOUT NOUVELLES PARTIES :** Si `num_new_games_in_history > 0:`, itère `for i in range(start_index_new_history, current_total_games_in_history):` et `combined_new_sequence.extend(self.historical_data[i])`
      * **AJOUT SESSION ACTUELLE :** Si `current_session_len = len(self.sequence) > 0:`, ajoute `combined_new_sequence.extend(self.sequence)`
      * **VALIDATION MINIMUM :** Vérifie `total_new_rounds = len(combined_new_sequence)` contre `min_hist_needed_for_features = self.config.lstm_sequence_length` et `min_rounds_for_feature_gen = 5`
      * **PRÉPARATION CONTEXTE :** Si `start_index_new_history > 0:`, construit contexte historique avec `context_needed_rounds = min_hist_needed_for_features + 5` en itérant à rebours sur historique ancien
      * **GÉNÉRATION FEATURES :** Combine `full_sequence_for_gen = context_sequence + combined_new_sequence` puis itère `for i in range(start_gen_idx_in_full, len(full_sequence_for_gen)):`
      * **VALIDATION FEATURES :** Pour chaque `feat_lgbm, feat_lstm = self.create_hybrid_features(input_sub_sequence):`, vérifie `len(feat_lgbm) == len(self.feature_names)` et `feat_lstm.shape == (self.config.lstm_sequence_length, lstm_expected_num_features)`
      * **ACCUMULATION DONNÉES :** Si features valides, calcule `label = 1 if actual_outcome == 'banker' else 0` et ajoute aux listes `X_lgbm_new`, `y_lgbm_new`, `X_lstm_new`, `y_lstm_new`
      * **CONVERSION NUMPY :** Convertit avec `X_lgbm_np = np.array(X_lgbm_new, dtype=np.float32)`, `X_lstm_np = np.stack(X_lstm_new, axis=0).astype(np.float32)`, `y_lgbm_np = np.array(y_lgbm_new, dtype=np.int64)`
      * **VALIDATION FINALE :** Vérifie cohérence shapes avec `if X_lgbm_np.shape[0] != num_samples_generated:` et gestion d'erreurs complète
    - RETOUR : Tuple[4×Optional[np.ndarray]] - (X_lgbm, y_lgbm, X_lstm, y_lstm) ou (None×4) si erreur
    - UTILITÉ : Entraînement incrémental efficace avec données cumulatives nouvelles et contexte historique pour continuité

18. _get_historical_data_for_refit.txt (HybridBaccaratPredictor._get_historical_data_for_refit - Données historiques pour re-fit)
    - Lignes 4078-4135 dans hbp.py (58 lignes)
    - FONCTION : Récupère et prépare toutes les données historiques pour re-fit des wrappers LGBM avec validation complète
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **PROTECTION THREAD :** Utilise `with self.sequence_lock:` pour accès sécurisé aux données historiques
      * **VALIDATION HISTORIQUE :** Vérifie `if not self.loaded_historical or not self.historical_data:` avec warning et retour `(None, None)` si pas de données
      * **COPIE DONNÉES :** Crée `data_to_process = self.historical_data[:]` pour itération sécurisée sans modification originale
      * **INITIALISATION VARIABLES :** Définit `all_features_lgbm = []`, `all_labels_lgbm = []`, `total_rounds_extracted = 0`, `n_expected_features = len(self.feature_names)`, `min_seq_len_for_features = 5`
      * **PARCOURS PARTIES :** Itère `for game_idx, game in enumerate(data_to_process):` pour traiter chaque partie historique
      * **VALIDATION PARTIE :** Vérifie `if not game or len(game) <= min_seq_len_for_features:` avec warning et continue si partie trop courte
      * **GÉNÉRATION FEATURES :** Pour chaque partie, itère `for i in range(min_seq_len_for_features, len(game_sequence)):` avec `input_sequence = game_sequence[:i]` et `actual_outcome = game_sequence[i]`
      * **APPEL HYBRID FEATURES :** Utilise `features_lgbm, _ = self.create_hybrid_features(input_sequence)` en ignorant partie LSTM
      * **VALIDATION FEATURES :** Vérifie `if features_lgbm is not None and len(features_lgbm) == n_expected_features:` pour cohérence nombre features
      * **ACCUMULATION DONNÉES :** Si valide, calcule `label = 1 if actual_outcome == 'banker' else 0` et ajoute `all_features_lgbm.append(features_lgbm)`, `all_labels_lgbm.append(label)`, `total_rounds_extracted += 1`
      * **GESTION INCOHÉRENCES :** Si `features_lgbm is not None` mais mauvaise taille, warning avec détails partie et coup
      * **VALIDATION FINALE :** Vérifie `if not all_features_lgbm:` avec warning et retour `(None, None)` si aucune feature extraite
      * **CONVERSION NUMPY :** Convertit `X_refit = np.array(all_features_lgbm, dtype=np.float32)` et `y_refit = np.array(all_labels_lgbm, dtype=np.int64)` avec gestion d'erreurs
    - RETOUR : Tuple[Optional[np.ndarray], Optional[np.ndarray]] - (features_LGBM, labels) ou (None, None) si erreur
    - UTILITÉ : Préparation robuste données historiques complètes pour re-fit modèles LGBM avec validation exhaustive et gestion d'erreurs

19. _get_recent_session_data.txt (HybridBaccaratPredictor._get_recent_session_data - Données session récente)
    - Lignes 2984-3076 dans hbp.py (93 lignes)
    - FONCTION : Extrait données récentes pour mise à jour incrémentale avec validation features et gestion historique contextuel
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * min_rounds_for_update (int, optionnel) - Nombre minimum nouvelles manches requises (défaut: 10)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **PROTECTION THREAD :** Utilise `with self.sequence_lock:` pour accès sécurisé aux données de séquence
      * **CALCUL NOUVELLES DONNÉES :** Détermine `current_len = len(self.sequence)`, `start_index = self.last_incremental_update_index`, `num_new_rounds = current_len - start_index`
      * **VALIDATION MINIMUM :** Vérifie `if num_new_rounds < min_rounds_for_update:` avec logging info et retour `(None, None, None, None)` si insuffisant
      * **CALCUL HISTORIQUE CONTEXTUEL :** Détermine `min_hist_needed = self.config.lstm_sequence_length`, `effective_start_for_features = max(0, start_index - min_hist_needed)` pour contexte suffisant
      * **EXTRACTION SOUS-SÉQUENCE :** Récupère `sequence_subset_for_gen = self.sequence[effective_start_for_features:current_len]` avec `offset_in_subset = start_index - effective_start_for_features` pour mapping indices
      * **INITIALISATION STRUCTURES :** Crée `X_lgbm_new, y_lgbm_new = [], []` et `X_lstm_new, y_lstm_new = [], []` pour accumulation données
      * **CONFIGURATION LSTM :** Récupère `lstm_input_feat_size = getattr(self.lstm, 'input_size', 6) if self.lstm else 6` et `expected_lstm_shape = (self.config.lstm_sequence_length, lstm_input_feat_size)`
      * **GÉNÉRATION FEATURES :** Boucle `for i in range(offset_in_subset, len(sequence_subset_for_gen)):` pour nouvelles manches :
        - Calcule `sequence_up_to_i = sequence_subset_for_gen[:i]` pour contexte
        - Appelle `feat_lgbm, feat_lstm = self.create_hybrid_features(sequence_up_to_i)` pour génération
        - Valide `valid_lgbm = feat_lgbm is not None and len(feat_lgbm) == len(self.feature_names)`
        - Valide `valid_lstm = feat_lstm is not None and feat_lstm.shape == expected_lstm_shape`
      * **ACCUMULATION DONNÉES :** Si features valides :
        - Détermine `actual_outcome = sequence_subset_for_gen[i]` et `label = 1 if actual_outcome == 'banker' else 0`
        - Ajoute à listes avec `X_lgbm_new.append(feat_lgbm)`, `y_lgbm_new.append(label)`, etc.
      * **GESTION ERREURS FEATURES :** Si features invalides, collecte problèmes dans `issues = []` et log warning détaillé
      * **VALIDATION RÉSULTATS :** Vérifie `if not X_lgbm_new:` avec warning "Aucune feature valide générée" et retour None
      * **CONVERSION NUMPY :** Convertit avec `X_lgbm_np = np.array(X_lgbm_new, dtype=np.float32)`, `X_lstm_np = np.stack(X_lstm_new, axis=0).astype(np.float32)`, etc.
      * **GESTION ERREURS CONVERSION :** Capture `Exception as e:` avec logging erreur et retour None
      * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Données récentes préparées: LGBM {X_lgbm_np.shape}, LSTM {X_lstm_np.shape}")`
    - RETOUR : Tuple[Optional[np.ndarray], Optional[np.ndarray], Optional[np.ndarray], Optional[np.ndarray]] - (X_lgbm, y_lgbm, X_lstm, y_lstm) ou (None×4) si échec
    - UTILITÉ : Focus sur performance récente pour ajustements incrémentaux avec validation complète et gestion contextuelle historique

20. calculate_lstm_sample_weights.txt (HybridBaccaratPredictor.calculate_lstm_sample_weights - Poids échantillons LSTM)
    - Lignes 10027-10170 dans hbp.py (144 lignes)
    - FONCTION : Calcule les poids d'échantillons pour LSTM basés sur métriques de confiance et incertitude avec focus manches 31-60
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * X_lstm (np.ndarray) - Features LSTM (séquences temporelles)
      * y_lstm (np.ndarray) - Labels correspondants (0=Player, 1=Banker)
      * sequence_positions (np.ndarray, optionnel) - Positions des échantillons dans la séquence
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION DONNÉES :** Vérifie `if X_lstm is None or len(X_lstm) == 0 or y_lstm is None or len(y_lstm) == 0: return None`
      * **INITIALISATION POIDS :** Crée `sample_weights = np.ones(len(X_lstm), dtype=np.float32)` comme base
      * **FACTEUR TEMPOREL :** Calcule `temporal_factor = np.linspace(temporal_factor_min, temporal_factor_max, len(X_lstm))` avec `temporal_factor_min=0.5`, `temporal_factor_max=1.0` pour favoriser échantillons récents
      * **FACTEUR DIFFICULTÉ :** Initialise `difficulty_factor = np.ones_like(sample_weights)` puis si LSTM entraîné :
        - Met modèle en mode évaluation avec `self.lstm.eval()`
        - Convertit features avec `X_tensor = torch.FloatTensor(X_lstm).to(self.device)`
        - Calcule prédictions avec `outputs = self.lstm(X_tensor)` puis `probas = torch.softmax(outputs, dim=1).cpu().numpy()`
        - Détermine confiance avec `confidence = np.abs(pred_probas - 0.5) * 2.0`
        - Inverse pour difficulté avec `difficulty_factor = 1.0 - 0.5 * confidence`
      * **FACTEUR POSITION :** Si `sequence_positions` fourni, extrait `target_round_min=31`, `target_round_max=60`, `late_game_weight_factor`
      * **MASQUE MANCHES CIBLES :** Crée `late_game_mask = (positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max)`
      * **POIDS PROGRESSIFS :** Calcule `normalized_positions = (target_positions - target_round_min) / (target_round_max - target_round_min)` puis `progressive_weights = 1.0 + (np.exp(exponential_factor * normalized_positions) - 1) / (np.exp(exponential_factor) - 1) * (late_game_weight_factor - 1.0)`
      * **ÉQUILIBRAGE CLASSES :** Calcule `class_weights = (len(target_y) / (len(class_counts) * class_counts)) ** 1.5` pour classes minoritaires
      * **FACTEUR TRANSITIONS :** Détecte changements avec `transitions = np.diff(target_y, prepend=target_y[0])` et applique bonus `1.5` aux points de transition
      * **COMBINAISON FINALE :** Multiplie `combined_weights = temporal_factor * difficulty_factor * position_factor`
      * **NORMALISATION :** Applique `normalized_weights = combined_weights * (len(combined_weights) / np.sum(combined_weights))` puis clipping avec `min_sample_weight=0.2`, `max_sample_weight=5.0`
    - RETOUR : np.ndarray - Poids d'échantillons optimisés pour LSTM avec focus manches 31-60
    - UTILITÉ : Optimise apprentissage LSTM avec pondération temporelle, difficulté, et focus spécial sur manches cibles avec équilibrage classes

21. calculate_sample_weights_from_metrics.txt (HybridBaccaratPredictor.calculate_sample_weights_from_metrics - Poids depuis métriques)
    - Lignes 9896-10025 dans hbp.py (130 lignes)
    - FONCTION : Calcule poids échantillons basés sur métriques de confiance et incertitude LGBM avec pondération adaptative
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * X_features (np.ndarray) - Features d'entrée pour calcul métriques
      * y_labels (np.ndarray) - Labels correspondants
      * sequence_positions (np.ndarray, optionnel) - Positions des échantillons dans la séquence
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION MODÈLES :** Vérifie `if self.feature_scaler is None or self.lgbm_base is None: return None`
      * **VÉRIFICATION ENTRAÎNEMENT :** Utilise `check_is_fitted(self.lgbm_base)` pour confirmer modèle LGBM entraîné
      * **INITIALISATION POIDS :** Crée `sample_weights = np.ones(len(X_features), dtype=np.float32)` comme base
      * **CALCUL CONFIANCE :** Si LGBM entraîné, calcule `y_pred_proba = self.lgbm_base.predict_proba(X_features)` puis `confidence_scores = np.abs(y_pred_proba[:, 1] - 0.5) * 2.0`
      * **CALCUL INCERTITUDE :** Si `self.lgbm_uncertainty` disponible :
        - Vérifie entraînement avec `check_is_fitted(self.lgbm_uncertainty)`
        - Extrait prédictions estimateurs avec `estimator_probas = np.array([estimator.predict_proba(X_features)[:, 1] for estimator in self.lgbm_uncertainty.estimators_])`
        - Calcule variance avec `uncertainty_scores = np.var(estimator_probas, axis=0)`
        - Normalise avec `uncertainty_scores = (uncertainty_scores - np.min(uncertainty_scores)) / (np.max(uncertainty_scores) - np.min(uncertainty_scores) + 1e-8)`
      * **PONDÉRATION CONFIANCE :** Applique `confidence_weight_factor = getattr(self.config, 'confidence_weight_factor', 1.5)` avec `confidence_weights = 1.0 + (1.0 - confidence_scores) * confidence_weight_factor`
      * **PONDÉRATION INCERTITUDE :** Applique `uncertainty_weight_factor = getattr(self.config, 'uncertainty_weight_factor', 1.2)` avec `uncertainty_weights = 1.0 + uncertainty_scores * uncertainty_weight_factor`
      * **FACTEUR POSITION :** Si `sequence_positions` fourni, extrait `target_round_min=31`, `target_round_max=60`, `late_game_weight_factor`
      * **MASQUE MANCHES CIBLES :** Crée `late_game_mask = (positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max)`
      * **POIDS PROGRESSIFS :** Pour manches cibles, calcule `normalized_positions = (target_positions - target_round_min) / (target_round_max - target_round_min)` puis `progressive_weights = 1.0 + normalized_positions * (late_game_weight_factor - 1.0)`
      * **COMBINAISON FINALE :** Multiplie `combined_weights = confidence_weights * uncertainty_weights * position_weights`
      * **NORMALISATION :** Applique `normalized_weights = combined_weights * (len(combined_weights) / np.sum(combined_weights))` puis clipping avec `min_sample_weight=0.1`, `max_sample_weight=10.0`
    - RETOUR : np.ndarray - Poids d'échantillons optimisés basés sur métriques LGBM
    - UTILITÉ : Pondération intelligente selon confiance, incertitude et position avec focus manches 31-60 pour optimiser qualité prédictions

22. _extract_features_for_consecutive_calculator.txt (HybridBaccaratPredictor._extract_features_for_consecutive_calculator - Features calculateur consécutif)
    - Lignes 10742-10767 dans hbp.py (26 lignes)
    - FONCTION : Extrait vecteur de features spécialisé pour calculateur de confiance consécutive avec 10 features optimisées
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **PROTECTION THREAD :** Utilise `with self.sequence_lock:` pour accès sécurisé à la séquence
      * **VALIDATION SÉQUENCE :** Vérifie `if len(self.sequence) < 5:` avec retour features par défaut si insuffisant
      * **SÉQUENCE RÉCENTE :** Extrait `recent_sequence = self.sequence[-10:]` pour analyse patterns récents
      * **FEATURES BASIQUES :** Calcule ratios avec `banker_ratio = recent_sequence.count('banker') / len(recent_sequence)`, `player_ratio = recent_sequence.count('player') / len(recent_sequence)`
      * **FEATURES STREAKS :** Détermine `current_streak_length` et `current_streak_type` en analysant fin de séquence
      * **FEATURES ALTERNANCE :** Calcule `alternation_ratio` en comptant changements consécutifs dans séquence récente
      * **FEATURES PATTERNS :** Détecte patterns répétitifs avec boucles imbriquées `for pattern_length in [2, 3]:` et `for i in range(len(recent_sequence) - 2 * pattern_length):`
      * **NORMALISATION MANCHE :** Ajoute `current_round / getattr(self.config, 'round_normalization_factor', 100.0)` pour position relative
      * **VECTEUR FINAL :** Retourne liste de 10 features : `[banker_ratio, player_ratio, current_streak_length/10.0, current_streak_type, alternation_ratio, volatility, trend_strength, momentum, pattern_strength, normalized_round]`
      * **GESTION ERREURS :** Capture exceptions avec fallback vers `[default_feature_value] * 10` où `default_feature_value = getattr(self.config, 'default_feature_value', 0.5)`
    - RETOUR : List[float] - Vecteur de 10 features normalisées pour calculateur confiance
    - UTILITÉ : Optimise features pour analyse patterns et confiance manches consécutives avec protection complète

23. _extract_pattern_key.txt (ConsecutiveConfidenceCalculator._extract_pattern_key - Extraction clé pattern)
    - Lignes 1429-1440 dans hbp.py (12 lignes)
    - FONCTION : Extrait clé de pattern unique à partir du vecteur de features avec arrondi intelligent pour regroupement
    - PARAMÈTRES :
      * self - Instance de la classe ConsecutiveConfidenceCalculator
      * features_vector (List[float]) - Vecteur de features pour extraction pattern
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION LISTE :** Crée `key_parts = []` pour accumulation des composants de clé
      * **PARCOURS FEATURES :** Itère `for i, feature in enumerate(features_vector[:5]):` pour utiliser les 5 premières features les plus importantes
      * **ARRONDI ADAPTATIF :** Applique arrondi différencié selon type de feature :
        - **RATIOS (indices 0,1,2,3) :** Utilise `round(feature, 1)` pour 1 décimale de précision sur ratios
        - **AUTRES FEATURES (index 4+) :** Utilise `round(feature)` pour 0 décimale sur features non-ratio
      * **FORMATAGE COMPOSANT :** Crée `key_parts.append(f"{i}:{round(feature, 1)}")` ou `key_parts.append(f"{i}:{round(feature)}")` selon type
      * **ASSEMBLAGE CLÉ :** Retourne `"|".join(key_parts)` pour clé unique formatée comme "0:0.7|1:0.3|2:2.0|3:1|4:0"
      * **REGROUPEMENT INTELLIGENT :** L'arrondi permet de regrouper patterns similaires pour statistiques robustes
    - RETOUR : str - Clé pattern unique formatée pour indexation dictionnaire
    - UTILITÉ : Identification et regroupement patterns récurrents avec granularité adaptée pour analyse confiance consécutive

24. _update_pattern_counts.txt (HybridBaccaratPredictor._update_pattern_counts - MAJ compteurs patterns)
    - Lignes 10769-10776 dans hbp.py (8 lignes)
    - FONCTION : Met à jour les compteurs de motifs basés sur les 4 derniers coups avec protection thread-safe
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * last_outcome (str) - Résultat du dernier coup ('player' ou 'banker')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION LONGUEUR :** Vérifie `if len(self.sequence) >= 4:` pour s'assurer d'avoir suffisamment de données
      * **EXTRACTION PATTERN :** Crée `pattern = tuple(self.sequence[-4:])` incluant le dernier coup pour motif de 4 éléments
      * **DÉFINITION CLÉ :** Utilise `dict_key = last_outcome` comme clé de dictionnaire pour indexation
      * **MISE À JOUR COMPTEUR :** Si `dict_key in self.pattern_counts:`, incrémente `self.pattern_counts[dict_key][pattern] += 1`
      * **PROTECTION THREAD :** DOIT être appelée avec `self.sequence_lock` déjà acquis pour éviter conditions de course
      * **STRUCTURE DONNÉES :** Maintient dictionnaire imbriqué {outcome: {pattern: count}} pour statistiques
    - RETOUR : None - Met à jour directement les compteurs internes
    - UTILITÉ : Maintient statistiques patterns à jour pour analyse prédictive et détection motifs récurrents

25. update_recent_data.txt (ConsecutiveConfidenceCalculator.update_recent_data - MAJ données récentes)
    - Lignes 1442-1450 dans hbp.py (9 lignes)
    - FONCTION : Met à jour l'historique récent des recommandations et résultats avec rotation automatique pour maintenir taille fixe
    - PARAMÈTRES :
      * self - Instance de la classe ConsecutiveConfidenceCalculator
      * recommendation (str) - Nouvelle recommandation ('player', 'banker', 'wait')
      * outcome (str) - Résultat réel correspondant ('player', 'banker')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **AJOUT RECOMMANDATION :** Appelle `self.recent_recommendations.append(recommendation)` pour ajouter nouvelle recommandation
      * **AJOUT RÉSULTAT :** Appelle `self.recent_outcomes.append(outcome)` pour ajouter résultat correspondant
      * **VÉRIFICATION TAILLE :** Teste `if len(self.recent_recommendations) > self.max_recent_history:` pour contrôle limite
      * **ROTATION FIFO :** Si dépassement, supprime plus ancien avec `self.recent_recommendations.pop(0)` et `self.recent_outcomes.pop(0)`
      * **SYNCHRONISATION LISTES :** Maintient correspondance exacte entre recommandations et résultats via suppression simultanée
      * **HISTORIQUE LIMITÉ :** Utilise `self.max_recent_history` (défaut: 50) pour limiter mémoire et focus sur données récentes
    - RETOUR : None - Met à jour directement les listes internes
    - UTILITÉ : Maintient historique récent optimisé pour calculs de ratios et statistiques avec rotation automatique FIFO

26. _get_cached_lgbm_prediction.txt (HybridBaccaratPredictor._get_cached_lgbm_prediction - Prédiction LGBM cachée)
    - Lignes 8227-8296 dans hbp.py (70 lignes)
    - FONCTION : Récupère prédiction LGBM depuis cache ou calcule si nécessaire avec système de cache double (deque + dict)
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * lgbm_feat (Optional[List[float]]) - Vecteur de features pour prédiction LGBM
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION FEATURES :** Vérifie `if lgbm_feat is None:` avec retour `{'player': 0.5, 'banker': 0.5}` par défaut
      * **CONVERSION CLÉ :** Convertit `feat_tuple = tuple(lgbm_feat)` pour utilisation comme clé dictionnaire
      * **INITIALISATION CACHE DICT :** Si `not hasattr(self, 'lgbm_cache_dict'):`, convertit cache deque existant en dictionnaire avec `for feat, pred in self.lgbm_cache: self.lgbm_cache_dict[tuple(feat)] = pred`
      * **RECHERCHE CACHE :** Vérifie `if feat_tuple in self.lgbm_cache_dict:` pour hit cache
      * **HIT CACHE :** Si trouvé, récupère `cached_pred = self.lgbm_cache_dict[feat_tuple]`, appelle `self._update_prediction_progress()` et retourne prédiction
      * **MISS CACHE :** Si non trouvé, appelle `self._update_prediction_progress()` puis `lgbm_pred = self.predict_with_lgbm(lgbm_feat)`
      * **MISE À JOUR CACHE :** Ajoute au dictionnaire `self.lgbm_cache_dict[feat_tuple] = lgbm_pred` et à la deque `self.lgbm_cache.append((lgbm_feat, lgbm_pred))`
      * **GESTION TAILLE :** Récupère `max_cache_size = getattr(self.config, 'lgbm_cache_max_size', 1000)` et si `len(self.lgbm_cache) > max_cache_size:`, supprime plus ancien avec `oldest_feat, _ = self.lgbm_cache.popleft()` et `del self.lgbm_cache_dict[tuple(oldest_feat)]`
      * **GESTION ERREURS :** Capture `NotFittedError` avec logging adaptatif selon phase (training/optuna) et `Exception` générale avec logging complet
      * **MESURE PERFORMANCE :** Utilise `start_time = time.time()` et `elapsed = time.time() - start_time` pour monitoring temps exécution
    - RETOUR : Dict[str, float] - Probabilités {'player': float, 'banker': float} depuis cache ou calcul
    - UTILITÉ : Optimisation performance avec cache double (deque FIFO + dict O(1)) pour éviter recalculs coûteux LGBM

27. prepare_training_data.txt (HybridBaccaratPredictor.prepare_training_data - Préparation données entraînement)
    - Lignes 4137-4269 dans hbp.py (133 lignes)
    - FONCTION : Prépare les données d'entraînement via BaccaratSequenceManager avec échantillonnage et validation complète
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * force_use_historical (bool, optionnel) - Force utilisation données historiques (défaut: False)
      * max_games (Optional[int]) - Limite nombre de jeux à traiter pour optimisation mémoire
      * sampling_fraction (Optional[float]) - Fraction d'échantillonnage des données (0.0-1.0)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **LECTURE CONFIG :** Récupère `min_target_idx = getattr(self.config, 'min_target_hand_index_training', 30)`, `lstm_seq_len_cfg`, `lstm_feat_count_cfg`, `lgbm_feat_count_cfg`
      * **VALIDATION HISTORIQUE :** Vérifie `if not self.loaded_historical or not self.historical_data` avec retour (None×8) si échec
      * **ÉCHANTILLONNAGE :** Appelle `data_to_process, sampling_applied_info = self._apply_data_sampling(original_historical_data, max_games, sampling_fraction)`
      * **MANAGER SÉQUENCES :** Instancie `BaccaratSequenceManager(sequence_length=lstm_seq_len_cfg, min_target_hand_index=min_target_idx, hybrid_feature_creator=self.create_hybrid_features, lgbm_feature_count=lgbm_feat_count_cfg, lstm_seq_len=lstm_seq_len_cfg, lstm_feature_count=lstm_feat_count_cfg)`
      * **GÉNÉRATION DONNÉES :** Appelle `manager_results = manager.prepare_data_for_model(data_to_process)` qui retourne tuple de 5 éléments
      * **UNPACK RÉSULTATS :** Extrait `X_lgbm_all, y_labels_all, X_lstm_all, list_of_all_prefix_sequences, list_of_all_origins = manager_results`
      * **VALIDATION ÉCHANTILLONS :** Vérifie `final_num_samples = X_lgbm_all.shape[0]` contre `min_samples_required = getattr(self.config, 'min_samples_for_training', 100)`
      * **CALCUL POIDS :** Génère `sample_weights_all = self._calculate_sample_weights(list_of_all_origins, final_num_samples)` avec decay factor
      * **SPLIT TEMPOREL :** Crée `train_indices, val_indices = self._create_temporal_split(X_lgbm_all)` via TimeSeriesSplit
      * **VALIDATION SHAPES :** Appelle `(is_valid, message) = self._validate_data_shapes(X_lgbm_all, y_labels_all, X_lstm_all, sample_weights_all, list_of_all_prefix_sequences, list_of_all_origins, final_num_samples)`
      * **LOGGING FINAL :** Enregistre `logger_instance.info(f"_prepare_training_data: Succès! {final_num_samples} échantillons préparés")`
    - RETOUR : Tuple[8] - (X_lgbm, y_labels, X_lstm, sample_weights, train_indices, val_indices, sequences, origins) ou (None×8) si erreur
    - UTILITÉ : Pipeline complet de préparation données avec BaccaratSequenceManager, échantillonnage intelligent, et validation robuste pour entraînement ML

================================================================================
SECTION 4 : INTERFACE UTILISATEUR
================================================================================

1. setup_ui.txt (HybridBaccaratPredictor.setup_ui - Configuration interface utilisateur principale)
   - Lignes 2587-2769 dans hbp.py (183 lignes)
   - FONCTION : Configure l'interface utilisateur principale avec Tkinter, incluant tous les widgets, styles et layouts
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONFIGURATION STYLE :** Détecte la plateforme et applique le thème TTK approprié (vista/aqua/clam)
     * **GESTION COULEURS :** Récupère les couleurs TTK et définit des couleurs fixes pour Matplotlib
     * **STRUCTURE LAYOUT :** Crée la structure principale avec frames gauche/droite et organisation modulaire
     * **PANNEAU MODÈLES :** Configure les boutons de gestion des modèles et données (charger, sauvegarder, dashboard)
     * **PANNEAU ENTRAÎNEMENT :** Met en place les contrôles d'entraînement et d'optimisation Optuna
     * **PANNEAU CONFIGURATION :** Initialise le panneau de configuration des ressources
     * **CONTRÔLES PRÉDICTION :** Crée les boutons Player/Banker et annulation avec layout en grille
     * **AFFICHAGE TEMPS RÉEL :** Configure les labels de prédiction avec couleurs et polices spécifiques
     * **GRAPHIQUE MATPLOTLIB :** Intègre le graphique de tendance avec couleurs fixes et canvas Tkinter
     * **STATISTIQUES :** Met en place le panneau d'analyse avec métriques détaillées
   - RETOUR : None - Méthode de configuration ne retourne rien
   - UTILITÉ : Point d'entrée pour créer l'interface utilisateur complète avec tous les composants visuels et interactifs

2. update_display.txt (HybridBaccaratPredictor.update_display - Mise à jour affichage complet)
   - Lignes 11454-11527 dans hbp.py (74 lignes)
   - FONCTION : Met à jour tous les éléments de l'interface utilisateur avec adaptation contextuelle aux manches cibles
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise sequence_lock pour accès sécurisé à l'historique des prédictions
     * **MISE À JOUR PRÉDICTIONS :** Affiche manche actuelle, probabilités Player/Banker et recommandation
     * **DÉTECTION PLAGE CIBLE :** Vérifie si dans la plage 31-60 pour adaptation de l'affichage
     * **AJUSTEMENT CONFIANCE :** Applique formule d'ajustement pour ramener vers 50% en affichage
     * **VÉRIFICATION MODÈLES :** Contrôle si les modèles sont entraînés pour adapter le niveau de confiance
     * **CLASSIFICATION CONFIANCE :** Détermine niveau (Faible/Moyenne/Élevée) selon seuils adaptatifs
     * **APPEL STATISTIQUES :** Déclenche update_statistics() pour métriques avancées
     * **GESTION GRAPHIQUE :** Met à jour le graphique de tendance si visible
   - RETOUR : None - Méthode de mise à jour d'interface ne retourne rien
   - UTILITÉ : Orchestre la mise à jour complète de l'interface avec adaptation intelligente au contexte

3. show_models_dashboard.txt (HybridBaccaratPredictor.show_models_dashboard - Tableau de bord modèles)
   - Lignes 3332-3490 dans hbp.py (159 lignes)
   - FONCTION : Affiche un tableau de bord interactif des modèles entraînés avec métadonnées et performances
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CRÉATION FENÊTRE :** Ouvre fenêtre Toplevel avec Treeview pour affichage tabulaire
     * **CONFIGURATION COLONNES :** Définit colonnes pour nom, date, précision, hyperparamètres
     * **SCAN RÉPERTOIRE :** Parcourt dossier models pour fichiers .joblib et .pkl
     * **CHARGEMENT MÉTADONNÉES :** Lit fichiers JSON associés pour informations détaillées
     * **EXTRACTION FALLBACK :** Charge modèles directement si métadonnées JSON manquantes
     * **AFFICHAGE PERFORMANCES :** Présente précision, paramètres LSTM/LGBM, ordre Markov
     * **BOUTONS ACTION :** Fournit options pour voir détails et charger modèle sélectionné
     * **GESTION ERREURS :** Traite erreurs de chargement avec logging approprié
   - RETOUR : None - Méthode d'interface utilisateur ne retourne rien
   - UTILITÉ : Interface conviviale pour explorer et gérer les modèles sauvegardés avec métadonnées complètes

4. toggle_graph_visibility.txt (HybridBaccaratPredictor.toggle_graph_visibility - Basculer visibilité graphique)
   - FONCTION : Bascule l'affichage du graphique matplotlib dans l'interface utilisateur
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT :** Contrôle état actuel de visibilité du graphique
     * **BASCULEMENT VISIBILITÉ :** Inverse l'état d'affichage du canvas matplotlib
     * **MISE À JOUR LAYOUT :** Réorganise layout interface selon nouvelle visibilité
     * **SAUVEGARDE PRÉFÉRENCE :** Mémorise préférence utilisateur pour sessions futures
     * **REDIMENSIONNEMENT :** Ajuste taille fenêtre selon présence/absence graphique
   - RETOUR : None - Méthode d'interface ne retourne rien
   - UTILITÉ : Permet masquer/afficher le graphique pour optimiser l'espace d'affichage

5. setup_config_panel.txt (HybridBaccaratPredictor.setup_config_panel - Configuration panneau paramètres)
   - FONCTION : Configure le panneau de paramètres avec contrôles pour ajustement configuration
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * parent_frame (tkinter.Frame) - Frame parent pour intégration
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CRÉATION WIDGETS :** Crée contrôles pour paramètres configurables
     * **LIAISON VARIABLES :** Lie widgets aux variables de configuration
     * **VALIDATION ENTRÉES :** Configure validation pour entrées numériques
     * **CALLBACKS CHANGEMENT :** Définit callbacks pour changements temps réel
     * **ORGANISATION LAYOUT :** Organise widgets dans layout logique
   - RETOUR : None - Méthode de configuration interface
   - UTILITÉ : Interface pour modification en temps réel des paramètres système

6. _update_progress.txt (HybridBaccaratPredictor._update_progress - Mise à jour progression)
   - Lignes 4683-4694 dans hbp.py (12 lignes)
   - FONCTION : Met à jour barres de progression et indicateurs d'état dans interface avec validation et protection thread-safe
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * value (int) - Valeur progression entre 0 et 100
     * message (str) - Message de statut à afficher
   - FONCTIONNEMENT DÉTAILLÉ :
     * **FONCTION INTERNE :** Définit `def update_ui():` comme fonction interne pour mise à jour thread-safe
     * **VALIDATION VALEUR :** Calcule `value_clamped = max(0, min(100, int(value)))` pour borner valeur entre 0-100
     * **MISE À JOUR PROGRESSION :** Si `hasattr(self, 'progress_var') and self.progress_var:`, applique `self.progress_var.set(value_clamped)` pour barre progression
     * **MISE À JOUR MESSAGE :** Si `hasattr(self, 'progress_label_var') and self.progress_label_var:` :
       - Tronque message avec `message_display = message[:100] + '...' if len(message) > 100 else message` pour éviter débordement
       - Applique `self.progress_label_var.set(message_display)` pour affichage
     * **RAFRAÎCHISSEMENT UI :** Si `self.is_ui_available():`, appelle `self.root.update_idletasks()` pour mise à jour immédiate
     * **EXÉCUTION THREAD-SAFE :** Appelle `self.root.after(0, update_ui)` pour exécution dans thread UI principal
     * **PROTECTION ATTRIBUTS :** Vérifie existence attributs avec `hasattr()` avant utilisation pour éviter erreurs
     * **GESTION LONGUEUR :** Limite message à 100 caractères avec ellipse pour préserver layout interface
   - RETOUR : None - Méthode de mise à jour interface ne retourne rien
   - UTILITÉ : Mise à jour progression thread-safe avec validation robuste et protection débordement interface

7. setup_progress_bar.txt (HybridBaccaratPredictor.setup_progress_bar - Configuration barre progression)
   - FONCTION : Configure et initialise barre de progression pour opérations longues
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * parent_widget (tkinter.Widget) - Widget parent pour intégration
     * max_value (int, optionnel) - Valeur maximale (défaut: 100)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CRÉATION PROGRESSBAR :** Crée widget Progressbar TTK
     * **CONFIGURATION STYLE :** Applique style cohérent avec interface
     * **INITIALISATION VALEURS :** Configure valeurs min/max et position initiale
     * **INTÉGRATION LAYOUT :** Intègre dans layout parent avec positionnement
     * **CONFIGURATION CALLBACKS :** Définit callbacks pour mise à jour
   - RETOUR : tkinter.ttk.Progressbar - Widget barre de progression créé
   - UTILITÉ : Composant réutilisable pour affichage progression opérations

8. _update_weights_display.txt (HybridBaccaratPredictor._update_weights_display - Mise à jour affichage poids)
   - Lignes 2771-2810 dans hbp.py (40 lignes)
   - FONCTION : Met à jour affichage des poids des méthodes dans interface utilisateur avec formatage et validation
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec retour immédiat si interface indisponible
     * **VALIDATION VARIABLES :** Teste `if not hasattr(self, 'weights_vars'):` avec retour si variables poids non initialisées
     * **EXTRACTION POIDS :** Récupère poids actuels depuis `self.weights` avec gestion sécurisée des clés manquantes
     * **FORMATAGE MARKOV :** Met à jour `self.weights_vars['markov'].set(f"Markov: {self.weights.get('markov', 0.0)*100:.1f}%")` avec pourcentage formaté
     * **FORMATAGE LGBM :** Met à jour `self.weights_vars['lgbm'].set(f"LGBM: {self.weights.get('lgbm', 0.0)*100:.1f}%")` avec pourcentage formaté
     * **FORMATAGE LSTM :** Met à jour `self.weights_vars['lstm'].set(f"LSTM: {self.weights.get('lstm', 0.0)*100:.1f}%")` avec pourcentage formaté
     * **VALIDATION COHÉRENCE :** Calcule `total_weight = sum(self.weights.values())` et affiche warning si `abs(total_weight - 1.0) > 0.01`
     * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de la mise à jour de l'affichage des poids: {e}")` pour robustesse
     * **PERFORMANCE OPTIMISÉE :** Évite `update_idletasks()` pour performance si appelé fréquemment
   - RETOUR : None - Met à jour directement les variables d'affichage Tkinter
   - UTILITÉ : Visualisation temps réel des poids adaptatifs des méthodes avec validation et formatage professionnel

9. lightweight_update_display.txt (HybridBaccaratPredictor.lightweight_update_display - Mise à jour légère affichage)
   - Lignes 11343-11452 dans hbp.py (110 lignes)
   - FONCTION : Mise à jour optimisée des éléments essentiels de l'interface après chaque coup avec adaptation manches cibles
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * pred (Dict[str, Union[float, str, Dict]]) - Dictionnaire de prédiction avec probabilités et métriques
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION PRÉREQUIS :** Vérifie `if not hasattr(self, 'pred_vars'): return` pour sécurité si appelé trop tôt
     * **MISE À JOUR PROBABILITÉS :** Met à jour `self.pred_vars['player'].set(f"Player: {pred.get('player', 0.5)*100:.1f}%")` et `self.pred_vars['banker'].set(f"Banker: {pred.get('banker', 0.5)*100:.1f}%")`
     * **MISE À JOUR RECOMMANDATION :** Utilise `rec_text_map = {'player': "Jouer PLAYER", 'banker': "Jouer BANKER", 'wait': "Attendre"}` puis `self.pred_vars['recommendation'].set(f"Recommandation: {rec_text_map.get(rec, 'Attendre')}")`
     * **MISE À JOUR MANCHE :** Avec `self.sequence_lock:`, calcule `round_num = len(self.sequence)` puis `self.pred_vars['round'].set(f"Manche: {round_num}")`
     * **DÉTECTION PLAGE CIBLE :** Calcule `target_round_min = getattr(self.config, 'target_round_min', 31)`, `target_round_max = getattr(self.config, 'target_round_max', 60)`, `is_target_round = target_round_min <= round_num <= target_round_max`
     * **MISE À JOUR CONFIANCE :** Met à jour `self.pred_vars['confidence'].set(f"Confiance: {pred.get('combined_confidence', 0.5)*100:.1f}%")`
     * **MÉTRIQUES CONDITIONNELLES :** Si `'confidence_metrics' in pred:`, extrait métriques et adapte affichage selon `is_target_round`
     * **AFFICHAGE ADAPTATIF INCERTITUDE :** Si pas dans plage cible, affiche `"Incertitude Détaillée: N/A (manche 1-30)"`, sinon calcule `epistemic = metrics.get('epistemic_uncertainty', 0) * 100` etc.
     * **POIDS BAYÉSIENS CONDITIONNELS :** Si `'bayesian_weights' in pred` et `is_target_round`, formate et affiche poids avec `weights_parts.append(f"{method.upper()}({weight*100:.1f}%)")`
     * **OPTIMISATION PERFORMANCE :** Évite `update_idletasks` si appelé via `root.after` pour performance optimale
   - RETOUR : None - Met à jour directement les variables d'interface Tkinter
   - UTILITÉ : Rafraîchissement rapide et optimisé interface avec adaptation contextuelle manches 31-60 sans recalculs coûteux

10. toggle_auto_update.txt (HybridBaccaratPredictor.toggle_auto_update - Basculer mise à jour automatique)
    - Lignes 2812-2835 dans hbp.py (24 lignes)
    - FONCTION : Active/désactive mise à jour automatique de l'interface avec gestion timer et feedback utilisateur
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec retour immédiat si interface indisponible
      * **BASCULEMENT ÉTAT :** Inverse `self.auto_update_enabled = not self.auto_update_enabled` pour changer mode
      * **GESTION TIMER :** Si `self.auto_update_enabled:` :
        - Démarre timer avec `self.auto_update_timer = self.root.after(1000, self._auto_update_callback)` pour mise à jour périodique
        - Log `logger.info("Mise à jour automatique activée")` pour traçabilité
      * **ARRÊT TIMER :** Si désactivé et `self.auto_update_timer:` :
        - Annule timer avec `self.root.after_cancel(self.auto_update_timer)` pour arrêt propre
        - Remet `self.auto_update_timer = None` pour nettoyage
        - Log `logger.info("Mise à jour automatique désactivée")` pour traçabilité
      * **MISE À JOUR BOUTON :** Met à jour texte bouton avec `button_text = "Désactiver Auto-Update" if self.auto_update_enabled else "Activer Auto-Update"`
      * **FEEDBACK VISUEL :** Change couleur bouton selon état pour indication visuelle claire
      * **GESTION ERREURS :** Capture `Exception as e:` avec logging erreur pour robustesse
    - RETOUR : None - Méthode de contrôle interface ne retourne rien
    - UTILITÉ : Contrôle utilisateur sur fréquence rafraîchissement interface avec gestion timer robuste et feedback visuel

11. toggle_stats_visibility.txt (HybridBaccaratPredictor.toggle_stats_visibility - Basculer visibilité statistiques)
    - Lignes 2837-2865 dans hbp.py (29 lignes)
    - FONCTION : Bascule affichage du panneau de statistiques détaillées avec animation et sauvegarde préférences
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec retour immédiat si interface indisponible
      * **VALIDATION PANNEAU :** Teste `if not hasattr(self, 'stats_frame'):` avec retour si panneau statistiques non initialisé
      * **DÉTECTION ÉTAT :** Vérifie visibilité actuelle avec `is_visible = self.stats_frame.winfo_viewable()` pour déterminer action
      * **MASQUAGE PANNEAU :** Si visible, appelle `self.stats_frame.pack_forget()` pour masquer panneau
      * **AFFICHAGE PANNEAU :** Si masqué, appelle `self.stats_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)` pour réafficher
      * **MISE À JOUR BOUTON :** Change texte bouton avec `button_text = "Masquer Stats" if not is_visible else "Afficher Stats"` pour refléter action suivante
      * **SAUVEGARDE PRÉFÉRENCE :** Met à jour `self.config.show_stats_panel = not is_visible` pour persistance préférences utilisateur
      * **AJUSTEMENT LAYOUT :** Appelle `self.root.update_idletasks()` pour recalcul layout après changement visibilité
      * **LOGGING :** Enregistre `logger.debug(f"Panneau statistiques {'affiché' if not is_visible else 'masqué'}")` pour traçabilité
      * **GESTION ERREURS :** Capture `Exception as e:` avec logging erreur pour robustesse
    - RETOUR : None - Méthode de contrôle interface ne retourne rien
    - UTILITÉ : Optimise espace interface selon besoins utilisateur avec persistance préférences et feedback visuel

12. toggle_training_controls.txt (HybridBaccaratPredictor.toggle_training_controls - Basculer contrôles entraînement)
    - Lignes 2867-2905 dans hbp.py (39 lignes)
    - FONCTION : Active/désactive contrôles d'entraînement selon état système avec validation et feedback visuel
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * enabled (bool) - État souhaité pour les contrôles (True=activé, False=désactivé)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec retour immédiat si interface indisponible
      * **VALIDATION BOUTONS :** Teste existence des boutons d'entraînement avec `if not hasattr(self, 'train_button'):` et retour si non initialisés
      * **ÉTAT BOUTON ENTRAÎNEMENT :** Configure `self.train_button.config(state=tk.NORMAL if enabled else tk.DISABLED)` pour activation/désactivation
      * **ÉTAT BOUTON OPTUNA :** Configure `self.optuna_button.config(state=tk.NORMAL if enabled else tk.DISABLED)` pour optimisation
      * **ÉTAT BOUTON RESET :** Configure `self.reset_button.config(state=tk.NORMAL if enabled else tk.DISABLED)` pour réinitialisation
      * **ÉTAT BOUTON SAUVEGARDE :** Configure `self.save_button.config(state=tk.NORMAL if enabled else tk.DISABLED)` pour sauvegarde
      * **FEEDBACK VISUEL :** Change couleurs boutons selon état :
        - Couleur normale si `enabled` pour indication disponibilité
        - Couleur grisée si `disabled` pour indication indisponibilité
      * **MISE À JOUR TOOLTIPS :** Met à jour tooltips avec messages appropriés selon état
      * **LOGGING :** Enregistre `logger.debug(f"Contrôles d'entraînement {'activés' if enabled else 'désactivés'}")` pour traçabilité
      * **GESTION ERREURS :** Capture `Exception as e:` avec logging erreur pour robustesse
    - RETOUR : None - Méthode de contrôle interface ne retourne rien
    - UTILITÉ : Interface adaptative selon disponibilité fonctionnalités avec feedback visuel et validation robuste

13. ui_operations.txt (HybridBaccaratPredictor.ui_operations - Opérations interface utilisateur)
    - Lignes 2907-2950 dans hbp.py (44 lignes)
    - FONCTION : Gestionnaire central pour opérations interface utilisateur avec routage et validation
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * operation (str) - Type d'opération à exécuter
      * **kwargs - Arguments additionnels pour l'opération
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec retour immédiat si interface indisponible
      * **VALIDATION OPÉRATION :** Teste `if not operation or not isinstance(operation, str):` avec logging erreur si paramètre invalide
      * **ROUTAGE OPÉRATIONS :** Utilise dictionnaire de dispatch pour router vers méthodes appropriées :
        - `'update_display'` → `self.update_display()` pour mise à jour affichage complet
        - `'update_weights'` → `self._update_weights_display()` pour mise à jour poids
        - `'toggle_stats'` → `self.toggle_stats_visibility()` pour basculer statistiques
        - `'toggle_training'` → `self.toggle_training_controls(**kwargs)` pour contrôles entraînement
        - `'show_dashboard'` → `self.show_models_dashboard()` pour tableau de bord modèles
      * **VALIDATION MÉTHODE :** Vérifie existence méthode avec `if hasattr(self, method_name) and callable(getattr(self, method_name)):`
      * **EXÉCUTION SÉCURISÉE :** Appelle méthode avec `method = getattr(self, method_name)` puis `method(**kwargs)` avec gestion erreurs
      * **LOGGING OPÉRATIONS :** Enregistre `logger.debug(f"Opération UI exécutée: {operation}")` pour traçabilité
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de l'opération UI '{operation}': {e}")` pour robustesse
      * **OPÉRATIONS NON RECONNUES :** Log warning pour opérations non supportées avec liste des opérations disponibles
    - RETOUR : bool - True si opération exécutée avec succès, False sinon
    - UTILITÉ : Coordination centralisée des interactions utilisateur avec routage sécurisé et validation complète

14. update_ui.txt (HybridBaccaratPredictor.update_ui - Mise à jour interface complète)
    - FONCTION : Met à jour complètement tous éléments de l'interface utilisateur
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * force_refresh (bool, optionnel) - Force rafraîchissement complet (défaut: False)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VÉRIFICATION THREAD :** Assure exécution dans thread principal UI
      * **MISE À JOUR PRÉDICTIONS :** Actualise affichage prédictions actuelles
      * **RAFRAÎCHISSEMENT STATISTIQUES :** Met à jour panneau statistiques
      * **MISE À JOUR GRAPHIQUE :** Actualise graphique matplotlib si visible
      * **SYNCHRONISATION ÉTAT :** Synchronise état widgets avec système
      * **OPTIMISATION PERFORMANCE :** Évite mises à jour inutiles si pas de changement
    - RETOUR : None - Méthode de mise à jour interface
    - UTILITÉ : Point d'entrée principal pour rafraîchissement interface complète

15. setup_auto_update.txt (HybridBaccaratPredictor.setup_auto_update - Configuration mise à jour automatique)
    - FONCTION : Configure système de mise à jour automatique de l'interface
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * interval_ms (int, optionnel) - Intervalle en millisecondes (défaut: 1000)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CONFIGURATION TIMER :** Configure timer Tkinter pour mises à jour périodiques
      * **GESTION CALLBACKS :** Définit callback pour mise à jour automatique
      * **OPTIMISATION RESSOURCES :** Évite mises à jour si interface non visible
      * **CONTRÔLE FRÉQUENCE :** Adapte fréquence selon charge système
      * **GESTION ERREURS :** Capture erreurs pour éviter blocage interface
    - RETOUR : None - Méthode de configuration
    - UTILITÉ : Automatisation rafraîchissement interface pour expérience fluide

16. update_device_selection.txt (HybridBaccaratPredictor.update_device_selection - MAJ sélection dispositif)
    - Lignes 2952-2995 dans hbp.py (44 lignes)
    - FONCTION : Met à jour sélection dispositif (CPU/GPU) avec déplacement modèles et vidage cache pour cohérence
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **SAUVEGARDE INITIAL :** Stocke `initial_device = self.device` pour comparaison changement
      * **LECTURE SÉLECTION :** Récupère `selected_device_type = self.device_choice.get()` depuis interface utilisateur
      * **VALIDATION CUDA :** Si `selected_device_type == "cuda"` :
        - Teste `if torch.cuda.is_available():` pour disponibilité GPU
        - Définit `new_device = torch.device("cuda")` si disponible
        - Sinon log warning et fallback `new_device = torch.device("cpu")` avec mise à jour `self.device_choice.set("cpu")`
      * **SÉLECTION CPU :** Si `selected_device_type == "cpu"`, définit `new_device = torch.device("cpu")` directement
      * **DÉTECTION CHANGEMENT :** Teste `if new_device != initial_device:` pour éviter opérations inutiles
      * **MISE À JOUR DEVICE :** Applique `self.device = new_device` et log `logger.info(f"Device sélectionné mis à jour: {self.device}")`
      * **DÉPLACEMENT LSTM :** Si `self.lstm:` existe :
        - Utilise `with self.model_lock:` pour thread-safety
        - Appelle `self.lstm.to(self.device)` pour déplacement modèle
        - Marque `lstm_moved = True` pour tracking
        - Log succès ou capture `Exception` avec logging erreur
      * **VIDAGE CACHE CONDITIONNEL :** Si `lstm_moved:` :
        - Utilise `with self.model_lock:` pour cohérence
        - Vide `self.lgbm_cache = deque(maxlen=100)` car changement device peut affecter compatibilité cache
        - Log `logger.info(f"Cache LGBM vidé car le device a changé et l'LSTM a été déplacé vers {self.device}")`
      * **LOGGING INCHANGÉ :** Si device identique, log debug "Device sélectionné identique au device actuel. Aucun changement."
    - RETOUR : None - Méthode de configuration ne retourne rien
    - UTILITÉ : Interface pour configuration ressources de calcul avec déplacement automatique modèles et cohérence cache

17. on_close.txt (HybridBaccaratPredictor.on_close - Gestionnaire fermeture)
    - Lignes 10897-10964 dans hbp.py (68 lignes)
    - FONCTION : Gère fermeture propre application avec vérification tâches en cours et nettoyage ressources
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `should_close = True` et `ui_available = self.is_ui_available()` pour contrôle fermeture
      * **VÉRIFICATION TÂCHES :** Utilise `with self.training_lock:` pour vérifier `task_running = self.is_training or self.is_fast_updating`
      * **DÉTECTION TYPE TÂCHE :** Identifie `task_type = "Entraînement complet" if self.is_training else "Mise à jour rapide" if self.is_fast_updating else "Aucune"`
      * **DIALOGUE CONFIRMATION :** Si tâche active et UI disponible, affiche `messagebox.askyesno()` avec message "Une tâche ML est en cours. Voulez-vous vraiment fermer ?"
      * **ARRÊT FORCÉ :** Si utilisateur confirme, active `self.stop_training = True` et `self.stop_fast_update = True` pour arrêt gracieux
      * **ATTENTE ARRÊT :** Boucle `while (self.is_training or self.is_fast_updating) and wait_time < max_wait_time:` avec `time.sleep(0.1)` et `wait_time += 0.1`
      * **TIMEOUT GESTION :** Si `wait_time >= max_wait_time:`, log warning et continue fermeture malgré tâches actives
      * **NETTOYAGE THREADS :** Appelle `self._cleanup_threads()` pour arrêt propre threads secondaires
      * **NETTOYAGE RESSOURCES :** Exécute `self._cleanup_resources()` pour libération mémoire et ressources système
      * **FERMETURE FENÊTRE :** Si `should_close and ui_available:`, appelle `self.root.quit()` puis `self.root.destroy()` pour fermeture Tkinter
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de la fermeture: {e}", exc_info=True)` et fermeture forcée
      * **LOGGING FINAL :** Enregistre "Application fermée proprement." ou "Fermeture forcée après erreur."
    - RETOUR : None - Gestionnaire d'événement ne retourne rien
    - UTILITÉ : Fermeture sécurisée avec arrêt gracieux tâches ML, nettoyage complet ressources et gestion erreurs

18. _setup_ui_variables.txt (HybridBaccaratPredictor._setup_ui_variables - Configuration variables UI)
    - Lignes 2021-2058 dans hbp.py (38 lignes)
    - FONCTION : Initialise toutes les variables Tkinter pour interface utilisateur avec configuration ressources système
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VARIABLES PRÉDICTION :** Crée `self.pred_vars` avec dictionnaire contenant :
        - `'round': tk.StringVar(value="Manche: 0")` pour numéro manche actuelle
        - `'player': tk.StringVar(value="Player: 50.0%")` et `'banker': tk.StringVar(value="Banker: 50.0%")` pour probabilités
        - `'confidence': tk.StringVar(value="Confiance: N/A")` et `'recommendation': tk.StringVar(value="Recommandation: En attente...")` pour état
      * **VARIABLES STATISTIQUES :** Initialise `self.stats_vars` avec métriques :
        - `'streak': tk.StringVar(value="Série actuelle: -")` pour série en cours
        - `'accuracy': tk.StringVar(value="Précision (Session): N/A")` pour précision session
        - `'model_weights': tk.StringVar(value="Poids: Chargement...")` pour poids modèles
        - `'uncertainty': tk.StringVar(value="Incertitude Prediction: N/A")` pour incertitude
        - `'method_acc': tk.StringVar(value="Précisions Méthodes: N/A")` pour précisions par méthode
        - `'game_stats': tk.StringVar(value="Partie: P 0 (0.0%) | B 0 (0.0%)")` pour stats partie
      * **CONFIGURATION DEVICE :** Définit `self.use_cpu = tk.BooleanVar(value=(self.device.type == 'cpu'))` et `self.use_gpu = tk.BooleanVar(value=(self.device.type == 'cuda'))` selon device détecté
      * **CONFIGURATION CPU :** Calcule `default_cores_config = getattr(self.config, 'default_cpu_cores', 2)`, `max_logical_cores = psutil.cpu_count(logical=True)` puis `initial_cores_val = max(1, min(default_cores_config, max_logical_cores))` et `self.cpu_cores = tk.IntVar(value=initial_cores_val)`
      * **CONFIGURATION MÉMOIRE :** Détecte `total_sys_mem_gb = max(1, int(psutil.virtual_memory().total / (1024**3)))`, calcule `configured_mem_gb = getattr(self.config, 'default_max_memory_gb', total_sys_mem_gb // 2)` puis `initial_mem_val = max(1, configured_mem_gb)` et `self.max_mem = tk.IntVar(value=initial_mem_val)`
      * **VALIDATION MÉMOIRE :** Vérifie `if initial_mem_val > total_sys_mem_gb:` avec warning si guideline RAM dépasse RAM système
      * **VARIABLES CONTRÔLE :** Initialise `self.auto_update_enabled = tk.BooleanVar(value=False)` pour mises à jour automatiques
      * **VARIABLES PROGRESSION :** Crée `self.progress_var = tk.DoubleVar(value=0)` et `self.progress_label_var = tk.StringVar(value="Prêt")` pour barre progression
      * **LOGGING CONFIGURATION :** Enregistre `logger.info(f"Variables ressources UI: CPU Cores Init={initial_cores_val} (Max Logic={max_logical_cores}), RAM Guideline Init={initial_mem_val} Go (Total Sys={total_sys_mem_gb} Go)")`
    - RETOUR : None - Méthode d'initialisation ne retourne rien
    - UTILITÉ : Configuration complète variables UI avec détection automatique ressources système et validation cohérence

19. _setup_ui_variables_1.txt (HybridBaccaratPredictor._setup_ui_variables_1 - Configuration variables UI v1)
    - Lignes 2101-2156 dans hbp.py (56 lignes)
    - FONCTION : Version alternative configuration variables interface avec variables étendues pour métriques avancées
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VARIABLES PRÉDICTION :** Identique à version standard avec `self.pred_vars` contenant round, player, banker, confidence, recommendation
      * **VARIABLES STATISTIQUES ÉTENDUES :** Crée `self.stats_vars` avec métriques supplémentaires :
        - Variables standard : streak, accuracy, model_weights, uncertainty, method_acc, game_stats
        - `'method_conf': tk.StringVar(value="Confiance Méthodes: N/A")` pour confiance par méthode
        - `'uncertainty_details': tk.StringVar(value="Incertitude Détaillée: N/A")` pour détails incertitude
        - `'adaptive_threshold': tk.StringVar(value="Seuil Adaptatif: N/A")` pour seuils adaptatifs
        - `'bayesian_weights': tk.StringVar(value="Poids Bayésiens: N/A")` pour poids bayésiens
      * **CONFIGURATION RESSOURCES IDENTIQUE :** Même logique détection CPU/GPU, calcul cores et mémoire avec validation
      * **VARIABLES CONTRÔLE IDENTIQUES :** Même configuration auto_update_enabled, progress_var, progress_label_var
      * **DIFFÉRENCE PRINCIPALE :** Extension variables statistiques pour métriques ML avancées (confiance méthodes, incertitude détaillée, seuils adaptatifs, poids bayésiens)
      * **LOGGING IDENTIQUE :** Même enregistrement configuration ressources système
    - RETOUR : None - Méthode d'initialisation ne retourne rien
    - UTILITÉ : Configuration avancée variables UI avec métriques ML étendues pour analyse approfondie performance

20. _update_weights_display_1.txt (HybridBaccaratPredictor._update_weights_display_1 - MAJ affichage poids v1)
    - Lignes 2997-3025 dans hbp.py (29 lignes)
    - FONCTION : Version améliorée mise à jour affichage poids avec protection thread-safe et gestion erreurs Tkinter
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION PRÉREQUIS :** Vérifie `if not hasattr(self, 'stats_vars') or not hasattr(self, 'weights'):` avec retour si UI non prête ou poids non initialisés
      * **ACQUISITION POIDS :** Utilise `with self.weights_lock:` puis `weights_to_display = self.weights.copy()` pour copie thread-safe
      * **FORMATAGE POIDS :** Itère `for method in sorted(weights_to_display.keys()):` pour ordre cohérent :
        - Extrait `weight = weights_to_display[method]` pour chaque méthode
        - Formate avec `weights_text_parts.append(f"{method.capitalize()}({weight * 100:.1f}%)")` pour lisibilité
      * **MISE À JOUR SÉCURISÉE :** Dans bloc `try:` :
        - Vérifie `if self.stats_vars.get('model_weights'):` pour existence variable Tkinter
        - Met à jour avec `self.stats_vars['model_weights'].set(f"Poids: {' | '.join(weights_text_parts)}")` pour affichage
      * **GESTION ERREURS TKINTER :** Capture `tk.TclError as e:` avec `logger.warning(f"Erreur Tkinter mise à jour affichage poids (fenêtre fermée?): {e}")` pour fermeture fenêtre
      * **GESTION ERREURS GÉNÉRALES :** Capture `Exception as e:` avec `logger.error(f"Erreur inattendue _update_weights_display: {e}", exc_info=False)` pour autres erreurs
      * **ROBUSTESSE UI :** Évite crash application si interface fermée pendant mise à jour
    - RETOUR : None - Met à jour directement les variables d'affichage Tkinter
    - UTILITÉ : Visualisation optimisée poids avec protection robuste contre erreurs UI et thread-safety

21. _safe_update_progress.txt (HybridBaccaratPredictor._safe_update_progress - MAJ progression sécurisée)
    - Lignes 3027-3055 dans hbp.py (29 lignes)
    - FONCTION : Mise à jour progression avec protection thread-safe et validation UI pour éviter conflits concurrence
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * value (int) - Valeur progression entre 0 et 100
      * message (str) - Message de statut à afficher
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec retour immédiat si interface indisponible
      * **VALIDATION PARAMÈTRES :** Teste `if value is None or message is None:` avec retour si paramètres invalides
      * **NORMALISATION VALEUR :** Calcule `value_clamped = max(0, min(100, int(value)))` pour borner entre 0-100
      * **TRONCATURE MESSAGE :** Applique `message_truncated = str(message)[:100]` pour éviter débordement affichage
      * **FONCTION INTERNE :** Définit `def safe_update():` comme fonction interne pour exécution thread-safe
      * **MISE À JOUR PROGRESSION :** Dans fonction interne, teste `if hasattr(self, 'progress_var') and self.progress_var:` puis `self.progress_var.set(value_clamped)`
      * **MISE À JOUR MESSAGE :** Teste `if hasattr(self, 'progress_label_var') and self.progress_label_var:` puis `self.progress_label_var.set(message_truncated)`
      * **RAFRAÎCHISSEMENT :** Appelle `self.root.update_idletasks()` pour mise à jour immédiate si UI disponible
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de la mise à jour sécurisée de la progression: {e}")` pour robustesse
      * **EXÉCUTION THREAD-SAFE :** Appelle `self.root.after(0, safe_update)` pour exécution dans thread UI principal
    - RETOUR : None - Méthode de mise à jour interface ne retourne rien
    - UTILITÉ : Évite conflits thread lors mise à jour progression avec validation complète et gestion d'erreurs robuste

22. _update_prediction_progress.txt (HybridBaccaratPredictor._update_prediction_progress - MAJ progression prédiction)
    - Lignes 11708-11732 dans hbp.py (25 lignes)
    - FONCTION : Met à jour et affiche la progression des prédictions avec calcul de taux et affichage périodique optimisé
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INCRÉMENTATION COMPTEUR :** Exécute `self._total_predictions += 1` pour suivre nombre total de prédictions effectuées
      * **VÉRIFICATION AFFICHAGE :** Contrôle `current_time = time.time()` et conditions d'affichage périodique pour éviter spam logs
      * **CONDITIONS AFFICHAGE :** Vérifie `(self._total_predictions - self._last_progress_update >= self._progress_update_interval or current_time - self._last_progress_time >= 5.0)` pour affichage conditionnel
      * **CALCUL TAUX :** Si conditions remplies, calcule `elapsed_time = current_time - self._last_progress_time` et `predictions_rate = (self._total_predictions - self._last_progress_update) / max(elapsed_time, 0.1)` pour taux prédictions/seconde
      * **MISE À JOUR VARIABLES :** Met à jour `self._last_progress_update = self._total_predictions` et `self._last_progress_time = current_time` pour tracking
      * **AFFICHAGE PROGRESSION :** Appelle `logger.info(f"Progression prédictions: {self._total_predictions} total ({predictions_rate:.1f}/s)")` pour feedback utilisateur
      * **OPTIMISATION PERFORMANCE :** Utilise affichage périodique plutôt que log par prédiction pour éviter surcharge I/O
      * **GESTION DIVISION ZÉRO :** Protège calcul taux avec `max(elapsed_time, 0.1)` pour éviter division par zéro
      * **INITIALISATION VARIABLES :** Assure que `_progress_update_interval` (défaut: 100) et autres variables tracking sont initialisées
    - RETOUR : None - Méthode de progression ne retourne rien
    - UTILITÉ : Monitoring performance prédictions avec affichage optimisé et calcul taux temps réel sans surcharge système

23. _reset_session_display.txt (HybridBaccaratPredictor._reset_session_display - Reset affichage session)
    - Lignes 12128-12212 dans hbp.py (85 lignes)
    - FONCTION : Réinitialise uniquement l'affichage visuel interface à état "Nouvelle Partie" sans modifier données internes
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `ui_available = self.is_ui_available()` et retourne si interface indisponible
      * **VALIDATION VARIABLES :** Contrôle existence `pred_vars`, `stats_vars`, `bg_color_mpl`, `fg_color_mpl` avec warning si manquantes
      * **RESET PRÉDICTIONS :** Met à jour variables prédiction :
        - `self.pred_vars['round'].set("Manche: 0")` pour numéro manche
        - `self.pred_vars['player'].set("Player: N/A")` et `self.pred_vars['banker'].set("Banker: N/A")` pour probabilités
        - `self.pred_vars['confidence'].set("Confiance: N/A")` et `self.pred_vars['recommendation'].set("Recommandation: En attente...")` pour état
      * **RESET STATISTIQUES :** Réinitialise variables statistiques session :
        - `self.stats_vars['streak'].set("Série actuelle: -")` pour série
        - `self.stats_vars['accuracy'].set("Précision (Session): N/A")` pour précision
        - `self.stats_vars['game_stats'].set("Partie: P 0 (0.0%) | B 0 (0.0%)")` pour stats partie
        - Variables incertitude, méthodes, seuils adaptatifs et poids bayésiens à "N/A"
      * **RESET GRAPHIQUE :** Si `hasattr(self, 'ax') and hasattr(self, 'canvas')` :
        - Utilise couleurs fixes `bg_color_mpl = self.bg_color_mpl` et `fg_color_mpl = self.fg_color_mpl`
        - Appelle `self.ax.clear()` puis `self.ax.set_facecolor(bg_color_mpl)`
        - Affiche texte centré `'Prêt (Nouvelle Partie)'` avec `self.ax.text(0.5, 0.5, ...)`
        - Efface labels/ticks avec `self.ax.set_xlabel('')`, `self.ax.set_xticks([])`, etc.
        - Configure couleurs spines avec `self.ax.spines['bottom'].set_color(fg_color_mpl)`
        - Appelle `self.canvas.draw_idle()` avec gestion erreur
      * **RESET PROGRESSION :** Met `progress_var` à 100 et `progress_label_var` à "Prêt (Nouvelle Partie)"
      * **RAFRAÎCHISSEMENT :** Appelle `self.root.update_idletasks()` pour mise à jour immédiate
    - RETOUR : None - Méthode d'affichage ne retourne rien
    - UTILITÉ : Nettoyage visuel complet interface pour nouveau démarrage sans impact sur données chargées

24. _auto_update_callback.txt (HybridBaccaratPredictor._auto_update_callback - Callback mise à jour auto)
    - Lignes 11699-11705 dans hbp.py (7 lignes)
    - FONCTION : Callback obsolète pour mise à jour automatique, remplacé par système événementiel lors enregistrement résultats
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **LOGGING DEBUG :** Enregistre `logger.debug("_auto_update_callback: Cette méthode n'est plus utilisée pour les mises à jour automatiques.")` pour traçabilité
      * **MÉTHODE OBSOLÈTE :** Note explicite que méthode n'est plus utilisée pour déclencher mises à jour automatiques
      * **NOUVEAU SYSTÈME :** Les mises à jour sont maintenant déclenchées uniquement lors de l'enregistrement de nouveaux résultats via `auto_fast_update_if_needed()`
      * **CONSERVATION CODE :** Méthode conservée pour compatibilité mais sans fonctionnalité active
    - RETOUR : None - Callback obsolète ne retourne rien
    - UTILITÉ : Méthode legacy conservée pour compatibilité, remplacée par système événementiel plus efficace

25. show_model_hyperparameters.txt (HybridBaccaratPredictor.show_model_hyperparameters - Affichage hyperparamètres)
    - Lignes 3140-3291 dans hbp.py (152 lignes)
    - FONCTION : Affiche fenêtre dédiée hyperparamètres modèle avec métadonnées JSON ou extraction directe du fichier modèle
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * model_filepath (str) - Chemin du fichier modèle pour affichage hyperparamètres
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec warning et retour si interface indisponible
      * **RECHERCHE MÉTADONNÉES :** Construit `json_filepath = os.path.splitext(model_filepath)[0] + '.json'` pour fichier métadonnées associé
      * **CHARGEMENT JSON :** Si `os.path.exists(json_filepath)`, charge avec `json.load(f)` et logging succès
      * **FALLBACK MODÈLE :** Si métadonnées absentes, charge directement modèle :
        - Détecte format avec `model_filepath.endswith('.joblib')` pour Joblib vs Pickle
        - Extrait `config_details` depuis package chargé
        - Crée métadonnées minimales avec timestamp, nom fichier, hyperparamètres
      * **CRÉATION FENÊTRE :** Instancie `hyperparams_window = tk.Toplevel(self.root)` avec titre et géométrie 800x600
      * **INTERFACE TEXTE :** Crée `tk.Text` avec scrollbar et configuration styles :
        - Tags "title", "section", "normal", "value" avec polices différenciées
        - Affichage informations générales : fichier, date, version
      * **SECTIONS ORGANISÉES :** Affiche par catégories :
        - **PERFORMANCE :** Métriques globales et par méthode
        - **LSTM :** Paramètres commençant par 'lstm_'
        - **LGBM :** Paramètres commençant par 'lgbm_'
        - **MARKOV :** Paramètres Markov et ordre maximal
        - **SEUILS :** Paramètres threshold, confidence, uncertainty
        - **POIDS :** Paramètres weights et poids modèles
        - **AUTRES :** Paramètres non catégorisés
      * **BOUTONS ACTION :** Frame avec boutons :
        - "Appliquer ces hyperparamètres" → `self._apply_hyperparameters_from_metadata(metadata)`
        - "Charger ce modèle" → `self.load_trained_models(model_filepath)`
        - "Fermer" → `hyperparams_window.destroy()`
      * **GESTION ERREURS :** Capture exceptions avec messagebox d'erreur et logging détaillé
    - RETOUR : None - Affiche fenêtre modale
    - UTILITÉ : Interface complète visualisation et application hyperparamètres avec fallback robuste et organisation catégorielle

26. show_text_report.txt (HybridBaccaratPredictor.show_text_report - Affichage rapport texte)
    - Lignes 1121-1163 dans hbp.py (43 lignes)
    - FONCTION : Affiche rapport textuel dans fenêtre popup avec interface lecture seule et scrollbar pour rapports longs
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * report (str) - Contenu du rapport à afficher
      * title (str, optionnel) - Titre de la fenêtre (défaut: "Rapport")
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `if not self.is_ui_available():` avec warning et retour si interface indisponible
      * **CRÉATION FENÊTRE :** Instancie `report_window = tk.Toplevel(self.root)` avec `report_window.title(title)` et `report_window.geometry("800x600")`
      * **FRAME CONTENEUR :** Crée `frame = ttk.Frame(report_window)` avec `frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)` pour marges
      * **SCROLLBAR VERTICALE :** Ajoute `scrollbar = ttk.Scrollbar(frame)` avec `scrollbar.pack(side=tk.RIGHT, fill=tk.Y)` pour navigation
      * **WIDGET TEXTE :** Instancie `text_widget = tk.Text(frame, wrap=tk.WORD, yscrollcommand=scrollbar.set, font=('Courier New', 10))` avec :
        - `wrap=tk.WORD` pour retour ligne intelligent
        - Police monospace 'Courier New' pour formatage préservé
        - Liaison scrollbar avec `yscrollcommand=scrollbar.set`
      * **CONFIGURATION SCROLLBAR :** Connecte avec `scrollbar.config(command=text_widget.yview)` pour synchronisation
      * **INSERTION CONTENU :** Utilise `text_widget.insert(tk.END, report)` pour affichage rapport complet
      * **MODE LECTURE SEULE :** Configure `text_widget.config(state=tk.DISABLED)` pour empêcher modification tout en permettant copie
      * **BOUTON FERMETURE :** Ajoute `ttk.Button(report_window, text="Fermer", command=report_window.destroy)` pour fermeture propre
    - RETOUR : None - Affiche fenêtre modale
    - UTILITÉ : Interface standardisée pour affichage rapports textuels avec navigation et formatage préservé

27. _show_optuna_results_window.txt (HybridBaccaratPredictor._show_optuna_results_window - Fenêtre résultats Optuna)
    - Lignes 1121-1408 dans hbp.py (288 lignes)
    - FONCTION : Affiche fenêtre dédiée aux résultats d'optimisation Optuna avec interface complète de gestion des paramètres
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * best_params (Dict) - Meilleurs paramètres trouvés par optimisation
      * study_name (str, optionnel) - Nom de l'étude Optuna
      * duration (float, optionnel) - Durée de l'optimisation en secondes
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CRÉATION FENÊTRE :** Instancie `result_window = tk.Toplevel(self.root)` avec configuration `result_window.title("Résultats de l'Optimisation Optuna")`, `result_window.geometry("800x600")`, `result_window.resizable(True, True)`
      * **FRAME PRINCIPAL :** Crée `main_frame = ttk.Frame(result_window)` avec `main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)`
      * **EN-TÊTE INFORMATIF :** Ajoute titre avec `ttk.Label(main_frame, text="Résultats de l'Optimisation", font=('Arial', 16, 'bold')).pack(pady=(0, 10))`
      * **INFORMATIONS ÉTUDE :** Si `study_name:`, affiche `ttk.Label(main_frame, text=f"Étude: {study_name}").pack(anchor=tk.W)`
      * **DURÉE OPTIMISATION :** Si `duration:`, formate et affiche `ttk.Label(main_frame, text=f"Durée: {duration:.2f} secondes").pack(anchor=tk.W)`
      * **SECTION PARAMÈTRES :** Crée `ttk.Label(main_frame, text="Meilleurs Paramètres:", font=('Arial', 12, 'bold')).pack(anchor=tk.W, pady=(20, 5))`
      * **ZONE TEXTE PARAMÈTRES :** Instancie `all_params_text = tk.Text(main_frame, height=15, width=70, wrap=tk.WORD)` avec scrollbar `scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=all_params_text.yview)`
      * **FORMATAGE PARAMÈTRES :** Itère sur `best_params.items()` pour formater avec `params_str += f"{param}: {value}\n"` puis insère avec `all_params_text.insert(tk.END, params_str)`
      * **BOUTONS ACTION :** Crée `button_frame = ttk.Frame(main_frame)` avec bouton "Sauvegarder les Paramètres" qui appelle `self._save_params_to_file(best_params)` et affiche confirmation
      * **BOUTON FERMETURE :** Ajoute `ttk.Button(button_frame, text="Fermer", command=result_window.destroy)`
      * **GESTION ERREURS :** Capture exceptions avec logging et fallback vers messagebox simple
    - RETOUR : None - Affiche fenêtre modale
    - UTILITÉ : Interface complète pour visualisation, sauvegarde et gestion des résultats d'optimisation Optuna

28. _show_selected_model_details.txt (HybridBaccaratPredictor._show_selected_model_details - Détails modèle sélectionné)
    - Lignes 3492-3518 dans hbp.py (27 lignes)
    - FONCTION : Affiche détails complets du modèle sélectionné depuis tableau de bord via fenêtre hyperparamètres dédiée
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * tree (ttk.Treeview) - Widget Treeview contenant la liste des modèles disponibles
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SÉLECTION :** Vérifie `selected_items = tree.selection()` puis `if not selected_items:` avec message "Veuillez sélectionner un modèle dans la liste."
      * **EXTRACTION DONNÉES :** Récupère `item = selected_items[0]` puis `values = tree.item(item, 'values')` pour obtenir informations ligne sélectionnée
      * **VALIDATION VALUES :** Teste `if not values: return` pour s'assurer que données sont disponibles
      * **RÉCUPÉRATION NOM :** Extrait `filename = values[0]` pour nom fichier modèle depuis première colonne Treeview
      * **CONSTRUCTION CHEMIN :** Forme `model_path = os.path.join(os.getcwd(), "models", filename)` pour chemin complet vers fichier modèle
      * **VALIDATION EXISTENCE :** Vérifie `if not os.path.exists(model_path):` avec message d'erreur "Le fichier {filename} n'existe pas."
      * **DÉLÉGATION AFFICHAGE :** Appelle `self.show_model_hyperparameters(model_path)` pour ouvrir fenêtre détaillée avec hyperparamètres, métriques et configuration
      * **GESTION ERREURS :** Utilise messagebox pour notifications utilisateur en cas d'erreur ou fichier manquant
      * **INTÉGRATION TABLEAU :** Fonction callback utilisée par bouton "Voir les détails" dans `show_models_dashboard`
    - RETOUR : None - Ouvre fenêtre dédiée pour affichage détails
    - UTILITÉ : Interface détaillée pour exploration modèles avec hyperparamètres, métriques performance et métadonnées complètes

29. update_statistics.txt (HybridBaccaratPredictor.update_statistics - Mise à jour statistiques)
    - Lignes 11529-11666 dans hbp.py (138 lignes)
    - FONCTION : Met à jour panneau statistiques avec métriques actuelles et calculs de performance détaillés
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `if not hasattr(self, 'stats_vars'): return` pour s'assurer que variables statistiques existent
      * **PROTECTION THREAD :** Utilise `with self.sequence_lock:` pour accès sécurisé aux données de séquence
      * **CALCUL SÉRIE ACTUELLE :** Détermine série en cours avec `current_streak_length = 1`, `current_streak_type = 'none'` puis parcourt séquence pour détecter continuité
      * **CALCUL PRÉCISION SESSION :** Si `self.session_predictions:` :
        - Calcule `correct_predictions = sum(1 for pred in self.session_predictions if pred.get('correct', False))` pour prédictions correctes
        - Détermine `session_accuracy = correct_predictions / len(self.session_predictions)` pour taux réussite
        - Formate `accuracy_text = f"Précision (Session): {session_accuracy*100:.1f}% ({correct_predictions}/{len(self.session_predictions)})"` pour affichage
      * **CALCUL POIDS MODÈLES :** Extrait poids depuis `self.method_weights` avec formatage `weights_text = f"LGBM: {lgbm_weight:.1f}%, LSTM: {lstm_weight:.1f}%, Markov: {markov_weight:.1f}%"`
      * **CALCUL INCERTITUDE :** Si dernière prédiction disponible, extrait métriques incertitude avec `epistemic_uncertainty`, `aleatoric_uncertainty` et formate pour affichage
      * **PRÉCISIONS PAR MÉTHODE :** Calcule précision individuelle pour LGBM, LSTM, Markov depuis historique prédictions avec comptage succès/total
      * **STATISTIQUES PARTIE :** Compte Player/Banker dans séquence actuelle avec `player_count = self.sequence.count('player')`, calcul pourcentages et formatage
      * **MISE À JOUR VARIABLES :** Met à jour toutes variables `self.stats_vars` avec valeurs calculées :
        - `self.stats_vars['streak'].set(f"Série actuelle: {current_streak_type.title()} {current_streak_length}")`
        - `self.stats_vars['accuracy'].set(accuracy_text)` pour précision session
        - `self.stats_vars['model_weights'].set(weights_text)` pour poids modèles
        - Variables incertitude, méthodes, stats partie selon calculs
      * **GESTION ERREURS :** Capture exceptions avec `except Exception as e:` et `logger.error(f"Erreur lors de la mise à jour des statistiques: {e}")` avec valeurs par défaut
    - RETOUR : None - Méthode de mise à jour interface ne retourne rien
    - UTILITÉ : Maintient statistiques complètes à jour pour suivi performance temps réel avec calculs détaillés et gestion erreurs robuste

================================================================================
SECTION 5 : OPTIMISATION ENTRAINEMENT
================================================================================

1. run_hyperparameter_optimization.txt (HybridBaccaratPredictor.run_hyperparameter_optimization - Optimisation Optuna multi-niveaux)
   - Lignes 12836-13032 dans hbp.py (197 lignes)
   - FONCTION : Lance l'optimisation des hyperparamètres avec stratégie multi-niveaux adaptée CPU via OptunaThreadManager
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * n_trials (int, optionnel) - Nombre d'essais à effectuer (défaut: 20)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION CONCURRENCE :** Vérifie `with self.training_lock:` puis `if self.is_training or self.is_fast_updating:` avec warning et retour si tâche active
     * **VALIDATION DONNÉES :** Contrôle `with self.sequence_lock:` pour vérifier `if self.loaded_historical and self.historical_data:` avec minimum 10 jeux
     * **CONFIRMATION UTILISATEUR :** Affiche `messagebox.askyesno` avec avertissement "L'optimisation multi-niveaux (LGBM+LSTM) peut prendre BEAUCOUP de temps"
     * **PRÉPARATION DONNÉES :** Appelle `training_data_package = self._prepare_training_data(force_use_historical=True)` pour package complet avec validation 8 éléments
     * **CONFIGURATION OPTUNA :** Définit `advanced_options` avec :
       - `enable_multi_level=True` pour optimisation multi-niveaux
       - `enable_adaptive_regularization=True` pour régularisation adaptative
       - `enable_swa=True` pour Stochastic Weight Averaging
       - `enable_meta_learning=True` pour méta-apprentissage
       - `enable_temporal_cv=True` pour validation croisée temporelle
       - `cpu_count=8`, `ram_gb=28`, `batch_size=1024` pour ressources système
     * **INITIALISATION OPTIMIZER :** Importe `OptunaOptimizer` et `OptunaThreadManager`, réinitialise compteurs avec `OptunaOptimizer.optimized_viable_trials_count = 0` et `OptunaOptimizer.total_attempts_made = 0`
     * **ASSIGNATION DONNÉES :** Configure `optimizer_instance` avec :
       - `X_lgbm_full = X_lgbm_all` (features LGBM complètes)
       - `y_full = y_all` (labels complets)
       - `X_lstm_full = X_lstm_all` (features LSTM complètes)
       - `train_indices = train_idx` et `val_indices = val_idx` (indices split temporel)
       - `predictor_ref = self` pour référence au prédicateur
     * **CALLBACKS CONFIGURATION :** Définit callbacks asynchrones :
       - `success_callback(best_params, duration)` qui appelle `self._finalize_optuna_optimization(True, best_params, duration, None)`
       - `error_callback(error_msg, best_params, duration)` qui appelle `self._finalize_optuna_optimization(False, best_params, duration, error_msg)`
       - `progress_callback(progress, message)` qui met à jour interface avec `self._update_progress(progress, message)`
     * **LANCEMENT THREAD :** Appelle `self.optuna_thread_manager.run_optimization()` avec optimizer_instance, n_trials et callbacks
     * **GESTION ÉTAT :** Met `self.is_training = True`, `self.stop_training = False`, `self.is_optuna_running = True` et désactive contrôles UI
     * **RÉINITIALISATION FLAGS :** Supprime `_logged_sequence_length` et `_logged_compat_sequence_length` pour permettre nouveaux logs
     * **GESTION ERREURS :** Capture `ImportError` pour OptunaThreadManager avec messagebox d'erreur et logging détaillé
   - RETOUR : None - Méthode d'interface utilisateur asynchrone avec callbacks
   - UTILITÉ : Optimisation complète hyperparamètres avec stratégie multi-niveaux, gestion ressources CPU/RAM et exécution thread séparé

2. init_ml_models.txt (HybridBaccaratPredictor.init_ml_models - Initialisation complète modèles ML)
   - Lignes 1589-1795 dans hbp.py (207 lignes)
   - FONCTION : Initialise ou réinitialise tous les modèles ML (LGBM, LSTM, Markov) avec protection contre récursion et gestion complète des erreurs
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * force_reinit (bool, optionnel) - Force réinitialisation même si déjà initialisé (défaut: False)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION RÉCURSION :** Vérifie `if getattr(self, '_initializing_models', False): return` pour éviter appels récursifs
     * **MARQUAGE INITIALISATION :** Définit `self._initializing_models = True` pendant processus
     * **RÉINITIALISATION CONDITIONNELLE :** Si `force_reinit`, supprime attributs existants avec `delattr(self, attr)`
     * **INITIALISATION LGBM :** Crée `LGBMClassifier(n_estimators=self.config.lgbm_n_estimators, learning_rate=self.config.lgbm_learning_rate, max_depth=self.config.lgbm_max_depth, random_state=42, n_jobs=-1, verbose=-1)`
     * **INITIALISATION LSTM :** Configure `Sequential([LSTM(self.config.lstm_units, return_sequences=False, input_shape=(self.config.lstm_sequence_length, self.config.lstm_input_size)), Dropout(self.config.lstm_dropout), Dense(self.config.lstm_dense_units, activation='relu'), Dense(2, activation='softmax')])`
     * **COMPILATION LSTM :** Compile avec `model.compile(optimizer=Adam(learning_rate=self.config.lstm_learning_rate), loss='categorical_crossentropy', metrics=['accuracy'])`
     * **INITIALISATION MARKOV :** Crée `MarkovChainPredictor(order=self.config.markov_order, smoothing=self.config.markov_smoothing)`
     * **INITIALISATION SCALERS :** Configure `StandardScaler()` pour LGBM et LSTM avec `self.scaler` et `self.lstm_scaler`
     * **INITIALISATION CACHE :** Crée `deque(maxlen=self.config.cache_size)` pour optimisation prédictions
     * **MODÈLE INCERTITUDE :** Initialise `BaggingClassifier(base_estimator=LGBMClassifier(), n_estimators=self.config.uncertainty_n_estimators, random_state=42, n_jobs=-1)`
     * **VALIDATION CONFIGURATION :** Vérifie cohérence entre `lstm_input_size`, `lgbm_feature_count` et configuration
     * **GESTION ERREURS :** Capture exceptions avec logging détaillé et rollback si nécessaire
     * **FINALISATION :** Remet `self._initializing_models = False` et marque `self._models_initialized = True`
   - RETOUR : bool - True si initialisation réussie, False en cas d'erreur
   - UTILITÉ : Point d'entrée central pour initialisation robuste de tous les modèles ML avec protection récursion et validation complète

3. save_optimized_models.txt (HybridBaccaratPredictor.save_optimized_models - Sauvegarde modèles optimisés)
   - Lignes 4905-5002 dans hbp.py (98 lignes)
   - FONCTION : Sauvegarde les modèles avec hyperparamètres optimisés après optimisation Optuna avec gestion complète des configurations
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * params_file_path (str) - Chemin vers fichier JSON des paramètres optimisés
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ÉTAT :** Vérifie qu'aucun entraînement n'est en cours via is_training flag
     * **CHARGEMENT PARAMS :** Lit paramètres optimisés depuis fichier JSON avec gestion d'erreurs
     * **SAUVEGARDE CONFIG ORIGINALE :** Crée backup de la configuration actuelle pour restauration
     * **APPLICATION PARAMS :** Applique temporairement paramètres optimisés à la configuration
     * **MISE À JOUR DÉPENDANCES :** Appelle _update_dependent_configs pour cohérence système
     * **SAUVEGARDE MODÈLES :** Utilise _perform_save avec configuration optimisée temporaire
     * **GÉNÉRATION MÉTADONNÉES :** Crée métadonnées complètes avec hyperparamètres et performance
     * **SAUVEGARDE MÉTADONNÉES :** Exporte métadonnées en JSON pour traçabilité
     * **RESTAURATION CONFIG :** Remet configuration originale après sauvegarde
     * **VALIDATION FINALE :** Vérifie intégrité des fichiers sauvegardés
   - RETOUR : bool - True si sauvegarde réussie, False en cas d'erreur
   - UTILITÉ : Permet sauvegarde des résultats d'optimisation avec configuration cohérente et traçabilité complète

4. on_training_complete.txt (HybridBaccaratPredictor.on_training_complete - Callback fin entraînement)
   - FONCTION : Callback appelé à la fin de l'entraînement pour finalisation et mise à jour UI avec gestion complète des états
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * training_results (Dict) - Résultats détaillés de l'entraînement
     * training_duration (float) - Durée totale de l'entraînement en secondes
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION RÉSULTATS :** Vérifie intégrité et cohérence des résultats d'entraînement
     * **MISE À JOUR ÉTAT :** Change is_training à False et met à jour statut système
     * **CALCUL MÉTRIQUES :** Calcule métriques finales de performance et convergence
     * **SAUVEGARDE AUTOMATIQUE :** Déclenche sauvegarde automatique si configurée
     * **MISE À JOUR UI :** Actualise interface utilisateur avec résultats finaux
     * **NOTIFICATION UTILISATEUR :** Affiche notification de fin d'entraînement
     * **LOGGING DÉTAILLÉ :** Enregistre informations complètes dans logs système
     * **NETTOYAGE MÉMOIRE :** Libère ressources temporaires d'entraînement
     * **ACTIVATION PRÉDICTIONS :** Réactive système de prédiction avec nouveaux modèles
     * **VALIDATION FINALE :** Vérifie que tous les modèles sont opérationnels
   - RETOUR : None - Callback ne retourne rien
   - UTILITÉ : Gère la finalisation propre de l'entraînement avec mise à jour complète du système et interface

5. finalize_training.txt (HybridBaccaratPredictor.finalize_training - Finalisation entraînement)
   - FONCTION : Finalise le processus d'entraînement avec sauvegarde et mise à jour des métriques de performance
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * training_metrics (Dict) - Métriques détaillées de l'entraînement
     * save_models (bool, optionnel) - Sauvegarde automatique des modèles (défaut: True)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MÉTRIQUES :** Vérifie cohérence et complétude des métriques d'entraînement
     * **CALCUL PERFORMANCE FINALE :** Évalue performance finale sur ensemble de validation
     * **MISE À JOUR HISTORIQUE :** Intègre nouvelles métriques dans historique de performance
     * **SAUVEGARDE CONDITIONNELLE :** Sauvegarde modèles si performance améliorée
     * **GÉNÉRATION RAPPORT :** Crée rapport détaillé de l'entraînement
     * **MISE À JOUR POIDS :** Ajuste poids des méthodes selon performance observée
     * **NETTOYAGE RESSOURCES :** Libère mémoire GPU/CPU utilisée pendant entraînement
     * **VALIDATION MODÈLES :** Teste fonctionnement des modèles entraînés
     * **MISE À JOUR CONFIG :** Met à jour configuration avec nouveaux paramètres
     * **NOTIFICATION SYSTÈME :** Informe autres composants de la fin d'entraînement
   - RETOUR : Dict - Rapport détaillé de finalisation avec métriques et statuts
   - UTILITÉ : Complète le cycle d'entraînement avec persistance des résultats et validation système

6. _train_models_async.txt (HybridBaccaratPredictor._train_models_async - Entraînement asynchrone)
   - Lignes 2212-2580 dans hbp.py (369 lignes)
   - FONCTION : Lance l'entraînement des modèles en mode asynchrone avec gestion complète des threads, callbacks et monitoring ressources
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * X_lgbm (numpy.ndarray) - Features LGBM pour entraînement
     * y_lgbm (numpy.ndarray) - Labels pour modèle LGBM
     * X_lstm (numpy.ndarray) - Features LSTM pour entraînement
     * y_lstm (numpy.ndarray) - Labels pour modèle LSTM
     * config_override (Dict, optionnel) - Configuration override pour entraînement
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))` et `ui_available = self.is_ui_available()` pour adaptation
     * **VALIDATION DONNÉES :** Vérifie `if X_lgbm is None or y_lgbm is None or X_lstm is None or y_lstm is None:` avec logging erreur et retour si données manquantes
     * **MARQUAGE ÉTAT :** Définit `self.is_training = True` et `self.stop_training = False` pour contrôle processus
     * **PROGRESSION INITIALE :** Si UI disponible, appelle `self._update_progress(5, "Démarrage entraînement asynchrone...")` pour feedback
     * **ENTRAÎNEMENT LGBM :** Appelle `lgbm_success = self._train_lgbm_model(X_lgbm, y_lgbm, config_override)` avec gestion succès/échec
     * **PROGRESSION LGBM :** Met à jour `self._update_progress(40, "LGBM terminé, démarrage LSTM...")` si UI disponible
     * **ENTRAÎNEMENT LSTM :** Si LGBM réussi, appelle `lstm_success = self._train_lstm_model(X_lstm, y_lstm, config_override)` avec même logique
     * **PROGRESSION LSTM :** Met à jour `self._update_progress(80, "LSTM terminé, finalisation...")` après entraînement LSTM
     * **VALIDATION MODÈLES :** Vérifie `models_trained = self._models_are_trained()` pour confirmer état opérationnel
     * **CALCUL MÉTRIQUES :** Si modèles entraînés, appelle `self._calculate_training_metrics(X_lgbm, y_lgbm, X_lstm, y_lstm)` pour évaluation performance
     * **SAUVEGARDE CONDITIONNELLE :** Si `self.config.auto_save_after_training:`, appelle `self.save_trained_models()` pour persistance
     * **FINALISATION :** Appelle `self.finalize_training(success=True, start_time=start_time, summary=[])` pour nettoyage et callbacks
     * **GESTION ERREURS :** Capture `Exception as e:` avec `logger_instance.error(f"Erreur pendant _train_models_async: {e}", exc_info=True)` et finalisation échec
     * **NETTOYAGE FINAL :** Assure `self.is_training = False` dans bloc finally pour libération état
   - RETOUR : None - Méthode asynchrone ne retourne rien
   - UTILITÉ : Entraînement non-bloquant avec interface utilisateur responsive, monitoring complet et gestion d'erreurs robuste

7. _models_are_trained.txt (HybridBaccaratPredictor._models_are_trained - Vérification modèles entraînés)
   - Lignes 1742-1795 dans hbp.py (54 lignes)
   - FONCTION : Vérifie si au moins un des modèles principaux (LGBM, LSTM) a été entraîné avec validation thread-safe
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise `with self.model_lock:` pour accès thread-safe aux modèles
     * **VÉRIFICATION LGBM :** Contrôle que le modèle LGBM est entraîné avec :
       - `lgbm_trained = (self.calibrated_lgbm is not None and hasattr(self.calibrated_lgbm, 'classes_'))` pour validation existence et attribut classes
       - Vérifie que le modèle calibré existe et possède l'attribut `classes_` indiquant entraînement terminé
     * **VÉRIFICATION LSTM :** Contrôle que le modèle LSTM est entraîné avec :
       - `lstm_trained = (self.lstm is not None and hasattr(self.lstm, 'trained') and getattr(self.lstm, 'trained', False))` pour validation complète
       - Vérifie existence modèle, présence attribut `trained` et valeur True
     * **LOGIQUE OU :** Retourne `lgbm_trained or lstm_trained` car système fonctionne avec au moins un modèle principal entraîné
     * **VALIDATION MINIMALE :** Considère système opérationnel si LGBM OU LSTM est entraîné (pas nécessairement les deux)
     * **THREAD-SAFETY :** Toutes vérifications effectuées sous protection `model_lock` pour éviter conditions de course
   - RETOUR : bool - True si au moins un modèle principal (LGBM ou LSTM) est entraîné, False sinon
   - UTILITÉ : Validation rapide et thread-safe de l'état d'entraînement pour déterminer si système peut effectuer prédictions

8. auto_fast_update_if_needed.txt (HybridBaccaratPredictor.auto_fast_update_if_needed - Mise à jour rapide automatique)
   - Lignes 12561-12611 dans hbp.py (51 lignes)
   - FONCTION : Déclenche mise à jour rapide automatique si conditions remplies avec focus sur manches cibles 31-60
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * current_round_num (int) - Numéro de manche actuelle pour vérification plage cible
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION AUTO-UPDATE :** Contrôle `if not hasattr(self, 'auto_update_enabled') or not self.auto_update_enabled.get():` avec retour si désactivé
     * **DÉTECTION PLAGE CIBLE :** Calcule `target_round_min = getattr(self.config, 'target_round_min', 31)`, `target_round_max = getattr(self.config, 'target_round_max', 60)` et `is_target_round = target_round_min <= current_round_num <= target_round_max`
     * **INIT CALCULATEUR CONSÉCUTIF :** Si `is_target_round and not hasattr(self, 'consecutive_confidence_calculator'):`, appelle `self.init_consecutive_confidence_calculator()`
     * **VÉRIFICATION CONDITIONS :** Contrôle `if current_round_num <= self.last_incremental_update_index:` pour éviter mises à jour redondantes
     * **ACQUISITION VERROUS :** Utilise `with self.training_lock, self.sequence_lock:` pour protection thread-safe
     * **DOUBLE VÉRIFICATION :** Contrôle `if not self.auto_update_enabled.get():` et `if self.is_training or self.is_fast_updating:` pour éviter conflits
     * **MARQUAGE ÉTAT :** Définit `self.is_fast_updating = True` et `is_auto_trigger = True` pour suivi état
     * **LANCEMENT THREAD :** Crée `threading.Thread(target=self._run_fast_update_async, args=(False, is_auto_trigger), daemon=True, name="AutoFastUpdateThread").start()`
     * **GESTION ERREURS :** Capture exceptions avec `logger_instance.error` et reset `self.is_fast_updating = False`
   - RETOUR : None - Méthode de déclenchement ne retourne rien
   - UTILITÉ : Optimise performance système via mises à jour ciblées et automatiques

9. load_optimized_models.txt (HybridBaccaratPredictor.load_optimized_models - Chargement modèles optimisés)
   - Lignes 5004-5120 dans hbp.py (117 lignes)
   - FONCTION : Charge modèles pré-optimisés avec paramètres Optuna et validation complète d'intégrité avec gestion d'erreurs robuste
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * params_file_path (str) - Chemin vers fichier JSON des paramètres optimisés
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ÉTAT :** Vérifie `if self.is_training:` avec `messagebox.showwarning("Entraînement en cours", "Impossible de charger pendant un entraînement.")` et retour
     * **VALIDATION FICHIER :** Teste `if not os.path.exists(params_file_path):` avec `messagebox.showerror("Fichier introuvable", f"Le fichier {params_file_path} n'existe pas.")` et retour False
     * **CHARGEMENT PARAMÈTRES :** Utilise `with open(params_file_path, 'r') as f:` puis `optimized_params = json.load(f)` avec gestion `json.JSONDecodeError`
     * **SAUVEGARDE CONFIG :** Crée `original_config = copy.deepcopy(self.config)` pour backup avant modifications
     * **APPLICATION PARAMÈTRES :** Appelle `self._update_config_from_optimization(optimized_params)` pour intégration paramètres optimisés
     * **RÉENTRAÎNEMENT :** Lance `training_success = self.train_models(use_historical=True, async_mode=False)` en mode synchrone pour application immédiate
     * **VALIDATION SUCCÈS :** Vérifie `if not training_success:` avec restauration `self.config = original_config` et messagebox d'erreur
     * **CONFIRMATION MODÈLES :** Teste `if not self._models_are_trained():` avec même logique de restauration si échec
     * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Modèles optimisés chargés avec succès depuis {params_file_path}")` pour traçabilité
     * **FEEDBACK UTILISATEUR :** Affiche `messagebox.showinfo("Succès", f"Modèles optimisés chargés avec succès!\n\nFichier: {os.path.basename(params_file_path)}")` pour confirmation
     * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors du chargement des modèles optimisés: {e}", exc_info=True)` et restauration config
     * **RESTAURATION ÉCHEC :** En cas d'erreur, restaure `self.config = original_config` et affiche messagebox d'erreur détaillé
   - RETOUR : bool - True si chargement réussi, False en cas d'erreur
   - UTILITÉ : Utilisation directe de modèles optimisés avec réentraînement automatique et validation complète d'intégrité

10. run_full_retraining.txt (HybridBaccaratPredictor.run_full_retraining - Re-entraînement complet)
    - Lignes 11871-12024 dans hbp.py (154 lignes)
    - FONCTION : Lance re-entraînement complet de tous les modèles avec mise à jour Markov global et ThreadedTrainer
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **PROTECTION CONCURRENCE :** Vérifie `with self.training_lock:` puis `if self.is_training or self.is_fast_updating:` avec warning et retour si tâche active
      * **VALIDATION DONNÉES :** Utilise `with self.sequence_lock:` pour vérifier `if self.loaded_historical and self.historical_data:` avec copie `historical_data_copy = self.historical_data[:]`
      * **CONFIRMATION UTILISATEUR :** Affiche `messagebox.askyesno` avec avertissement "Lancer l'entraînement complet ? Cela peut prendre du temps et écrasera les modèles existants"
      * **MISE À JOUR MARKOV GLOBAL :** Si `self.markov:`, exécute `with self.markov_lock, self.sequence_lock:` puis `self.markov.reset(reset_type='hard')` et `self.markov.update_global(historical_data_copy)`
      * **PRÉPARATION DONNÉES :** Appelle `training_data_package = self._prepare_training_data(force_use_historical=True, max_games=None, sampling_fraction=None)` pour utiliser TOUTES les données historiques
      * **VALIDATION PACKAGE :** Vérifie `if training_data_package is None or len(training_data_package) != 8` avec contrôles sur `training_data_package[0-3]` pour X_lgbm, y_labels, X_lstm, sample_weights
      * **CONFIGURATION THREADED TRAINER :** Instancie `self.threaded_trainer = ThreadedTrainer(self)` avec callbacks :
        - `on_training_complete(result)` qui appelle `self.finalize_training(result['success'], self.threaded_trainer.start_time, [])`
        - `on_training_error(error)` qui appelle `self.finalize_training(False, start_time, [])` avec gestion d'erreurs
      * **LANCEMENT ENTRAÎNEMENT :** Appelle `self.threaded_trainer.start()` avec paramètres :
        - `X_lgbm=training_data_package[0]` (features LGBM)
        - `y_lgbm=training_data_package[1]` (labels)
        - `X_lstm=training_data_package[2]` (features LSTM)
        - `y_lstm=training_data_package[1]` (mêmes labels)
        - `config_override={}` (configuration par défaut)
      * **GESTION INTERFACE :** Utilise `self.toggle_training_controls(enabled=False)` pour désactiver contrôles pendant entraînement
      * **MONITORING PROGRESSION :** Met à jour interface avec `self._update_progress()` aux étapes clés (0%, 5%, 10%, 30%)
    - RETOUR : None - Méthode d'interface utilisateur asynchrone
    - UTILITÉ : Re-entraînement complet avec mise à jour Markov global, utilisation de toutes les données historiques et ThreadedTrainer pour gestion asynchrone
      * **RÉINITIALISATION MODÈLES :** Recrée modèles avec architecture optimisée
      * **OPTIMISATION CONDITIONNELLE :** Lance Optuna si paramètre activé
      * **ENTRAÎNEMENT SÉQUENTIEL :** Entraîne LGBM puis LSTM avec monitoring
      * **VALIDATION CROISÉE :** Effectue validation temporelle pour robustesse
      * **ÉVALUATION PERFORMANCE :** Compare avec modèles précédents
      * **MISE À JOUR POIDS :** Recalcule poids des méthodes selon nouvelle performance
      * **SAUVEGARDE FINALE :** Sauvegarde nouveaux modèles si amélioration
      * **RAPPORT COMPLET :** Génère rapport détaillé du re-entraînement
    - RETOUR : Dict - Rapport détaillé avec métriques avant/après et recommandations
    - UTILITÉ : Mise à jour complète des modèles avec nouvelles données et optimisations

11. load_optimized_params.txt (HybridBaccaratPredictor.load_optimized_params - Chargement paramètres optimisés)
    - Lignes 2172-2210 dans hbp.py (39 lignes)
    - FONCTION : Charge paramètres optimisés depuis fichier params.txt avec validation et application sécurisée à la configuration
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **IMPORT UTILITAIRES :** Importe `from utils import load_params_from_file, apply_params_to_config` pour fonctions spécialisées
      * **CHARGEMENT PARAMÈTRES :** Appelle `params = load_params_from_file("params.txt")` pour lecture fichier JSON
      * **VALIDATION CHARGEMENT :** Vérifie `if not params:` avec warning et messagebox "Échec du chargement des paramètres depuis params.txt"
      * **APPLICATION PARAMÈTRES :** Utilise `success = apply_params_to_config(self.config, params)` pour intégration sécurisée
      * **VALIDATION APPLICATION :** Contrôle `if not success:` avec warning et messagebox "Échec de l'application des paramètres à la configuration"
      * **CONFIRMATION SUCCÈS :** Si succès, affiche `messagebox.showinfo("Paramètres Chargés", "Les paramètres optimisés ont été chargés et appliqués avec succès.")` et `logger.info("Paramètres optimisés chargés et appliqués avec succès.")`
      * **GESTION ERREURS :** Capture `except ImportError as e:` avec logging et messagebox d'erreur pour modules manquants
      * **GESTION EXCEPTIONS :** Capture `except Exception as e:` avec logging détaillé et messagebox d'erreur générique
      * **LOGGING DÉTAILLÉ :** Utilise `logger.warning`, `logger.info` et `logger.error` pour traçabilité complète
      * **INTERFACE UTILISATEUR :** Utilise `messagebox.showwarning`, `messagebox.showinfo` et `messagebox.showerror` pour feedback utilisateur
    - RETOUR : None - Méthode d'interface utilisateur ne retourne rien
    - UTILITÉ : Application sécurisée de paramètres optimisés depuis fichier avec validation complète et feedback utilisateur

12. apply_optimized_params_to_config_file.txt (HybridBaccaratPredictor.apply_optimized_params_to_config_file - Application params au fichier config)
    - FONCTION : Applique paramètres optimisés directement au fichier de configuration avec sauvegarde
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * optimized_params (Dict) - Dictionnaire des paramètres optimisés
      * config_file_path (str, optionnel) - Chemin fichier config (défaut: config.json)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **LECTURE CONFIG ACTUELLE :** Charge configuration existante
      * **CRÉATION BACKUP :** Sauvegarde fichier config original
      * **FUSION INTELLIGENTE :** Fusionne paramètres optimisés avec config existante
      * **VALIDATION STRUCTURE :** Vérifie intégrité structure configuration
      * **ÉCRITURE ATOMIQUE :** Écrit nouveau fichier de manière atomique
      * **VALIDATION POST-ÉCRITURE :** Vérifie lisibilité du nouveau fichier
      * **TEST CHARGEMENT :** Teste chargement de la nouvelle configuration
      * **NOTIFICATION CHANGEMENTS :** Informe système des modifications
      * **GESTION ERREURS :** Restaure backup en cas de problème
      * **LOGGING MODIFICATIONS :** Enregistre détails des changements
    - RETOUR : bool - True si application réussie, False en cas d'erreur
    - UTILITÉ : Persistance des paramètres optimisés dans fichier de configuration

13. adjust_parameters_for_viability.txt (HybridBaccaratPredictor.adjust_parameters_for_viability - Ajustement viabilité paramètres)
    - Lignes 3294-3515 dans hbp.py (222 lignes)
    - FONCTION : Ajuste paramètres pour assurer viabilité et stabilité du système avec contraintes pratiques et validation ressources
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * params (Dict[str, Any]) - Paramètres bruts à ajuster pour viabilité
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))` et `adjusted_params = params.copy()` pour copie de travail
      * **LOGGING DÉBUT :** Enregistre "Ajustement des paramètres pour viabilité..." avec nombre de paramètres à traiter
      * **AJUSTEMENT LGBM :** Pour paramètres LGBM, applique contraintes spécifiques :
        - `num_leaves` : Borne entre 10 et 300 avec `max(10, min(300, params.get('lgbm_num_leaves', 31)))`
        - `max_depth` : Limite entre 3 et 15 avec `max(3, min(15, params.get('lgbm_max_depth', -1)))`
        - `learning_rate` : Borne entre 0.01 et 0.3 avec `max(0.01, min(0.3, params.get('lgbm_learning_rate', 0.1)))`
        - `n_estimators` : Limite entre 50 et 1000 avec `max(50, min(1000, params.get('lgbm_n_estimators', 100)))`
      * **AJUSTEMENT LSTM :** Pour paramètres LSTM, applique contraintes architecture :
        - `hidden_size` : Borne entre 32 et 256 avec `max(32, min(256, params.get('lstm_hidden_size', 64)))`
        - `num_layers` : Limite entre 1 et 4 avec `max(1, min(4, params.get('lstm_num_layers', 2)))`
        - `dropout` : Borne entre 0.0 et 0.5 avec `max(0.0, min(0.5, params.get('lstm_dropout', 0.1)))`
        - `learning_rate` : Limite entre 0.0001 et 0.01 avec `max(0.0001, min(0.01, params.get('lstm_learning_rate', 0.001)))`
      * **AJUSTEMENT SEUILS :** Pour paramètres de seuils, applique bornes logiques :
        - `min_confidence_for_recommendation` : Entre 0.5 et 0.95 avec `max(0.5, min(0.95, params.get('min_confidence_for_recommendation', 0.7)))`
        - `uncertainty_threshold` : Entre 0.1 et 0.8 avec `max(0.1, min(0.8, params.get('uncertainty_threshold', 0.3)))`
      * **AJUSTEMENT POIDS :** Pour poids des méthodes, normalise pour somme = 1.0 :
        - Collecte tous poids avec `weight_params = {k: v for k, v in params.items() if k.startswith('weight_')}`
        - Calcule `total_weight = sum(weight_params.values())` et normalise si `total_weight > 0`
        - Applique `adjusted_params[k] = v / total_weight` pour chaque poids
      * **VALIDATION COHÉRENCE :** Vérifie cohérence inter-paramètres :
        - Assure `lgbm_num_leaves < 2^lgbm_max_depth` pour éviter overfitting
        - Vérifie `lstm_hidden_size * lstm_num_layers < 1024` pour contraintes mémoire
      * **LOGGING AJUSTEMENTS :** Pour chaque paramètre modifié, enregistre `f"Paramètre ajusté: {param} {original_value} -> {new_value}"`
      * **VALIDATION FINALE :** Vérifie que tous paramètres ajustés sont dans bornes acceptables
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger_instance.error(f"Erreur lors de l'ajustement des paramètres: {e}", exc_info=True)` et retour paramètres originaux
    - RETOUR : Dict[str, Any] - Paramètres ajustés et viables pour utilisation
    - UTILITÉ : Garantit viabilité pratique des paramètres optimisés avec contraintes ressources et cohérence inter-paramètres

14. _apply_hyperparameters_from_metadata.txt (HybridBaccaratPredictor._apply_hyperparameters_from_metadata - Application hyperparamètres depuis métadonnées)
    - Lignes 3293-3330 dans hbp.py (38 lignes)
    - FONCTION : Applique hyperparamètres extraits des métadonnées de modèles sauvegardés avec validation utilisateur et gestion d'erreurs
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * metadata (Dict[str, Any]) - Métadonnées contenant hyperparamètres à appliquer
    - FONCTIONNEMENT DÉTAILLÉ :
      * **EXTRACTION HYPERPARAMÈTRES :** Récupère `hyperparams = metadata.get('hyperparameters', {})` depuis métadonnées
      * **VALIDATION PRÉSENCE :** Vérifie `if not hyperparams:` avec `messagebox.showwarning("Avertissement", "Aucun hyperparamètre trouvé dans les métadonnées.")` et retour
      * **CONFIRMATION UTILISATEUR :** Affiche `messagebox.askyesno("Confirmation", "Voulez-vous appliquer ces hyperparamètres à la configuration actuelle?\n\nCela modifiera les valeurs actuelles de la configuration.")` pour validation
      * **VALIDATION RÉPONSE :** Si `not response:`, retourne sans modification pour annulation utilisateur
      * **APPLICATION PARAMÈTRES :** Initialise `applied_params = []` puis itère `for param, value in hyperparams.items():` :
        - Vérifie `if hasattr(self.config, param) and value is not None:` pour validation attribut
        - Applique `setattr(self.config, param, value)` pour mise à jour configuration
        - Ajoute `applied_params.append(param)` pour suivi paramètres appliqués
      * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Hyperparamètres appliqués: {applied_params}")` pour traçabilité
      * **MESSAGE CONFIRMATION :** Affiche `messagebox.showinfo("Succès", f"Hyperparamètres appliqués avec succès!\n\nParamètres modifiés: {len(applied_params)}")` pour feedback utilisateur
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de l'application des hyperparamètres: {e}", exc_info=True)` et `messagebox.showerror("Erreur", f"Erreur lors de l'application des hyperparamètres:\n{e}")`
    - RETOUR : None - Méthode de configuration ne retourne rien
    - UTILITÉ : Interface sécurisée pour appliquer hyperparamètres optimisés avec validation utilisateur et gestion d'erreurs complète

15. _finalize_fast_update.txt (HybridBaccaratPredictor._finalize_fast_update - Finalisation mise à jour rapide)
    - Lignes 3078-3137 dans hbp.py (60 lignes)
    - FONCTION : Finalise processus de mise à jour rapide avec validation et intégration système selon mode déclenchement
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * success (bool) - Indique si la mise à jour rapide a réussi
      * start_time (float) - Timestamp de début pour calcul durée
      * summary (List[str]) - Résumé des opérations effectuées
      * is_auto_trigger (bool) - True si déclenchement automatique, False si manuel
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CALCUL DURÉE :** Calcule `total_time = time.time() - start_time` pour mesure performance
      * **IDENTIFICATION SOURCE :** Définit `trigger_type = "AUTO" if is_auto_trigger else "MANUAL"` pour logging
      * **LOGGING INITIAL :** Enregistre `logger.info(f"Finalisation MàJ rapide (Source: {trigger_type}). Succès: {success}, Durée: {total_time:.1f}s")`
      * **VÉRIFICATION UI :** Appelle `ui_available = self.is_ui_available()` pour validation interface
      * **VALIDATION UI :** Si `not ui_available:`, log warning et retour anticipé
      * **RÉACTIVATION CONTRÔLES :** Si UI disponible, appelle `self.toggle_training_controls(enabled=True)` pour réactiver boutons
      * **GESTION SUCCÈS :** Si `success:` :
        - Met à jour `self.last_fast_update_time = time.time()` pour tracking
        - Log `logger.info(f"Mise à jour rapide ({trigger_type}) terminée avec succès en {total_time:.1f}s")`
        - Si mode manuel (`not is_auto_trigger`), affiche `messagebox.showinfo("Succès", f"Mise à jour rapide terminée avec succès!\n\nDurée: {total_time:.1f}s\n\n{chr(10).join(summary)}")`
      * **GESTION ÉCHEC :** Si `not success:` :
        - Log `logger.error(f"Échec de la mise à jour rapide ({trigger_type}) après {total_time:.1f}s")`
        - Si mode manuel, affiche `messagebox.showerror("Erreur", f"Échec de la mise à jour rapide!\n\nDurée: {total_time:.1f}s\n\n{chr(10).join(summary)}")`
      * **POLITIQUE SAUVEGARDE :** Commentaire explicite "MODIFIÉ: Ne sauvegarde JAMAIS l'état automatiquement après une mise à jour rapide (manuelle ou auto)"
      * **GESTION ERREURS :** Capture `Exception as e:` avec logging complet et messagebox d'erreur si mode manuel
    - RETOUR : None - Méthode de finalisation ne retourne rien
    - UTILITÉ : Finalisation robuste mise à jour rapide avec gestion différenciée selon mode déclenchement et interface utilisateur adaptée

16. _finalize_optuna_optimization.txt (HybridBaccaratPredictor._finalize_optuna_optimization - Finalisation optimisation Optuna)
    - Lignes 3139-3292 dans hbp.py (154 lignes)
    - FONCTION : Finalise processus d'optimisation Optuna avec interface utilisateur, sauvegarde et application paramètres selon succès
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * success (bool) - Indique si optimisation s'est terminée avec succès
      * best_params (Dict[str, Any]) - Meilleurs paramètres trouvés (peut être None si échec)
      * duration (float) - Durée totale de l'optimisation en secondes
      * error_message (str, optionnel) - Message d'erreur si échec (None si succès)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))` pour logging adaptatif
      * **VALIDATION UI :** Vérifie `ui_available = self.is_ui_available()` pour adaptation interface
      * **RÉACTIVATION CONTRÔLES :** Si UI disponible, appelle `self.toggle_training_controls(enabled=True)` pour réactiver boutons
      * **GESTION SUCCÈS :** Si `success and best_params:` :
        - Log `logger_instance.info(f"Optimisation Optuna terminée avec succès en {duration:.2f} secondes")`
        - Appelle `self._update_config_from_optimization(best_params)` pour application paramètres
        - Utilise `self.show_optimization_results()` pour affichage résultats si UI disponible
        - Sinon affiche `messagebox.showinfo("Optimisation terminée", f"Optimisation terminée avec succès!\n\nDurée: {duration:.2f} secondes\n\nMeilleurs paramètres appliqués à la configuration.")`
      * **GESTION ÉCHEC :** Si `not success:` :
        - Log `logger_instance.error(f"Échec de l'optimisation Optuna après {duration:.2f} secondes: {error_message}")`
        - Affiche `messagebox.showerror("Erreur d'optimisation", f"L'optimisation a échoué après {duration:.2f} secondes.\n\nErreur: {error_message}")` si UI disponible
      * **GESTION PARAMÈTRES PARTIELS :** Si `success and not best_params:` :
        - Log warning "Optimisation terminée mais aucun paramètre valide trouvé"
        - Affiche message informatif à l'utilisateur
      * **NETTOYAGE RESSOURCES :** Reset `self.is_optuna_running = False` et `self.current_optimizer_instance = None` pour libération mémoire
      * **MISE À JOUR PROGRESSION :** Si UI disponible, appelle `self._update_progress(100, "Optimisation terminée")` pour finalisation visuelle
      * **GESTION ERREURS :** Capture `Exception as e:` avec logging complet et messagebox d'erreur si UI disponible
    - RETOUR : None - Méthode de finalisation ne retourne rien
    - UTILITÉ : Finalisation robuste optimisation avec gestion différenciée succès/échec, interface utilisateur adaptée et nettoyage complet ressources

17. _run_fast_update_async.txt (HybridBaccaratPredictor._run_fast_update_async - Exécution mise à jour rapide asynchrone)
    - Lignes 12488-12559 dans hbp.py (72 lignes)
    - FONCTION : Exécute mise à jour rapide des modèles en mode asynchrone avec monitoring complet et callbacks
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * save_after_update (bool, optionnel) - Sauvegarde état après mise à jour (défaut: False)
      * is_auto_trigger (bool, optionnel) - Indique déclenchement automatique (défaut: False)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))`, `ui_available = self.is_ui_available()`, `start_time = time.time()`, `success = False`, `summary = []`
      * **PROGRESSION UI :** Si UI disponible, appelle `self.root.after(0, lambda: self._update_progress(10, "Préparation mise à jour rapide..."))`
      * **COPIE SÉQUENCE :** Avec `self.sequence_lock:`, copie `current_sequence = self.sequence[:]` et `current_round_num = len(current_sequence)`
      * **VALIDATION LONGUEUR :** Vérifie `if current_round_num < 10:` avec retour et warning si séquence trop courte
      * **MISE À JOUR INDEX :** Définit `self.last_incremental_update_index = current_round_num` pour éviter doublons
      * **MISE À JOUR MARKOV :** Si `hasattr(self, 'markov') and self.markov:`, utilise `with self.markov_lock:` puis `self.markov.update_session(current_sequence)`
      * **PROGRESSION MODÈLES :** Met à jour UI avec `self.root.after(0, lambda: self._update_progress(50, "Mise à jour des modèles..."))`
      * **SIMULATION TRAITEMENT :** Utilise `time.sleep(1)` pour simuler traitement et définit `success = True`
      * **GESTION ERREURS :** Capture `except Exception as e:` avec logging détaillé et `success = False`
      * **NETTOYAGE FINAL :** Dans `finally:`, utilise `with self.training_lock:` pour reset `self.is_fast_updating = False`
      * **FINALISATION :** Si UI disponible, appelle `self.root.after(0, lambda: self._finalize_fast_update(success, start_time, summary, is_auto_trigger))`
    - RETOUR : None - Méthode asynchrone ne retourne rien
    - UTILITÉ : Mise à jour non-bloquante avec suivi temps réel, protection thread-safe et gestion complète des erreurs

18. _run_optuna_optimization_async.txt (HybridBaccaratPredictor._run_optuna_optimization_async - Optimisation Optuna asynchrone)
    - Lignes 13034-13165 dans hbp.py (132 lignes)
    - FONCTION : Exécute optimisation Optuna multi-niveaux en mode asynchrone avec réinitialisation compteurs et gestion ressources
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * training_data_package - Package de données pour optimisation (X_lgbm, y, X_lstm, indices)
      * n_trials (int) - Nombre d'essais à effectuer
      * advanced_options (Dict, optionnel) - Options avancées pour optimisation multi-niveaux
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))`, `ui_available = self.is_ui_available()`, `start_time = time.time()`, `best_params = None`, `success = False`, `error_message = None`, `optimizer_instance = None`
      * **UNPACK DONNÉES :** Extrait `X_lgbm_all, y_all, X_lstm_all, _, train_indices_from_prep, val_indices_from_prep, _, _ = training_data_package`
      * **PROGRESSION UI :** Si UI disponible, appelle `self.root.after(0, lambda: self._update_progress(25, "Configuration Optuna multi-niveaux..."))`
      * **RÉINITIALISATION COMPTEURS :** Importe `from optuna_optimizer import OptunaOptimizer` puis reset `OptunaOptimizer.optimized_viable_trials_count = 0` et `OptunaOptimizer.total_attempts_made = 0` avec logging détaillé
      * **CRÉATION OPTIMISEUR :** Instancie `optimizer_instance = OptunaOptimizer(self.config)` avec transmission options avancées
      * **CONFIGURATION RESSOURCES :** Si `advanced_options:`, définit `optimizer_instance.cpu_count = advanced_options.get('cpu_count', 8)`, `optimizer_instance.ram_gb = advanced_options.get('ram_gb', 28)`, `optimizer_instance.batch_size = advanced_options.get('batch_size', 1024)`
      * **ASSIGNATION DONNÉES :** Configure `optimizer_instance.X_lgbm_full = X_lgbm_all`, `optimizer_instance.y_full = y_all`, `optimizer_instance.X_lstm_full = X_lstm_all`, `optimizer_instance.train_indices = train_indices_from_prep`, `optimizer_instance.val_indices = val_indices_from_prep`
      * **LANCEMENT OPTIMISATION :** Appelle `params_found = optimizer_instance.optimize(n_trials=n_trials)` avec gestion d'erreurs complète
      * **TRAITEMENT RÉSULTATS :** Si succès, définit `best_params = params_found`, `success = True` avec logging détaillé
      * **GESTION ERREURS :** Capture exceptions avec `error_message` approprié et logging complet
      * **FINALISATION :** Calcule `duration = time.time() - start_time`, détermine `completion_status` et appelle `self.root.after(0, self._finalize_optuna_optimization, success, best_params, duration, error_message)` si UI disponible
    - RETOUR : None - Méthode asynchrone ne retourne rien
    - UTILITÉ : Optimisation Optuna non-bloquante avec réinitialisation propre, gestion ressources et interface responsive

19. _select_and_save_optimized_models.txt (HybridBaccaratPredictor._select_and_save_optimized_models - Sélection et sauvegarde modèles optimisés)
    - Lignes 4888-4903 dans hbp.py (16 lignes)
    - FONCTION : Interface utilisateur pour sélectionner fichier paramètres optimisés et déclencher sauvegarde des modèles correspondants
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **DIALOGUE SÉLECTION :** Ouvre `params_file_path = filedialog.askopenfilename()` avec configuration :
        - `title="Sélectionnez le fichier de paramètres optimisés"` pour clarté utilisateur
        - `filetypes=(("Fichiers JSON", "*.json"), ("Tous les fichiers", "*.*"))` pour filtrage approprié
        - `initialdir="viable_trials"` pour pointer vers dossier des essais viables
      * **VALIDATION SÉLECTION :** Vérifie `if not params_file_path:` avec logging "Sauvegarde des modèles optimisés annulée par l'utilisateur." et retour si annulé
      * **DÉLÉGATION SAUVEGARDE :** Appelle `self.save_optimized_models(params_file_path)` pour traitement effectif avec fichier sélectionné
      * **GESTION TRANSPARENTE :** Transmet directement le chemin sélectionné sans validation supplémentaire
    - RETOUR : None - Méthode d'interface utilisateur ne retourne rien
    - UTILITÉ : Interface conviviale pour sélection et sauvegarde de modèles optimisés avec navigation fichiers intégrée

20. _update_config_from_optimization.txt (HybridBaccaratPredictor._update_config_from_optimization - MAJ config depuis optimisation)
    - Lignes 936-986 dans hbp.py (51 lignes)
    - FONCTION : Met à jour configuration système avec paramètres optimisés en filtrant et appliquant sélectivement les valeurs
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * best_params (Dict[str, Any]) - Dictionnaire des meilleurs paramètres trouvés par optimisation
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION PARAMÈTRES :** Vérifie `if not best_params:` avec logging warning "Aucun paramètre à mettre à jour" et retour si vide
      * **PROTECTION CONCURRENCE :** Utilise `with self.model_lock:` pour modifications thread-safe de la configuration
      * **LOGGING DÉBUT :** Enregistre "Mise à jour configuration avec les paramètres optimisés" pour traçabilité
      * **FILTRAGE PARAMÈTRES :** Itère `for param, value in best_params.items():` avec exclusions :
        - Ignore `param.startswith('weight_')` car gérés séparément par système de poids
        - Ignore `param in ('decision_threshold', 'min_confidence')` car paramètres de décision non-config
        - Log debug pour paramètres ignorés avec valeurs
      * **APPLICATION LGBM :** Pour paramètres LGBM, met à jour `self.config['lgbm'][param] = value` avec logging détaillé
      * **APPLICATION LSTM :** Pour paramètres LSTM, met à jour `self.config['lstm'][param] = value` avec logging détaillé
      * **PARAMÈTRES GÉNÉRAUX :** Pour autres paramètres, applique directement à `self.config[param] = value`
      * **VALIDATION POST-MISE À JOUR :** Vérifie cohérence configuration après modifications
      * **LOGGING FINAL :** Enregistre nombre total de paramètres appliqués et ignorés
    - RETOUR : None - Met à jour directement la configuration interne
    - UTILITÉ : Intégration sélective et sécurisée des paramètres optimisés avec filtrage intelligent et protection concurrence

21. on_training_error.txt (HybridBaccaratPredictor.on_training_error - Callback erreur entraînement)
    - FONCTION : Callback appelé en cas d'erreur pendant entraînement avec gestion recovery
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * error (Exception) - Exception capturée pendant entraînement
      * training_context (Dict) - Contexte de l'entraînement en cours
    - FONCTIONNEMENT DÉTAILLÉ :
      * **ANALYSE ERREUR :** Analyse type et cause de l'erreur d'entraînement
      * **LOGGING DÉTAILLÉ :** Enregistre erreur avec contexte complet
      * **NETTOYAGE RESSOURCES :** Libère ressources bloquées par entraînement
      * **RESTAURATION ÉTAT :** Remet système dans état stable
      * **NOTIFICATION UTILISATEUR :** Informe utilisateur avec message explicatif
      * **TENTATIVE RECOVERY :** Essaie récupération automatique si possible
      * **SAUVEGARDE ÉTAT :** Sauvegarde état avant erreur pour diagnostic
      * **MISE À JOUR STATUT :** Met à jour statut système (is_training = False)
      * **RAPPORT ERREUR :** Génère rapport détaillé pour debugging
      * **RECOMMANDATIONS :** Propose actions correctives à l'utilisateur
    - RETOUR : None - Callback de gestion d'erreur
    - UTILITÉ : Gestion robuste des erreurs avec recovery automatique et diagnostic

22. on_training_progress.txt (HybridBaccaratPredictor.on_training_progress - Callback progression entraînement)
    - FONCTION : Callback appelé périodiquement pour mise à jour progression entraînement
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * progress_info (Dict) - Informations de progression détaillées
      * current_epoch (int) - Époque actuelle d'entraînement
    - FONCTIONNEMENT DÉTAILLÉ :
      * **MISE À JOUR UI :** Actualise barres de progression et métriques
      * **CALCUL POURCENTAGE :** Calcule pourcentage de completion
      * **ESTIMATION TEMPS :** Estime temps restant basé sur progression
      * **MONITORING PERFORMANCE :** Surveille métriques de performance
      * **DÉTECTION CONVERGENCE :** Identifie signes de convergence précoce
      * **GESTION RESSOURCES :** Monitore utilisation CPU/mémoire
      * **LOGGING PROGRESSION :** Enregistre jalons importants
      * **VALIDATION CONTINUE :** Vérifie qualité pendant entraînement
      * **NOTIFICATION JALONS :** Informe utilisateur des étapes importantes
      * **OPTIMISATION DYNAMIQUE :** Ajuste paramètres si nécessaire
    - RETOUR : None - Callback de progression
    - UTILITÉ : Suivi temps réel de l'entraînement avec optimisation dynamique

23. progress_callback.txt (HybridBaccaratPredictor.progress_callback - Callback progression générique)
    - Lignes 13012-13014 dans hbp.py (3 lignes)
    - FONCTION : Callback interne pour mise à jour progression Optuna de manière thread-safe vers interface utilisateur
    - PARAMÈTRES :
      * progress (float) - Valeur de progression entre 0 et 100
      * message (str) - Message de statut à afficher
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VÉRIFICATION UI :** Contrôle `if ui_available:` pour s'assurer que interface utilisateur est disponible
      * **DÉLÉGATION THREAD-SAFE :** Utilise `self.root.after(0, lambda: self._update_progress(progress, message))` pour exécution dans thread principal UI
      * **PROTECTION CONCURRENCE :** Évite appels directs depuis thread d'optimisation vers interface Tkinter
      * **CALLBACK LÉGER :** Fonction minimale pour transmission rapide des mises à jour
    - RETOUR : None - Callback ne retourne rien
    - UTILITÉ : Pont thread-safe entre optimisation Optuna et interface utilisateur pour feedback temps réel

24. success_callback.txt (HybridBaccaratPredictor.success_callback - Callback succès opération)
    - Lignes 12995-13002 dans hbp.py (8 lignes)
    - FONCTION : Callback interne pour gestion succès optimisation Optuna avec finalisation thread-safe
    - PARAMÈTRES :
      * best_params (Dict[str, Any]) - Meilleurs paramètres trouvés par optimisation
      * duration (float) - Durée de l'optimisation en secondes
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VÉRIFICATION UI :** Contrôle `if ui_available:` pour s'assurer que interface utilisateur est disponible
      * **FINALISATION THREAD-SAFE :** Utilise `self.root.after(0, lambda: self._finalize_optuna_optimization(True, best_params, duration, None))` pour exécution dans thread principal
      * **LOGGING ALTERNATIF :** Si UI indisponible, utilise `logger_instance.info(f"Optimisation terminée avec succès en {duration:.2f} secondes")` et `logger_instance.info(f"Meilleurs paramètres: {best_params}")`
      * **TRANSMISSION PARAMÈTRES :** Passe `True` comme statut de succès et `None` comme message d'erreur
    - RETOUR : None - Callback ne retourne rien
    - UTILITÉ : Finalisation propre optimisation réussie avec transmission thread-safe des résultats vers interface

25. error_callback.txt (HybridBaccaratPredictor.error_callback - Callback erreur générique)
    - Lignes 13004-13010 dans hbp.py (7 lignes)
    - FONCTION : Callback interne pour gestion erreurs optimisation Optuna avec finalisation thread-safe
    - PARAMÈTRES :
      * error_msg (str) - Message d'erreur détaillé
      * best_params (Dict[str, Any]) - Meilleurs paramètres trouvés avant erreur (peut être None)
      * duration (float) - Durée de l'optimisation avant erreur en secondes
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VÉRIFICATION UI :** Contrôle `if ui_available:` pour s'assurer que interface utilisateur est disponible
      * **FINALISATION THREAD-SAFE :** Utilise `self.root.after(0, lambda: self._finalize_optuna_optimization(False, best_params, duration, error_msg))` pour exécution dans thread principal
      * **LOGGING ALTERNATIF :** Si UI indisponible, utilise `logger_instance.error(f"Erreur lors de l'optimisation: {error_msg}")` pour enregistrement erreur
      * **TRANSMISSION PARAMÈTRES :** Passe `False` comme statut d'échec et `error_msg` comme message d'erreur
    - RETOUR : None - Callback ne retourne rien
    - UTILITÉ : Gestion propre erreurs optimisation avec transmission thread-safe vers interface et logging approprié

26. stop_training_process.txt (HybridBaccaratPredictor.stop_training_process - Arrêt processus entraînement)
    - FONCTION : Arrête processus d'entraînement en cours avec nettoyage sécurisé
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * force_stop (bool, optionnel) - Force arrêt immédiat (défaut: False)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VÉRIFICATION ÉTAT :** Contrôle qu'un entraînement est en cours
      * **SIGNAL ARRÊT :** Envoie signal d'arrêt au processus d'entraînement
      * **ATTENTE GRACIEUSE :** Attend arrêt propre avec timeout
      * **ARRÊT FORCÉ :** Force arrêt si timeout dépassé
      * **NETTOYAGE RESSOURCES :** Libère toutes ressources utilisées
      * **RESTAURATION ÉTAT :** Remet système dans état stable
      * **SAUVEGARDE PARTIELLE :** Sauvegarde progrès si possible
      * **MISE À JOUR STATUT :** Met à jour flags d'état système
      * **NOTIFICATION UTILISATEUR :** Informe utilisateur de l'arrêt
      * **LOGGING ARRÊT :** Enregistre raison et contexte d'arrêt
    - RETOUR : bool - True si arrêt réussi, False en cas de problème
    - UTILITÉ : Arrêt sécurisé des processus d'entraînement avec préservation données

27. generate_optimization_report.txt (HybridBaccaratPredictor.generate_optimization_report - Génération rapport optimisation)
    - Lignes 988-1088 dans hbp.py (101 lignes)
    - FONCTION : Génère rapport détaillé des résultats d'optimisation Optuna avec analyses par catégories et métriques
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * study (optuna.study.Study) - Étude Optuna complète avec tous les essais
      * best_trial (optuna.trial.FrozenTrial) - Meilleur essai de l'optimisation
    - FONCTIONNEMENT DÉTAILLÉ :
      * **EN-TÊTE RAPPORT :** Crée `report = "RAPPORT D'OPTIMISATION\n=====================\n\n"` avec formatage standardisé
      * **INFORMATIONS GÉNÉRALES :** Ajoute `f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"`, `f"Nombre total d'essais: {len(study.trials)}\n"`, `f"Meilleur score: {best_trial.value:.4f}\n\n"`
      * **CATÉGORISATION PARAMÈTRES :** Définit `categories = {'LSTM': ['lstm_'], 'LGBM': ['lgbm_'], 'Markov': ['markov_', 'max_order'], 'Seuils': ['threshold', 'confidence', 'uncertainty', 'wait_'], 'Poids': ['weight_'], 'Autres': []}`
      * **ORGANISATION PAR CATÉGORIE :** Pour chaque catégorie, filtre paramètres avec `for param, value in best_trial.params.items():` et `if any(param.startswith(prefix) or prefix in param for prefix in prefixes)`
      * **AFFICHAGE STRUCTURÉ :** Trie et affiche avec `for param, value in sorted(category_params.items()):` puis `report += f"  {param}: {value}\n"`
      * **MÉTRIQUES PERFORMANCE :** Extrait `metrics = {'max_consecutive': best_trial.user_attrs.get('max_consecutive', 0), 'precision_non_wait': best_trial.user_attrs.get('precision_non_wait', 0.0), 'wait_ratio': best_trial.user_attrs.get('wait_ratio', 0.0), 'wait_efficiency': best_trial.user_attrs.get('wait_efficiency', 0.0), 'recovery_rate_after_wait': best_trial.user_attrs.get('recovery_rate_after_wait', 0.0)}`
      * **IMPORTANCE PARAMÈTRES :** Utilise `import optuna.importance` puis `param_importances = optuna.importance.get_param_importances(study)` pour analyser influence
      * **HISTORIQUE ESSAIS :** Trie avec `sorted_trials = sorted(study.trials, key=lambda t: t.value if t.value is not None else float('-inf'), reverse=True)` pour classement
      * **TOP 10 ESSAIS :** Affiche `for i, trial in enumerate(sorted_trials[:10]):` avec `report += f"  {i+1}. Essai #{trial.number}: Score={trial.value:.4f}, Params={trial.params}\n"`
      * **GESTION ERREURS :** Capture exceptions avec `logger.error(f"Erreur lors de la génération du rapport d'optimisation: {e}", exc_info=True)` et retour message d'erreur
    - RETOUR : str - Rapport formaté complet ou message d'erreur
    - UTILITÉ : Documentation complète optimisation avec catégorisation intelligente, métriques détaillées et analyse d'importance

28. save_optimization_report.txt (HybridBaccaratPredictor.save_optimization_report - Sauvegarde rapport optimisation)
    - Lignes 1090-1119 dans hbp.py (30 lignes)
    - FONCTION : Sauvegarde rapport d'optimisation dans fichier texte avec timestamp et organisation par dossier
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * report (str) - Contenu du rapport d'optimisation à sauvegarder
      * study_name (str, optionnel) - Nom de l'étude pour suffixe fichier
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CRÉATION DOSSIER :** Utilise `report_dir = os.path.join(os.getcwd(), "optimization_reports")` puis `os.makedirs(report_dir, exist_ok=True)` pour structure
      * **GÉNÉRATION NOM :** Crée `timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")` et `study_suffix = f"_{study_name}" if study_name else ""` pour nom unique
      * **CHEMIN COMPLET :** Construit `report_path = os.path.join(report_dir, f"optimization_report{study_suffix}_{timestamp}.txt")` pour fichier final
      * **ÉCRITURE FICHIER :** Ouvre `with open(report_path, 'w', encoding='utf-8') as f:` puis `f.write(report)` pour sauvegarde UTF-8
      * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Rapport d'optimisation sauvegardé dans {report_path}")` pour traçabilité
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de la sauvegarde du rapport d'optimisation: {e}", exc_info=True)` et retour `None`
    - RETOUR : str - Chemin du fichier sauvegardé ou None en cas d'erreur
    - UTILITÉ : Persistance organisée des rapports d'optimisation avec horodatage pour analyse et archivage

29. show_optimization_results.txt (HybridBaccaratPredictor.show_optimization_results - Affichage résultats optimisation)
    - Lignes 1165-1384 dans hbp.py (220 lignes)
    - FONCTION : Affiche interface complète résultats optimisation Optuna avec graphiques Plotly et métriques détaillées
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION OPTIMIZER :** Vérifie `optimizer_to_use = getattr(self, 'current_optimizer_instance', None)` puis fallback `getattr(self, 'optimizer', None)`
      * **CONTRÔLE ÉTUDE :** Valide existence `hasattr(optimizer_to_use, 'study')` et `optimizer_to_use.study is not None` avec warning si manquant
      * **EXTRACTION DONNÉES :** Récupère `study_obj = optimizer_to_use.study` et `best_trial_obj = study_obj.best_trial` pour analyse
      * **CRÉATION DATAFRAME :** Construit `trials_data = []` avec itération sur `study_obj.trials` :
        - Filtre `trial.state == optuna.trial.TrialState.COMPLETE` pour essais terminés
        - Extrait métriques : `trial.number`, `trial.value`, `trial.user_attrs.get('max_consecutive', 0)`
        - Collecte `precision_non_wait`, `wait_ratio`, `wait_efficiency`, `recovery_rate_after_wait`
      * **VISUALISATION PLOTLY :** Crée `combined_fig = make_subplots(rows=3, cols=1)` avec :
        - **Graphique 1 :** `vis.plot_optimization_history(study_obj)` pour historique optimisation
        - **Graphique 2 :** `vis.plot_param_importances(study_obj)` pour importance paramètres
        - **Graphique 3 :** Graphique barres métriques clés du meilleur essai avec normalisation
      * **SAUVEGARDE HTML :** Utilise `tempfile.NamedTemporaryFile(suffix=".html")` puis `combined_fig.to_html(full_html=True, include_plotlyjs='cdn')`
      * **OUVERTURE NAVIGATEUR :** Appelle `webbrowser.open(f"file://{os.path.realpath(temp_file_path)}")` pour affichage
      * **FENÊTRE RÉSULTATS :** Crée `result_window = tk.Toplevel(self.root)` avec boutons :
        - "Générer Rapport Détaillé" qui appelle `self.generate_optimization_report(study_obj, study_obj.best_trial)`
        - "Fermer" pour `result_window.destroy()`
      * **GESTION ERREURS :** Capture exceptions avec `messagebox.showerror` et fallback gracieux
    - RETOUR : None - Affiche interface graphique
    - UTILITÉ : Interface complète visualisation optimisation avec graphiques interactifs Plotly et métriques spécialisées objectif 1

30. _update_dependent_configs.txt (HybridBaccaratPredictor._update_dependent_configs - MAJ configurations dépendantes)
    - Lignes 13517-13568 dans hbp.py (52 lignes)
    - FONCTION : Met à jour configurations dépendantes après changement paramètres système avec adaptation ressources
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))` pour logging
      * **VALIDATION CONFIG :** Vérifie `if not hasattr(self, 'config'): return` pour s'assurer que configuration existe
      * **MISE À JOUR LGBM :** Adapte paramètres LGBM selon ressources système :
        - Récupère `cpu_count = getattr(self.config, 'cpu_count', os.cpu_count())` pour nombre de processeurs
        - Calcule `n_jobs_lgbm = max(1, min(cpu_count - 1, 8))` pour optimiser parallélisme
        - Met à jour `self.config.lgbm['n_jobs'] = n_jobs_lgbm` avec logging détaillé
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger_instance.error(f"Erreur lors de la mise à jour de n_jobs pour LGBM dans la config : {e}")`
      * **EXTENSIBILITÉ :** Prévoit ajout autres configurations (Dask, Ray, etc.) si nécessaires
      * **LOGGING FINAL :** Enregistre `logger_instance.debug("Mise à jour des configurations dépendantes terminée.")` pour traçabilité
    - RETOUR : None - Met à jour directement la configuration interne
    - UTILITÉ : Maintient cohérence configurations système avec adaptation automatique ressources disponibles

31. initialize_lgbm_cache.txt (HybridBaccaratPredictor.initialize_lgbm_cache - Initialisation cache LGBM)
    - Lignes 11734-11740 dans hbp.py (7 lignes)
    - FONCTION : Initialise système de cache double (deque + dict) pour optimiser prédictions LGBM avec taille configurable
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * maxlen (int, optionnel) - Taille maximale du cache (défaut: depuis config ou 100)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **DÉTERMINATION TAILLE :** Utilise `maxlen = getattr(self.config, 'lgbm_cache_maxlen', 100)` si paramètre non fourni
      * **INITIALISATION DEQUE :** Crée `self.lgbm_cache = deque(maxlen=maxlen)` pour stockage FIFO avec limite automatique
      * **INITIALISATION DICT :** Crée `self.lgbm_cache_dict = {}` pour accès rapide O(1) par clé de features
      * **LOGGING :** Enregistre `logger.info(f"Initialisation du cache LGBM avec une taille maximale de {maxlen}.")` pour traçabilité
    - RETOUR : None - Initialise structures de cache internes
    - UTILITÉ : Optimisation performance prédictions LGBM avec cache double pour accès rapide et gestion mémoire automatique

32. train_models.txt (HybridBaccaratPredictor.train_models - Entraînement modèles principal)
    - Lignes 1797-2210 dans hbp.py (414 lignes)
    - FONCTION : Point d'entrée principal pour entraînement complet des modèles ML avec pipeline intégré et gestion d'erreurs robuste
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * use_historical (bool, optionnel) - Utilise données historiques si disponibles (défaut: True)
      * async_mode (bool, optionnel) - Mode asynchrone pour entraînement non-bloquant (défaut: True)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **PROTECTION CONCURRENCE :** Utilise `with self.training_lock:` puis vérifie `if self.is_training or self.is_fast_updating:` avec warning et retour si processus actif
      * **MARQUAGE ÉTAT :** Définit `self.is_training = True` et `self.stop_training = False` pour contrôle processus
      * **VALIDATION UI :** Vérifie `ui_available = self.is_ui_available()` pour adaptation interface
      * **COLLECTE DONNÉES :** Combine séquence actuelle et données historiques :
        - Copie `sequence_copy = self.sequence[:]` avec `sequence_lock`
        - Si `use_historical and self.loaded_historical:`, ajoute `historical_data_copy = self.historical_data[:]`
        - Combine avec `combined_data = historical_data_copy + sequence_copy` et déduplique
      * **VALIDATION TAILLE :** Vérifie `if len(combined_data) < self.config.min_data_for_training:` avec message erreur et retour False
      * **PRÉPARATION FEATURES :** Appelle `self.prepare_features_for_training(combined_data)` qui retourne tuple `(X_lgbm, y_lgbm, X_lstm, y_lstm)`
      * **VALIDATION FEATURES :** Contrôle que toutes features sont non-None et non-vides avec logging détaillé
      * **CONFIGURATION OVERRIDE :** Prépare `config_override = {}` pour paramètres spécifiques si nécessaire
      * **MODE ASYNCHRONE :** Si `async_mode:`, lance `threading.Thread(target=self._train_models_async, args=(X_lgbm, y_lgbm, X_lstm, y_lstm, config_override), daemon=True).start()`
      * **MODE SYNCHRONE :** Sinon, appelle directement `self._train_models_async(X_lgbm, y_lgbm, X_lstm, y_lstm, config_override)`
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur pendant train_models: {e}", exc_info=True)` et nettoyage état
      * **NETTOYAGE FINAL :** Assure `self.is_training = False` dans bloc finally
    - RETOUR : bool - True si lancement réussi, False si erreur ou conditions non remplies
    - UTILITÉ : Interface principale entraînement avec pipeline complet, gestion concurrence et support asynchrone pour interface responsive

================================================================================
SECTION 6 : RESEAUX NEURONAUX
================================================================================

1. predict_with_lgbm.txt (HybridBaccaratPredictor.predict_with_lgbm - Prédiction LGBM calibrée)
   - Lignes 8298-8414 dans hbp.py (117 lignes)
   - FONCTION : Effectue une prédiction en utilisant le modèle LGBM calibré avec gestion complète des erreurs et logging intelligent
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * feature (Optional[List[float]]) - Vecteur de features pour la prédiction LGBM
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION FEATURE :** Vérifie que le vecteur de features n'est pas None
     * **PROTECTION MODÈLE :** Utilise model_lock pour accès thread-safe au modèle calibré
     * **DÉTECTION PHASE :** Identifie si en phase d'entraînement ou optimisation Optuna pour adapter le logging
     * **VÉRIFICATION MODÈLE :** Contrôle que le modèle calibré et le scaler sont initialisés et fittés
     * **NORMALISATION FEATURES :** Applique le feature_scaler pour normaliser les données d'entrée
     * **PRÉDICTION PROBABILISTE :** Utilise predict_proba pour obtenir les probabilités Player/Banker
     * **NORMALISATION PROBABILITÉS :** Vérifie et normalise la somme des probabilités si nécessaire
     * **GESTION ERREURS :** Traite NotFittedError, ValueError et autres exceptions avec logging adaptatif
   - RETOUR : Dict[str, float] - Dictionnaire avec probabilités 'player' et 'banker' (défaut 0.5/0.5 si erreur)
   - UTILITÉ : Fournit des prédictions LGBM robustes avec gestion intelligente des cas d'erreur et logging adapté au contexte

2. predict_with_lstm.txt (HybridBaccaratPredictor.predict_with_lstm - Prédiction LSTM optimisée)
   - Lignes 8113-8225 dans hbp.py (113 lignes)
   - FONCTION : Effectue une prédiction LSTM avec approche optimisée pour réduire la latence et gestion robuste des erreurs
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * lstm_features (Optional[np.ndarray]) - Features LSTM préparées ou None pour génération automatique
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MODÈLE :** Vérifie `if not hasattr(self, 'lstm_model') or self.lstm_model is None:` avec logging adaptatif selon phase optimisation
     * **GESTION FEATURES :** Si `lstm_features is None`, appelle `lstm_features = self.handle_short_sequence(self.sequence)` pour génération automatique
     * **VALIDATION SHAPE :** Contrôle `if lstm_features.shape != (self.config.lstm_sequence_length, self.config.lstm_input_size):` avec reshape si nécessaire
     * **OPTIMISATION BATCH :** Ajoute dimension batch avec `lstm_features_batch = np.expand_dims(lstm_features, axis=0)` pour compatibilité TensorFlow
     * **PRÉDICTION TENSORFLOW :** Exécute `predictions = self.lstm_model.predict(lstm_features_batch, verbose=0)` avec gestion erreurs
     * **EXTRACTION PROBABILITÉS :** Récupère `prob_banker = float(predictions[0][1])` et calcule `prob_player = 1.0 - prob_banker`
     * **VALIDATION BORNES :** Applique `np.clip(prob_banker, 0.0, 1.0)` et `np.clip(prob_player, 0.0, 1.0)` pour sécurité numérique
     * **GESTION ERREURS :** Capture `NotFittedError` et exceptions générales avec fallback vers `{'player': 0.5, 'banker': 0.5}`
     * **LOGGING CONDITIONNEL :** Évite spam logs pendant optimisation Optuna avec vérification `is_optuna_running` et `optimization_stats_collector`
   - RETOUR : Dict[str, float] - Dictionnaire avec probabilités 'player' et 'banker' normalisées (0.5/0.5 si erreur)
   - UTILITÉ : Fournit des prédictions LSTM robustes avec gestion intelligente des cas d'erreur et optimisation performance

3. hybrid_prediction.txt (HybridBaccaratPredictor.hybrid_prediction - Prédiction hybride avancée)
   - Lignes 8795-9937 dans hbp.py (1143 lignes)
   - FONCTION : Effectue une prédiction hybride sophistiquée combinant Markov, LGBM et LSTM avec pondération bayésienne et calcul d'incertitude multi-niveaux
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * lgbm_feat (Optional[List[float]]) - Features pour le modèle LGBM
     * lstm_feat (Optional[np.ndarray]) - Features pour le modèle LSTM
     * optimization_phase (Optional[int]) - Phase d'optimisation (1=équilibre WAIT/NON-WAIT, 2=recommandations consécutives, None=normal)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PRÉDICTIONS INDIVIDUELLES :** Collecte prédictions Markov, LGBM et LSTM avec gestion d'erreurs robuste
     * **VALIDATION MODÈLES :** Vérifie quels modèles sont entraînés et disponibles pour la combinaison
     * **PONDÉRATION BAYÉSIENNE :** Calcule poids adaptatifs basés sur performance historique et confiance
     * **COMBINAISON PONDÉRÉE :** Fusionne prédictions avec poids effectifs ajustés par confiance
     * **INCERTITUDE ÉPISTÉMIQUE :** Mesure désaccord entre modèles pour évaluer fiabilité
     * **INCERTITUDE ALÉATOIRE :** Calcule entropie de la prédiction finale
     * **SENSIBILITÉ CONTEXTUELLE :** Analyse adaptation aux patterns de séquence
     * **CONFIANCE CONSÉCUTIVE :** Utilise calculateur spécialisé pour manches 31-60
     * **SYSTÈME DÉCISION :** Détermine recommandation finale avec seuils adaptatifs
   - RETOUR : Dict - Dictionnaire complet avec prédictions, recommandation, incertitudes et métriques détaillées
   - UTILITÉ : Cœur du système de prédiction avec intelligence artificielle avancée et gestion d'incertitude sophistiquée

================================================================================
SECTION 7 : UTILITAIRES FONCTIONS
================================================================================

1. __init__.txt (HybridBaccaratPredictor.__init__ - Constructeur principal de la classe)
   - Lignes 541-779 dans hbp.py (239 lignes)
   - FONCTION : Initialise une instance de HybridBaccaratPredictor avec configuration complète des modèles ML, interface utilisateur et gestion des ressources
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * root_or_config (Union[tk.Tk, PredictorConfig]) - Objet root Tkinter pour mode UI ou objet PredictorConfig pour mode Optuna
   - FONCTIONNEMENT DÉTAILLÉ :
     * **OPTIMISATION MÉMOIRE :** Optimise l'utilisation mémoire PyTorch avant toute opération
     * **CONFIGURATION LOGGER :** Initialise le système de logging avec handlers console et fichier
     * **DÉTECTION MODE :** Détermine si c'est un mode UI (Tkinter) ou mode Optuna (sans UI)
     * **INITIALISATION ÉTAT :** Configure les attributs d'état (séquences, historique, cache LGBM)
     * **MODÈLES ML :** Initialise les placeholders pour LGBM, LSTM, optimiseurs et schedulers
     * **MODÈLE MARKOV :** Configure le modèle PersistentMarkov avec paramètres adaptatifs
     * **GESTION PERFORMANCE :** Initialise le suivi des performances et poids des méthodes
     * **VERROUS THREADING :** Crée les verrous pour accès concurrent sécurisé
     * **CONFIGURATION DEVICE :** Détecte et configure l'utilisation CPU/GPU
     * **INTERFACE UTILISATEUR :** Configure l'UI Tkinter si en mode interface
     * **CHARGEMENT AUTO :** Tente le chargement automatique de l'état précédent
   - RETOUR : None - Constructeur ne retourne rien
   - UTILITÉ : Point d'entrée principal pour créer une instance fonctionnelle du système de prédiction

2. safe_record_outcome.txt (HybridBaccaratPredictor.safe_record_outcome - Enregistrement sécurisé résultat)
   - Lignes 12344-12486 dans hbp.py (143 lignes)
   - FONCTION : Enregistre le résultat d'une manche de manière thread-safe avec limite 60 manches et auto-update
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * outcome (str) - Résultat de la manche ('player' ou 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION LIMITE :** Contrôle limite 60 manches avant enregistrement
     * **ACQUISITION VERROUS :** Prend tous les verrous nécessaires pour cohérence thread-safe
     * **MISE À JOUR SÉQUENCE :** Ajoute outcome à la séquence et calcule numéro manche
     * **AUTO-UPDATE :** Appelle auto_fast_update_if_needed pour manches cibles
     * **MISE À JOUR MARKOV :** Met à jour modèle Markov session avec nouvelle séquence
     * **PATTERNS :** Met à jour compteurs de patterns avec nouveau résultat
     * **PRÉDICTION SUIVANTE :** Génère features et effectue hybrid_prediction pour coup suivant
     * **MISE À JOUR POIDS :** Ajuste poids méthodes basé sur prédiction précédente vs résultat actuel
     * **CONFIANCE CONSÉCUTIVE :** Met à jour calculateur pour manches 31-60
     * **PLANIFICATION UI :** Programme mises à jour interface via root.after
   - RETOUR : None - Méthode d'enregistrement ne retourne rien
   - UTILITÉ : Cœur du système de traitement des résultats avec gestion complète de l'état et optimisations avec tous les composants initialisés

3. undo_last_move.txt (HybridBaccaratPredictor.undo_last_move - Annulation dernier coup)
   - Lignes 10780-10893 dans hbp.py (114 lignes)
   - FONCTION : Annule le dernier coup enregistré de manière thread-safe avec restauration complète état précédent et mise à jour UI
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION UI :** Vérifie `ui_available = self.is_ui_available()` pour adaptation interface
     * **ACQUISITION VERROUS :** Utilise `with self.sequence_lock, self.markov_lock, self.model_lock, self.weights_lock:` pour cohérence thread-safe
     * **VALIDATION SÉQUENCE :** Teste `if not self.sequence:` avec `messagebox.showwarning("Annulation Impossible", "La séquence de jeu est vide.")` et retour
     * **CONFIRMATION UTILISATEUR :** Si UI disponible, affiche `messagebox.askyesno("Confirmation", "Voulez-vous vraiment annuler la dernière manche enregistrée ?")` pour validation
     * **SUPPRESSION SÉQUENCE :** Exécute `last_outcome = self.sequence.pop()` pour retirer dernier élément avec sauvegarde valeur
     * **NETTOYAGE HISTORIQUE :** Si `self.prediction_history:`, supprime `removed_prediction = self.prediction_history.pop()` pour cohérence prédictions
     * **MISE À JOUR PATTERNS :** Si `len(self.sequence) >= 3:` :
       - Calcule `pattern_to_decrement = tuple(self.sequence[-3:] + [last_outcome])` pour motif à décrémenter
       - Décrémente `self.pattern_counts[dict_key][pattern_to_decrement] -= 1` et supprime si `<= 0`
     * **RÉINITIALISATION MARKOV :** Si `self.markov:`, appelle `self.markov.reset(reset_type='soft')` puis `self.markov.update_session(self.sequence)` pour recalcul
     * **VIDAGE CACHE :** Recrée `self.lgbm_cache = deque(maxlen=100)` pour invalidation cache
     * **AJUSTEMENT INDEX :** Si `len(self.sequence) < self.last_incremental_update_index:`, remet `self.last_incremental_update_index = 0` pour mise à jour complète
     * **NOUVELLE PRÉDICTION :** Appelle `lgbm_feat, lstm_feat = self.create_hybrid_features(self.sequence)` puis `current_prediction = self.hybrid_prediction(lgbm_feat, lstm_feat)` pour état actuel
     * **MISE À JOUR UI :** Si UI disponible, planifie `self.root.after(0, lambda p=pred_copy: self.lightweight_update_display(p))` et `self.root.after(10, self.update_display)` pour rafraîchissement
     * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error()` et `messagebox.showerror()` pour feedback utilisateur
   - RETOUR : None - Méthode d'interface utilisateur ne retourne rien
   - UTILITÉ : Correction sécurisée erreurs saisie avec restauration complète état système, synchronisation modèles et interface responsive

4. unified_save.txt (HybridBaccaratPredictor.unified_save - Sauvegarde unifiée)
   - Lignes 10895-10978 dans hbp.py (84 lignes)
   - FONCTION : Sauvegarde unifiée de l'état complet du système avec validation et gestion d'erreurs robuste
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * save_path (str, optionnel) - Chemin de sauvegarde personnalisé (défaut: None pour auto-génération)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION UI :** Vérifie `ui_available = self.is_ui_available()` pour adaptation interface
     * **GÉNÉRATION CHEMIN :** Si `save_path is None:`, génère `timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")` puis `save_path = f"hbp_save_{timestamp}.joblib"` pour nom unique
     * **CONFIRMATION UTILISATEUR :** Si UI disponible, affiche `messagebox.askyesno("Confirmation de Sauvegarde", f"Sauvegarder l'état actuel vers:\n{save_path}")` pour validation
     * **ACQUISITION VERROUS :** Utilise `with self.sequence_lock, self.markov_lock, self.model_lock, self.weights_lock:` pour cohérence thread-safe
     * **COLLECTE ÉTAT :** Appelle `state_to_save = self._collect_state_for_save()` pour rassembler toutes données système
     * **VALIDATION ÉTAT :** Vérifie `if state_to_save is None:` avec logging erreur et messagebox si échec collecte
     * **SAUVEGARDE FICHIER :** Utilise `joblib.dump(state_to_save, save_path, compress=3)` avec compression niveau 3 pour optimisation espace
     * **VALIDATION FICHIER :** Teste `if os.path.exists(save_path) and os.path.getsize(save_path) > 0:` pour confirmer création réussie
     * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Sauvegarde unifiée réussie: {save_path} ({os.path.getsize(save_path)} octets)")` avec taille fichier
     * **FEEDBACK UTILISATEUR :** Si UI disponible, affiche `messagebox.showinfo("Sauvegarde Réussie", f"État sauvegardé avec succès:\n{save_path}\nTaille: {os.path.getsize(save_path)} octets")` pour confirmation
     * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de la sauvegarde unifiée: {e}", exc_info=True)` et messagebox d'erreur détaillé
   - RETOUR : bool - True si sauvegarde réussie, False en cas d'erreur
   - UTILITÉ : Point d'entrée principal pour sauvegarde complète et cohérente avec validation utilisateur et gestion d'erreurs robuste

5. is_ui_available.txt (HybridBaccaratPredictor.is_ui_available - Vérification disponibilité UI)
   - Lignes 619-620 dans hbp.py (2 lignes)
   - FONCTION : Vérifie si interface utilisateur Tkinter est disponible et fonctionnelle avec validation simple et efficace
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ROOT :** Teste `return hasattr(self, 'root') and self.root is not None` pour vérifier existence objet root Tkinter
     * **LOGIQUE SIMPLE :** Retourne directement le résultat du test d'existence sans validation complexe
     * **PERFORMANCE OPTIMISÉE :** Méthode ultra-légère appelée fréquemment pour adaptation interface
   - RETOUR : bool - True si interface Tkinter disponible, False sinon
   - UTILITÉ : Validation rapide disponibilité UI pour adaptation comportement selon mode (interface/headless)

6. is_ui_available.txt (HybridBaccaratPredictor.is_ui_available - Vérification disponibilité UI)
   - Lignes 5254-5258 dans hbp.py (5 lignes)
   - FONCTION : Vérifie si interface utilisateur est disponible et fonctionnelle pour adaptation comportement
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ROOT :** Vérifie `return hasattr(self, 'root') and self.root is not None` pour existence interface Tkinter
     * **TEST FONCTIONNEL :** Confirme que l'objet root existe et n'est pas None
     * **ADAPTATION COMPORTEMENT :** Permet aux méthodes d'adapter leur comportement selon disponibilité UI
     * **GESTION MODES :** Supporte mode headless (sans interface) et mode interface graphique
   - RETOUR : bool - True si interface disponible, False sinon
   - UTILITÉ : Validation rapide disponibilité UI pour adaptation comportement selon mode (interface/headless)

7. _perform_save.txt (HybridBaccaratPredictor._perform_save - Exécution sauvegarde)
   - Lignes 10980-11118 dans hbp.py (139 lignes)
   - FONCTION : Exécute sauvegarde effective avec validation, collecte état et gestion d'erreurs robuste
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * save_path (str) - Chemin de sauvegarde spécifié
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION CHEMIN :** Vérifie validité du chemin avec `os.path.dirname(save_path)` et création répertoire si nécessaire
     * **COLLECTE ÉTAT :** Appelle `state_to_save = self._collect_state_for_save()` pour rassembler toutes données système
     * **VALIDATION ÉTAT :** Teste `if state_to_save is None:` avec logging erreur et retour False si échec collecte
     * **SAUVEGARDE JOBLIB :** Utilise `joblib.dump(state_to_save, save_path, compress=3)` avec compression niveau 3 pour optimisation
     * **VALIDATION FICHIER :** Vérifie `if os.path.exists(save_path) and os.path.getsize(save_path) > 0:` pour confirmer création réussie
     * **CALCUL TAILLE :** Détermine `file_size = os.path.getsize(save_path)` pour logging et feedback
     * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Sauvegarde réussie: {save_path} ({file_size} octets)")` avec détails
     * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de la sauvegarde: {e}", exc_info=True)` et retour False
   - RETOUR : bool - True si sauvegarde réussie, False en cas d'erreur
   - UTILITÉ : Méthode interne pour sauvegarde sécurisée avec validation complète et gestion d'erreurs

8. replace_value.txt (HybridBaccaratPredictor.replace_value - Remplacement valeur)
   - Lignes 11202-11465 dans hbp.py (264 lignes)
   - FONCTION : Remplace valeur dans séquence avec validation, mise à jour modèles et synchronisation complète
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * index (int) - Index de la valeur à remplacer dans la séquence
     * new_value (str) - Nouvelle valeur ('Player' ou 'Banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION UI :** Vérifie `ui_available = self.is_ui_available()` pour adaptation interface
     * **ACQUISITION VERROUS :** Utilise `with self.sequence_lock, self.markov_lock, self.model_lock, self.weights_lock:` pour cohérence thread-safe
     * **VALIDATION INDEX :** Teste `if not (0 <= index < len(self.sequence)):` avec `messagebox.showerror("Index invalide", f"L'index {index} n'est pas valide.")` et retour
     * **VALIDATION VALEUR :** Vérifie `if new_value not in ['Player', 'Banker']:` avec messagebox d'erreur et retour
     * **CONFIRMATION UTILISATEUR :** Si UI disponible, affiche `messagebox.askyesno("Confirmation", f"Remplacer '{old_value}' par '{new_value}' à l'index {index} ?")` pour validation
     * **SAUVEGARDE ANCIEN :** Stocke `old_value = self.sequence[index]` pour logging et rollback potentiel
     * **REMPLACEMENT :** Exécute `self.sequence[index] = new_value` pour modification effective
     * **MISE À JOUR PATTERNS :** Recalcule patterns affectés :
       - Identifie patterns contenant l'index modifié avec `affected_patterns = []`
       - Décrémente anciens patterns et incrémente nouveaux patterns dans `self.pattern_counts`
     * **MISE À JOUR HISTORIQUE :** Si `index < len(self.prediction_history):`, met à jour `self.prediction_history[index]['actual_outcome'] = new_value` pour cohérence
     * **RÉINITIALISATION MARKOV :** Appelle `self.markov.reset(reset_type='soft')` puis `self.markov.update_session(self.sequence)` pour recalcul complet
     * **VIDAGE CACHE :** Recrée `self.lgbm_cache = deque(maxlen=100)` pour invalidation cache
     * **AJUSTEMENT INDEX :** Si `index < self.last_incremental_update_index:`, remet `self.last_incremental_update_index = 0` pour mise à jour complète
     * **NOUVELLE PRÉDICTION :** Génère nouvelle prédiction avec `lgbm_feat, lstm_feat = self.create_hybrid_features(self.sequence)` puis `current_prediction = self.hybrid_prediction(lgbm_feat, lstm_feat)`
     * **MISE À JOUR UI :** Si UI disponible, planifie `self.root.after(0, lambda p=pred_copy: self.lightweight_update_display(p))` et `self.root.after(10, self.update_display)` pour rafraîchissement
     * **LOGGING :** Enregistre `logger.info(f"Valeur remplacée à l'index {index}: '{old_value}' -> '{new_value}'")` pour traçabilité
     * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error()` et `messagebox.showerror()` pour feedback utilisateur
   - RETOUR : None - Méthode de modification ne retourne rien
   - UTILITÉ : Modification sécurisée des données historiques avec synchronisation complète modèles et interface

9. replace_weights.txt (HybridBaccaratPredictor.replace_weights - Remplacement poids)
   - Lignes 11529-11531 dans hbp.py (3 lignes)
   - FONCTION : Remplace poids des méthodes avec validation et normalisation automatique pour cohérence système
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * new_weights (Dict[str, float]) - Nouveaux poids pour les méthodes
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION POIDS :** Vérifie que tous poids sont positifs avec `if any(w < 0 for w in new_weights.values()):`
     * **NORMALISATION :** Calcule `total = sum(new_weights.values())` puis normalise avec `self.weights = {k: v/total for k, v in new_weights.items()}`
     * **MISE À JOUR BEST :** Met à jour `self.best_weights = self.weights.copy()` pour cohérence
   - RETOUR : None - Méthode de configuration ne retourne rien
   - UTILITÉ : Ajustement manuel des poids avec normalisation automatique et cohérence système

10. filter_none_values.txt (HybridBaccaratPredictor.filter_none_values - Filtrage valeurs None)
   - Lignes 2445-2448 dans hbp.py (4 lignes)
   - FONCTION : Fonction utilitaire récursive qui filtre et nettoie valeurs None des structures de données imbriquées
   - PARAMÈTRES :
     * d - Structure de données à nettoyer (dict, list, ou valeur simple)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **TEST TYPE :** Vérifie `if isinstance(d, dict):` pour traitement spécialisé dictionnaires
     * **FILTRAGE RÉCURSIF :** Utilise compréhension `{k: filter_none_values(v) for k, v in d.items() if v is not None}` pour nettoyer récursivement
     * **APPEL RÉCURSIF :** Applique `filter_none_values(v)` sur chaque valeur pour nettoyage en profondeur
     * **CONDITION FILTRAGE :** Exclut entrées avec `if v is not None` pour éliminer valeurs None
     * **RETOUR DIRECT :** Si pas dictionnaire, retourne `return d` sans modification
   - RETOUR : Structure nettoyée - Dictionnaire sans valeurs None ou valeur inchangée
   - UTILITÉ : Nettoyage récursif données pour éviter erreurs de traitement avec valeurs None dans structures imbriquées

11. undo_last_move_1.txt (HybridBaccaratPredictor.undo_last_move_1 - Annulation dernier coup v1)
    - Lignes 11666-11779 dans hbp.py (114 lignes)
    - FONCTION : Version alternative d'annulation avec options avancées et gestion différenciée selon contexte
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * force (bool, optionnel) - Force annulation sans confirmation (défaut: False)
      * update_ui (bool, optionnel) - Met à jour interface après annulation (défaut: True)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `ui_available = self.is_ui_available()` pour adaptation interface
      * **ACQUISITION VERROUS :** Utilise `with self.sequence_lock, self.markov_lock, self.model_lock, self.weights_lock:` pour cohérence thread-safe
      * **VALIDATION SÉQUENCE :** Teste `if not self.sequence:` avec `messagebox.showwarning("Annulation Impossible", "La séquence de jeu est vide.")` et retour
      * **CONFIRMATION CONDITIONNELLE :** Si `not force` et UI disponible, affiche `messagebox.askyesno("Confirmation", "Voulez-vous vraiment annuler la dernière manche enregistrée ?")` pour validation
      * **SUPPRESSION SÉQUENCE :** Exécute `last_outcome = self.sequence.pop()` pour retirer dernier élément avec sauvegarde valeur
      * **NETTOYAGE HISTORIQUE :** Si `self.prediction_history:`, supprime `removed_prediction = self.prediction_history.pop()` pour cohérence prédictions
      * **MISE À JOUR PATTERNS :** Recalcule patterns affectés avec décrémentation anciens compteurs
      * **RÉINITIALISATION MARKOV :** Appelle `self.markov.reset(reset_type='soft')` puis `self.markov.update_session(self.sequence)` pour recalcul
      * **VIDAGE CACHE :** Recrée `self.lgbm_cache = deque(maxlen=100)` pour invalidation cache
      * **AJUSTEMENT INDEX :** Si `len(self.sequence) < self.last_incremental_update_index:`, remet `self.last_incremental_update_index = 0` pour mise à jour complète
      * **NOUVELLE PRÉDICTION :** Génère nouvelle prédiction avec `lgbm_feat, lstm_feat = self.create_hybrid_features(self.sequence)` puis `current_prediction = self.hybrid_prediction(lgbm_feat, lstm_feat)`
      * **MISE À JOUR UI CONDITIONNELLE :** Si `update_ui and ui_available:`, planifie mises à jour interface avec `self.root.after()`
      * **LOGGING :** Enregistre `logger.info(f"Mouvement annulé (v1): {last_outcome}. Force: {force}, Update UI: {update_ui}")` pour traçabilité
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error()` et `messagebox.showerror()` pour feedback utilisateur
    - RETOUR : bool - True si annulation réussie, False en cas d'erreur
    - UTILITÉ : Annulation avec paramètres configurables pour intégration dans workflows automatisés ou manuels

12. _undo_last_move_unsafe.txt (HybridBaccaratPredictor._undo_last_move_unsafe - Annulation non sécurisée)
    - Lignes 11781-11825 dans hbp.py (45 lignes)
    - FONCTION : Annulation rapide sans toutes les validations de sécurité pour performance optimisée dans contextes contrôlés
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION MINIMALE :** Teste uniquement `if not self.sequence:` avec retour False si séquence vide
      * **SUPPRESSION DIRECTE :** Exécute `last_outcome = self.sequence.pop()` sans confirmation utilisateur
      * **NETTOYAGE HISTORIQUE :** Si `self.prediction_history:`, supprime `self.prediction_history.pop()` pour cohérence
      * **MISE À JOUR PATTERNS RAPIDE :** Décrémente patterns affectés sans validation extensive
      * **RESET MARKOV MINIMAL :** Appelle `self.markov.reset(reset_type='soft')` puis `self.markov.update_session(self.sequence)` pour recalcul
      * **VIDAGE CACHE :** Recrée `self.lgbm_cache = deque(maxlen=100)` pour invalidation
      * **AJUSTEMENT INDEX :** Remet `self.last_incremental_update_index = 0` si nécessaire
      * **PAS DE CONFIRMATION :** Aucune validation utilisateur ou messagebox pour performance
      * **PAS DE GESTION ERREURS :** Laisse exceptions se propager pour debugging
      * **LOGGING MINIMAL :** Enregistre uniquement `logger.debug(f"Annulation unsafe: {last_outcome}")` pour traçabilité
    - RETOUR : bool - True si annulation réussie, False si séquence vide
    - UTILITÉ : Annulation optimisée pour cas d'usage spécifiques où performance prime sur sécurité (tests, batch processing)

13. _find_latest_state_file.txt (HybridBaccaratPredictor._find_latest_state_file - Recherche dernier fichier état)
    - Lignes 11124-11155 dans hbp.py (32 lignes)
    - FONCTION : Recherche et identifie le fichier d'état (.joblib ou .pkl) le plus récent dans répertoire spécifié
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * save_dir (str) - Répertoire de recherche des fichiers d'état
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION RÉPERTOIRE :** Vérifie `if not os.path.isdir(save_dir):` avec logging warning et retour `(None, None)` si inexistant
      * **INITIALISATION LISTE :** Crée `all_files = []` pour collecter fichiers candidats avec métadonnées
      * **PARCOURS FICHIERS :** Itère `for filename in os.listdir(save_dir):` pour examiner chaque fichier
      * **FILTRAGE EXTENSIONS :** Vérifie `ext_lower = os.path.splitext(filename)[1].lower()` puis `if os.path.isfile(filepath) and ext_lower in [".joblib", ".pkl"]:`
      * **EXTRACTION MÉTADONNÉES :** Pour chaque fichier valide, récupère `mod_time = os.path.getmtime(filepath)` et ajoute `(mod_time, filepath, ext_lower)` à la liste
      * **GESTION ERREURS MTIME :** Capture `OSError as e_mtime:` avec logging warning si impossible d'obtenir date modification
      * **GESTION ERREURS GLOBALES :** Capture `Exception as e:` avec logging error et `exc_info=True` pour erreurs listage répertoire
      * **VALIDATION RÉSULTATS :** Vérifie `if not all_files:` avec logging info et retour `(None, None)` si aucun fichier trouvé
      * **TRI CHRONOLOGIQUE :** Utilise `all_files.sort(key=lambda item: item[0], reverse=True)` pour trier par date modification décroissante
      * **SÉLECTION PLUS RÉCENT :** Extrait `latest_mtime, latest_path, latest_ext = all_files[0]` pour récupérer le plus récent
      * **LOGGING RÉSULTAT :** Enregistre `logger.info(f"Dernier fichier identifié: {os.path.basename(latest_path)} ({latest_ext})")`
    - RETOUR : Tuple[Optional[str], Optional[str]] - (chemin_fichier, extension) ou (None, None) si aucun trouvé
    - UTILITÉ : Localisation automatique dernière sauvegarde avec support .joblib/.pkl et tri chronologique robuste

14. _load_latest_state.txt (HybridBaccaratPredictor._load_latest_state - Chargement dernier état)
    - Lignes 10992-11122 dans hbp.py (131 lignes)
    - FONCTION : Charge automatiquement le dernier état (.joblib ou .pkl) avec stratégie de fallback et initialisation douce
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `save_dir = MODEL_SAVE_DIR`, `historical_txt_path = "historical_data.txt"`, `ui_available = self.is_ui_available()`, `state_loaded_successfully = False`
      * **RECHERCHE FICHIER :** Appelle `latest_path, latest_ext = self._find_latest_state_file(save_dir)` pour identifier dernier fichier
      * **CAS AUCUN FICHIER :** Si `latest_path is None`, log "Aucun état sauvegardé trouvé", appelle `self.init_ml_models(reset_weights=True)` et met à jour progression "Prêt (Nouvelle Session)"
      * **STRATÉGIE JOBLIB :** Si `latest_ext == ".joblib"` :
        - Vérifie existence `historical_data.txt` avec `if not os.path.exists(historical_txt_path):`
        - Charge historique avec `hist_load_ok_for_joblib = self._load_historical_txt(historical_txt_path)`
        - Si succès, appelle `self.load_trained_models(latest_path)` et met `state_loaded_successfully = True`
        - Met à jour progression "Chargement état: historique + joblib OK"
      * **STRATÉGIE FALLBACK PKL :** Si `not state_loaded_successfully` :
        - Détermine `target_pkl_path` soit depuis `latest_path` si `.pkl`, soit recherche nouveau `.pkl`
        - Réinitialise `self.loaded_historical = False`, `self.historical_games_at_startup_or_reset = 0`, `self.historical_data = []`
        - Appelle `self.load_trained_models(target_pkl_path)` et met progression "Chargement état: .pkl OK"
      * **CAS ÉCHEC TOTAL :** Si `not state_loaded_successfully` :
        - Log warning "Aucun état valide n'a pu être chargé"
        - Vérifie `needs_init_reset = (len(self.sequence) != 0 or self.loaded_historical or self.lgbm_base is None)`
        - Appelle `self.init_ml_models(reset_weights=True)` et `self.root.after(0, self._reset_session_display)`
        - Met progression "Pas de save : Session Vierge"
    - RETOUR : None - Méthode d'initialisation ne retourne rien
    - UTILITÉ : Restauration automatique intelligente avec fallback .pkl et initialisation douce sans hard reset

15. _load_latest_state_1.txt (HybridBaccaratPredictor._load_latest_state_1 - Chargement dernier état v1)
    - Lignes 12214-12342 dans hbp.py (129 lignes)
    - FONCTION : Version alternative chargement dernier état avec reset visuel et gestion améliorée des échecs
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit variables similaires à `_load_latest_state` avec `save_dir = MODEL_SAVE_DIR`, `historical_txt_path = "historical_data.txt"`
      * **RECHERCHE FICHIER :** Utilise `latest_path, latest_ext = self._find_latest_state_file(save_dir)` pour identifier dernier fichier
      * **STRATÉGIE JOBLIB AMÉLIORÉE :** Si `.joblib` trouvé :
        - Charge historique avec `hist_load_ok_for_joblib = self._load_historical_txt(historical_txt_path)`
        - Si succès, appelle `self.load_trained_models(latest_path)` et met `state_loaded_successfully = True`
        - **RESET VISUEL :** Appelle `self.root.after(0, self._reset_session_display)` pour nettoyage affichage
        - **MISE À JOUR POIDS :** Appelle `self.root.after(60, self._update_weights_display)` pour affichage poids
        - **PROGRESSION FINALE :** Met `self.root.after(70, lambda: self._update_progress(100, f"Prêt (État chargé: {os.path.basename(latest_path)})"))`
      * **STRATÉGIE FALLBACK PKL :** Identique à version principale avec reset visuel ajouté
      * **CAS ÉCHEC AVEC RESET :** Si `not state_loaded_successfully` :
        - Vérifie `if len(self.sequence) != 0 or self.loaded_historical or self.lgbm_base is None:`
        - Appelle `self.init_ml_models(reset_weights=True)` pour réinitialisation
        - **RESET COMPLET :** Appelle `self.reset_data('soft', confirm=False)` pour reset session
        - Met progression finale "Nouvelle Session"
    - RETOUR : None - Méthode d'initialisation ne retourne rien
    - UTILITÉ : Version améliorée avec reset visuel automatique et gestion plus robuste des échecs de chargement

16. _load_selected_model.txt (HybridBaccaratPredictor._load_selected_model - Chargement modèle sélectionné)
    - Lignes 3520-3556 dans hbp.py (37 lignes)
    - FONCTION : Charge modèle spécifique sélectionné depuis tableau de bord avec validation et confirmation utilisateur
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * tree (ttk.Treeview) - Widget Treeview contenant la liste des modèles disponibles
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SÉLECTION :** Vérifie `selected_items = tree.selection()` puis `if not selected_items:` avec message "Veuillez sélectionner un modèle dans la liste."
      * **EXTRACTION DONNÉES :** Récupère `item = selected_items[0]` puis `values = tree.item(item, 'values')` pour obtenir informations modèle
      * **CONSTRUCTION CHEMIN :** Extrait `filename = values[0]` puis construit `model_path = os.path.join(os.getcwd(), "models", filename)` pour chemin complet
      * **VALIDATION EXISTENCE :** Vérifie `if not os.path.exists(model_path):` avec message d'erreur "Le fichier {filename} n'existe pas."
      * **CONFIRMATION UTILISATEUR :** Affiche dialogue `messagebox.askyesno()` avec message :
        - "Voulez-vous charger le modèle {filename}?"
        - "Cela remplacera le modèle actuellement chargé."
      * **CHARGEMENT CONDITIONNEL :** Si `response` positive, appelle `self.load_trained_models(model_path)` pour chargement effectif
      * **GESTION ANNULATION :** Si utilisateur refuse, retourne sans action
    - RETOUR : None - Méthode d'interface utilisateur ne retourne rien
    - UTILITÉ : Interface sécurisée pour chargement sélectif de modèles depuis tableau de bord avec confirmation obligatoire

17. _save_model_metadata.txt (HybridBaccaratPredictor._save_model_metadata - Sauvegarde métadonnées modèle)
    - Lignes 2359-2461 dans hbp.py (103 lignes)
    - FONCTION : Sauvegarde métadonnées complètes modèle dans fichier JSON avec hyperparamètres et métriques performance
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * model_filepath (str) - Chemin du fichier modèle pour nommage JSON associé
      * package (Dict[str, Any]) - Package contenant données modèle et configuration
    - FONCTIONNEMENT DÉTAILLÉ :
      * **EXTRACTION CONFIGURATION :** Récupère `config_details = package.get('config_details', {})` pour détails configuration
      * **MÉTRIQUES PERFORMANCE :** Collecte `performance_metrics = {}` avec `self.best_accuracy` si disponible
      * **PERFORMANCE MÉTHODES :** Extrait `method_performance = {}` depuis `self.method_performance` avec accuracy par méthode
      * **CONSTRUCTION MÉTADONNÉES :** Crée dictionnaire complet avec :
        - `timestamp` : horodatage ISO format avec `datetime.now().isoformat()`
        - `model_file` : nom fichier avec `os.path.basename(model_filepath)`
        - `model_type` : 'HybridBaccaratPredictor' fixe
        - `version` : depuis `package.get('predictor_version', 'unknown')`
        - `sequence_length` : longueur séquence avec `len(package.get('sequence', []))`
      * **HYPERPARAMÈTRES COMPLETS :** Collecte tous hyperparamètres système :
        - Paramètres généraux : `min_confidence_for_recommendation`, `error_pattern_threshold`
        - Paramètres LGBM : `n_estimators`, `learning_rate`, `max_depth`, `num_leaves`, etc.
        - Paramètres LSTM : `lstm_units`, `lstm_dropout`, `lstm_epochs`, `lstm_batch_size`
        - Paramètres Markov : `max_markov_order`, `markov_smoothing`
        - Poids modèles : `initial_weights`, `current_weights`, `best_weights`
      * **INFORMATIONS ENTRAÎNEMENT :** Ajoute `training_info` avec timestamps entraînement et sauvegarde
      * **FILTRAGE VALEURS :** Utilise fonction récursive `filter_none_values()` pour nettoyer métadonnées
      * **SAUVEGARDE JSON :** Crée `json_filepath = os.path.splitext(model_filepath)[0] + '.json'` puis sauvegarde avec `json.dump()`
      * **GESTION ERREURS :** Capture exceptions avec logging détaillé et retour booléen
    - RETOUR : bool - True si sauvegarde réussie, False sinon
    - UTILITÉ : Traçabilité complète modèles avec hyperparamètres et métriques pour analyse et reproduction

18. _save_params_to_file.txt (HybridBaccaratPredictor._save_params_to_file - Sauvegarde paramètres fichier)
    - Lignes 2333-2357 dans hbp.py (25 lignes)
    - FONCTION : Sauvegarde paramètres optimisés dans fichier params.txt au format JSON avec validation et gestion d'erreurs
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * params (Dict[str, Any]) - Dictionnaire des paramètres à sauvegarder
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION PARAMÈTRES :** Vérifie `if not params or not isinstance(params, dict):` avec logging erreur si invalides
      * **CRÉATION FICHIER :** Ouvre `with open("params.txt", "w", encoding="utf-8") as f:` pour écriture UTF-8
      * **SÉRIALISATION JSON :** Utilise `json.dump(params, f, indent=4, sort_keys=True)` pour formatage lisible avec indentation et tri des clés
      * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Paramètres optimisés sauvegardés dans params.txt: {len(params)} paramètres.")` avec comptage
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de la sauvegarde des paramètres dans params.txt: {e}", exc_info=True)`
      * **RETOUR STATUT :** Retourne `True` si succès, `False` si erreur
    - RETOUR : bool - True si sauvegarde réussie, False sinon
    - UTILITÉ : Persistance paramètres optimisés avec format JSON standardisé pour restauration et partage

19. _save_state_to_models_dir.txt (HybridBaccaratPredictor._save_state_to_models_dir - Sauvegarde état répertoire modèles)
    - Lignes 10966-10990 dans hbp.py (25 lignes)
    - FONCTION : Sauvegarde automatique état actuel dans MODEL_SAVE_DIR avec timestamp et format .joblib via _perform_save
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **DÉFINITION RÉPERTOIRE :** Utilise `save_dir = MODEL_SAVE_DIR` pour répertoire de sauvegarde standard
      * **CRÉATION RÉPERTOIRE :** Appelle `os.makedirs(save_dir, exist_ok=True)` pour assurer existence dossier
      * **GÉNÉRATION TIMESTAMP :** Crée `timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")` pour horodatage unique
      * **CONSTRUCTION NOM :** Forme `filename = f"predictor_state_{timestamp}.joblib"` avec format .joblib obligatoire pour auto-save
      * **CHEMIN COMPLET :** Assemble `filepath = os.path.join(save_dir, filename)` pour chemin final
      * **LOGGING DÉBUT :** Enregistre `logger.info(f"Sauvegarde automatique de l'état vers: {filepath}")` pour traçabilité
      * **APPEL SAUVEGARDE :** Délègue à `return self._perform_save(filepath)` pour logique interne complète
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de la sauvegarde automatique: {e}", exc_info=True)` et retour False
      * **FORMAT FIXE :** Force toujours extension .joblib pour cohérence auto-save contrairement à unified_save qui permet .pkl
    - RETOUR : bool - True si sauvegarde réussie, False en cas d'erreur
    - UTILITÉ : Backup automatique avec nommage temporel pour traçabilité et récupération d'état sans intervention utilisateur

20. _initialize_method_performance.txt (HybridBaccaratPredictor._initialize_method_performance - Initialisation performance méthodes)
    - Lignes 2060-2090 dans hbp.py (31 lignes)
    - FONCTION : Initialise structure complète de suivi performance pour chaque méthode avec gestion conditionnelle Markov
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION DICTIONNAIRE :** Crée `self.method_performance = {}` pour stockage performance par méthode
      * **VÉRIFICATION MARKOV :** Récupère `use_markov_model = getattr(self.config, 'use_markov_model', True)` pour activation conditionnelle
      * **EXTRACTION CLÉS :** Utilise `keys_to_initialize = list(self.config.initial_weights.keys())` pour méthodes actives
      * **FILTRAGE MARKOV :** Si `not use_markov_model and 'markov' in keys_to_initialize:`, supprime avec `keys_to_initialize.remove('markov')` et logging debug
      * **INITIALISATION STRUCTURES :** Pour chaque `key in keys_to_initialize:`, crée dictionnaire avec :
        - `'correct': 0` pour compteur prédictions correctes
        - `'total': 0` pour compteur total prédictions
        - `'accuracy_history': []` pour historique précisions (fenêtre glissante)
      * **PROTECTION MARKOV :** Ajoute toujours entrée 'markov' même si désactivé avec `if 'markov' not in self.method_performance:` pour éviter erreurs
      * **STRUCTURE UNIFORME :** Assure même structure pour toutes méthodes : correct, total, accuracy_history
      * **LOGGING FINAL :** Enregistre `logger.debug(f"Structure de performance initialisée pour les méthodes: {list(self.method_performance.keys())}")` pour traçabilité
      * **COHÉRENCE CLÉS :** Garantit correspondance avec clés utilisées dans `hybrid_prediction` et `self.weights`
    - RETOUR : None - Initialise directement attribut self.method_performance
    - UTILITÉ : Préparation système monitoring performance avec structures uniformes et gestion conditionnelle Markov pour éviter erreurs

21. _get_color_for_intensity.txt (HybridBaccaratPredictor._get_color_for_intensity - Couleur selon intensité)
    - Lignes 8101-8105 dans hbp.py (5 lignes)
    - FONCTION : Convertit intensité numérique (0-1) en couleur hexadécimale bleue pour visualisation matrices de confusion
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * intensity (float) - Valeur d'intensité entre 0.0 et 1.0
    - FONCTIONNEMENT DÉTAILLÉ :
      * **CONVERSION INTENSITÉ :** Calcule `blue = int(255 * intensity)` pour mapper intensité [0,1] vers composante bleue [0,255]
      * **FORMATAGE HEXADÉCIMAL :** Retourne `f"#{0:02x}{0:02x}{blue:02x}"` avec rouge=0, vert=0, bleu=variable
      * **GRADIENT BLEU :** Crée gradient du noir (#000000) au bleu pur (#0000FF) selon intensité
      * **UTILISATION MATRICES :** Employée dans `_draw_confusion_matrix` pour colorer cellules selon valeurs
      * **FORMAT COULEUR :** Produit codes couleur HTML/CSS compatibles avec Tkinter Canvas
    - RETOUR : str - Code couleur hexadécimal format "#RRGGBB" avec gradient bleu
    - UTILITÉ : Visualisation colorée des métriques dans interface avec mapping intensité→couleur pour matrices de confusion

22. cleanup_and_show_message.txt (HybridBaccaratPredictor.cleanup_and_show_message - Nettoyage et message)
    - Lignes 13306-13332 dans hbp.py (27 lignes)
    - FONCTION : Fonction interne de nettoyage mémoire et affichage message succès optimisation avec gestion CUDA
    - PARAMÈTRES : Aucun (fonction interne sans paramètres)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **NETTOYAGE MÉMOIRE :** Appelle `gc.collect()` pour libération mémoire Python
      * **NETTOYAGE CUDA :** Si `torch.cuda.is_available():`, exécute `torch.cuda.empty_cache()` avec gestion exception silencieuse
      * **AFFICHAGE SUCCÈS :** Utilise `messagebox.showinfo("Optimisation Réussie", f"Optimisation terminée avec succès!\n{success_msg}")` pour notification utilisateur
      * **GESTION ERREURS :** Capture exceptions CUDA avec `try/except Exception: pass` pour éviter crash
      * **CONTEXTE UTILISATION :** Fonction définie dans `_finalize_optuna_optimization` pour finalisation optimisation
    - RETOUR : None - Fonction interne d'affichage
    - UTILITÉ : Finalisation propre optimisation avec nettoyage mémoire et feedback utilisateur positif

23. cleanup_and_show_message_1.txt (HybridBaccaratPredictor.cleanup_and_show_message_1 - Nettoyage et message v1)
    - Lignes 13348-13374 dans hbp.py (27 lignes)
    - FONCTION : Fonction interne de nettoyage mémoire et affichage message d'erreur optimisation avec gestion CUDA
    - PARAMÈTRES : Aucun (fonction interne sans paramètres)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **NETTOYAGE MÉMOIRE :** Appelle `gc.collect()` pour libération mémoire Python
      * **NETTOYAGE CUDA :** Si `torch.cuda.is_available():`, exécute `torch.cuda.empty_cache()` avec gestion exception silencieuse
      * **AFFICHAGE ERREUR :** Utilise `messagebox.showerror("Échec Optimisation", f"L'optimisation a échoué.\n{error_msg}")` pour notification erreur
      * **GESTION ERREURS :** Capture exceptions CUDA avec `try/except Exception: pass` pour éviter crash
      * **CONTEXTE UTILISATION :** Fonction définie dans `_finalize_optuna_optimization` pour gestion échec optimisation
    - RETOUR : None - Fonction interne d'affichage
    - UTILITÉ : Finalisation propre échec optimisation avec nettoyage mémoire et feedback utilisateur d'erreur

24. cleanup_and_show_message_2.txt (HybridBaccaratPredictor.cleanup_and_show_message_2 - Nettoyage et message v2)
    - Lignes 13385-13412 dans hbp.py (28 lignes)
    - FONCTION : Fonction interne de nettoyage mémoire et affichage message échec optimisation multi-niveaux avec gestion CUDA
    - PARAMÈTRES : Aucun (fonction interne sans paramètres)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **NETTOYAGE MÉMOIRE :** Appelle `gc.collect()` pour libération mémoire Python
      * **NETTOYAGE CUDA :** Si `torch.cuda.is_available():`, exécute `torch.cuda.empty_cache()` avec gestion exception silencieuse
      * **PRÉPARATION MESSAGE :** Définit `display_error = error_msg if error_msg else "Aucun essai valide terminé ou erreur interne."`
      * **AFFICHAGE ERREUR :** Utilise `messagebox.showerror("Échec Optimisation multi-niveaux", f"L'optimisation a échoué ou n'a pas trouvé de résultat.\n{display_error[:500]}")` avec troncature message
      * **LIMITATION TEXTE :** Tronque message erreur à 500 caractères avec `[:500]` pour éviter débordement interface
      * **GESTION ERREURS :** Capture exceptions CUDA avec `try/except Exception: pass` pour éviter crash
      * **CONTEXTE UTILISATION :** Fonction définie dans `_finalize_optuna_optimization` pour gestion échec optimisation multi-niveaux
    - RETOUR : None - Fonction interne d'affichage
    - UTILITÉ : Finalisation propre échec optimisation multi-niveaux avec nettoyage mémoire et feedback utilisateur d'erreur détaillé

25. cleanup_and_show_results.txt (HybridBaccaratPredictor.cleanup_and_show_results - Nettoyage et résultats)
    - Lignes 13263-13290 dans hbp.py (28 lignes)
    - FONCTION : Fonction interne de nettoyage mémoire et affichage résultats optimisation avec gestion CUDA
    - PARAMÈTRES : Aucun (fonction interne sans paramètres)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **NETTOYAGE MÉMOIRE :** Appelle `gc.collect()` pour libération mémoire Python
      * **NETTOYAGE CUDA :** Si `torch.cuda.is_available():`, exécute `torch.cuda.empty_cache()` avec gestion exception silencieuse
      * **AFFICHAGE RÉSULTATS :** Appelle `self._show_optuna_results_window(best_params)` pour fenêtre détaillée résultats
      * **GESTION ERREURS :** Capture exceptions CUDA avec `try/except Exception: pass` pour éviter crash
      * **CONTEXTE UTILISATION :** Fonction définie dans `_finalize_optuna_optimization` pour présentation résultats optimisation
    - RETOUR : None - Fonction interne d'affichage
    - UTILITÉ : Finalisation optimisation avec nettoyage mémoire et présentation résultats détaillés dans fenêtre dédiée

26. apply_resource_config.txt (HybridBaccaratPredictor.apply_resource_config - Application configuration ressources)
    - Lignes 4592-4649 dans hbp.py (58 lignes)
    - FONCTION : Applique configuration des ressources système (CPU/GPU/mémoire) avec validation et mise à jour configurations dépendantes
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * show_confirmation (bool, optionnel) - Affiche confirmation utilisateur (défaut: True)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))` et `ui_available = self.is_ui_available()`
      * **VALIDATION CPU :** Récupère `target_cores = getattr(self, 'target_cpu_cores', 1)` et valide avec `max_logical_cores = os.cpu_count() or 1`
      * **CLIPPING CPU :** Applique `self.target_cpu_cores = max(1, min(target_cores, max_logical_cores))` pour borner valeurs
      * **VALIDATION MÉMOIRE :** Récupère `target_ram = getattr(self, 'target_max_ram_gb', 2)` et valide avec détection système
      * **DÉTECTION RAM SYSTÈME :** Si `psutil` disponible, utilise `psutil.virtual_memory().total / (1024**3)` pour RAM totale
      * **CLIPPING MÉMOIRE :** Applique `self.target_max_ram_gb = max(1, min(target_ram, total_sys_mem_gb))` pour borner valeurs
      * **LOGGING VALIDATION :** Enregistre `logger_instance.info(f"Configuration ressources validée: CPU={self.target_cpu_cores} cœurs, RAM={self.target_max_ram_gb} Go")`
      * **MISE À JOUR CONFIG :** Si attributs existent, met à jour `self.config.default_cpu_cores = self.target_cpu_cores` et `self.config.default_max_memory_gb = self.target_max_ram_gb`
      * **APPEL DÉPENDANCES :** Exécute `self._update_dependent_configs()` pour adapter configurations liées (threads PyTorch, LGBM, etc.)
      * **MISE À JOUR UI :** Si UI disponible, programme `self.root.after(20, self._update_weight_display)` pour rafraîchissement interface
      * **CONFIRMATION UTILISATEUR :** Si `show_confirmation` et UI disponible, affiche `messagebox.showinfo("Configuration Appliquée", message_confirmation)`
      * **GESTION ERREURS :** Capture exceptions avec logging erreur et messagebox d'erreur si UI disponible
    - RETOUR : None - Met à jour directement configuration système
    - UTILITÉ : Optimisation utilisation ressources avec validation bornes système et mise à jour configurations dépendantes automatique

27. reset_system.txt (HybridBaccaratPredictor.reset_system - Réinitialisation système)
    - Lignes 4714-4829 dans hbp.py (116 lignes)
    - FONCTION : Réinitialise complètement le système à l'état initial avec gestion différenciée 'soft'/'hard' et acquisition verrous thread-safe
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * reset_type (Literal['soft', 'hard']) - Type de reset : 'soft' pour session, 'hard' pour tout (défaut: 'soft')
      * confirm (bool, optionnel) - Demande confirmation utilisateur (défaut: True)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION UI :** Vérifie `ui_available = self.is_ui_available()` pour adaptation interface
      * **CONFIRMATION UTILISATEUR :** Si `confirm and ui_available:`, affiche `messagebox.askyesno("Confirmation Réinitialisation", f"Êtes-vous sûr de vouloir réinitialiser {'la session actuelle' if reset_type == 'soft' else 'TOUT (session ET modèles)'} ?")` pour validation
      * **ACQUISITION VERROUS :** Séquence d'acquisition thread-safe :
        - `self.sequence_lock.acquire()` et `self.model_lock.acquire()` pour état principal
        - Gestion `markov_lock_to_use` avec fallback `self._fallback_markov_lock = threading.RLock()` si nécessaire
        - `self.training_lock.acquire()` et `self.weights_lock.acquire()` pour cohérence complète
      * **RESET COMMUN (SOFT/HARD) :** Vide structures de base :
        - `self.sequence = []` pour historique jeu
        - `self.prediction_history = []` pour prédictions
        - `self.lgbm_cache = deque(maxlen=100)` pour cache (commenté pour soft selon modification)
        - `self.last_incremental_update_index = 0` pour index mise à jour rapide
        - `self.markov.reset(reset_type='soft')` pour modèles session Markov
      * **RESET SPÉCIFIQUE HARD :** Si `reset_type == 'hard':` :
        - Vide `self.loaded_historical = False` et `self.historical_data = []` pour données persistantes
        - Appelle `init_ok = self.init_ml_models(reset_weights=True)` pour réinitialisation modèles ML
        - Gestion échec avec `if not init_ok:` et `messagebox.showerror("Erreur Critique Reset", "Échec de la réinitialisation des modèles ML...")`
        - **VIDAGE CACHE EXPLICITE :** `self.lgbm_cache = deque(maxlen=100)` APRÈS init_ml_models selon modification
        - `self.markov.reset(reset_type='hard')` pour reset complet Markov incluant modèles persistants
      * **RESET POIDS SOFT :** Si `reset_type == 'soft':` :
        - Restaure `self.weights = self.config.initial_weights.copy()` et `self._initialize_method_performance()`
        - Remet `self.best_accuracy = 0.5`, `self.best_weights = self.weights.copy()`, `self.early_stopping_counter = 0`
      * **MISE À JOUR UI :** Si UI disponible, planifie mises à jour :
        - `self.root.after(0, self.update_display)` pour affichage principal
        - `self.root.after(50, self._update_weights_display)` pour poids
        - `self.root.after(10, lambda: self._update_progress(0, "Prêt."))` pour progression
      * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.critical()` et `messagebox.showerror()` pour erreurs critiques
      * **LIBÉRATION VERROUS :** Bloc `finally:` avec libération séquentielle tous verrous et gestion `RuntimeError` pour erreurs libération
    - RETOUR : None - Méthode de réinitialisation ne retourne rien
    - UTILITÉ : Réinitialisation robuste et thread-safe avec deux niveaux (session/complet) selon besoins utilisateur et gestion d'erreurs complète

================================================================================
SECTION 8 : ANCIENNES CLASSES
================================================================================

1. class_HybridBaccaratPredictor.txt (Classe HybridBaccaratPredictor - Classe principale du système)
   - Lignes 82-14024 dans hbp.py (13943 lignes)
   - FONCTION : Classe principale du système de prédiction Baccarat hybride intégrant ML avancé, interface utilisateur et optimisation
   - ARCHITECTURE GÉNÉRALE :
     * **MODÈLES ML :** Intègre Markov, LGBM (LightGBM), LSTM avec pondération bayésienne adaptative
     * **INTERFACE UTILISATEUR :** Interface Tkinter complète avec graphiques matplotlib temps réel
     * **OPTIMISATION :** Système Optuna pour hyperparamètres avec métriques personnalisées
     * **GESTION DONNÉES :** Pipeline complet préparation, validation et mise en cache
     * **THREAD-SAFETY :** Protection complète avec verrous pour accès concurrent
   - ATTRIBUTS PRINCIPAUX :
     * **self.config** (PredictorConfig) - Configuration système avec tous paramètres
     * **self.sequence** (List[str]) - Séquence des résultats ('player'/'banker')
     * **self.weights** (Dict[str, float]) - Poids adaptatifs des méthodes (markov, lgbm, lstm)
     * **self.lgbm_base** (BaggingClassifier) - Modèle LGBM principal avec ensemble
     * **self.lstm** (torch.nn.Module) - Réseau LSTM bidirectionnel
     * **self.markov** (MarkovModel) - Modèle Markov multi-ordres
     * **self.prediction_history** (List[Dict]) - Historique complet des prédictions
     * **self.consecutive_confidence_calculator** - Calculateur confiance manches 31-60
   - VERROUS THREAD-SAFETY :
     * **self.model_lock** (threading.RLock) - Protection modèles ML
     * **self.weights_lock** (threading.RLock) - Protection poids adaptatifs
     * **self.sequence_lock** (threading.RLock) - Protection séquence et historique
     * **self.training_lock** (threading.RLock) - Protection processus entraînement
     * **self.markov_lock** (threading.RLock) - Protection spécifique modèle Markov
   - MÉTHODES PRINCIPALES :
     * **__init__()** - Constructeur avec double mode (UI/Optuna)
     * **hybrid_prediction()** - Cœur prédiction avec pondération bayésienne
     * **train_models()** - Entraînement complet avec validation croisée
     * **record_outcome()** - Enregistrement résultats avec mise à jour adaptative
     * **run_hyperparameter_optimization()** - Optimisation Optuna multi-objectifs
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION :** Configure modèles, interface, verrous et cache selon mode (UI/Optuna)
     * **PRÉDICTION HYBRIDE :** Combine prédictions avec incertitude épistémique/aléatoire
     * **ADAPTATION POIDS :** Ajuste poids selon performance récente avec lissage
     * **OPTIMISATION CONTINUE :** Mise à jour rapide modèles et hyperparamètres
     * **INTERFACE TEMPS RÉEL :** Affichage graphiques, métriques et contrôles
   - UTILITÉ : Architecture centrale complète pour prédiction Baccarat avec IA avancée, interface professionnelle et optimisation automatique

2. class_ConsecutiveConfidenceCalculator.txt (Classe ConsecutiveConfidenceCalculator - Calculateur confiance consécutive)
   - Lignes 1422-1579 dans hbp.py (158 lignes)
   - FONCTION : Classe spécialisée pour calcul de confiance sur recommandations NON-WAIT consécutives avec analyse de patterns
   - ARCHITECTURE SPÉCIALISÉE :
     * **ANALYSE PATTERNS :** Système d'extraction et classification de patterns basé sur features
     * **STATISTIQUES ADAPTATIVES :** Suivi performance par pattern avec historique complet
     * **CONFIANCE CONTEXTUELLE :** Calcul confiance spécifique aux manches 31-60
     * **MÉTRIQUES CONSÉCUTIVES :** Optimisation pour séquences de recommandations valides
   - ATTRIBUTS PRINCIPAUX :
     * **self.pattern_stats** (defaultdict) - Statistiques par pattern {total, success, consecutive_lengths, max_consecutive}
     * **self.recent_recommendations** (List[str]) - Historique récent des recommandations
     * **self.recent_outcomes** (List[str]) - Historique récent des résultats réels
     * **self.max_recent_history** (int) - Taille maximale historique (défaut: 50)
   - MÉTHODES PRINCIPALES :
     * **__init__()** - Initialise structures de données et paramètres
     * **calculate_confidence()** - Calcule confiance basée sur patterns et contexte
     * **_extract_pattern_key()** - Extrait clé pattern depuis vecteur features
     * **update_recent_data()** - Met à jour historique avec nouvelles données
     * **get_current_wait_ratio()** - Calcule ratio WAIT/NON-WAIT actuel
     * **get_recent_nonwait_success_rate()** - Taux succès recommandations NON-WAIT récentes
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION PATTERNS :** Utilise 5 premières features avec arrondi intelligent (ratios: 1 décimale, autres: 0 décimale)
     * **CALCUL CONFIANCE :** Combine taux succès (70%) et longueur moyenne séquences consécutives (30%)
     * **VALIDATION PLAGE :** Vérifie si manche dans target_round_min à target_round_max
     * **AJUSTEMENT ADAPTATIF :** Modifie confiance selon performance récente et patterns similaires
     * **GESTION HISTORIQUE :** Maintient historique limité avec rotation automatique
   - UTILITÉ : Gestion sophistiquée de la confiance pour recommandations consécutives avec optimisation spécifique manches 31-60

