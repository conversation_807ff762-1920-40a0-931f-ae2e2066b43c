# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 7405 à 7420
# Type: Méthode de la classe HybridBaccaratPredictor

    def _draw_curve(self, canvas, data, min_val, max_val, x_offset, y_offset, width, height, color="blue", label=""):
        """Dessine une courbe sur le canvas."""
        if not data:
            return

        # Calculer les points de la courbe
        points = []
        for i, val in enumerate(data):
            x = x_offset + (i / (len(data) - 1 if len(data) > 1 else 1)) * width
            y = y_offset + height - ((val - min_val) / (max_val - min_val if max_val > min_val else 1)) * height
            points.append(x)
            points.append(y)

        # Dessiner la courbe
        if len(points) >= 4:  # Au moins 2 points
            canvas.create_line(points, fill=color, width=2, smooth=True)