# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 2060 à 2090
# Type: Méthode de la classe HybridBaccaratPredictor

    def _initialize_method_performance(self):
       """Initialise la structure pour suivre la performance de chaque méthode."""
       self.method_performance = {}
       # Les clés ici DOIVENT correspondre aux clés utilisées dans hybrid_prediction et self.weights

       # Vérifier si le modèle Markov est activé
       use_markov_model = getattr(self.config, 'use_markov_model', True)

       # Déterminer les clés à initialiser
       keys_to_initialize = list(self.config.initial_weights.keys())
       if not use_markov_model and 'markov' in keys_to_initialize:
           logger.debug("_initialize_method_performance: Modèle <PERSON>ov désactivé, exclusion des statistiques Markov.")
           keys_to_initialize.remove('markov')

       # Initialiser les structures pour chaque méthode active
       for key in keys_to_initialize:
           self.method_performance[key] = {
               'correct': 0,      # Compteur de prédictions correctes
               'total': 0,        # Compteur total de prédictions faites par cette méthode
               'accuracy_history': [] # Historique des précisions (fenêtre glissante)
           }

       # Toujours inclure une entrée pour 'markov' même si désactivé (pour éviter les erreurs)
       if 'markov' not in self.method_performance:
           self.method_performance['markov'] = {
               'correct': 0,
               'total': 0,
               'accuracy_history': []
           }

       logger.debug(f"Structure de performance initialisée pour les méthodes: {list(self.method_performance.keys())}")