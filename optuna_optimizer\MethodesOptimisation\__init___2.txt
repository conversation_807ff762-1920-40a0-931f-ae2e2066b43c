# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 1008 à 1080
# Type: Méthode de la classe OptunaOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, config: 'PredictorConfig'):
        """
        Initialise l'optimiseur avec la configuration fournie.

        Args:
            config: Configuration du prédicteur
        """
        self.config = config
        self.study = None
        self.best_params = {}

        # Utiliser les paramètres de viabilité de la configuration
        self.max_viable_trials_to_find = getattr(config, 'optuna_viable_trials_required', OptunaOptimizer.max_viable_trials_to_find)
        self.max_fully_optimized_trials = getattr(config, 'optuna_viable_trials_required', OptunaOptimizer.max_fully_optimized_trials)
        self.max_trials = getattr(config, 'optuna_max_trials', 100)

        # Attributs pour stocker les données complètes et les indices
        self.X_lgbm_full = None
        self.y_full = None
        self.X_lstm_full = None
        self.train_indices = None
        self.val_indices = None

        # Attributs pour la journalisation avancée
        self.debug_mode = getattr(config, 'optuna_debug_mode', False)
        self.debug_log_dir = getattr(config, 'optuna_debug_log_dir', 'logs/optuna_debug')
        self.debug_log_file = None
        self.debug_trial_info = {}

        # Métriques de viabilité pour chaque essai
        self.viability_metrics = {}

        # Créer le répertoire de journalisation s'il n'existe pas
        if self.debug_mode and not os.path.exists(self.debug_log_dir):
            os.makedirs(self.debug_log_dir, exist_ok=True)

        # Afficher les paramètres de viabilité pour le débogage
        logger.warning("=" * 80)
        logger.warning("PARAMÈTRES DE VIABILITÉ:")
        logger.warning(f"optuna_viable_trials_required: {self.max_viable_trials_to_find}")
        logger.warning(f"optuna_max_trials: {self.max_trials}")
        logger.warning(f"viability_min_accuracy: {getattr(config, 'viability_min_accuracy', 'Non défini')}")
        logger.warning(f"viability_min_wait_ratio: {getattr(config, 'viability_min_wait_ratio', 'Non défini')}")
        logger.warning(f"viability_max_wait_ratio: {getattr(config, 'viability_max_wait_ratio', 'Non défini')}")
        logger.warning(f"viability_max_stability_variance: {getattr(config, 'viability_max_stability_variance', 'Non défini')}")
        logger.warning(f"viability_max_consecutive_errors: {getattr(config, 'viability_max_consecutive_errors', 'Non défini')}")
        logger.warning("=" * 80)

        # Détection du device (CPU ou GPU)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"OptunaOptimizer+ initialisé (Device: {self.device})")

        # Paramètres pour l'optimisation multi-niveaux
        self.advanced_options = {
            'enable_multi_level': True,  # Activer l'optimisation multi-niveaux
            'enable_adaptive_regularization': True,  # Activer la régularisation adaptative
            'enable_swa': True,  # Activer Stochastic Weight Averaging
            'enable_meta_learning': True,  # Activer le méta-apprentissage
            'enable_temporal_cv': True,  # Activer la validation croisée temporelle
        }

        # Paramètres pour la gestion des ressources CPU
        self.cpu_count = multiprocessing.cpu_count()  # Nombre de cœurs CPU disponibles
        self.ram_gb = 28  # RAM disponible en GB

        # Paramètres pour le batch size
        self.batch_size = 1024  # Batch size fixe pour LSTM (2x512 pour bidirectionnel)

        # Cache pour les évaluations intermédiaires
        self.evaluation_cache = {}

        # Initialiser l'ajusteur de plages dynamique
        self.range_adjuster = DynamicRangeAdjuster()