"""
Module contenant les modèles de prédiction pour le Baccarat.
Inclut les classes BaccaratDataset, EnhancedLSTMModel et PersistentMarkov.
"""

import logging
import threading
from collections import defaultdict
from typing import List, Dict, Tuple, Optional, Any, Union

import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset

logger = logging.getLogger(__name__)

class BaccaratDataset(Dataset):
    """Dataset PyTorch hautement optimisé pour les séquences de Baccarat avec préchargement en mémoire et accès rapide."""
    def __init__(self, sequences: np.ndarray, targets: np.ndarray, weights: Optional[np.ndarray] = None, sequence_positions: Optional[np.ndarray] = None):
        # Vérifications minimales pour la performance
        assert len(sequences) == len(targets), "Sequences and targets must have the same length"

        # Préchargement des données en mémoire pour un accès plus rapide
        # Conversion directe en tenseurs PyTorch avec pin_memory pour accélérer le transfert CPU->GPU
        self.sequences = torch.from_numpy(sequences).float().share_memory_()

        # Vérifier que les étiquettes sont bien des indices 0-based (0 = Player, 1 = Banker)
        unique_targets = np.unique(targets)
        if not np.all(np.isin(unique_targets, [0, 1])):
            error_msg = f"ERREUR CRITIQUE: Les étiquettes contiennent des valeurs invalides: {unique_targets}. Doivent être uniquement 0 (Player) ou 1 (Banker)."
            logging.getLogger(__name__).error(error_msg)
            raise ValueError(error_msg)

        # IMPORTANT: Utiliser UNIQUEMENT le système zero-based standard de PyTorch:
        # - 0 = Player
        # - 1 = Banker
        self.targets = torch.from_numpy(targets).long().share_memory_()

        # Pas de log de diagnostic pour éviter de surcharger les logs

        # Gestion optimisée des poids et positions
        self.has_weights = weights is not None
        self.has_sequence_positions = sequence_positions is not None

        if self.has_weights:
            self.weights = torch.from_numpy(weights).float().share_memory_()

        if self.has_sequence_positions:
            self.sequence_positions = torch.from_numpy(sequence_positions).long().share_memory_()

        # Préallouer les indices pour un accès plus rapide
        self.indices = torch.arange(len(sequences)).share_memory_()

    def __len__(self) -> int:
        return len(self.sequences)

    def __getitem__(self, idx: int):
        # Retour ultra-optimisé des données sans vérifications supplémentaires
        # Utilisation d'indexation vectorisée pour un accès plus rapide
        if self.has_weights and self.has_sequence_positions:
            return self.sequences[idx], self.targets[idx], self.weights[idx], self.sequence_positions[idx]
        elif self.has_weights:
            return self.sequences[idx], self.targets[idx], self.weights[idx]
        elif self.has_sequence_positions:
            return self.sequences[idx], self.targets[idx], self.sequence_positions[idx]
        else:
            return self.sequences[idx], self.targets[idx]


class EnhancedLSTMModel(nn.Module):
    def __init__(self, input_size, hidden_dim, output_size, num_layers=1, dropout_prob=0.2, bidirectional=False,
                 use_attention=True, use_residual=True, dropout_input=0.1, dropout_hidden=0.2, dropout_output=0.15):
        super(EnhancedLSTMModel, self).__init__()
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.bidirectional = bidirectional
        self.input_size = input_size
        self.use_attention = use_attention
        self.use_residual = use_residual

        # Normalisation de l'entrée pour stabiliser l'entraînement et réduire la val_loss
        self.input_norm = nn.LayerNorm(input_size)

        # Dropout spécifique pour l'entrée (modéré pour permettre un bon flux d'information)
        self.input_dropout = nn.Dropout(dropout_input)

        # LSTM principal avec dropout entre les couches
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout_hidden if num_layers > 1 else 0,
            bidirectional=bidirectional
        )

        # Dimension de sortie du LSTM (x2 si bidirectionnel)
        lstm_output_dim = hidden_dim * 2 if bidirectional else hidden_dim

        # Mécanisme d'attention pour se concentrer sur les séquences importantes
        if use_attention:
            self.attention = nn.Sequential(
                nn.Linear(lstm_output_dim, lstm_output_dim // 2),
                nn.Tanh(),
                nn.Linear(lstm_output_dim // 2, 1)
            )

        # Normalisation de couche pour stabiliser l'entraînement
        self.layer_norm = nn.LayerNorm(lstm_output_dim)

        # Couches supplémentaires pour augmenter la capacité du modèle
        self.fc1 = nn.Linear(lstm_output_dim, lstm_output_dim)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(dropout_output)

        # Couche de sortie finale
        self.fc2 = nn.Linear(lstm_output_dim, output_size)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Effectue la passe avant du modèle avec attention et connexions résiduelles.

        IMPORTANT: Cette fonction retourne des logits (non normalisés) qui doivent être
        traités avec softmax pour obtenir des probabilités. Les indices de sortie suivent
        le système zero-based standard de PyTorch:
        - Indice 0 = Player
        - Indice 1 = Banker
        """
        # Gérer les entrées 2D (batch_size=1 non explicite)
        if x.dim() == 2:
            # Ajoute la dimension batch: (seq_len, features) -> (1, seq_len, features)
            x = x.unsqueeze(0)
        elif x.dim() != 3:
            raise ValueError(f"Attendu input 2D ou 3D, reçu {x.dim()}D tensor.")

        batch_size = x.size(0)
        seq_len = x.size(1)
        device = x.device
        num_directions = 2 if self.bidirectional else 1

        # Normaliser l'entrée pour stabiliser l'entraînement et réduire la val_loss
        x = self.input_norm(x)

        # Appliquer le dropout à l'entrée
        x = self.input_dropout(x)

        # Initialiser les états cachés
        h0 = torch.zeros(self.num_layers * num_directions, batch_size, self.hidden_dim, device=device)
        c0 = torch.zeros(self.num_layers * num_directions, batch_size, self.hidden_dim, device=device)

        # LSTM - obtenir toutes les sorties de séquence
        lstm_out, _ = self.lstm(x, (h0, c0))  # lstm_out: (batch, seq_len, hidden*directions)

        # Appliquer l'attention si activée
        if self.use_attention:
            # Calculer les scores d'attention pour chaque pas de temps
            attention_scores = self.attention(lstm_out).squeeze(-1)  # (batch, seq_len)
            attention_weights = torch.softmax(attention_scores, dim=1)  # (batch, seq_len)

            # Appliquer les poids d'attention à la sortie LSTM
            attention_weights = attention_weights.unsqueeze(-1)  # (batch, seq_len, 1)
            weighted_output = lstm_out * attention_weights  # (batch, seq_len, hidden*directions)
            context_vector = weighted_output.sum(dim=1)  # (batch, hidden*directions)
        else:
            # Sans attention, utiliser simplement la dernière sortie
            context_vector = lstm_out[:, -1, :]  # (batch, hidden*directions)

        # Normalisation de couche
        normalized_out = self.layer_norm(context_vector)

        # Couche dense intermédiaire avec activation ReLU
        dense_out = self.fc1(normalized_out)
        activated_out = self.relu(dense_out)

        # Connexion résiduelle si activée
        if self.use_residual:
            combined_out = activated_out + normalized_out
        else:
            combined_out = activated_out

        # Dropout final
        dropped_out = self.dropout(combined_out)

        # Couche de sortie
        logits = self.fc2(dropped_out)  # (batch, output_size)

        return logits

class PersistentMarkov:
    """
    Gère les modèles Markov globaux (historique complet) et de session (partie en cours),
    avec lissage configurable et thread-safety.
    """
    def __init__(self, max_order: int, smoothing: float):
        """
        Initialise le gestionnaire de modèles Markov.

        Args:
            max_order (int): L'ordre maximal des chaînes de Markov à considérer (1-12).
                            Doit être un entier car représente un nombre discret de manches.
            smoothing (float): Le facteur de lissage de Laplace (alpha) à appliquer
                               lors du calcul des probabilités pour éviter les probas nulles.
        """
        # Assurer que max_order est un entier
        if isinstance(max_order, float):
            logger.warning(f"max_order reçu comme flottant ({max_order}), conversion en entier.")
            max_order = int(max_order)

        # Garantir une valeur entre 1 et 12
        self.max_order = max(1, min(12, max_order))

        # Valider le paramètre smoothing
        if not isinstance(smoothing, (int, float)) or smoothing < 0:
            raise ValueError("smoothing doit être un nombre >= 0")
        self.smoothing: float = smoothing

        # Pour les structures de données, utiliser directement max_order
        self.max_order_int = self.max_order

        # Utilise une liste où l'index correspond à l'ordre (index 0 non utilisé pour les modèles)
        # global_models[order][state_tuple] = defaultdict(int) -> {'outcome': count}
        self.global_models: List[defaultdict] = [defaultdict(lambda: defaultdict(int)) for _ in range(self.max_order_int + 1)]
        self.session_models: List[defaultdict] = [defaultdict(lambda: defaultdict(int)) for _ in range(self.max_order_int + 1)]
        self.lock: threading.RLock = threading.RLock()
        logger.info(f"PersistentMarkov initialisé avec max_order={self.max_order} (max_order_int={self.max_order_int}), smoothing={self.smoothing}")
        logger.info(f"Taille des modèles: global_models={len(self.global_models)}, session_models={len(self.session_models)}")

    def get_combined_probs(self, sequence: List[str], global_weight: float = None, context_weight: float = None, decay_factor: float = None) -> Dict[str, float]:
        """
        Combine les probabilités globales et de session pour prédire le prochain coup.
        Utilise le lissage configuré, pondère les différents ordres et enrichit avec contexte.

        Args:
            sequence (List[str]): La séquence de jeu actuelle jusqu'au coup t-1.
            global_weight (float, optional): Le poids à accorder aux modèles globaux (entre 0 et 1).
                                            Le poids de session sera (1 - global_weight).
                                            Si None, utilise la valeur par défaut 0.6.
            context_weight (float, optional): Le poids à accorder au contexte local dans le modèle de Markov.
                                            Si None, utilise la valeur par défaut 0.8.
            decay_factor (float, optional): Le facteur de décroissance pour l'influence des états passés.
                                          Si None, utilise la valeur par défaut 0.95.

        Returns:
            Dict[str, float]: Un dictionnaire avec les probabilités prédites pour 'player' et 'banker'.
                              Retourne {'player': 0.5, 'banker': 0.5} si pas assez de données.
        """
        # Vérification de sécurité pour éviter les erreurs d'index
        if not isinstance(sequence, list):
            logger.warning("get_combined_probs: sequence n'est pas une liste. Retour 50/50.")
            return {'player': 0.5, 'banker': 0.5}

        if not sequence or len(sequence) < 1:
            logger.debug("get_combined_probs: sequence vide ou trop courte. Retour 50/50.")
            return {'player': 0.5, 'banker': 0.5}

        # Vérifier que tous les éléments de la séquence sont des chaînes valides
        for i, item in enumerate(sequence):
            if not isinstance(item, str):
                logger.warning(f"get_combined_probs: élément non-string à l'index {i}: {type(item)}. Retour 50/50.")
                return {'player': 0.5, 'banker': 0.5}
            if item not in ('player', 'banker'):
                logger.warning(f"get_combined_probs: valeur invalide à l'index {i}: '{item}'. Retour 50/50.")
                return {'player': 0.5, 'banker': 0.5}

        # Utiliser la valeur par défaut depuis la configuration si global_weight est None
        if global_weight is None:
            # Récupérer la valeur depuis la configuration si disponible, sinon utiliser 0.15 comme valeur par défaut
            from config import PredictorConfig
            config = PredictorConfig()
            global_weight = getattr(config, 'markov_global_weight', 0.15)

        # Vérifier et clamper global_weight
        global_weight = max(0.0, min(1.0, global_weight))
        session_weight = 1.0 - global_weight

        # Poids décroissant pour les ordres plus élevés
        # Récupérer l'exposant depuis la configuration
        from config import PredictorConfig
        config = PredictorConfig()
        order_weight_exponent = getattr(config, 'markov_order_weight_exponent', 1.5)

        # Utiliser self.max_order_int pour les boucles (déjà un entier)

        # Calculer les poids avec l'exposant configuré
        order_weights = {}
        for order in range(1, self.max_order_int + 1):
            base_weight = 1.0 / (order ** order_weight_exponent)
            if order == self.max_order_int:
                # Donner un poids légèrement plus élevé à l'ordre maximal
                order_weights[order] = base_weight * 1.1
            else:
                order_weights[order] = base_weight

        total_order_weight = sum(order_weights.values())
        norm_order_weights = {o: w / total_order_weight for o, w in order_weights.items()} if total_order_weight > 1e-9 else {}

        final_probs = {'player': 0.0, 'banker': 0.0}
        accumulated_weight = 0.0
        num_classes = 2

        # Récupérer le poids du contexte depuis les paramètres ou la configuration
        if context_weight is None:
            # Récupérer la valeur depuis la configuration si disponible, sinon utiliser 0.8 comme valeur par défaut
            from config import PredictorConfig
            config = PredictorConfig()
            context_weight = getattr(config, 'markov_context_weight', 0.8)

        # Vérifier et clamper context_weight
        context_weight = max(0.0, min(1.0, context_weight))

        # Récupérer le facteur de décroissance depuis les paramètres ou la configuration
        if decay_factor is None:
            # Récupérer la valeur depuis la configuration si disponible, sinon utiliser 0.95 comme valeur par défaut
            from config import PredictorConfig
            config = PredictorConfig()
            decay_factor = getattr(config, 'markov_decay_factor', 0.95)

        # Vérifier et clamper decay_factor
        decay_factor = max(0.0, min(1.0, decay_factor))

        # Analyser le contexte de la séquence
        context_factor = self._analyze_sequence_context(sequence)

        # Appliquer le poids du contexte configuré
        context_factor = context_factor * context_weight

        with self.lock:
            # Utiliser self.max_order_int pour l'itération (déjà un entier)

            # Itérer sur les ordres de 1 à self.max_order_int
            for order in range(1, self.max_order_int + 1):
                # Vérifier que la séquence est suffisamment longue pour cet ordre
                if len(sequence) >= order:
                    # Vérifier que l'ordre est disponible dans les modèles
                    if order >= len(self.global_models) or order >= len(self.session_models):
                        logger.warning(f"get_combined_probs: Ordre {order} non disponible (max global: {len(self.global_models)-1}, max session: {len(self.session_models)-1}). Ignoré.")
                        continue

                    try:
                        state = tuple(sequence[-order:])

                        # --- Calcul des probabilités globales avec lissage ---
                        global_counts = self.global_models[order].get(state, defaultdict(int))
                        global_total = sum(global_counts.values())
                    except IndexError as e:
                        logger.warning(f"get_combined_probs: Erreur d'index pour l'ordre {order}: {e}")
                        continue  # Passer à l'ordre suivant
                    denominator_global = global_total + self.smoothing * num_classes
                    global_p_player = (global_counts.get('player', 0) + self.smoothing) / denominator_global
                    global_p_banker = (global_counts.get('banker', 0) + self.smoothing) / denominator_global
                    norm_factor_g = global_p_player + global_p_banker
                    if norm_factor_g > 1e-9:
                        global_p_player /= norm_factor_g
                        global_p_banker /= norm_factor_g

                    # --- Calcul des probabilités de session avec lissage ---
                    session_counts = self.session_models[order].get(state, defaultdict(int))
                    session_total = sum(session_counts.values())
                    denominator_session = session_total + self.smoothing * num_classes
                    session_p_player = (session_counts.get('player', 0) + self.smoothing) / denominator_session
                    session_p_banker = (session_counts.get('banker', 0) + self.smoothing) / denominator_session
                    norm_factor_s = session_p_player + session_p_banker
                    if norm_factor_s > 1e-9:
                        session_p_player /= norm_factor_s
                        session_p_banker /= norm_factor_s

                    # --- Combinaison pondérée (global vs session) pour cet ordre ---
                    # Adapter les poids en fonction du facteur contextuel et du facteur de décroissance
                    # Appliquer le facteur de décroissance pour les ordres plus élevés
                    order_decay = decay_factor ** (order - 1)

                    # Adapter le poids global en fonction du contexte et du facteur de décroissance
                    adaptive_global_weight = global_weight * (1 - context_factor) * order_decay
                    adaptive_session_weight = 1.0 - adaptive_global_weight

                    combined_p_player = adaptive_global_weight * global_p_player + adaptive_session_weight * session_p_player
                    combined_p_banker = adaptive_global_weight * global_p_banker + adaptive_session_weight * session_p_banker
                    norm_factor_c = combined_p_player + combined_p_banker
                    if norm_factor_c > 1e-9:
                        combined_p_player /= norm_factor_c
                        combined_p_banker = 1.0 - combined_p_player

                    # --- Pondération par l'ordre et Accumulation ---
                    order_w = norm_order_weights.get(order, 0)
                    if order_w > 0:
                        # Adaptation dynamique du poids de l'ordre en fonction du contexte
                        adaptive_order_weight = order_w

                        # Récupérer les paramètres depuis la configuration
                        from config import PredictorConfig
                        config = PredictorConfig()
                        short_order_threshold = getattr(config, 'markov_short_order_threshold', 2)
                        high_volatility_threshold = getattr(config, 'markov_high_volatility_threshold', 0.5)
                        short_order_boost_factor = getattr(config, 'markov_short_order_boost_factor', 0.5)
                        long_order_threshold = getattr(config, 'markov_long_order_threshold', 3)
                        low_volatility_threshold = getattr(config, 'markov_low_volatility_threshold', 0.5)
                        long_order_boost_factor = getattr(config, 'markov_long_order_boost_factor', 0.5)

                        if order <= short_order_threshold and context_factor > high_volatility_threshold:
                            # Donner plus de poids aux ordres courts en cas de forte volatilité
                            adaptive_order_weight *= (1 + context_factor * short_order_boost_factor)
                        elif order >= long_order_threshold and context_factor < low_volatility_threshold:
                            # Donner plus de poids aux ordres longs en cas de faible volatilité
                            adaptive_order_weight *= (1 + (1 - context_factor) * long_order_boost_factor)

                        final_probs['player'] += combined_p_player * adaptive_order_weight
                        final_probs['banker'] += combined_p_banker * adaptive_order_weight
                        accumulated_weight += adaptive_order_weight

        # --- Normalisation Finale et Gestion Poids Manquants ---
        if accumulated_weight > 1e-9:
            missing_weight = max(0.0, 1.0 - accumulated_weight)
            final_probs['player'] = final_probs['player'] + (0.5 * missing_weight)
            final_probs['banker'] = final_probs['banker'] + (0.5 * missing_weight)

            total_final = final_probs['player'] + final_probs['banker']
            if total_final > 1e-9:
                final_probs['player'] /= total_final
                final_probs['banker'] /= total_final
            else:
                final_probs = {'player': 0.5, 'banker': 0.5}
        else:
            final_probs = {'player': 0.5, 'banker': 0.5}

        # Clip final par sécurité
        final_probs['player'] = max(0.0, min(1.0, final_probs['player']))
        final_probs['banker'] = 1.0 - final_probs['player']

        return final_probs

    def _analyze_sequence_context(self, sequence: List[str]) -> float:
        """
        Analyse le contexte de la séquence pour adapter les poids des modèles.

        Args:
            sequence (List[str]): Séquence de résultats

        Returns:
            float: Facteur contextuel entre 0 et 1
                - Proche de 0: séquence stable, modèle global plus pertinent
                - Proche de 1: séquence volatile, modèle de session plus pertinent
        """
        # Vérifications de sécurité
        if not isinstance(sequence, list):
            logger.warning("_analyze_sequence_context: sequence n'est pas une liste. Retour 0.5.")
            return 0.5

        if len(sequence) < 10:
            # Pas assez de données pour une analyse fiable
            return 0.5

        # Vérifier que tous les éléments sont des chaînes valides
        for item in sequence:
            if not isinstance(item, str) or item not in ('player', 'banker'):
                # Si un élément n'est pas valide, utiliser une valeur par défaut
                return 0.5

        try:
            # 1. Calculer la volatilité récente (alternances dans les 10 derniers coups)
            try:
                # Utiliser min() pour éviter de demander plus d'éléments que la séquence n'en contient
                recent_seq = sequence[-min(10, len(sequence)):]
                alternances = 0
                for i in range(1, len(recent_seq)):
                    if recent_seq[i] != recent_seq[i-1]:
                        alternances += 1

                volatility = alternances / (len(recent_seq) - 1) if len(recent_seq) > 1 else 0.5
            except IndexError as e:
                logger.warning(f"_analyze_sequence_context: Erreur lors du calcul de la volatilité: {e}")
                volatility = 0.5  # Valeur par défaut en cas d'erreur

            # 2. Détecter les streaks récents
            try:
                current_streak = 1
                if len(sequence) > 1:  # S'assurer qu'il y a au moins 2 éléments
                    last_element = sequence[-1]  # Stocker le dernier élément pour éviter les accès répétés
                    for i in range(len(sequence)-2, -1, -1):
                        if sequence[i] == last_element:
                            current_streak += 1
                        else:
                            break
            except IndexError as e:
                logger.warning(f"_analyze_sequence_context: Erreur lors du calcul du streak: {e}")
                current_streak = 1  # Valeur par défaut en cas d'erreur

            streak_factor = min(current_streak / 10, 1.0)  # Normaliser

            # 3. Combiner les facteurs (volatilité générale + importance du streak actuel)
            context_factor = (volatility * 0.7) + (streak_factor * 0.3)

            return max(0.0, min(1.0, context_factor))  # Garantir que le résultat est entre 0 et 1
        except Exception as e:
            logger.error(f"Erreur dans _analyze_sequence_context: {e}")
            return 0.5  # Valeur par défaut en cas d'erreur

    def update_global(self, sequences: List[List[str]]) -> None:
        """
        Met à jour les modèles globaux (basés sur l'historique) avec plusieurs séquences.

        Args:
            sequences (List[List[str]]): Une liste de séquences de jeu, où chaque séquence
                                         est une liste de résultats ('player', 'banker').
        """
        if not sequences:
            logger.warning("update_global appelée avec une liste de séquences vide.")
            return

        with self.lock:
            logger.debug(f"Début mise à jour globale Markov avec {len(sequences)} séquences.")
            count = 0
            for seq in sequences:
                if not isinstance(seq, list):
                    logger.warning(f"Élément non-liste trouvé dans sequences: {type(seq)}. Ignoré.")
                    continue
                if not seq: continue # Ignorer séquences vides

                # Utiliser self.max_order_int défini dans le constructeur
                # Cela garantit la cohérence avec la taille des modèles initialisés

                # Apprendre jusqu'à self.max_order_int
                for order in range(1, self.max_order_int + 1): # Commence à l'ordre 1
                    # On a besoin d'au moins 'order' éléments pour former l'état et 1 pour l'issue
                    if len(seq) > order:
                        for i in range(order, len(seq)):
                            # L'état est la séquence de 'order' éléments précédents
                            state = tuple(seq[i - order : i])
                            outcome = seq[i]
                            # On ne compte que Player (P) et Banker (B) comme issues valides
                            if outcome in ('player', 'banker'):
                                self.global_models[order][state][outcome] += 1
                                count += 1
                            # else: logger.debug(f"Outcome global non P/B ignoré: {outcome}")
            logger.debug(f"Ajouté {count} transitions ('player'/'banker') aux modèles globaux Markov.")

    def update_session(self, sequence: List[str]) -> None:
        """
        Met à jour les modèles de session avec la séquence de jeu actuelle.
        Seul le dernier coup et les états le précédant sont ajoutés.

        Args:
            sequence (List[str]): La séquence de jeu en cours.
        """
        # Vérifications de sécurité
        if not isinstance(sequence, list):
            logger.warning("update_session: sequence n'est pas une liste.")
            return

        if not sequence:
            # logger.debug("update_session appelée avec une séquence vide.")
            return

        try:
            with self.lock:
                # logger.debug(f"Mise à jour session Markov (longueur séquence: {len(sequence)})")

                # Vérifier que le dernier élément est valide
                if len(sequence) == 0:
                    return

                last_outcome = sequence[-1]
                if not isinstance(last_outcome, str):
                    logger.warning(f"update_session: dernier élément n'est pas une chaîne: {type(last_outcome)}")
                    return

                if last_outcome not in ('player', 'banker'):
                    # logger.debug(f"Outcome session non P/B ignoré: {last_outcome}")
                    return # Ignorer autres issues comme 'tie' etc.

                # Utiliser self.max_order_int pour l'itération (déjà un entier)

                # Apprendre jusqu'à self.max_order_int pour les modèles de session
                for order in range(1, self.max_order_int + 1): # Commence à l'ordre 1
                    # L'état précède le dernier outcome. Il faut donc len > order.
                    if len(sequence) > order:
                        # Vérifier que tous les éléments de l'état sont valides
                        state_elements = sequence[-(order + 1) : -1]
                        valid_state = True
                        for elem in state_elements:
                            if not isinstance(elem, str) or elem not in ('player', 'banker'):
                                valid_state = False
                                break

                        if not valid_state:
                            continue

                        # L'état est la séquence de 'order' éléments *avant* le dernier
                        state = tuple(state_elements)
                        # Incrémenter le compteur pour cet état menant à ce résultat
                        self.session_models[order][state][last_outcome] += 1
                        # logger.debug(f"  Session Order {order}: State={state}, Outcome={last_outcome}, New Count={self.session_models[order][state][last_outcome]}")
        except Exception as e:
            logger.error(f"Erreur dans update_session: {e}")
            # Ne pas lever l'exception pour éviter de perturber le flux principal

    def export_models(self) -> Dict[str, Any]:
        """
        Exporte l'état actuel des modèles (global et session) et la configuration
        utilisée pour les créer, dans un format sérialisable (dictionnaire).

        Returns:
            Dict[str, Any]: Un dictionnaire contenant:
                            - 'config': {'max_order': int, 'smoothing': float}
                            - 'global': Liste de dictionnaires (un par ordre > 0)
                                        {state_tuple: {outcome: count}}
                            - 'session': Liste de dictionnaires (idem pour session)
        """
        with self.lock:
            logger.info("Exportation des modèles Markov...")

            # Utiliser directement la valeur flottante de max_order
            # Pas besoin de conversion, car nous acceptons maintenant les flottants

            # Convertir les defaultdict en dict standard pour la sérialisation
            # On n'exporte que les ordres 1 à max_order (index 0 est vide)
            global_export = []
            for order_model in self.global_models[1:]: # Exclure index 0
                global_export.append({state: dict(outcomes) for state, outcomes in order_model.items()})

            session_export = []
            for order_model in self.session_models[1:]: # Exclure index 0
                session_export.append({state: dict(outcomes) for state, outcomes in order_model.items()})

            export_data = {
                'config': {
                    'max_order': self.max_order,
                    'smoothing': self.smoothing
                },
                'global': global_export,
                'session': session_export
            }
            logger.info("Exportation Markov terminée.")
            return export_data

    def load_models(self, data: Optional[Dict[str, Any]]) -> bool:
         """
         Charge l'état des modèles (global et session) et la configuration
         depuis un dictionnaire (typiquement issu d'une désérialisation).
         Met à jour l'instance actuelle.

         Args:
             data (Optional[Dict[str, Any]]): Le dictionnaire contenant les données
                                              à charger (format attendu de `export_models`).
                                              Si None ou invalide, la fonction échoue.

         Returns:
             bool: True si le chargement a réussi, False sinon.
         """
         if not isinstance(data, dict):
             logger.error("load_models: Données invalides fournies (pas un dictionnaire ou None).")
             return False

         with self.lock:
            logger.info("Chargement des modèles Markov depuis les données fournies...")
            try:
                # --- Charger et vérifier la Configuration ---
                loaded_config = data.get('config')
                if isinstance(loaded_config, dict):
                    prev_max_order = self.max_order
                    prev_smoothing = self.smoothing

                    loaded_max_order = loaded_config.get('max_order', self.max_order)
                    loaded_smoothing = loaded_config.get('smoothing', self.smoothing)

                    # Valider loaded_max_order (doit être un entier)
                    if isinstance(loaded_max_order, float):
                        logger.warning(f"max_order chargé comme flottant ({loaded_max_order}), conversion en entier.")
                        loaded_max_order = int(loaded_max_order)

                    if not isinstance(loaded_max_order, int) or loaded_max_order < 1:
                        logger.warning(f"max_order chargé invalide ({loaded_max_order}), utilisation de la valeur actuelle ({self.max_order}).")
                        loaded_max_order = self.max_order

                    # Garantir une valeur entre 1 et 12
                    loaded_max_order = max(1, min(12, loaded_max_order))
                    if not isinstance(loaded_smoothing, (int, float)) or loaded_smoothing < 0:
                        logger.warning(f"smoothing chargé invalide ({loaded_smoothing}), utilisation de la valeur actuelle ({self.smoothing}).")
                        loaded_smoothing = self.smoothing

                    self.max_order = loaded_max_order
                    self.smoothing = loaded_smoothing

                    if prev_max_order != self.max_order or prev_smoothing != self.smoothing:
                          logger.warning(f"Configuration Markov chargée différente des valeurs actuelles: "
                                       f"MaxOrder={prev_max_order}->{self.max_order}, "
                                       f"Smoothing={prev_smoothing:.4f}->{self.smoothing:.4f}")

                    # Calculer max_order_int pour les structures de données (égal à max_order car c'est un entier)
                    self.max_order_int = self.max_order

                    # Redimensionner les listes de modèles si max_order_int a changé
                    expected_len = self.max_order_int + 1
                    if len(self.global_models) != expected_len:
                         logger.warning(f"Redimensionnement des listes de modèles Markov pour max_order={self.max_order}")
                         self.global_models = [defaultdict(lambda: defaultdict(int)) for _ in range(expected_len)]
                         self.session_models = [defaultdict(lambda: defaultdict(int)) for _ in range(expected_len)]
                else:
                     logger.warning("Aucune section 'config' valide trouvée dans les données Markov chargées. Utilisation des valeurs actuelles.")

                # --- Charger les Modèles Globaux ---
                loaded_global = data.get('global')
                # Vérifier si la liste chargée contient des données valides
                if isinstance(loaded_global, list) and len(loaded_global) > 0:
                     # Utiliser self.max_order_int pour les structures de données (déjà un entier)

                     # Réinitialiser les modèles actuels avant de charger
                     self.global_models = [defaultdict(lambda: defaultdict(int)) for _ in range(self.max_order_int + 1)]
                     # Charger chaque ordre disponible, jusqu'à max_order
                     loaded_orders = 0
                     for order_idx, model_dict in enumerate(loaded_global):
                         if order_idx >= self.max_order:
                             logger.info(f"Chargement Global: Ignoré les ordres > {self.max_order} car max_order actuel = {self.max_order}")
                             break  # Ne pas dépasser max_order

                         order = order_idx + 1 # L'index 0 de la liste correspond à l'ordre 1
                         if not isinstance(model_dict, dict):
                              logger.warning(f"Chargement Global: Données invalides pour l'ordre {order}. Ignoré.")
                              continue

                         loaded_orders += 1
                         for state_key, outcomes_dict in model_dict.items():
                              # Assurer que state_key est un tuple (problème potentiel avec JSON)
                              if not isinstance(state_key, tuple):
                                    try:
                                         state_tuple = tuple(state_key)
                                         logger.debug(f"Clé état convertie en tuple: {state_key} -> {state_tuple}")
                                    except TypeError:
                                         logger.warning(f"Chargement Global Ordre {order}: Impossible de convertir la clé état '{state_key}' en tuple. Ignoré.")
                                         continue
                              else:
                                   state_tuple = state_key

                              if not isinstance(outcomes_dict, dict):
                                  logger.warning(f"Chargement Global Ordre {order}: Données d'outcomes invalides pour état {state_tuple}. Ignoré.")
                                  continue

                              # Important: Reconstruire avec defaultdict(int) pour les outcomes
                              self.global_models[order][state_tuple] = defaultdict(int, outcomes_dict)

                     logger.info(f"Modèles Markov globaux chargés pour les ordres 1 à {loaded_orders} (max_order actuel = {self.max_order}).")
                else:
                     logger.warning(f"Données Markov 'global' non trouvées ou invalides. Les modèles globaux sont laissés vides.")
                     # Garder les modèles globaux vides (réinitialisés juste avant)

                # --- Charger les Modèles de Session ---
                loaded_session = data.get('session')
                if isinstance(loaded_session, list) and len(loaded_session) > 0:
                     # Utiliser max_order_int pour les structures de données
                     max_order_int = int(self.max_order) + 1 if self.max_order > int(self.max_order) else int(self.max_order)

                     # Réinitialiser les modèles de session avant de charger
                     self.session_models = [defaultdict(lambda: defaultdict(int)) for _ in range(max_order_int + 1)]
                     # Charger chaque ordre disponible, jusqu'à max_order
                     loaded_orders = 0
                     for order_idx, model_dict in enumerate(loaded_session):
                         if order_idx >= int(self.max_order):
                             logger.info(f"Chargement Session: Ignoré les ordres > {int(self.max_order)} car max_order actuel = {self.max_order}")
                             break  # Ne pas dépasser la partie entière de max_order

                         order = order_idx + 1
                         if not isinstance(model_dict, dict):
                             logger.warning(f"Chargement Session: Données invalides pour l'ordre {order}. Ignoré.")
                             continue

                         loaded_orders += 1
                         for state_key, outcomes_dict in model_dict.items():
                              if not isinstance(state_key, tuple):
                                  try:
                                      state_tuple = tuple(state_key)
                                      logger.debug(f"Session: Clé état convertie en tuple: {state_key} -> {state_tuple}")
                                  except TypeError:
                                      logger.warning(f"Chargement Session Ordre {order}: Impossible de convertir la clé état '{state_key}' en tuple. Ignoré.")
                                      continue
                              else:
                                  state_tuple = state_key

                              if not isinstance(outcomes_dict, dict):
                                  logger.warning(f"Chargement Session Ordre {order}: Données d'outcomes invalides pour état {state_tuple}. Ignoré.")
                                  continue

                              self.session_models[order][state_tuple] = defaultdict(int, outcomes_dict)
                     logger.info(f"Modèles Markov de session chargés pour les ordres 1 à {loaded_orders} (max_order actuel = {self.max_order}).")
                else:
                    logger.warning(f"Données Markov 'session' non trouvées ou invalides. Les modèles de session sont réinitialisés (vides).")
                    # Utiliser max_order_int pour les structures de données
                    max_order_int = int(self.max_order) + 1 if self.max_order > int(self.max_order) else int(self.max_order)

                    # Assurer que session est vide si non chargé correctement
                    self.session_models = [defaultdict(lambda: defaultdict(int)) for _ in range(max_order_int + 1)]


                logger.info("Chargement Markov terminé.")
                return True # Succès du chargement

            except Exception as e:
                logger.error(f"Erreur majeure pendant le chargement des modèles Markov: {e}", exc_info=True)
                # En cas d'erreur, réinitialiser complètement pour éviter un état corrompu
                self.reset(reset_type='hard') # Réinitialise global et session
                return False # Échec du chargement

    def reset(self, reset_type: str = 'soft') -> None:
        """
        Réinitialise les compteurs des modèles Markov.

        Args:
            reset_type (str): Type de réinitialisation :
                              - 'soft': Réinitialise uniquement les modèles de session.
                              - 'hard': Réinitialise les modèles globaux ET de session.
        """
        with self.lock:
            # Utiliser max_order_int pour les structures de données
            max_order_int = int(self.max_order) + 1 if self.max_order > int(self.max_order) else int(self.max_order)

            if reset_type == 'hard':
                self.global_models = [defaultdict(lambda: defaultdict(int)) for _ in range(max_order_int + 1)]
                logger.info("Modèles Markov globaux réinitialisés (hard reset).")

            # Dans tous les cas (soft ou hard), réinitialiser la session
            self.session_models = [defaultdict(lambda: defaultdict(int)) for _ in range(max_order_int + 1)]
            logger.info(f"Modèles Markov de session réinitialisés (reset type: {reset_type}).")

        # Optionnel : ajouter un petit `gc.collect()` ici si la mémoire est une préoccupation majeure
        # import gc
        # gc.collect()

