# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 622 à 666
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

    def _extract_pattern_key(self, features: List[float]) -> str:
        """
        Extrait une clé de pattern à partir des features.
        Cette méthode convertit un vecteur de features en une chaîne de caractères
        qui peut être utilisée comme clé dans un dictionnaire.

        Args:
            features: Vecteur de features

        Returns:
            str: Clé de pattern
        """
        if features is None or not isinstance(features, (list, tuple, np.ndarray)) or len(features) == 0:
            return "empty_pattern"

        try:
            # Limiter le nombre de features utilisées pour la clé
            max_features = min(self.max_pattern_length, len(features))
            selected_features = features[:max_features]

            # Discrétiser les features pour réduire le nombre de patterns uniques
            # et améliorer la généralisation
            discretized_features = []
            for feature in selected_features:
                # Arrondir à 1 décimale pour les features continues
                if isinstance(feature, (int, float)):
                    # Discrétiser différemment selon la plage de valeurs
                    if 0 <= feature <= 1:  # Features normalisées entre 0 et 1
                        # Discrétiser en 5 niveaux: 0.0, 0.25, 0.5, 0.75, 1.0
                        discretized = round(feature * 4) / 4
                    else:  # Autres features
                        # Arrondir à l'entier le plus proche
                        discretized = round(feature)
                    discretized_features.append(str(discretized))
                else:
                    # Pour les features non numériques, utiliser la représentation en chaîne
                    discretized_features.append(str(feature))

            # Créer la clé de pattern
            pattern_key = "_".join(discretized_features)
            return pattern_key

        except Exception as e:
            logger.error(f"Erreur lors de l'extraction de la clé de pattern: {e}", exc_info=True)
            return "error_pattern"