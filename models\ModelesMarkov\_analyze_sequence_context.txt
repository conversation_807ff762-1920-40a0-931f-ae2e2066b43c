# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\models.py
# Lignes: 432 à 496
# Type: Méthode de la classe PersistentMarkov

    def _analyze_sequence_context(self, sequence: List[str]) -> float:
        """
        Analyse le contexte de la séquence pour adapter les poids des modèles.

        Args:
            sequence (List[str]): Séquence de résultats

        Returns:
            float: Facteur contextuel entre 0 et 1
                - Proche de 0: séquence stable, modèle global plus pertinent
                - Proche de 1: séquence volatile, modèle de session plus pertinent
        """
        # Vérifications de sécurité
        if not isinstance(sequence, list):
            logger.warning("_analyze_sequence_context: sequence n'est pas une liste. Retour 0.5.")
            return 0.5

        if len(sequence) < 10:
            # Pas assez de données pour une analyse fiable
            return 0.5

        # Vérifier que tous les éléments sont des chaînes valides
        for item in sequence:
            if not isinstance(item, str) or item not in ('player', 'banker'):
                # Si un élément n'est pas valide, utiliser une valeur par défaut
                return 0.5

        try:
            # 1. Calculer la volatilité récente (alternances dans les 10 derniers coups)
            try:
                # Utiliser min() pour éviter de demander plus d'éléments que la séquence n'en contient
                recent_seq = sequence[-min(10, len(sequence)):]
                alternances = 0
                for i in range(1, len(recent_seq)):
                    if recent_seq[i] != recent_seq[i-1]:
                        alternances += 1

                volatility = alternances / (len(recent_seq) - 1) if len(recent_seq) > 1 else 0.5
            except IndexError as e:
                logger.warning(f"_analyze_sequence_context: Erreur lors du calcul de la volatilité: {e}")
                volatility = 0.5  # Valeur par défaut en cas d'erreur

            # 2. Détecter les streaks récents
            try:
                current_streak = 1
                if len(sequence) > 1:  # S'assurer qu'il y a au moins 2 éléments
                    last_element = sequence[-1]  # Stocker le dernier élément pour éviter les accès répétés
                    for i in range(len(sequence)-2, -1, -1):
                        if sequence[i] == last_element:
                            current_streak += 1
                        else:
                            break
            except IndexError as e:
                logger.warning(f"_analyze_sequence_context: Erreur lors du calcul du streak: {e}")
                current_streak = 1  # Valeur par défaut en cas d'erreur

            streak_factor = min(current_streak / 10, 1.0)  # Normaliser

            # 3. Combiner les facteurs (volatilité générale + importance du streak actuel)
            context_factor = (volatility * 0.7) + (streak_factor * 0.3)

            return max(0.0, min(1.0, context_factor))  # Garantir que le résultat est entre 0 et 1
        except Exception as e:
            logger.error(f"Erreur dans _analyze_sequence_context: {e}")
            return 0.5  # Valeur par défaut en cas d'erreur