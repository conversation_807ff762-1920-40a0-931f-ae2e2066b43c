# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 14784 à 14918
# Type: Méthode de la classe MetaOptimizer

    def _create_advanced_study(self, study_name, direction='minimize', load_if_exists=True,
                              pruner=None, sampler=None, storage=None, n_startup_trials=10):
        """
        Crée une étude Optuna avancée avec des options de configuration étendues.
        Cette méthode permet de personnaliser le pruner, le sampler et le stockage de l'étude.

        Args:
            study_name: Nom de l'étude
            direction: Direction de l'optimisation ('minimize' ou 'maximize')
            load_if_exists: Charger l'étude si elle existe déjà
            pruner: Pruner à utiliser (None pour utiliser celui de la configuration)
            sampler: Sampler à utiliser (None pour utiliser celui de la configuration)
            storage: Stockage à utiliser (None pour utiliser celui de la configuration)
            n_startup_trials: Nombre d'essais aléatoires avant d'utiliser le sampler

        Returns:
            optuna.study.Study: L'étude créée
        """
        import optuna
        from optuna.samplers import TPESampler, RandomSampler, CmaEsSampler
        from optuna.pruners import MedianPruner, PercentilePruner, ThresholdPruner
        import os
        import tempfile

        # Configurer le stockage
        if storage is None:
            # Utiliser le stockage configuré ou créer un stockage temporaire
            storage_type = getattr(self.config, 'optuna_storage_type', 'sqlite')

            if storage_type == 'sqlite':
                # Créer un répertoire pour les bases de données SQLite si nécessaire
                db_dir = getattr(self.config, 'optuna_db_dir', os.path.join(tempfile.gettempdir(), 'optuna_studies'))
                os.makedirs(db_dir, exist_ok=True)

                # Créer le chemin de la base de données
                db_path = os.path.join(db_dir, f"{study_name.replace(' ', '_')}.db")
                storage = f"sqlite:///{db_path}"
                logger.warning(f"Utilisation du stockage SQLite: {db_path}")
            elif storage_type == 'memory':
                storage = None
                logger.warning("Utilisation du stockage en mémoire")
            else:
                # Utiliser la valeur configurée directement
                storage = getattr(self.config, 'optuna_storage', None)
                logger.warning(f"Utilisation du stockage configuré: {storage}")

        # Configurer le pruner
        if pruner is None:
            pruner_type = getattr(self.config, 'optuna_pruner_type', 'median')

            if pruner_type == 'median':
                n_warmup_steps = getattr(self.config, 'optuna_pruner_warmup_steps', 5)
                interval_steps = getattr(self.config, 'optuna_pruner_interval_steps', 1)
                pruner = MedianPruner(n_warmup_steps=n_warmup_steps, interval_steps=interval_steps)
                logger.warning(f"Utilisation du MedianPruner (warmup={n_warmup_steps}, interval={interval_steps})")
            elif pruner_type == 'percentile':
                percentile = getattr(self.config, 'optuna_pruner_percentile', 25.0)
                n_warmup_steps = getattr(self.config, 'optuna_pruner_warmup_steps', 5)
                interval_steps = getattr(self.config, 'optuna_pruner_interval_steps', 1)
                pruner = PercentilePruner(percentile=percentile, n_warmup_steps=n_warmup_steps, interval_steps=interval_steps)
                logger.warning(f"Utilisation du PercentilePruner (percentile={percentile}, warmup={n_warmup_steps})")
            elif pruner_type == 'threshold':
                lower = getattr(self.config, 'optuna_pruner_threshold_lower', float('inf'))
                upper = getattr(self.config, 'optuna_pruner_threshold_upper', float('inf'))
                pruner = ThresholdPruner(lower=lower, upper=upper)
                logger.warning(f"Utilisation du ThresholdPruner (lower={lower}, upper={upper})")
            elif pruner_type == 'none':
                pruner = None
                logger.warning("Aucun pruner utilisé")
            else:
                # Utiliser le pruner par défaut
                pruner = MedianPruner()
                logger.warning("Utilisation du MedianPruner par défaut")

        # Configurer le sampler
        if sampler is None:
            sampler_type = getattr(self.config, 'optuna_sampler_type', 'tpe')

            if sampler_type == 'tpe':
                # Configurer le TPESampler avec des paramètres avancés
                multivariate = getattr(self.config, 'optuna_tpe_multivariate', True)
                constant_liar = getattr(self.config, 'optuna_tpe_constant_liar', True)
                prior_weight = getattr(self.config, 'optuna_tpe_prior_weight', 1.0)

                sampler = TPESampler(
                    n_startup_trials=n_startup_trials,
                    multivariate=multivariate,
                    constant_liar=constant_liar,
                    prior_weight=prior_weight
                )
                logger.warning(f"Utilisation du TPESampler (startup={n_startup_trials}, multivariate={multivariate})")
            elif sampler_type == 'random':
                sampler = RandomSampler()
                logger.warning("Utilisation du RandomSampler")
            elif sampler_type == 'cmaes':
                # Le CmaEsSampler est plus adapté aux problèmes continus
                sampler = CmaEsSampler(n_startup_trials=n_startup_trials)
                logger.warning(f"Utilisation du CmaEsSampler (startup={n_startup_trials})")
            else:
                # Utiliser le sampler par défaut
                sampler = TPESampler(n_startup_trials=n_startup_trials)
                logger.warning(f"Utilisation du TPESampler par défaut (startup={n_startup_trials})")

        # Créer l'étude
        try:
            study = optuna.create_study(
                study_name=study_name,
                direction=direction,
                storage=storage,
                load_if_exists=load_if_exists,
                pruner=pruner,
                sampler=sampler
            )

            logger.warning(f"Étude '{study_name}' créée avec succès (direction={direction})")

            # Stocker des informations supplémentaires dans l'étude
            study.set_user_attr('creation_time', optuna.datetime.datetime.now().isoformat())
            study.set_user_attr('config', str(self.config))

            return study

        except Exception as e:
            logger.error(f"Erreur lors de la création de l'étude: {e}")

            # Fallback: créer une étude en mémoire
            logger.warning("Création d'une étude en mémoire comme fallback")
            study = optuna.create_study(
                study_name=f"{study_name}_fallback",
                direction=direction,
                pruner=pruner,
                sampler=sampler
            )

            return study