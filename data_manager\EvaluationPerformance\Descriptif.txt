DESCRIPTIF DÉTAILLÉ DES MÉTHODES - ÉVALUATION PERFORMANCE
================================================================================

Ce fichier contient la description détaillée des méthodes d'évaluation de performance du système data_manager.py.

ÉTAT DOCUMENTATION : PREMIÈRE VAGUE FONCTIONNELLE
- **Couverture** : Méthodes d'évaluation de performance documentées
- **Niveau** : Documentation fonctionnelle standardisée
- **Qualité** : 15-25 lignes par méthode minimum

DOMAINE FONCTIONNEL : ÉVALUATION PERFORMANCE
- Calcul des métriques de performance
- Validation des prédictions
- Analyse des résultats
- Interface avec scikit-learn

================================================================================

1. evaluate_performance.txt (BaccaratSequenceManager.evaluate_performance - ÉVALUATION PERFORMANCE MODÈLES)
   - Lignes 297-378 dans data_manager.py (82 lignes)
   - FONCTION : Évalue performance (loss, accuracy) sur données déjà filtrées en utilisant scikit-learn au lieu de TensorFlow
   - PARAMÈTRES :
     * self - Instance de BaccaratSequenceManager
     * y_true (np.ndarray) - Résultats réels (0 ou 1) des mains évaluées, doit être 1D
     * y_pred_proba (np.ndarray) - Probabilités prédites pour classe 1 des mains évaluées, doit être 1D
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION TYPES** : `if not isinstance(y_true, np.ndarray) or not isinstance(y_pred_proba, np.ndarray): return {'loss': np.nan, 'accuracy': np.nan}`
     * **VALIDATION DIMENSIONS** : `if y_true.ndim != 1 or y_pred_proba.ndim != 1:` avec warning détaillé
     * **GESTION FLATTEN** : `if y_pred_proba.ndim == 2 and y_pred_proba.shape[1] == 1: y_pred_proba = y_pred_proba.flatten()` commentaire "ex: sortie modèle (N, 1)"
     * **RE-VÉRIFICATION** : `if y_true.ndim != 1 or y_pred_proba.ndim != 1: return {'loss': np.nan, 'accuracy': np.nan}` après flatten
     * **VÉRIFICATION VIDE** : `if y_true.shape[0] == 0 or y_pred_proba.shape[0] == 0: self.logger.warning("Données d'évaluation vides"); return {'loss': np.nan, 'accuracy': np.nan}`
     * **VALIDATION SHAPES** : `if y_true.shape != y_pred_proba.shape: self.logger.warning(f"Shapes incompatibles - y_true: {y_true.shape}, y_pred_proba: {y_pred_proba.shape}"); return {'loss': np.nan, 'accuracy': np.nan}`
     * **CONTRÔLE ÉTIQUETTES** : `unique_labels = np.unique(y_true); if not np.all(np.isin(unique_labels, [0, 1])): self.logger.warning(...)`
     * **CONVERSION CLASSES** : `try: y_pred_class = (y_pred_proba >= 0.5).astype(int) except Exception as e_astype: return {'loss': np.nan, 'accuracy': np.nan}`
     * **INITIALISATION MÉTRIQUES** : `loss = np.nan`, `accuracy = np.nan`
     * **GESTION CLASSE UNIQUE** : `if len(unique_labels) < 2: self.logger.warning(...); loss = np.nan` commentaire "log_loss peut échouer"
     * **CALCUL LOG LOSS** : `else: loss = log_loss(y_true, y_pred_proba, eps=1e-15)` commentaire "eps similaire à celui de Keras"
     * **CALCUL ACCURACY** : `accuracy = accuracy_score(y_true, y_pred_class)`
     * **VALIDATION FINITUDE** : `if not np.isfinite(loss): loss = np.nan; if not np.isfinite(accuracy): accuracy = np.nan`
     * **RETOUR SUCCÈS** : `return {'loss': loss, 'accuracy': accuracy}`
     * **GESTION ERREURS** : `except ValueError as ve:` et `except Exception as e:` avec logging détaillé et return NaN
     * **ROBUSTESSE** : Commentaire "sklearn gère le clipping interne pour éviter log(0)"
   - RETOUR : dict - Dictionnaire contenant 'loss' et 'accuracy', retourne NaNs si inputs invalides
   - UTILITÉ : Méthode essentielle pour évaluation performance modèles ML. Critique pour métriques robustes. Remplace TensorFlow par scikit-learn pour compatibilité.