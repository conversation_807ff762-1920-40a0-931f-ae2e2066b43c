# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 8589 à 8622
# Type: Méthode de la classe HybridBaccaratPredictor

    def calculate_bayesian_weights(self, current_weights: Dict[str, float],
                                  method_confidences: Dict[str, float]) -> Dict[str, float]:
        """
        Calcule les poids bayésiens des modèles en fonction de leur confiance.

        Args:
            current_weights (Dict[str, float]): Poids actuels des modèles
            method_confidences (Dict[str, float]): Confiance calculée pour chaque modèle

        Returns:
            Dict[str, float]: Poids bayésiens ajustés
        """
        # Utiliser un epsilon depuis la configuration pour éviter les divisions par zéro
        epsilon = getattr(self.config, 'epsilon_value', 1e-9)
        bayesian_weights = {}

        # Calculer le produit des poids actuels et des confidences (P(M) * P(D|M))
        weighted_confidences = {
            method: current_weights.get(method, 0) * confidence
            for method, confidence in method_confidences.items()
        }

        # Normaliser pour obtenir P(M|D)
        total_weighted_confidence = sum(weighted_confidences.values())
        if total_weighted_confidence > epsilon:
            bayesian_weights = {
                method: conf / total_weighted_confidence
                for method, conf in weighted_confidences.items()
            }
        else:
            # Fallback aux poids originaux si la somme est trop petite
            bayesian_weights = current_weights.copy()

        return bayesian_weights