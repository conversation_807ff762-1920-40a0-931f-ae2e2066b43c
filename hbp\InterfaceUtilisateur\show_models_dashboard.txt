# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 3332 à 3490
# Type: Méthode de la classe HybridBaccaratPredictor

    def show_models_dashboard(self) -> None:
        """
        Affiche un tableau de bord des modèles entraînés avec leurs hyperparamètres et performances.
        """
        if not self.is_ui_available():
            logger.warning("Interface utilisateur non disponible pour afficher le tableau de bord des modèles")
            return

        # Créer une fenêtre pour le tableau de bord
        dashboard_window = tk.Toplevel(self.root)
        dashboard_window.title("Tableau de bord des modèles")
        dashboard_window.geometry("1000x600")

        # Créer un widget Treeview pour afficher les modèles
        frame = ttk.Frame(dashboard_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Créer les colonnes
        columns = ('filename', 'date', 'accuracy', 'lstm_hidden', 'lstm_layers', 'lgbm_trees', 'markov_order')
        tree = ttk.Treeview(frame, columns=columns, show='headings')

        # Définir les en-têtes de colonnes
        tree.heading('filename', text='Nom du fichier')
        tree.heading('date', text='Date')
        tree.heading('accuracy', text='Précision')
        tree.heading('lstm_hidden', text='LSTM Hidden')
        tree.heading('lstm_layers', text='LSTM Layers')
        tree.heading('lgbm_trees', text='LGBM Trees')
        tree.heading('markov_order', text='Markov Order')

        # Configurer les colonnes
        tree.column('filename', width=250)
        tree.column('date', width=150)
        tree.column('accuracy', width=100)
        tree.column('lstm_hidden', width=100)
        tree.column('lstm_layers', width=100)
        tree.column('lgbm_trees', width=100)
        tree.column('markov_order', width=100)

        # Ajouter une scrollbar
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        tree.pack(fill=tk.BOTH, expand=True)

        # Charger les informations sur les modèles
        models_dir = os.path.join(os.getcwd(), "models")
        if os.path.exists(models_dir):
            model_files = [f for f in os.listdir(models_dir) if f.endswith('.joblib') or f.endswith('.pkl')]

            for model_file in model_files:
                model_path = os.path.join(models_dir, model_file)
                json_path = os.path.splitext(model_path)[0] + '.json'

                # Valeurs par défaut
                date = "Inconnue"
                accuracy = "N/A"
                lstm_hidden = "N/A"
                lstm_layers = "N/A"
                lgbm_trees = "N/A"
                markov_order = "N/A"

                # Essayer de charger les métadonnées depuis le fichier JSON
                if os.path.exists(json_path):
                    try:
                        with open(json_path, 'r', encoding='utf-8') as f:
                            metadata = json.load(f)

                        # Extraire les informations
                        timestamp = metadata.get('timestamp', '')
                        if timestamp:
                            try:
                                date = datetime.fromisoformat(timestamp).strftime("%Y-%m-%d %H:%M")
                            except (ValueError, TypeError):
                                date = timestamp

                        # Extraire les métriques de performance
                        performance_metrics = metadata.get('performance_metrics', {})
                        if 'best_accuracy' in performance_metrics:
                            try:
                                accuracy = f"{float(performance_metrics['best_accuracy']):.3f}"
                            except (ValueError, TypeError):
                                accuracy = str(performance_metrics['best_accuracy'])

                        # Extraire les hyperparamètres
                        hyperparams = metadata.get('hyperparameters', {})
                        if 'lstm_hidden_dim' in hyperparams:
                            lstm_hidden = str(hyperparams['lstm_hidden_dim'])
                        if 'lstm_num_layers' in hyperparams:
                            lstm_layers = str(hyperparams['lstm_num_layers'])
                        if 'lgbm_n_estimators' in hyperparams:
                            lgbm_trees = str(hyperparams['lgbm_n_estimators'])
                        if 'max_markov_order' in hyperparams:
                            markov_order = str(hyperparams['max_markov_order'])
                    except Exception as e:
                        logger.error(f"Erreur lors du chargement des métadonnées pour {model_file}: {e}", exc_info=True)

                # Si aucun fichier JSON n'existe, essayer d'extraire les informations du fichier modèle
                if date == "Inconnue" and (accuracy == "N/A" or lstm_hidden == "N/A"):
                    try:
                        # Déterminer le format du fichier
                        if model_file.endswith('.joblib'):
                            loaded_package = joblib.load(model_path)
                        else:  # .pkl
                            with open(model_path, 'rb') as f:
                                loaded_package = pickle.load(f)

                        # Extraire la date
                        if 'save_timestamp' in loaded_package:
                            timestamp = loaded_package['save_timestamp']
                            if timestamp:
                                try:
                                    date = datetime.fromisoformat(timestamp).strftime("%Y-%m-%d %H:%M")
                                except (ValueError, TypeError):
                                    date = str(timestamp)

                        # Extraire la précision
                        if 'best_accuracy' in loaded_package:
                            try:
                                accuracy = f"{float(loaded_package['best_accuracy']):.3f}"
                            except (ValueError, TypeError):
                                accuracy = str(loaded_package['best_accuracy'])

                        # Extraire les hyperparamètres
                        config_details = loaded_package.get('config_details', {})
                        if 'lstm_hidden_dim' in config_details:
                            lstm_hidden = str(config_details['lstm_hidden_dim'])
                        if 'lstm_num_layers' in config_details:
                            lstm_layers = str(config_details['lstm_num_layers'])
                        if 'max_markov_order' in config_details:
                            markov_order = str(config_details['max_markov_order'])

                        # Essayer d'extraire les paramètres LGBM
                        if 'lgbm_base' in loaded_package and loaded_package['lgbm_base'] is not None:
                            try:
                                lgbm_params = loaded_package['lgbm_base'].get_params()
                                if 'n_estimators' in lgbm_params:
                                    lgbm_trees = str(lgbm_params['n_estimators'])
                            except Exception:
                                pass
                    except Exception as e:
                        logger.error(f"Erreur lors de l'extraction des informations du modèle {model_file}: {e}", exc_info=True)

                # Ajouter le modèle au tableau
                tree.insert('', tk.END, values=(model_file, date, accuracy, lstm_hidden, lstm_layers, lgbm_trees, markov_order))
        else:
            logger.warning(f"Répertoire des modèles non trouvé: {models_dir}")
            messagebox.showinfo("Information", f"Aucun modèle trouvé dans le répertoire {models_dir}")

        # Ajouter des boutons d'action
        button_frame = ttk.Frame(dashboard_window)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(button_frame, text="Voir les détails",
                  command=lambda: self._show_selected_model_details(tree)).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Charger le modèle",
                  command=lambda: self._load_selected_model(tree)).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Fermer",
                  command=dashboard_window.destroy).pack(side=tk.RIGHT, padx=5)