# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 154 à 157
# Type: Méthode

            def custom_get_trial_msg(trial, value):
                """Version personnalisée de _get_trial_msg qui n'affiche pas les paramètres."""
                # Retourner directement le message simplifié
                return f"Trial {trial.number} finished with value: {value}."