# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 617 à 632
# Type: Méthode de la classe LSTMMemoryContext
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        Appelé à la sortie du bloc with.
        Restaure l'état d'entraînement précédent du modèle.
        """
        # Restaurer l'état d'entraînement précédent
        if self.previous_training_state is not None:
            if self.previous_training_state:
                self.model.train()
            else:
                self.model.eval()

        # Nettoyer la mémoire PyTorch
        MemoryManager.cleanup_pytorch_memory()

        return False  # Ne pas supprimer l'exception si elle existe