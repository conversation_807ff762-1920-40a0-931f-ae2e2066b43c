# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 14393 à 14417
# Type: Méthode de la classe MetaOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def _is_in_problematic_region(self, param_name: str, value: float) -> bool:
        """
        Vérifie si une valeur de paramètre se trouve dans une région problématique.

        Args:
            param_name: Nom du paramètre
            value: Valeur du paramètre

        Returns:
            bool: True si la valeur est dans une région problématique, False sinon
        """
        if param_name not in self.problematic_params:
            return False

        # Vérifier les régions problématiques pour trop peu de WAIT
        for region in self.problematic_params[param_name]['too_few_wait']:
            if region['min'] - self.exclusion_margin <= value <= region['max'] + self.exclusion_margin:
                return True

        # Vérifier les régions problématiques pour trop de WAIT
        for region in self.problematic_params[param_name]['too_many_wait']:
            if region['min'] - self.exclusion_margin <= value <= region['max'] + self.exclusion_margin:
                return True

        return False