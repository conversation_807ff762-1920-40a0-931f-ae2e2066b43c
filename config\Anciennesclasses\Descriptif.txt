DESCRIPTIF DÉTAILLÉ DES MÉTHODES - ANCIENNES CLASSES
================================================================================

Ce fichier contient la description détaillée des classes définies dans le système.

ÉTAT DOCUMENTATION : PREMIÈRE VAGUE FONCTIONNELLE
- **Couverture** : 100% des classes documentées
- **Niveau** : Documentation fonctionnelle standardisée
- **Qualité** : 15-25 lignes par classe

DOMAINE FONCTIONNEL : Définitions de classes
- Classes de configuration
- Classes de filtrage
- Structures de données

TOTAL : 2 CLASSES ANALYSÉES

1. class_PredictorConfig.txt (PredictorConfig - CLASSE CONFIGURATION PRINCIPALE)
   - Lignes 157-1031 dans config.py (875 lignes)
   - FONCTION : Classe principale de configuration du système de prédiction ML contenant tous les hyperparamètres, seuils de décision et espaces de recherche Optuna
   - RESPONSABILITÉS :
     * **CONSTANTES MÉTRIQUES** : `METRIC_PRECISION_NON_WAIT = 'precision_non_wait_late_game'`, `METRIC_WAIT_RATIO = 'wait_ratio'`, `OPTIMAL_WAIT_RATIO = 0.4`
     * **HYPERPARAMÈTRES LSTM** : `lstm_hidden_size: int = 512`, `lstm_num_layers: int = 1`, `lstm_dropout: float = 0.27`, `lstm_learning_rate: float = 8.57e-05`
     * **HYPERPARAMÈTRES LGBM** : `lgbm_learning_rate: float = 0.008`, `lgbm_max_depth: int = 3`, `lgbm_num_leaves: int = 26`, `lgbm_n_estimators: int = 109`
     * **HYPERPARAMÈTRES MARKOV** : `markov_max_order: int = 1`, `markov_smoothing: float = 0.15`, `markov_global_weight: float = 0.15`
     * **SEUILS DÉCISION** : `decision_threshold_min: float = 0.3`, `decision_threshold_max: float = 0.6`, `transition_uncertainty_threshold: float = 0.6`
     * **POIDS MODÈLES** : `initial_weights: Dict[str, float] = {'lgbm': 0.20, 'lstm': 0.60, 'markov': 0.20}` avec normalisation automatique
     * **ESPACE OPTUNA** : `optuna_search_space: Dict[str, tuple]` avec 100+ paramètres et plages exactes `('float', min, max)`, `('int', min, max)`, `('categorical', [options])`
     * **FEATURES SÉLECTIONNÉES** : `selected_features: List[str]` avec 28 features exactes incluant 'banker_count', 'player_count', 'confidence'
     * **PARAMÈTRES PERFORMANCE** : `use_advanced_cache: bool = True`, `max_advanced_cache_size_gb: float = 8.0`, `historical_data_sample_percentage: float = 0.10`
     * **VIABILITÉ** : `viability_min_accuracy: float = 0.55`, `viability_min_wait_ratio: float = 0.2`, `viability_max_wait_ratio: float = 0.5`
   - MÉTHODES PRINCIPALES :
     * __init__(config_overrides=None) - Initialisation complète avec 200+ attributs
     * clone() - Création copie profonde avec copy.deepcopy()
     * get_min_sequence_length() - Calcul max(markov_min, lstm_min, lgbm_min)
     * _detect_resources_with_psutil() - Détection CPU/RAM avec fallbacks
   - ARCHITECTURE :
     * **CENTRALISATION** : Point unique pour 200+ paramètres configurables
     * **OPTIMISATION OPTUNA** : Intégration native avec espaces de recherche définis
     * **FLEXIBILITÉ** : Support config_overrides et variables environnement
     * **COHÉRENCE** : Synchronisation garantie entre entraînement et optimisation
     * **ROBUSTESSE** : Fallbacks multiples pour détection ressources et valeurs par défaut
   - UTILITÉ : Classe centrale du système de configuration. Essentielle pour maintenir la cohérence des paramètres. Critique pour l'optimisation Optuna et la reproductibilité des expériences ML.

2. class_SilentOptunaFilter.txt (SilentOptunaFilter - CLASSE FILTRE LOGGING OPTUNA)
   - Lignes 21-118 dans config.py (98 lignes)
   - FONCTION : Classe de filtrage héritant de logging.Filter pour supprimer les messages répétitifs et verbeux d'Optuna et du système ML
   - RESPONSABILITÉS :
     * **HÉRITAGE FILTER** : `class SilentOptunaFilter(logging.Filter):` étend classe de base Python
     * **PATTERNS RÉPÉTITIFS** : `self.repetitive_patterns = [...]` liste exacte de 35 chaînes de caractères
     * **COMPTEUR OCCURRENCES** : `self.repetitive_counts = {}` dictionnaire pour tracking messages vus
     * **DÉTECTION THREADS** : `"OptunaOptimization" in threading.current_thread().name` identification threads optimisation
     * **CORRECTION NIVEAUX** : `record.levelno = logging.INFO; record.levelname = "INFO"` rétrogradation CRITICAL vers INFO
     * **FILTRAGE IMMÉDIAT** : Conditions exactes `"DIAGNOSTIC TRAIN:" in message or "Gradient norm élevé:" in message`
     * **MOTS-CLÉS IMPORTANTS** : Liste exacte `["VIABLE", "ESSAI", "OPTIMISATION", "WAIT", "NON-WAIT", "VAGUE", "Progression", "Epoch", "Val Loss", "Val Accuracy", "Train Loss", "Train Accuracy", "Objectif 1", "Objectif 2", "Score composite", "Early stopping"]`
     * **GESTION PREMIÈRE OCCURRENCE** : `record.msg += " (messages similaires seront filtrés)"` modification message original
     * **PRÉSERVATION WARNING** : `if record.levelno >= logging.WARNING:` test niveau numérique exact
     * **EXCEPTION SPÉCIFIQUE** : `if "Erreur lors de l'optimisation de la mémoire PyTorch" in message: return False`
   - MÉTHODES PRINCIPALES :
     * __init__() - `super().__init__()` puis initialisation patterns et compteurs
     * filter(record) - Logique complète avec 10+ conditions de filtrage
   - ARCHITECTURE :
     * **PATTERN MATCHING** : Utilise `any(pattern in message for pattern in self.repetitive_patterns)` pour détection O(n)
     * **COMPTAGE STATEFUL** : `self.repetitive_counts[message] = 1` puis incrémentation pour tracking
     * **FILTRAGE INTELLIGENT** : Équilibre entre réduction bruit et préservation information critique
     * **THREAD-AWARE** : `return not is_optuna_thread` suppression finale logs threads optimisation
     * **MODIFICATION DYNAMIQUE** : Altération directe attributs LogRecord pour correction niveaux
   - UTILITÉ : Classe essentielle pour maintenir des logs lisibles pendant l'optimisation Optuna. Réduit drastiquement le volume de logs sans perdre d'informations critiques. Indispensable pour le debugging efficace des optimisations longues.
