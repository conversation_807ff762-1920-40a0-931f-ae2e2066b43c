# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1582 à 1589
# Type: Méthode de la classe ThreadedTrainer

    def is_training_running(self):
        """
        Vérifie si l'entraînement est en cours d'exécution.

        Returns:
            bool: True si l'entraînement est en cours d'exécution, False sinon
        """
        return self.is_running