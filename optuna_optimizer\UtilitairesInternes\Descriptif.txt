DESCRIPTIF DÉTAILLÉ DES MÉTHODES - UTILITAIRESINTERNES
===========================================================

Ce fichier contient la description détaillée de toutes les méthodes
présentes dans le sous-dossier UtilitairesInternes.

1. __init___1.txt (DynamicRangeAdjuster.__init__ - HOMONYME de la méthode 21)
   - Lignes 244-264 dans optuna_optimizer.py (21 lignes)
   - FONCTION : Constructeur de la classe DynamicRangeAdjuster pour initialiser l'ajusteur de plages dynamique
   - PARAMÈTRES :
     * self - Instance de la classe DynamicRangeAdjuster
     * config_path (str, optionnel) - Chemin vers le fichier config.py, None utilise le chemin par défaut
   - FONCTIONNEMENT DÉTAILLÉ :
     * **HOMONYME IDENTIFIÉ :** Méthode homonyme de __init__ mais pour classe DynamicRangeAdjuster (vs OptunaMessageFilter)
     * **GESTION CHEMIN CONFIG :** Si config_path None, utilise os.path.join(os.getcwd(), "config.py")
     * **STOCKAGE PLAGES :** Initialise original_ranges{} pour stocker les plages originales des hyperparamètres
     * **PLAGES AJUSTÉES :** Initialise adjusted_ranges{} pour stocker les plages modifiées dynamiquement
     * **VERROU ÉTUDE :** Initialise study_lock=None pour gérer l'accès concurrent aux études Optuna
     * **HISTORIQUE AJUSTEMENTS :** Initialise adjustment_history[] pour tracer tous les ajustements effectués
     * **TIMESTAMP CONTRÔLE :** Initialise last_adjustment_time=0 pour contrôler la fréquence des ajustements
     * **INTERVALLE MINIMUM :** Définit adjustment_interval=60 secondes comme intervalle minimum entre ajustements
     * **LOGGING INITIALISATION :** Log l'initialisation avec le chemin de configuration utilisé
     * **ARCHITECTURE MODULAIRE :** Structure modulaire permettant ajustement dynamique des plages d'optimisation
     * **GESTION ÉTAT :** Maintient l'état complet de l'ajusteur pour persistance et récupération
     * **CONTRÔLE FRÉQUENCE :** Mécanisme de contrôle pour éviter ajustements trop fréquents
   - RETOUR : None (constructeur sans valeur de retour)
   - UTILITÉ : Initialise l'ajusteur de plages dynamique avec gestion de configuration, historique et contrôle de fréquence


2. __init___10.txt (OptimizationStatsCollector.__init__ - CONSTRUCTEUR - DOUBLON 10)
   - Lignes 13595-13603 dans optuna_optimizer.py (9 lignes)
   - FONCTION : Constructeur OptimizationStatsCollector avec trial_id optionnel et reset automatique
   - PARAMÈTRES :
     * self - Instance de la classe OptimizationStatsCollector
     * trial_id (Optional[int]) - Identifiant essai Optuna optionnel
   - FONCTIONNEMENT DÉTAILLÉ :
     * **ASSIGNATION ID :** self.trial_id = trial_id pour tracking essai
     * **RESET AUTOMATIQUE :** self.reset() pour initialisation statistiques
   - RETOUR : None (constructeur)
   - UTILITÉ : Initialisation collecteur statistiques avec reset automatique


3. __init___7.txt (StandardCrossEntropyLoss.__init__ - HOMONYME des méthodes précédentes)
   - Lignes 13322-13328 dans optuna_optimizer.py (7 lignes)
   - FONCTION : Constructeur de la classe StandardCrossEntropyLoss pour initialiser fonction de perte cross-entropy standard
   - PARAMÈTRES :
     * self - Instance de la classe StandardCrossEntropyLoss
     * weight (Tensor, optionnel) - Poids manuel pour chaque classe, None par défaut
     * size_average (bool, optionnel) - Paramètre déprécié pour moyennage par taille
     * ignore_index (int) - Index à ignorer dans le calcul de perte, -100 par défaut
     * reduce (bool, optionnel) - Paramètre déprécié pour réduction
     * reduction (str) - Type de réduction ('mean', 'sum', 'none'), 'mean' par défaut
     * label_smoothing (float) - Facteur de lissage des labels, 0.0 par défaut
   - FONCTIONNEMENT DÉTAILLÉ :
     * **HOMONYME IDENTIFIÉ :** Septième méthode __init__ homonyme pour classe StandardCrossEntropyLoss
     * **HÉRITAGE PYTORCH :** Appelle super().__init__() pour initialiser classe parent nn.Module
     * **ENCAPSULATION LOSS :** Crée self.standard_loss=nn.CrossEntropyLoss() avec tous paramètres
     * **CONFIGURATION COMPLÈTE :** Configure weight, size_average, ignore_index, reduce, reduction, label_smoothing
     * **WRAPPER FONCTIONNEL :** Agit comme wrapper autour de nn.CrossEntropyLoss standard PyTorch
     * **FLEXIBILITÉ PARAMÈTRES :** Supporte tous les paramètres de configuration de CrossEntropyLoss
     * **COMPATIBILITÉ PYTORCH :** Maintient compatibilité complète avec l'API PyTorch standard
     * **PERSONNALISATION LOSS :** Permet personnalisation future de la fonction de perte
     * **ARCHITECTURE MODULAIRE :** Architecture modulaire permettant extension et modification
     * **GESTION LABELS :** Gestion avancée des labels avec ignore_index et label_smoothing
     * **RÉDUCTION CONFIGURABLE :** Réduction configurable pour différents besoins d'entraînement
     * **INTÉGRATION SYSTÈME :** S'intègre dans le système d'optimisation pour calcul de perte
   - RETOUR : None (constructeur sans valeur de retour)
   - UTILITÉ : Initialise fonction de perte cross-entropy standard avec configuration complète et encapsulation PyTorch


4. __init___9.txt (OptimizerAdapter.__init__ - CONSTRUCTEUR - DOUBLON 9)
   - Lignes 13548-13551 dans optuna_optimizer.py (4 lignes)
   - FONCTION : Constructeur OptimizerAdapter avec instance optimiseur et callback progression
   - PARAMÈTRES :
     * self - Instance de la classe OptimizerAdapter
     * optimizer_instance - Instance optimiseur à adapter
     * n_trials - Nombre essais à effectuer
     * progress_callback (optionnel) - Callback progression
   - FONCTIONNEMENT DÉTAILLÉ :
     * **ASSIGNATION DIRECTE :** Stockage optimizer_instance, n_trials, progress_callback
   - RETOUR : None (constructeur)
   - UTILITÉ : Initialisation adaptateur optimiseur avec callback progression


5. _adjust_distribution.txt (MetaOptimizer._adjust_distribution - MÉTHODE AJUSTEMENT DISTRIBUTIONS ADAPTATIF)
   - Lignes 14419-14618 dans optuna_optimizer.py (200 lignes)
   - FONCTION : Ajuste distributions Optuna pour éviter régions problématiques avec approche adaptative basée historique essais
   - PARAMÈTRES :
     * self - Instance de la classe MetaOptimizer
     * name (str) - Nom du paramètre à ajuster
     * distribution (optuna.distributions.BaseDistribution) - Distribution originale
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION PRÉALABLE :** Vérifie analyzed_trials_count ≥ 5 et existence problematic_params[name]
     * **DISTRIBUTIONS UNIFORMES :** Traitement spécialisé UniformDistribution avec ajustements ciblés
     * **PARAMÈTRES SPÉCIFIQUES :** Logique dédiée min_confidence_for_recommendation, transition_uncertainty_threshold, global_uncertainty_factor
     * **AJUSTEMENT CONFIANCE :** Trop WAIT → réduire seuil (0.15-0.4), trop peu WAIT → augmenter seuil (0.6-0.8)
     * **AJUSTEMENT INCERTITUDE :** Trop WAIT → augmenter seuil (0.7-0.9), trop peu WAIT → réduire seuil (0.3-0.5)
     * **AJUSTEMENT FACTEUR GLOBAL :** Trop WAIT → réduire facteur (0.7-0.9), trop peu WAIT → augmenter facteur (1.1-1.3)
     * **ANALYSE ESSAIS RÉUSSIS :** Utilise successful_trials pour centrer distribution autour moyenne ± 2*écart-type
     * **SEGMENTATION ADAPTATIVE :** Divise espace en 10 segments, évalue chaque segment via _is_in_problematic_region()
     * **RECHERCHE SEGMENT OPTIMAL :** Trouve plus grand segment continu non problématique pour nouvelle distribution
     * **EXPLORATION ALÉATOIRE :** 15 tentatives valeurs aléatoires si tous segments problématiques
     * **ÉLARGISSEMENT EXPLORATOIRE :** Élargit plage ±15% si aucune région non problématique trouvée
     * **DISTRIBUTIONS ENTIÈRES :** IntUniformDistribution avec élargissement ±1
     * **DISTRIBUTIONS LOG :** LogUniformDistribution avec facteurs 0.9/1.1
     * **LOGGING DÉTAILLÉ :** Journalise tous ajustements avec plages avant/après
   - RETOUR : optuna.distributions.BaseDistribution - Distribution ajustée évitant régions problématiques
   - UTILITÉ : Optimisation intelligente espace recherche avec évitement adaptatif régions sous-performantes


6. _apply_nonlinear_formula.txt (OptunaOptimizer._apply_nonlinear_formula - MÉTHODE APPLICATION FORMULES NON LINÉAIRES)
   - Lignes 12285-12393 dans optuna_optimizer.py (109 lignes)
   - FONCTION : Applique formules non linéaires adaptées type paramètre avec ajustement complexité données et contraintes ressources
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * param_type - Type paramètre ('subsample', 'min_child_samples', etc.)
     * original_value - Valeur originale du paramètre
     * scale_factor - Facteur d'échelle
     * data_complexity (optionnel, défaut=1.0) - Indicateur complexité données
     * resource_constraints (optionnel, défaut=1.0) - Indicateur contraintes ressources
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PARAMÈTRES DÉCROISSANTS :** lgbm_subsample (exp(-0.05*sf)), lgbm_learning_rate (1/√sf), lstm_learning_rate (1/(1+0.2*log(sf+1)))
     * **PARAMÈTRES BATCH DÉCROISSANTS :** lstm_batch_size (x/√sf), markov_batch_size (x/√sf) avec bornes min/max
     * **PARAMÈTRES ÉPOQUES DÉCROISSANTS :** lstm_epochs (x/log(sf+1)), markov_smoothing (x/sf^0.3) avec bornes
     * **PARAMÈTRES CROISSANTS :** lgbm_min_child_samples (x*√sf), lgbm_num_iterations (x*log(sf+1)/log(11))
     * **PARAMÈTRES PROFONDEUR CROISSANTS :** markov_depth (x+log(sf+1)) avec bornes 3-10
     * **AJUSTEMENT COMPLEXITÉ :** Décroissants: sf*data_complexity/resource_constraints, Croissants: sf*data_complexity*resource_constraints
     * **BORNES SÉCURISÉES :** Chaque paramètre a min_val/max_val définis pour éviter valeurs aberrantes
     * **FALLBACK GRACIEUX :** Paramètres non reconnus retournent valeur originale avec warning
   - RETOUR : Valeur adaptée du paramètre avec formule non linéaire appliquée
   - UTILITÉ : Adaptation intelligente paramètres selon taille données avec formules mathématiques optimisées


7. _apply_optimal_batch_parameters.txt (OptunaOptimizer._apply_optimal_batch_parameters - MÉTHODE APPLICATION PARAMÈTRES BATCH OPTIMAUX)
   - Lignes 13127-13221 dans optuna_optimizer.py (95 lignes)
   - FONCTION : Applique paramètres batch optimaux à configuration selon phase optimisation avec adaptation complète
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * config - Configuration à mettre à jour
     * phase - Phase optimisation ('phase0', 'phase1', 'phase2', 'phase3')
     * for_full_training (bool, défaut=False) - Adapte pour entraînement complet 100% données
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION PARAMÈTRES :** Vérifie existence optimal_batch_params, fallback valeurs défaut
     * **CLONAGE CONFIG :** Copie configuration pour éviter modification original
     * **ADAPTATION COMPLÈTE :** Utilise adapt_parameters_for_full_training() si for_full_training=True
     * **PARAMÈTRES LGBM PHASE :** Applique lgbm_subsample, lgbm_min_child_samples spécifiques phase
     * **PARAMÈTRES LGBM AVANCÉS :** Configure lgbm_num_iterations, lgbm_learning_rate, lgbm_batch_size
     * **PARAMÈTRES LSTM CONDITIONNELS :** Active pour phase2/3, configure lstm_batch_size, lstm_epochs
     * **ÉPOQUES ADAPTATIVES :** Phase3→époques complètes, Phase2→moitié époques optimales
     * **PARAMÈTRES MARKOV :** Active use_markov_model, configure markov_batch_size pour phase markov
     * **CONFIGURATION MARKOV COMPLÈTE :** max_markov_order, markov_smoothing, weights, decay_factor
     * **LOGGING INFORMATIF :** Journalise application paramètres avec distinction entraînement complet
   - RETOUR : Configuration mise à jour avec paramètres optimaux appliqués
   - UTILITÉ : Application intelligente paramètres optimisés selon phase avec adaptation entraînement complet


8. _backup_original_ranges.txt (DynamicRangeAdjuster._backup_original_ranges - MÉTHODE SAUVEGARDE PLAGES ORIGINALES)
   - Lignes 654-657 dans optuna_optimizer.py (4 lignes)
   - FONCTION : Sauvegarde plages originales paramètres pour restauration ultérieure
   - PARAMÈTRES : self - Instance de la classe DynamicRangeAdjuster
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RÉCUPÉRATION PLAGES :** Utilise _get_current_ranges() pour obtenir plages actuelles
     * **SAUVEGARDE :** Stocke dans self.original_ranges pour référence
     * **LOGGING :** Journalise plages sauvegardées pour traçabilité
   - RETOUR : None (sauvegarde en place)
   - UTILITÉ : Préservation état initial pour restauration après ajustements dynamiques


9. _calculate_dynamic_scale_factor.txt (OptunaOptimizer._calculate_dynamic_scale_factor - MÉTHODE CALCUL FACTEUR ÉCHELLE DYNAMIQUE)
   - Lignes 12246-12283 dans optuna_optimizer.py (38 lignes)
   - FONCTION : Calcule dynamiquement facteur échelle basé nombre réel lignes historical_data.txt pour adaptation paramètres
   - PARAMÈTRES : self - Instance de la classe OptunaOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CHEMIN FICHIER :** os.path.join(os.getcwd(), "historical_data.txt")
     * **VÉRIFICATION EXISTENCE :** os.path.exists() avec fallback 10.0 si absent
     * **COMPTAGE LIGNES :** sum(1 for _ in f) pour total_lines efficace
     * **CALCUL OPTIMISATION :** optimization_lines = int(total_lines * 0.1) pour 10% données
     * **FACTEUR ÉCHELLE :** scale_factor = total_lines / optimization_lines si >0
     * **GESTION ERREURS :** try/except avec fallback 10.0 et logging erreur
     * **LOGGING INFORMATIF :** Journalise facteur calculé avec total_lines
   - RETOUR : float - Facteur échelle dynamique ou 10.0 par défaut
   - UTILITÉ : Adaptation automatique paramètres selon taille réelle dataset pour optimisation précise


10. _convert_indices_to_sequences.txt (OptunaOptimizer._convert_indices_to_sequences - MÉTHODE CONVERSION INDICES)
   - Lignes 5144-5170 dans optuna_optimizer.py (27 lignes)
   - FONCTION : Convertit indices d'échantillonnage en séquences complètes avec validation robuste
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * sequences - Liste des séquences d'origine
     * indices - Indices des séquences à extraire (list/numpy.ndarray)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION INDICES :** Vérifie indices non None, retourne toutes séquences si None
     * **CONVERSION TYPE :** Convertit numpy.ndarray vers list avec .tolist()
     * **VALIDATION LIMITES :** Filtre indices valides avec 0 <= i < len(sequences)
     * **ALERTE HORS LIMITES :** Log warning pour indices hors limites ignorés
     * **EXTRACTION SÉQUENCES :** Utilise list comprehension [sequences[i] for i in valid_indices]
   - RETOUR : List - Liste des séquences correspondant aux indices valides
   - UTILITÉ : Conversion sécurisée indices vers séquences pour échantillonnage et sous-ensembles


11. _convert_to_sklearn_estimator.txt (OptunaOptimizer._convert_to_sklearn_estimator - MÉTHODE CONVERSION ESTIMATEUR SKLEARN)
   - Lignes 2537-2730 dans optuna_optimizer.py (194 lignes)
   - FONCTION : Convertit meilleurs paramètres en estimateur scikit-learn avec pipeline prétraitement et intégration complète
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * best_params (optionnel) - Meilleurs paramètres (défaut: self.best_params)
     * estimator_type (str, défaut='classifier') - Type estimateur ('classifier' ou 'regressor')
     * include_preprocessing (bool, défaut=True) - Inclure étapes prétraitement
     * random_state (int, défaut=42) - Graine aléatoire reproductibilité
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION PARAMÈTRES :** Vérifie best_params disponibles, estimator_type valide ('classifier'/'regressor')
     * **CRÉATION CLASSE DYNAMIQUE :** type() pour OptunaOptimizedClassifier/Regressor héritant BaseEstimator + ClassifierMixin/RegressorMixin
     * **MÉTHODES SKLEARN STANDARD :** __init__, get_params(), set_params(), _more_tags() pour compatibilité complète
     * **MÉTHODES CLASSIFICATEUR :** fit() avec classes_ et n_classes_, predict(), predict_proba() pour classification
     * **MÉTHODES RÉGRESSEUR :** fit() et predict() pour régression sans probabilités
     * **IMPLÉMENTATION RÉELLE :** real_fit_model() avec RandomForestClassifier/Regressor selon type et paramètres
     * **EXTRACTION PARAMÈTRES :** Filtre paramètres 'model_*' et supprime préfixe pour configuration modèle
     * **GESTION MULTI-CLASSE :** Détection automatique classification binaire/multi-classe via n_classes_
     * **STOCKAGE MÉTADONNÉES :** X_train_, y_train_, feature_importances_ pour introspection
     * **PIPELINE PRÉTRAITEMENT :** ColumnTransformer avec StandardScaler (numériques) + OneHotEncoder (catégorielles)
     * **GESTION ERREURS :** try/except avec logging warnings pour robustesse
     * **VALIDATION RUNTIME :** Vérifications model_ entraîné et support predict_proba
   - RETOUR : sklearn.base.BaseEstimator ou Pipeline - Estimateur configuré avec meilleurs paramètres
   - UTILITÉ : Intégration transparente résultats optimisation dans écosystème scikit-learn avec compatibilité complète


12. _estimate_data_complexity.txt (OptunaOptimizer._estimate_data_complexity - MÉTHODE ESTIMATION COMPLEXITÉ DONNÉES)
   - Lignes 12395-12444 dans optuna_optimizer.py (50 lignes)
   - FONCTION : Estime complexité données en analysant diversité patterns échantillon avec calcul ratio P/B
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * sample_data (optionnel) - Échantillon données à analyser (défaut: self.sequences[:1000])
   - FONCTIONNEMENT DÉTAILLÉ :
     * **ÉCHANTILLONNAGE :** Utilise sample_data ou self.sequences[:1000] pour analyse
     * **FALLBACK DÉFAUT :** Retourne 1.0 si aucune donnée disponible
     * **ANALYSE PATTERNS :** Extrait patterns longueur 3 de target_sequence pour chaque séquence
     * **COMPTAGE PATTERNS :** Compte occurrences chaque pattern unique dans échantillon
     * **DIVERSITÉ PATTERNS :** unique_patterns / max_possible_patterns (2^3=8 pour binaire)
     * **CALCUL COMPLEXITÉ :** complexity = 0.5 + pattern_diversity pour normalisation
     * **GESTION ERREURS :** try/except avec fallback 1.0 et logging erreur
     * **LOGGING INFORMATIF :** Journalise complexité estimée pour traçabilité
   - RETOUR : float - Indicateur complexité données (1.0 = complexité moyenne)
   - UTILITÉ : Estimation intelligente complexité pour adaptation paramètres selon nature données


13. _export_generic_to_onnx.txt (OptunaOptimizer._export_generic_to_onnx - MÉTHODE EXPORT GÉNÉRIQUE ONNX)
   - Lignes 3015-3078 dans optuna_optimizer.py (64 lignes)
   - FONCTION : Tentative export générique modèle ONNX avec fallbacks multiples onnxmltools et hummingbird
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * model - Modèle à exporter (type générique)
     * output_path - Chemin fichier ONNX sortie
     * input_shape/input_names/output_names (optionnel) - Configuration entrées/sorties
     * opset_version (int, défaut=12) - Version ensemble opérations ONNX
   - FONCTIONNEMENT DÉTAILLÉ :
     * **TENTATIVE ONNXMLTOOLS :** convert_sklearn() avec FloatTensorType(input_shape)
     * **SÉRIALISATION :** onx.SerializeToString() vers fichier binaire
     * **FALLBACK HUMMINGBIRD :** hummingbird.ml.convert(model, 'pytorch') vers PyTorch
     * **EXPORT PYTORCH :** _export_pytorch_to_onnx() pour modèle converti
     * **GESTION ÉCHECS :** try/except imbriqués avec pass pour tentatives multiples
     * **MESSAGES INFORMATIFS :** logger.warning() pour succès et échecs
   - RETOUR : str - Chemin fichier ONNX généré ou None si échec toutes tentatives
   - UTILITÉ : Export robuste modèles génériques avec stratégies fallback pour compatibilité maximale


14. _export_pytorch_to_onnx.txt (OptunaOptimizer._export_pytorch_to_onnx - MÉTHODE EXPORT PYTORCH ONNX)
   - Lignes 2858-2935 dans optuna_optimizer.py (78 lignes)
   - FONCTION : Exporte modèle PyTorch au format ONNX avec validation et configuration axes dynamiques
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * model - Modèle PyTorch à exporter
     * output_path - Chemin fichier ONNX sortie
     * input_shape (optionnel) - Forme données entrée [batch_size, n_features]
     * input_names/output_names (optionnel) - Noms entrées/sorties modèle
     * opset_version (int, défaut=12) - Version ensemble opérations ONNX
     * dynamic_axes (optionnel) - Axes dynamiques entrées/sorties
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MODE ÉVALUATION :** model.eval() pour désactiver dropout/batch_norm
     * **ENTRÉE FACTICE :** torch.randn(*input_shape) avec défaut [1, 10]
     * **AXES DYNAMIQUES :** Configuration automatique {input_names[0]: {0: 'batch_size'}} si non spécifié
     * **EXPORT ONNX :** torch.onnx.export() avec export_params=True, do_constant_folding=True
     * **VALIDATION MODÈLE :** onnxruntime.InferenceSession() avec test inférence données factices
     * **GESTION ERREURS :** ImportError pour packages manquants avec instructions installation
   - RETOUR : str - Chemin fichier ONNX généré ou None si erreur
   - UTILITÉ : Export robuste modèles PyTorch vers ONNX avec validation pour déploiement cross-platform


15. _export_sklearn_to_onnx.txt (OptunaOptimizer._export_sklearn_to_onnx - MÉTHODE EXPORT SKLEARN ONNX)
   - Lignes 2790-2856 dans optuna_optimizer.py (67 lignes)
   - FONCTION : Exporte modèle scikit-learn au format ONNX avec types données et validation
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * model - Modèle scikit-learn à exporter
     * output_path - Chemin fichier ONNX sortie
     * input_shape/input_names/output_names (optionnel) - Configuration entrées/sorties
     * opset_version (int, défaut=12) - Version ensemble opérations ONNX
   - FONCTIONNEMENT DÉTAILLÉ :
     * **FORME ENTRÉE :** input_shape défaut [None, 10] pour [batch_size, n_features]
     * **TYPE DONNÉES :** FloatTensorType(input_shape) pour données numériques
     * **CONVERSION ONNX :** skl2onnx.convert_sklearn() avec initial_types et options
     * **OPTIONS SPÉCIALES :** zipmap=False pour classificateurs et target_opset
     * **SÉRIALISATION :** onx.SerializeToString() vers fichier binaire
     * **VALIDATION ONNX :** onnxruntime.InferenceSession() avec test inférence
     * **DONNÉES TEST :** np.random.rand(5, input_shape[1]) pour validation
   - RETOUR : str - Chemin fichier ONNX généré ou None si erreur
   - UTILITÉ : Export robuste modèles scikit-learn vers ONNX avec validation pour déploiement


16. _export_tensorflow_to_onnx.txt (OptunaOptimizer._export_tensorflow_to_onnx - MÉTHODE EXPORT TENSORFLOW ONNX)
   - Lignes 2937-3013 dans optuna_optimizer.py (77 lignes)
   - FONCTION : Exporte modèle TensorFlow/Keras au format ONNX avec gestion modèles sauvegardés et validation
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * model - Modèle TensorFlow/Keras à exporter
     * output_path - Chemin fichier ONNX sortie
     * input_shape/input_names/output_names (optionnel) - Configuration entrées/sorties
     * opset_version (int, défaut=12) - Version ensemble opérations ONNX
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉTECTION TYPE :** hasattr(model, 'save') pour distinction Keras vs TensorFlow
     * **KERAS EXPORT :** model.save() vers tempfile puis tf2onnx.convert.from_keras()
     * **TENSORFLOW EXPORT :** tf2onnx.convert.from_tensorflow() avec frozen_graph
     * **NETTOYAGE TEMP :** shutil.rmtree(temp_dir) après conversion Keras
     * **SÉRIALISATION :** model_proto.SerializeToString() vers fichier binaire
     * **VALIDATION ONNX :** onnxruntime.InferenceSession() avec test inférence
     * **GESTION SHAPES :** [dim if dim else 1 for dim in input_shape] pour dimensions dynamiques
   - RETOUR : str - Chemin fichier ONNX généré ou None si erreur
   - UTILITÉ : Export robuste modèles TensorFlow/Keras vers ONNX avec gestion types et validation


17. _export_to_onnx.txt (OptunaOptimizer._export_to_onnx - MÉTHODE EXPORT ONNX PRINCIPAL)
   - Lignes 2732-2788 dans optuna_optimizer.py (57 lignes)
   - FONCTION : Exporte modèle optimisé ONNX avec détection automatique type et routage méthodes spécialisées
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * model - Modèle à exporter (scikit-learn, PyTorch, TensorFlow/Keras)
     * output_path - Chemin fichier ONNX sortie
     * input_shape/input_names/output_names (optionnel) - Configuration entrées/sorties
     * opset_version/target_opset/dynamic_axes (optionnel) - Paramètres ONNX
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MODÈLE :** if model is None return None avec message warning
     * **CRÉATION RÉPERTOIRE :** os.makedirs(dirname(abspath(output_path))) pour structure dossiers
     * **NOMS DÉFAUT :** input_names=['input'], output_names=['output'] si non spécifiés
     * **DÉTECTION TYPE :** model_type = type(model).__module__.split('.')[0] pour routage
     * **ROUTAGE SKLEARN :** _export_sklearn_to_onnx() pour modèles scikit-learn
     * **ROUTAGE PYTORCH :** _export_pytorch_to_onnx() avec dynamic_axes pour PyTorch
     * **ROUTAGE TENSORFLOW :** _export_tensorflow_to_onnx() pour TensorFlow/Keras
     * **FALLBACK GÉNÉRIQUE :** _export_generic_to_onnx() pour types non reconnus
   - RETOUR : str - Chemin fichier ONNX généré ou None si erreur
   - UTILITÉ : Interface unifiée export ONNX avec détection automatique et routage intelligent


18. _extract_dataset_features.txt (OptunaOptimizer._extract_dataset_features - MÉTHODE EXTRACTION CARACTÉRISTIQUES DATASET)
   - Lignes 12488-12603 dans optuna_optimizer.py (116 lignes)
   - FONCTION : Extrait caractéristiques dataset pour méta-apprentissage avec analyse patterns, fréquences et entropie
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * sample_data (optionnel) - Échantillon données à analyser (défaut: self.sequences[:1000])
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CARACTÉRISTIQUES BASE :** total_lines, pattern_diversity, alternation_rate, p_frequency, b_frequency, longest_streak, avg_streak_length, entropy
     * **COMPTAGE LIGNES :** Lit historical_data.txt avec sum(1 for _ in f) pour total_lines
     * **ÉCHANTILLONNAGE :** Utilise sample_data ou self.sequences[:1000] pour analyse
     * **EXTRACTION SÉQUENCES :** Filtre seq['target_sequence'] depuis dictionnaires données
     * **ANALYSE PATTERNS :** Compte patterns longueur 3 avec pattern_counts[seq[i-2:i+1]]
     * **CALCUL ALTERNANCES :** Détecte changements seq[i] != seq[i-1] pour alternation_rate
     * **ANALYSE STREAKS :** Suit current_streak et streak_lengths pour longest/avg_streak_length
     * **FRÉQUENCES P/B :** Compte 'P' et 'B' pour p_frequency/b_frequency normalisées
     * **DIVERSITÉ PATTERNS :** unique_patterns / max_possible_patterns (2^3=8 pour binaire)
     * **ENTROPIE SHANNON :** -(p_prob*log2(p_prob) + b_prob*log2(b_prob)) pour imprévisibilité
     * **GESTION ERREURS :** try/except avec fallback caractéristiques défaut
   - RETOUR : Dict - Caractéristiques dataset avec métriques statistiques et patterns
   - UTILITÉ : Analyse complète dataset pour méta-apprentissage et adaptation paramètres intelligente


19. _fit_model.txt (OptunaOptimizer._fit_model - MÉTHODE ENTRAÎNEMENT MODÈLE PLACEHOLDER)
   - Lignes 2616-2619 dans optuna_optimizer.py (4 lignes)
   - FONCTION : Placeholder pour logique entraînement spécifique modèle (implémentation future)
   - PARAMÈTRES : self - Instance OptunaOptimizer, X - Données entrée, y - Cibles
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PLACEHOLDER :** pass - Aucune implémentation actuelle
     * **COMMENTAIRE :** Indique remplacement par implémentation réelle future
   - RETOUR : None (placeholder)
   - UTILITÉ : Structure pour future implémentation entraînement modèle spécialisé


20. _generate_lhs_points.txt (OptunaOptimizer._generate_lhs_points - MÉTHODE GÉNÉRATION POINTS LHS)
   - Lignes 5520-5552 dans optuna_optimizer.py (33 lignes)
   - FONCTION : Génère points selon méthode Latin Hypercube Sampling pour distribution optimale
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * n_dims - Nombre de dimensions
     * n_samples - Nombre d'échantillons à générer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION MATRICE :** Crée result = zeros(n_samples, n_dims)
     * **BOUCLE DIMENSIONS :** Traite chaque dimension indépendamment
     * **SEGMENTS ÉQUIDISTANTS :** Utilise np.linspace(0, 1, n_samples+1) pour division uniforme
     * **POINTS ALÉATOIRES :** np.random.uniform(segments[:-1], segments[1:]) dans chaque segment
     * **MÉLANGE DIMENSION :** np.random.shuffle(points) pour décorréler dimensions
     * **OPTIMISATION DISTRIBUTION :** Appelle _transform_lhs_points() pour améliorer répartition
   - RETOUR : numpy.ndarray - Points LHS générés (n_samples, n_dims)
   - UTILITÉ : Génération points LHS pour échantillonnage uniforme multi-dimensionnel


21. _get_current_ranges.txt (DynamicRangeAdjuster._get_current_ranges - MÉTHODE EXTRACTION PLAGES CONFIG)
   - Lignes 659-735 dans optuna_optimizer.py (77 lignes)
   - FONCTION : Extrait plages actuelles depuis config.py avec parsing regex multiple formats paramètres
   - PARAMÈTRES : self - Instance de la classe DynamicRangeAdjuster
   - FONCTIONNEMENT DÉTAILLÉ :
     * **LECTURE FICHIER :** Lit config.py avec encoding='utf-8' pour parsing complet
     * **FORMAT STANDARD :** Pattern1 'param_name': ('type', low, high) sans options
     * **FORMAT AVEC OPTIONS :** Pattern1b 'param_name': ('type', low, high, {'log': True})
     * **CONVERSION TYPES :** int(float(low/high)) pour 'int', float(low/high) pour autres
     * **PARAMÈTRES CATÉGORIELS :** Pattern2 'param_name': ('categorical', [val1, val2, ...])
     * **TRAITEMENT BOOLÉENS :** Remplace 'True'/'False' par True/False pour eval()
     * **ÉVALUATION SÉCURISÉE :** eval(f"[{categories_str}]") pour parsing liste catégories
     * **PARAMÈTRES BOOLÉENS :** Pattern3 self.param_name = True/False avec vérification non-duplication
     * **GESTION ERREURS :** try/except avec logging erreur et traceback pour debugging
     * **LOGGING INFORMATIF :** Journalise nombre paramètres trouvés pour validation
   - RETOUR : Dict[str, Tuple] - Plages actuelles {param_name: (param_type, low, high)} ou (param_type, categories)
   - UTILITÉ : Extraction robuste plages paramètres depuis config.py pour ajustement dynamique


22. _get_param_range.txt (MetaOptimizer._get_param_range - MÉTHODE RÉCUPÉRATION PLAGE PARAMÈTRE)
   - Lignes 14067-14086 dans optuna_optimizer.py (20 lignes)
   - FONCTION : Récupère plage valeurs pour paramètre depuis espace recherche configuration
   - PARAMÈTRES :
     * self - Instance de la classe MetaOptimizer
     * param_name - Nom du paramètre
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION CONFIG :** hasattr(self, 'config') et self.config is not None
     * **ESPACE RECHERCHE :** getattr(self.config, 'optuna_search_space', {})
     * **EXTRACTION PARAMÈTRE :** param_type, *param_args = search_space[param_name]
     * **TYPES SUPPORTÉS :** 'int' et 'float' avec retour (min_val, max_val)
     * **FALLBACK :** Retourne None si paramètre non trouvé ou type non supporté
   - RETOUR : tuple - (min_val, max_val) ou None si non disponible
   - UTILITÉ : Accès simple plages paramètres pour validation et ajustement méta-optimisation


23. _identify_out_of_range_params.txt (DynamicRangeAdjuster._identify_out_of_range_params - MÉTHODE IDENTIFICATION PARAMÈTRES HORS PLAGE)
   - Lignes 408-481 dans optuna_optimizer.py (74 lignes)
   - FONCTION : Identifie paramètres hors plages définies avec calcul nouvelles plages adaptées
   - PARAMÈTRES :
     * self - Instance de la classe DynamicRangeAdjuster
     * trials - Liste essais Optuna à analyser
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PLAGES ACTUELLES :** _get_current_ranges() pour référence config.py
     * **PARAMÈTRES BOOLÉENS :** Liste prédéfinie lstm_use_*, use_*, lgbm_use_* pour traitement spécial
     * **ANALYSE ESSAIS :** Parcourt trial.params.items() pour chaque essai
     * **VÉRIFICATION PLAGE :** value < low ou value > high pour paramètres numériques
     * **MARGE FLOAT :** 5% marge relative value*(1±margin) pour flottants
     * **MARGE INT :** Marge absolue int(value ± max(1, abs(value)*margin)) pour entiers
     * **ÉLARGISSEMENT :** min(curr_low, new_low), max(curr_high, new_high) si déjà hors plage
     * **CATÉGORIELS :** Ajoute value à liste catégories si value not in low
     * **EXCLUSION BOOLÉENS :** is_boolean_param pour éviter modification paramètres booléens
     * **LOGGING SUPPRIMÉ :** Commentaires pour éviter spam logs paramètres hors plage
   - RETOUR : Dict[str, Tuple] - Paramètres hors plage avec nouvelles plages {param_name: (param_type, new_low, new_high)}
   - UTILITÉ : Détection automatique paramètres hors plage pour ajustement dynamique espace recherche


24. _load_all_historical_data.txt (OptunaOptimizer._load_all_historical_data - MÉTHODE CHARGEMENT DONNÉES HISTORIQUES COMPLEXE)
   - Lignes 7496-7741 dans optuna_optimizer.py (246 lignes)
   - FONCTION : Chargement optimisé des données historiques avec cache avancé, traitement parallèle et échantillonnage stratifié
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * sample_percentage (float, défaut=0.10) - Pourcentage des données à échantillonner (forcé à 10%)
     * is_viability_check (bool, défaut=False) - Indique si c'est une vérification de viabilité (ignoré)
     * use_stratified (bool, défaut=True) - Utiliser échantillonnage stratifié au lieu d'aléatoire
     * use_parallel (bool, défaut=True) - Utiliser traitement parallèle pour accélération
     * max_cache_size_gb (float, défaut=5) - Taille maximale du cache en Go
     * force_sequential (bool, défaut=False) - Forcer traitement séquentiel même si parallèle activé
   - FONCTIONNEMENT DÉTAILLÉ :
     * **FORÇAGE ÉCHANTILLONNAGE :** Force sample_percentage à 10% pour garantir utilisation optimale
     * **ANALYSE TAILLE FICHIER :** Mesure taille historical_data.txt en MB/GB pour stratégie adaptative
     * **CACHE INTELLIGENT :** Vérifie existence _cached_historical_data pour éviter rechargements
     * **ÉCHANTILLONNAGE CACHE :** Applique échantillonnage sur données en cache si disponibles
     * **ÉCHANTILLONNAGE STRATIFIÉ :** Utilise _stratified_sampling() pour distribution représentative
     * **CONVERSION INDICES :** Utilise _convert_indices_to_sequences() pour séquences complètes
     * **ÉCHANTILLONNAGE ALÉATOIRE :** Fallback vers random.sample() si stratifié non demandé
     * **DÉTECTION FICHIER :** Vérifie existence historical_data.txt avec gestion erreurs
     * **ANALYSE VOLUMÉTRIE :** Calcule taille en bytes/GB pour décisions de traitement
     * **MODE STREAMING :** Bascule vers _load_historical_data_streaming() pour gros fichiers
     * **TRAITEMENT PARALLÈLE :** Utilise ProcessPoolExecutor avec 8 cœurs maximum
     * **MÉTHODE STATIQUE :** Utilise _process_line_static() pour éviter problèmes sérialisation
     * **PROGRESSION DÉTAILLÉE :** Affiche progression par tranches de 10% avec compteurs
     * **GESTION ERREURS PARALLÈLE :** Try/catch robuste avec fallback séquentiel automatique
     * **TRAITEMENT SÉQUENTIEL :** Mode de secours avec progression et gestion erreurs
     * **MESURE PERFORMANCE :** Chronomètre temps de chargement avec logging détaillé
     * **ESTIMATION MÉMOIRE :** Calcule taille estimée en mémoire avec sys.getsizeof()
     * **CACHE CONDITIONNEL :** Met en cache uniquement si taille < max_cache_size_gb
     * **LOGGING INTELLIGENT :** Utilise attributs de classe pour éviter logs répétitifs
     * **SÉQUENCES COMPLÈTES :** Conserve séquence 1-60 avec identification manches 31-60
     * **VALIDATION RÉSULTATS :** Compte séquences valides vs total lignes traitées
     * **GESTION EXCEPTIONS :** Try/except global avec traceback détaillé pour débogage
   - RETOUR : List[Dict] - Liste de dictionnaires avec full_sequence, target_sequence, sequence_data
   - UTILITÉ : Chargement haute performance des données historiques avec optimisations mémoire et parallélisme


25. _load_historical_data_streaming.txt (OptunaOptimizer._load_historical_data_streaming - MÉTHODE CHARGEMENT STREAMING)
   - Lignes 7743-7859 dans optuna_optimizer.py (117 lignes)
   - FONCTION : Chargement données historiques en mode streaming pour économiser mémoire sur gros fichiers
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * file_path - Chemin du fichier historical_data.txt
     * sample_percentage (float, optionnel) - Pourcentage des données à échantillonner
     * use_stratified (bool, défaut=True) - Utiliser échantillonnage stratifié
   - FONCTIONNEMENT DÉTAILLÉ :
     * **COMPTAGE LIGNES :** Première passe pour compter total_lines dans le fichier
     * **CALCUL ÉCHANTILLON :** Calcule sample_size = total_lines * sample_percentage
     * **SÉLECTION ALÉATOIRE :** Utilise random.sample() pour sélectionner indices lignes à traiter
     * **TRAITEMENT STREAMING :** Lit fichier ligne par ligne sans charger tout en mémoire
     * **FILTRAGE INDICES :** Traite uniquement lignes avec line_idx in selected_indices
     * **VALIDATION SÉQUENCES :** Vérifie len(full_sequence) >= 60 pour séquences complètes
     * **EXTRACTION MANCHES :** Sépare manches 1-30 (training) et 31-60 (target)
     * **CONVERSION FORMAT :** Convertit P/B vers player/banker en lowercase standard
     * **STRUCTURE DONNÉES :** Crée dictionnaires avec training_sequence, target_sequence, sequence_data
     * **PROGRESSION AFFICHAGE :** Log progression tous les 1000 séquences valides
     * **ÉCHANTILLONNAGE STRATIFIÉ :** Applique _stratified_sampling() sur données déjà échantillonnées
     * **CONVERSION INDICES :** Utilise _convert_indices_to_sequences() pour séquences finales
     * **GESTION ERREURS :** Try/catch par ligne avec logging détaillé des erreurs
     * **OPTIMISATION MÉMOIRE :** Évite chargement complet fichier en mémoire
   - RETOUR : List[Dict] - Liste dictionnaires avec training_sequence, target_sequence, sequence_data
   - UTILITÉ : Chargement efficace gros fichiers historiques avec contrôle mémoire et échantillonnage


26. _normalize_features.txt (OptunaOptimizer._normalize_features - MÉTHODE NORMALISATION FEATURES)
   - Lignes 5405-5443 dans optuna_optimizer.py (39 lignes)
   - FONCTION : Normalise features pour éviter problèmes numériques dans calculs distances
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * features - Tableau numpy caractéristiques (n_samples, n_features)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉE :** Vérifie features non None et non vide
     * **NETTOYAGE VALEURS :** Remplace valeurs non finies (NaN, inf) par 0
     * **CALCUL STATISTIQUES :** Calcule min, max, range par feature avec np.min/max(axis=0)
     * **PROTECTION DIVISION :** Met feature_range[range==0] = 1.0 pour éviter division par zéro
     * **NORMALISATION [0,1] :** Applique (features - min) / range pour normalisation standard
     * **VÉRIFICATION FINALE :** Remplace valeurs non finies restantes par 0.5 (milieu plage)
   - RETOUR : numpy.ndarray - Features normalisées dans [0,1]
   - UTILITÉ : Normalisation robuste pour calculs distances et algorithmes ML


27. _predict_model.txt (OptunaOptimizer._predict_model - MÉTHODE PRÉDICTION MODÈLE PLACEHOLDER)
   - Lignes 2621-2624 dans optuna_optimizer.py (4 lignes)
   - FONCTION : Placeholder pour logique prédiction spécifique modèle (implémentation future)
   - PARAMÈTRES : self - Instance OptunaOptimizer, X - Données entrée
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PLACEHOLDER :** pass - Aucune implémentation actuelle
     * **COMMENTAIRE :** Indique remplacement par implémentation réelle future
   - RETOUR : None (placeholder)
   - UTILITÉ : Structure pour future implémentation prédiction modèle spécialisé


28. _predict_proba_model.txt (OptunaOptimizer._predict_proba_model - PRÉDICTION PROBABILITÉS MODÈLE PRIVÉE)
   - Lignes 2626-2629 dans optuna_optimizer.py (4 lignes)
   - FONCTION : Méthode placeholder pour prédiction probabilités spécifique modèle
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * X - Données features pour prédiction
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PLACEHOLDER :** pass pour implémentation future
     * **COMMENTAIRE EXPLICATIF :** Indique remplacement par implémentation réelle
   - RETOUR : None (méthode placeholder)
   - UTILITÉ : Interface réservée prédiction probabilités spécifique modèle


29. _preprocess_batch.txt (OptunaOptimizer._preprocess_batch - MÉTHODE PRÉTRAITEMENT BATCH)
   - Lignes 13223-13315 dans optuna_optimizer.py (93 lignes)
   - FONCTION : Prétraite batch lignes historical_data.txt avec séquences 0/1 pour features LGBM/LSTM et cibles
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * batch_lines - Liste lignes à prétraiter
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PARAMÈTRES SÉQUENCE :** min_sequence_length=30, lstm_sequence_length=20, lgbm_sequence_length=20 depuis config
     * **NETTOYAGE LIGNE :** strip() et split(',') pour éléments 0/1 avec validation longueur minimale
     * **CONVERSION NUMÉRIQUE :** Filtrage éléments '0'/'1' uniquement avec ignore éléments non reconnus
     * **GÉNÉRATION EXEMPLES :** Boucle range(min_length-1, len(sequence)) pour échantillons glissants
     * **SÉQUENCES LSTM :** numeric_sequence[i-lstm_length+1:i+1] avec padding zéros si trop court
     * **SÉQUENCES LGBM :** numeric_sequence[i-lgbm_length+1:i+1] avec padding zéros si trop court
     * **CIBLES :** target = numeric_sequence[i+1] pour prédiction élément suivant
     * **GESTION ERREURS :** try/except par ligne avec continue si erreur prétraitement
     * **VALIDATION BATCH :** Vérification données valides avec fallback tableaux vides reshape()
   - RETOUR : Tuple[np.ndarray, np.ndarray, np.ndarray] - X_lgbm, X_lstm, y arrays
   - UTILITÉ : Prétraitement robuste données séquentielles avec génération features temporelles pour modèles hybrides


30. _preprocess_data_once.txt (OptunaOptimizer._preprocess_data_once - MÉTHODE PRÉTRAITEMENT DONNÉES UNIQUE)
   - Lignes 6443-6628 dans optuna_optimizer.py (186 lignes)
   - FONCTION : Prétraite données une seule fois et crée sous-ensembles adaptatifs pour chaque phase avec LHS et cache intelligent
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * force_reload (bool, défaut=False) - Force rechargement même si données en cache
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CACHE MULTI-NIVEAUX :** Vérifie cache avancé puis cache simple avant rechargement
     * **CHARGEMENT OPTIMISÉ :** Utilise load_and_preprocess_data() avec gestion erreurs robuste
     * **SOUS-ENSEMBLES ADAPTATIFS :** Tailles variables selon total_sequences (>10K→5%/15%/30%, >5K→10%/25%/50%, <5K→15%/40%/60%)
     * **ÉCHANTILLONNAGE INTELLIGENT :** Phase 0 LHS, Phase 1 stratifié, Phase 2 hybride (50% LHS + 50% stratifié)
     * **PHASE 0 LHS :** Latin Hypercube Sampling pour exploration uniforme espace données
     * **PHASE 1 STRATIFIÉE :** Échantillonnage stratifié pour représentation équilibrée
     * **PHASE 2 HYBRIDE :** Combine LHS et stratifié avec gestion doublons et complétion aléatoire
     * **PHASES COMPLÈTES :** Phase 3 et Markov utilisent 100% données (phase3_indices = markov_indices = all)
     * **OPTIMISATION MÉMOIRE :** _optimize_memory_for_full_dataset() pour usage 100% données
     * **CACHE AVANCÉ :** Stockage preprocessed_data et phase_data avec timestamps
     * **REPRODUCTIBILITÉ :** Graines fixes (random.seed(42), np.random.seed(42))
     * **MÉTRIQUES DÉTAILLÉES :** Logging pourcentages, formes données, temps prétraitement
     * **COLLECTE GARBAGE :** gc.collect() forcé pour libération mémoire
   - RETOUR : dict - Données prétraitées avec all_sequences, indices phases, métadonnées et formes
   - UTILITÉ : Prétraitement unique optimisé avec sous-ensembles adaptatifs et cache intelligent multi-niveaux


31. _preprocess_line.txt (OptunaOptimizer._preprocess_line - MÉTHODE PRÉTRAITEMENT LIGNE)
   - Lignes 5066-5140 dans optuna_optimizer.py (75 lignes)
   - FONCTION : Prétraite ligne données pour extraction features LGBM/LSTM avec fallbacks robustes
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * line - Ligne données à prétraiter (format CSV)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION LIGNE :** Vérification line non vide et strip() pour nettoyage
     * **PARSING CSV :** split(',') avec parts[:-1] pour séquence et parts[-1] pour résultat
     * **VALIDATION RÉSULTAT :** result in ['player', 'banker'] pour cibles valides
     * **INSTANCE HBP :** get_hbp_instance() pour accès méthodes HybridBaccaratPredictor
     * **FEATURES LGBM :** hbp_instance._create_lgbm_features(sequence) avec fallback liste vide
     * **FEATURES LSTM :** hbp_instance.create_lstm_sequence_features(sequence) avec gestion None
     * **FALLBACK LSTM :** np.zeros((lstm_sequence_length, lstm_input_size)) si instance indisponible
     * **CONFIGURATION :** lstm_sequence_length=20, lstm_input_size=15 depuis config ou défauts
     * **CONVERSION CIBLE :** y = 0 si 'player' else 1 pour format numérique
   - RETOUR : Tuple[array, array, int] - X_lgbm, X_lstm, y ou (None, None, None) si erreur
   - UTILITÉ : Prétraitement robuste lignes avec fallbacks pour validation empirique et évaluation


32. _process_line.txt (OptunaOptimizer._process_line - TRAITEMENT LIGNE PRIVÉ)
   - Lignes 5053-5064 dans optuna_optimizer.py (12 lignes)
   - FONCTION : Wrapper traitement ligne fichier historical_data.txt vers méthode statique
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * line_info - Tuple (line_idx, line) avec indice et contenu ligne
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉLÉGATION STATIQUE :** return self._process_line_static(line_info)
     * **WRAPPER INSTANCE :** Interface instance vers méthode statique
   - RETOUR : Dict or None - Données séquence ou None si ligne invalide
   - UTILITÉ : Interface instance pour traitement ligne avec délégation statique


33. _process_line_static.txt (OptunaOptimizer._process_line_static - MÉTHODE TRAITEMENT LIGNE STATIQUE)
   - Lignes 4990-5051 dans optuna_optimizer.py (62 lignes)
   - FONCTION : Version statique traitement ligne pour éviter problèmes sérialisation parallélisme
   - PARAMÈTRES :
     * line_info - Tuple (line_idx, line) avec indice et contenu ligne
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTHODE STATIQUE :** Indépendante instance, pas dépendances externes pour parallélisme
     * **FILTRAGE LIGNES :** Ignore lignes vides et commentaires commençant par #
     * **VALIDATION LONGUEUR :** Vérifie len(full_sequence) >= 60 pour séquences complètes
     * **EXTRACTION MANCHES :** Sépare manches 1-30 (training) et 31-60 (target)
     * **CONVERSION FORMAT :** Transforme P/B vers 'PLAYER'/'BANKER' puis 'player'/'banker'
     * **STRUCTURE DONNÉES :** Crée sequence_data avec round_num, outcome, line_idx
     * **SÉQUENCES STANDARDISÉES :** Génère training_sequence_std et target_sequence_std
     * **RETOUR STRUCTURÉ :** Dictionnaire avec training_sequence, target_sequence, sequence_data
     * **GESTION ERREURS :** Try/catch avec logging erreurs par ligne
     * **LOGGING INDÉPENDANT :** Utilise logging.getLogger(__name__) pour éviter dépendances
   - RETOUR : Dict - Données séquence formatées ou None si ligne invalide
   - UTILITÉ : Traitement parallèle lignes sans problèmes sérialisation multiprocessing


34. _profile_function.txt (OptunaOptimizer._profile_function - PROFILAGE FONCTION PRIVÉE)
   - Lignes 6630-6677 dans optuna_optimizer.py (48 lignes)
   - FONCTION : Profile fonction pour identification goulots étranglement avec rapport détaillé
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * func_name - Nom fonction à profiler
     * func - Fonction à profiler
     * *args/**kwargs - Arguments à passer à fonction
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION CONFIG :** if enable_profiling sinon exécution directe
     * **MESURE TEMPS :** start_time et elapsed_time pour durée totale
     * **PROFILER CPROFILE :** cProfile.Profile() pour analyse détaillée
     * **EXÉCUTION PROFILÉE :** profiler.enable() → func() → profiler.disable()
     * **RAPPORT STATS :** pstats.Stats().sort_stats('cumulative') top 20 fonctions
     * **LOGGING DÉTAILLÉ :** Temps + rapport complet via logger.warning()
   - RETOUR : Any - Résultat fonction profilée
   - UTILITÉ : Profilage performance avec analyse détaillée et rapports complets


35. _reload_search_space_from_config.txt (DynamicRangeAdjuster._reload_search_space_from_config - MÉTHODE RECHARGEMENT ESPACE RECHERCHE)
   - Lignes 895-953 dans optuna_optimizer.py (59 lignes)
   - FONCTION : Recharge espace recherche depuis config.py comme solution secours avec reload module et mise à jour sampler
   - PARAMÈTRES :
     * self - Instance de la classe DynamicRangeAdjuster
     * study - Étude Optuna à mettre à jour
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RELOAD MODULE :** importlib.reload(sys.modules['config']) pour prise compte modifications
     * **VÉRIFICATION ATTRIBUT :** hasattr(config, 'optuna_search_space') pour validation existence
     * **CRÉATION ESPACE :** new_search_space avec FloatDistribution/IntDistribution/CategoricalDistribution
     * **TYPES SUPPORTÉS :** float (low, high), int (low, high), categorical (choices)
     * **MISE À JOUR SAMPLER :** study.sampler._search_space = new_search_space si attribut existe
     * **GESTION ERREURS :** try/except avec traceback.format_exc() pour débogage
     * **FALLBACK GRACIEUX :** return False si échec avec messages informatifs
   - RETOUR : bool - True si rechargement réussi, False sinon
   - UTILITÉ : Solution secours robuste rechargement espace recherche avec gestion erreurs complète


36. _save_viable_trial.txt (OptunaOptimizer._save_viable_trial - MÉTHODE SAUVEGARDE ESSAI VIABLE)
   - Lignes 4839-4987 dans optuna_optimizer.py (149 lignes)
   - FONCTION : Sauvegarde essai viable avec sérialisation complète et sauvegarde modèles pour récupération erreur
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * trial_info - Informations essai viable à sauvegarder
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DOSSIER SAUVEGARDE :** viable_trials/ avec viable_trial_{trial_number}.json
     * **SÉRIALISATION AVANCÉE :** Conversion objets non-sérialisables avec gestion spéciale paramètres booléens
     * **PARAMÈTRES BOOLÉENS :** Traitement use_*, lstm_use_*, lgbm_use_ avec conversion 'true'/'false' strings
     * **TYPES SUPPORTÉS :** int/float/bool/str/None, np.ndarray→tolist(), list/tuple, objets avec __dict__
     * **DICTIONNAIRES IMBRIQUÉS :** Traitement récursif avec même logique sérialisation
     * **PARAMÈTRES CLÉS :** Affichage min_confidence_for_recommendation, wait_ratio_min_threshold, error_pattern_threshold, transition_uncertainty_threshold, wait_optimizer_confidence_threshold
     * **SAUVEGARDE MODÈLES :** Si final=True ou wave=3, sauvegarde modèles via get_hbp_instance()
     * **MODÈLES ENTRAÎNÉS :** models_{trial_number}/ avec optimized_models_score_{score:.4f}.joblib
     * **INTÉGRATION HBP :** _perform_save() de HybridBaccaratPredictor avec mise à jour models_path
   - RETOUR : None (sauvegarde fichiers)
   - UTILITÉ : Sauvegarde robuste essais viables avec modèles pour récupération automatique et continuité optimisation


37. _simple_lhs.txt (OptunaOptimizer._simple_lhs - MÉTHODE LATIN HYPERCUBE SAMPLING SIMPLE)
   - Lignes 5445-5518 dans optuna_optimizer.py (74 lignes)
   - FONCTION : Implémentation simplifiée Latin Hypercube Sampling avec calculs distances robustes
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * features - Tableau numpy caractéristiques (n_samples, n_features)
     * n_samples - Nombre d'échantillons à sélectionner
   - FONCTIONNEMENT DÉTAILLÉ :
     * **NORMALISATION FEATURES :** Utilise _normalize_features() pour éviter problèmes numériques
     * **GÉNÉRATION POINTS LHS :** Appelle _generate_lhs_points() pour points distribués uniformément
     * **CALCUL DISTANCES ROBUSTE :** 3 méthodes fallback (linalg.norm, manuel, Manhattan)
     * **PROTECTION DÉPASSEMENTS :** Utilise np.clip(-1e6, 1e6) pour éviter overflow
     * **DISTANCE EUCLIDIENNE :** Méthode principale avec np.linalg.norm pour stabilité
     * **DISTANCE MANHATTAN :** Fallback avec somme valeurs absolues si euclidienne échoue
     * **SÉLECTION PLUS PROCHE :** Utilise np.argmin(distances) pour trouver séquence optimale
     * **GESTION INFINIS :** Détecte distances infinies et choisit indice aléatoire
     * **ÉVITEMENT DOUBLONS :** Met features_copy[closest_idx] = max_value pour éviter resélection
     * **PROTECTION TYPE :** Utilise np.finfo(dtype).max pour valeur maximale sécurisée
     * **INDICES RESTANTS :** Maintient liste indices non sélectionnés pour choix aléatoire
     * **GESTION ERREURS :** Try/catch multiple avec logging détaillé des échecs
   - RETOUR : list - Indices des échantillons sélectionnés par LHS
   - UTILITÉ : LHS robuste pour échantillonnage optimal quand scikit-optimize indisponible


38. _stratified_sampling.txt (OptunaOptimizer._stratified_sampling - MÉTHODE ÉCHANTILLONNAGE STRATIFIÉ)
   - Lignes 5172-5403 dans optuna_optimizer.py (232 lignes)
   - FONCTION : Échantillonnage stratifié des séquences basé sur ratio P/B avec support LHS et méthodes multiples
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * sequences - Liste des séquences à échantillonner
     * max_samples (int) - Nombre maximum d'échantillons à retourner
     * method (str, défaut='lhs') - Méthode d'échantillonnage ('stratified', 'lhs', 'random')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION TAILLE :** Retourne tous indices si sequences <= max_samples
     * **MÉTHODE LHS :** Utilise Latin Hypercube Sampling avec scikit-optimize pour distribution optimale
     * **EXTRACTION FEATURES :** Calcule p_ratio, b_ratio, pb_ratio pour chaque séquence
     * **NORMALISATION :** Normalise features [0,1] pour calculs de distance cohérents
     * **POINTS LHS :** Génère points avec Lhs(criterion="maximin") pour espacement optimal
     * **DISTANCE EUCLIDIENNE :** Trouve séquences les plus proches des points LHS avec gestion erreurs
     * **FALLBACK LHS :** Utilise _simple_lhs() si scikit-optimize indisponible
     * **ÉCHANTILLONNAGE STRATIFIÉ :** Groupe par ratio P/B arrondi à 0.1 près
     * **DISTRIBUTION ÉQUITABLE :** Calcule samples_per_group pour représentation équilibrée
     * **COMPLÉTION ÉCHANTILLONS :** Ajoute échantillons supplémentaires si nécessaire
     * **ALERTE DIVERSITÉ :** Détecte et signale manque de diversité (<2 groupes)
     * **MÉTHODE RANDOM :** Fallback échantillonnage aléatoire simple
     * **GESTION ERREURS :** Try/catch robuste avec fallbacks multiples
     * **LOGGING DÉTAILLÉ :** Journalise méthode utilisée et nombre séquences sélectionnées
   - RETOUR : numpy.ndarray - Indices des séquences échantillonnées
   - UTILITÉ : Échantillonnage intelligent pour représentation équilibrée des données d'entraînement


39. _transform_lhs_points.txt (OptunaOptimizer._transform_lhs_points - MÉTHODE TRANSFORMATION POINTS LHS)
   - Lignes 5720-5810 dans optuna_optimizer.py (91 lignes)
   - FONCTION : Transforme points LHS pour améliorer distribution avec techniques avancées d'optimisation
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * points - Tableau numpy points LHS (n_samples, n_dims)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION TAILLE :** Retourne points originaux si n_samples < 5 ou n_dims < 2
     * **MATRICE DISTANCES :** Calcule distances euclidiennes entre tous points avec double boucle
     * **CALCUL ROBUSTE :** Utilise np.linalg.norm avec fallback manuel et np.clip protection
     * **DÉTECTION PROXIMITÉ :** Identifie points trop proches avec seuil 0.1/sqrt(n_dims)
     * **PERTURBATION POINTS :** Ajoute perturbation aléatoire ±0.05 aux points trop proches
     * **OPTIMISATION COUVERTURE :** Vérifie présence points près bords avec edge_margin=0.1
     * **AJOUT COINS :** Remplace points par coins si pas assez de couverture bords
     * **STRATÉGIE COINS :** 4 patterns différents (bas, haut, mélange alterné) pour couverture optimale
     * **SÉLECTION ALÉATOIRE :** Choisit min(4, n_samples//10) points à remplacer aléatoirement
     * **NORMALISATION FINALE :** Applique np.clip(points, 0, 1) pour garantir bornes [0,1]
     * **LOGGING INFORMATIF :** Journalise optimisations appliquées (proximité, couverture)
   - RETOUR : numpy.ndarray - Points LHS transformés avec distribution optimisée
   - UTILITÉ : Amélioration qualité échantillonnage LHS avec couverture espace optimale


40. _update_config_ranges.txt (DynamicRangeAdjuster._update_config_ranges - MÉTHODE MISE À JOUR PLAGES CONFIG - 3ème MÉTHODE LA PLUS LONGUE)
   - Lignes 483-652 dans optuna_optimizer.py (170 lignes)
   - FONCTION : Met à jour plages dans config.py et espace recherche mémoire avec patterns regex multiples et reload module
   - PARAMÈTRES :
     * self - Instance de la classe DynamicRangeAdjuster
     * new_ranges - Dict nouvelles plages {param_name: (param_type, new_low, new_high)}
   - FONCTIONNEMENT DÉTAILLÉ :
     * **LECTURE CONFIG :** open(config_path, 'r', encoding='utf-8') avec sauvegarde original_content
     * **PATTERNS REGEX :** 4 formats supportés ('param': ('type', low, high)), ('param': ('type', low, high, options)), ('param': ('categorical', [vals])), self.param = value
     * **MISE À JOUR FICHIER :** re.findall() et replace() pour chaque pattern avec commentaires automatiques
     * **PARAMÈTRES CATÉGORIELS :** Traitement spécial new_categories_str avec join() et quotes
     * **ÉCRITURE FICHIER :** write(config_content) si modifications détectées
     * **RELOAD MODULE :** importlib.reload(sys.modules['config']) pour prise compte modifications
     * **MISE À JOUR MÉMOIRE :** config.optuna_search_space avec préservation options existantes
     * **GESTION ERREURS :** try/except avec traceback.format_exc() et fallback gracieux
     * **SUIVI AJUSTEMENTS :** unadjusted_params et adjusted_ranges pour monitoring
   - RETOUR : Tuple[bool, List[str]] - Succès mise à jour et liste paramètres non ajustés
   - UTILITÉ : Mise à jour dynamique configuration avec synchronisation fichier/mémoire et gestion robuste erreurs


41. _update_group_search_space.txt (DynamicRangeAdjuster._update_group_search_space - MISE À JOUR ESPACE GROUPE)
   - Lignes 831-861 dans optuna_optimizer.py (31 lignes)
   - FONCTION : Met à jour espace recherche GroupSearchSpace avec nouvelles plages par groupe
   - PARAMÈTRES :
     * self - Instance de la classe DynamicRangeAdjuster
     * search_space - Espace recherche GroupSearchSpace à mettre à jour
     * new_ranges - Nouvelles plages (param_type, low, high) à appliquer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION STRUCTURE :** hasattr(search_space, 'groups') pour validation
     * **PARCOURS GROUPES :** Itération sur search_space.groups.items()
     * **VALIDATION CALLABLE :** hasattr(group_space, 'get') et callable() pour sécurité
     * **MISE À JOUR TYPES :** FloatDistribution, IntDistribution, CategoricalDistribution
     * **GESTION ERREURS :** try/except avec traceback.format_exc() pour débogage
   - RETOUR : None (mise à jour interne)
   - UTILITÉ : Mise à jour spécialisée espaces recherche groupés avec gestion erreurs


42. _update_intersection_search_space.txt (DynamicRangeAdjuster._update_intersection_search_space - MISE À JOUR ESPACE INTERSECTION)
   - Lignes 798-829 dans optuna_optimizer.py (32 lignes)
   - FONCTION : Met à jour espace recherche IntersectionSearchSpace avec nouvelles plages par sous-espace
   - PARAMÈTRES :
     * self - Instance de la classe DynamicRangeAdjuster
     * search_space - Espace recherche IntersectionSearchSpace à mettre à jour
     * new_ranges - Nouvelles plages (param_type, low, high) à appliquer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION STRUCTURE :** hasattr(search_space, '_study_id_to_search_space')
     * **PARCOURS SOUS-ESPACES :** Itération sur _study_id_to_search_space.items()
     * **VALIDATION CALLABLE :** hasattr(sub_space, 'get') et callable() pour sécurité
     * **MISE À JOUR TYPES :** FloatDistribution, IntDistribution, CategoricalDistribution
     * **GESTION ERREURS :** try/except avec traceback.format_exc() pour débogage
   - RETOUR : None (mise à jour interne)
   - UTILITÉ : Mise à jour spécialisée espaces recherche intersection avec gestion erreurs


43. _update_relative_search_space.txt (DynamicRangeAdjuster._update_relative_search_space - MISE À JOUR ESPACE RELATIF)
   - Lignes 863-893 dans optuna_optimizer.py (31 lignes)
   - FONCTION : Met à jour espace recherche RelativeSearchSpace avec nouvelles plages sur espace base
   - PARAMÈTRES :
     * self - Instance de la classe DynamicRangeAdjuster
     * search_space - Espace recherche RelativeSearchSpace à mettre à jour
     * new_ranges - Nouvelles plages (param_type, low, high) à appliquer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION STRUCTURE :** hasattr(search_space, 'base_search_space')
     * **ACCÈS ESPACE BASE :** base_space = search_space.base_search_space
     * **VALIDATION CALLABLE :** hasattr(base_space, 'get') et callable() pour sécurité
     * **MISE À JOUR TYPES :** FloatDistribution, IntDistribution, CategoricalDistribution
     * **GESTION ERREURS :** try/except avec traceback.format_exc() pour débogage
   - RETOUR : None (mise à jour interne)
   - UTILITÉ : Mise à jour spécialisée espaces recherche relatifs avec gestion erreurs


44. _validate_params_in_range.txt (OptunaOptimizer._validate_params_in_range - VALIDATION PARAMÈTRES PLAGE)
   - Lignes 4623-4676 dans optuna_optimizer.py (54 lignes)
   - FONCTION : Valide et ajuste paramètres selon plages définies avec correction automatique
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * params - Dictionnaire paramètres à valider
     * search_space (optionnel) - Espace recherche ou config.optuna_search_space
   - FONCTIONNEMENT DÉTAILLÉ :
     * **ESPACE DÉFAUT :** getattr(self.config, 'optuna_search_space', {}) si None
     * **COPIE SÉCURISÉE :** validated_params = params.copy() pour non-modification
     * **VALIDATION INT/FLOAT :** Ajustement min_val/max_val avec logging warnings
     * **VALIDATION CATEGORICAL :** Vérification valid_values avec fallback premier élément
     * **LOGGING AJUSTEMENTS :** Messages détaillés pour chaque correction appliquée
   - RETOUR : dict - Paramètres validés et ajustés
   - UTILITÉ : Validation robuste paramètres avec correction automatique et logging


45. adaptive_data_generator.txt (OptunaOptimizer.adaptive_data_generator - GÉNÉRATEUR DONNÉES ADAPTATIF)
   - Lignes 11399-11434 dans optuna_optimizer.py (36 lignes)
   - FONCTION : Générateur adaptatif traitement données par lots avec ajustement automatique taille selon mémoire disponible
   - PARAMÈTRES :
     * X - Données features
     * y - Données cibles
     * initial_batch_size (int, défaut=1000) - Taille batch initiale
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉLANGE INDICES :** np.random.shuffle(indices) pour randomisation
     * **DÉTECTION MÉMOIRE :** psutil.virtual_memory() pour available_gb
     * **AJUSTEMENT ADAPTATIF :** <2GB→100, <4GB→500, <8GB→1000, ≥8GB→2000
     * **FALLBACK SÉCURISÉ :** initial_batch_size si psutil indisponible
     * **GÉNÉRATION BATCH :** yield X[batch_indices], y[batch_indices] par range(0, n_samples, batch_size)
   - RETOUR : Generator - Générateur (X_batch, y_batch) adaptatif
   - UTILITÉ : Traitement mémoire-efficace données avec adaptation automatique ressources


46. adjust_ranges_for_study.txt (DynamicRangeAdjuster.adjust_ranges_for_study - MÉTHODE AJUSTEMENT PLAGES ÉTUDE)
   - Lignes 266-406 dans optuna_optimizer.py (141 lignes)
   - FONCTION : Ajuste plages étude selon meilleurs essais avec mise à jour config.py et espace recherche sampler
   - PARAMÈTRES :
     * self - Instance de la classe DynamicRangeAdjuster
     * study - Étude Optuna pour ajustement plages
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INTERVALLE AJUSTEMENT :** Vérification adjustment_interval pour éviter ajustements trop fréquents
     * **MEILLEURS ESSAIS :** study.get_trials(COMPLETE) triés par value selon direction MAXIMIZE/MINIMIZE
     * **TOP 10 ESSAIS :** best_trials[:10] pour analyse paramètres hors plage
     * **IDENTIFICATION HORS PLAGE :** _identify_out_of_range_params() pour détection paramètres problématiques
     * **SAUVEGARDE ORIGINALE :** _backup_original_ranges() si original_ranges vide
     * **MISE À JOUR CONFIG :** _update_config_ranges() avec gestion unadjusted_params
     * **VERROUILLAGE ÉTUDE :** study_lock = study.study_name pour éviter conflits
     * **HISTORIQUE :** adjustment_history avec timestamp, study_name, adjustments
     * **MISE À JOUR SAMPLER :** Gestion _search_space avec FloatDistribution/IntDistribution/CategoricalDistribution
     * **TYPES ESPACE :** IntersectionSearchSpace, GroupSearchSpace, RelativeSearchSpace avec méthodes spécifiques
     * **FALLBACK CONFIG :** _reload_search_space_from_config() si sampler non supporté
   - RETOUR : Dict[str, Tuple[str, float, float]] - Plages ajustées ou dict vide
   - UTILITÉ : Ajustement dynamique plages optimisation avec synchronisation config et sampler pour amélioration continue


47. class_to_label.txt (class_to_label - FONCTION CONVERSION CLASSE VERS ÉTIQUETTE)
   - Lignes 13351-13370 dans optuna_optimizer.py (20 lignes)
   - FONCTION : Convertit indice classe en étiquette lisible avec validation
   - PARAMÈTRES : class_index - Indice classe (0 ou 1)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION INDICE :** Vérifie class_index in [0, 1]
     * **LOGGING WARNING :** Alerte si indice invalide avec message détaillé
     * **CORRECTION AUTOMATIQUE :** max(0, min(1, class_index)) pour borner
     * **CONVERSION STANDARD :** 0 → "PLAYER", 1 → "BANKER"
   - RETOUR : str - "PLAYER" ou "BANKER" selon indice
   - UTILITÉ : Conversion sécurisée indices classes vers étiquettes lisibles


48. compare_predictions_with_targets.txt (compare_predictions_with_targets - FONCTION COMPARAISON PRÉDICTIONS)
   - Lignes 13372-13397 dans optuna_optimizer.py (26 lignes)
   - FONCTION : Compare prédictions avec cibles indices 0-based avec validation
   - PARAMÈTRES : predictions - Tenseur prédictions (0 ou 1), targets - Tenseur cibles (0 ou 1)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION PRÉDICTIONS :** Vérifie min/max dans [0,1] avec logging warnings
     * **VALIDATION CIBLES :** Vérifie min/max dans [0,1] avec logging warnings
     * **COMPARAISON DIRECTE :** predictions.eq(targets) pour égalité élément par élément
   - RETOUR : torch.Tensor - Tenseur booléen prédictions correctes
   - UTILITÉ : Comparaison sécurisée prédictions/cibles avec validation indices


49. create_variations.txt (create_variations - FONCTION CRÉATION VARIATIONS)
   - Fonction création variations hyperparamètres pour exploration espace
   - UTILITÉ : Génération variations intelligentes paramètres optimisation


50. custom_get_trial_msg.txt (custom_get_trial_msg - FONCTION MESSAGE ESSAI PERSONNALISÉ)
   - Fonction formatage messages essais Optuna avec informations détaillées
   - UTILITÉ : Affichage personnalisé progression essais avec métriques


51. error_callback.txt (error_callback - fonction locale)
   - Lignes 13004-13010 dans hbp.py
   - FONCTION : Callback local d'erreur pour optimisation Optuna
   - PARAMÈTRES : error_msg (str), best_params (Dict), duration (float)
   - FONCTIONNEMENT :
     * Si UI disponible : planifie _finalize_optuna_optimization(False, best_params, duration, error_msg)
     * Sinon : log erreur avec logger_instance.error()
     * Utilise root.after(0) pour thread-safety UI
   - RETOUR : None
   - UTILITÉ : Gestion d'erreurs thread-safe pour optimisation avec finalisation appropriée


52. evaluate_fold.txt (evaluate_fold - FONCTION ÉVALUATION PLI)
   - Fonction évaluation pli validation croisée avec métriques complètes
   - UTILITÉ : Évaluation robuste performance modèles par pli


53. finalize_adjustments.txt (finalize_adjustments - FONCTION FINALISATION AJUSTEMENTS)
   - Fonction finalisation ajustements hyperparamètres après optimisation
   - UTILITÉ : Consolidation finale paramètres optimisés avec validation


54. forward.txt (StandardCrossEntropyLoss.forward - MÉTHODE CALCUL PERTE)
   - Lignes 13330-13349 dans optuna_optimizer.py (20 lignes)
   - FONCTION : Calcule perte CrossEntropy avec indices 0-based et validation
   - PARAMÈTRES : self - Instance StandardCrossEntropyLoss, inputs - Logits modèle, targets - Cibles indices 0/1
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION CIBLES :** torch.min/max(targets) pour vérifier indices [0,1]
     * **LOGGING WARNING :** Alerte si indices invalides détectés
     * **CALCUL PERTE :** self.standard_loss(inputs, targets) PyTorch standard
   - RETOUR : torch.Tensor - Valeur perte calculée
   - UTILITÉ : Fonction perte sécurisée avec validation indices pour entraînement


55. get_accuracy.txt (get_accuracy - FONCTION CALCUL PRÉCISION)
   - Lignes 13399-13415 dans optuna_optimizer.py (17 lignes)
   - FONCTION : Calcule précision en comparant prédictions avec cibles indices 0-based
   - PARAMÈTRES : predictions - Tenseur prédictions (0 ou 1), targets - Tenseur cibles (0 ou 1)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **COMPARAISON DIRECTE :** compare_predictions_with_targets(predictions, targets)
     * **COMPTAGE CORRECT :** .sum().item() pour nombre prédictions correctes
     * **TAILLE TOTALE :** targets.size(0) pour nombre total échantillons
     * **CALCUL PRÉCISION :** correct/total avec protection division par zéro
   - RETOUR : float - Précision entre 0 et 1
   - UTILITÉ : Métrique précision standard pour évaluation modèles


56. get_predictions_from_outputs.txt (get_predictions_from_outputs - FONCTION EXTRACTION PRÉDICTIONS)
   - Lignes 13419-13432 dans optuna_optimizer.py (14 lignes)
   - FONCTION : Convertit sorties modèle en prédictions indices 0-based
   - PARAMÈTRES : outputs - Sorties modèle (logits)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION MAXIMUM :** torch.max(outputs, 1) pour indices valeurs maximales
     * **RETOUR INDICES :** predicted (indices 0-based directement)
   - RETOUR : torch.Tensor - Prédictions indices 0-based (0 ou 1)
   - UTILITÉ : Conversion logits vers prédictions classes discrètes


57. get_size.txt (OptunaOptimizer.get_size - FONCTION CALCUL TAILLE RÉCURSIVE)
   - Lignes 6308-6321 dans optuna_optimizer.py (14 lignes)
   - FONCTION : Calcule taille récursive objet avec gestion références circulaires
   - PARAMÈTRES : obj - Objet à mesurer, seen - Set objets déjà vus (défaut=None)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **TAILLE BASE :** sys.getsizeof(obj) pour taille objet principal
     * **GESTION CIRCULAIRE :** seen set avec id(obj) pour éviter boucles infinies
     * **RÉCURSION DICT :** Somme get_size(k) + get_size(v) pour clés/valeurs
     * **RÉCURSION COLLECTIONS :** Somme get_size(i) pour list/tuple/set/frozenset
   - RETOUR : int - Taille totale en octets
   - UTILITÉ : Mesure mémoire précise objets complexes avec structures imbriquées


58. label_to_class.txt (label_to_class - FONCTION CONVERSION ÉTIQUETTE VERS CLASSE)
   - Lignes 13434-13445 dans optuna_optimizer.py (12 lignes)
   - FONCTION : Convertit étiquette en indice classe avec normalisation casse
   - PARAMÈTRES : label - Étiquette ('player', 'banker', 'PLAYER', 'BANKER')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **NORMALISATION CASSE :** label.lower() pour uniformité
     * **CONVERSION DIRECTE :** "player" → 0, autres → 1
   - RETOUR : int - 0 pour player, 1 pour banker
   - UTILITÉ : Conversion rapide étiquettes vers indices classes


59. load_and_preprocess_data.txt (OptunaOptimizer.load_and_preprocess_data - MÉTHODE CHARGEMENT PRÉTRAITEMENT DONNÉES)
   - Lignes 11800-11961 dans optuna_optimizer.py (162 lignes)
   - FONCTION : Charge et prétraite données d'entraînement avec taille batch adaptative limitée à 10% du fichier
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * historical_data_path - Chemin vers fichier historical_data.txt
   - FONCTIONNEMENT DÉTAILLÉ :
     * **ANALYSE TAILLE FICHIER :** Compte total_lines avec sum(1 for _ in f)
     * **LIMITATION 10% :** Calcule lines_to_load = int(total_lines * 0.10)
     * **BATCH SIZE ADAPTATIF :** Détermine load_batch_size selon taille fichier
       - Petits fichiers (<1000) : 50-200 lignes (20% des lignes)
       - Fichiers moyens (<10000) : 100-500 lignes (10% des lignes)
       - Grands fichiers (≥10000) : 200-2000 lignes (5% des lignes)
     * **ÉCHANTILLONNAGE ALÉATOIRE :** Sélectionne indices avec random.sample()
     * **TRAITEMENT PAR BATCH :** Charge et prétraite par load_batch_size
     * **PRÉTRAITEMENT BATCH :** Utilise _preprocess_batch() pour chaque lot
     * **ACCUMULATION DONNÉES :** Stocke dans all_X_lgbm, all_X_lstm, all_y
     * **PROGRESSION MONITORING :** Affiche pourcentage traitement
     * **VALIDATION DIMENSIONS :** Vérifie cohérence formes avant concaténation
     * **CONCATÉNATION FINALE :** np.concatenate() pour assembler données complètes
     * **OPTIMISATION BATCH :** Appelle _optimize_training_batch_sizes()
     * **GESTION ERREURS ROBUSTE :** Try/catch avec fallback tableaux vides
     * **LOGGING DÉTAILLÉ :** Journalise formes, distribution classes, progression
   - RETOUR : Tuple[np.ndarray, np.ndarray, np.ndarray] - X_lgbm_full, X_lstm_full, y_full
   - UTILITÉ : Chargement optimisé données avec prétraitement batch adaptatif et limitation mémoire


60. normalize_outcome.txt (OptunaOptimizer.normalize_outcome - FONCTION NORMALISATION RÉSULTATS)
   - Lignes 10230-10244 dans optuna_optimizer.py (15 lignes)
   - FONCTION : Normalise valeurs résultats en format standard BANKER/PLAYER avec variations
   - PARAMÈTRES : value - Valeur à normaliser (string)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **NETTOYAGE :** value.strip().upper() pour format standard
     * **VALIDATION DIRECTE :** Retourne si déjà "BANKER" ou "PLAYER"
     * **DÉTECTION PLAYER :** Patterns "PLAY", "P ", " P", "JOUEUR" → "PLAYER"
     * **DÉTECTION BANKER :** Patterns "BANK", "B ", " B", "BANQUIER" → "BANKER"
     * **FALLBACK SÉCURISÉ :** Valeurs invalides → "BANKER" avec logging warning
   - RETOUR : str - "BANKER" ou "PLAYER" normalisé
   - UTILITÉ : Standardisation résultats pour traitement uniforme données


61. on_epoch_end.txt (on_epoch_end - CALLBACK FIN ÉPOQUE)
   - Callback fin époque pour adaptation paramètres LSTM
   - UTILITÉ : Monitoring et adaptation fin chaque époque entraînement


62. on_optimization_complete.txt (on_optimization_complete - CALLBACK OPTIMISATION COMPLÈTE)
   - Callback fin optimisation avec finalisation et sauvegarde
   - UTILITÉ : Actions finales après optimisation complète


63. on_optimization_error.txt (on_optimization_error - CALLBACK ERREUR OPTIMISATION)
   - Callback gestion erreurs optimisation avec récupération
   - UTILITÉ : Gestion robuste erreurs avec tentatives récupération


64. on_optimization_progress.txt (on_optimization_progress - CALLBACK PROGRESSION OPTIMISATION)
   - Callback progression optimisation avec monitoring temps réel
   - UTILITÉ : Suivi progression avec métriques temps réel


65. prepare_data_for_next_phase.txt (OptunaOptimizer.prepare_data_for_next_phase - PRÉPARATION DONNÉES PHASE SUIVANTE)
   - Lignes 7362-7407 dans optuna_optimizer.py (46 lignes)
   - FONCTION : Prépare données pour phase optimisation suivante avec parallélisation et curriculum learning
   - PARAMÈTRES :
     * Fonction interne utilisant subset_indices et phase_to du contexte parent
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PARALLÉLISATION :** ThreadPoolExecutor(max_workers=3) pour préparation simultanée
     * **SOUS-TÂCHES :** prepare_lgbm_features(), prepare_lstm_features(), prepare_targets()
     * **EXTRACTION DONNÉES :** X_lgbm_full[subset_indices], X_lstm_full[subset_indices], y_full[subset_indices]
     * **SYNCHRONISATION :** concurrent.futures.wait() pour attendre toutes tâches
     * **MISE EN CACHE :** _cache_features() pour optimisation accès futurs
     * **CURRICULUM LEARNING :** Calcul difficulté moyenne si difficulty_scores disponible
     * **PHASE MARKOV :** Préparation spécifique si phase_to == 'markov'
   - RETOUR : Tuple[Shape, Shape, Shape] - Formes des données préparées
   - UTILITÉ : Préparation efficace données multi-phases avec parallélisation et apprentissage progressif


66. prepare_lgbm_features.txt (OptunaOptimizer.prepare_lgbm_features - PRÉPARATION FEATURES LGBM)
   - Lignes 7366-7367 dans optuna_optimizer.py (2 lignes)
   - FONCTION : Extrait features LightGBM pour indices spécifiés
   - PARAMÈTRES :
     * Fonction interne utilisant self.X_lgbm_full et subset_indices du contexte
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION SIMPLE :** return self.X_lgbm_full[subset_indices]
     * **INDEXATION NUMPY :** Utilise indexation avancée pour sous-ensemble
   - RETOUR : ndarray - Features LightGBM pour indices sélectionnés
   - UTILITÉ : Extraction rapide features LGBM pour sous-ensemble données


67. prepare_lstm_features.txt (OptunaOptimizer.prepare_lstm_features - PRÉPARATION FEATURES LSTM)
   - Lignes 7369-7370 dans optuna_optimizer.py (2 lignes)
   - FONCTION : Extrait features LSTM pour indices spécifiés
   - PARAMÈTRES :
     * Fonction interne utilisant self.X_lstm_full et subset_indices du contexte
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION SIMPLE :** return self.X_lstm_full[subset_indices]
     * **INDEXATION NUMPY :** Utilise indexation avancée pour sous-ensemble
   - RETOUR : ndarray - Features LSTM pour indices sélectionnés
   - UTILITÉ : Extraction rapide features LSTM pour sous-ensemble données


68. prepare_targets.txt (OptunaOptimizer.prepare_targets - PRÉPARATION CIBLES)
   - Lignes 7372-7373 dans optuna_optimizer.py (2 lignes)
   - FONCTION : Extrait cibles pour indices spécifiés
   - PARAMÈTRES :
     * Fonction interne utilisant self.y_full et subset_indices du contexte
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION SIMPLE :** return self.y_full[subset_indices]
     * **INDEXATION NUMPY :** Utilise indexation avancée pour sous-ensemble
   - RETOUR : ndarray - Cibles pour indices sélectionnés
   - UTILITÉ : Extraction rapide cibles pour sous-ensemble données


69. prepare_train_data.txt (OptunaOptimizer.prepare_train_data - PRÉPARATION DONNÉES ENTRAÎNEMENT)
   - Lignes 9446-9456 dans optuna_optimizer.py (11 lignes)
   - FONCTION : Prépare données entraînement LSTM avec conversion tenseurs PyTorch et logging classes
   - PARAMÈTRES :
     * Fonction interne utilisant X_lstm_train et y_train du contexte parent
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONVERSION FEATURES :** torch.tensor(X_lstm_train, dtype=torch.float32) pour compatibilité LSTM
     * **CONVERSION CIBLES :** torch.tensor(y_train, dtype=torch.long) pour classes 1 et 2 directes
     * **LOGGING CLASSES :** np.unique(y_train) pour vérification classes uniques
     * **LOGGING FORMES :** X_lstm_train.shape et y_train.shape pour validation dimensions
     * **SANS AJUSTEMENT :** Création directe tenseurs sans modification classes
   - RETOUR : Tuple[Tensor, Tensor] - (X_lstm_train_tensor, y_train_tensor)
   - UTILITÉ : Préparation tenseurs PyTorch entraînement avec validation et logging


70. prepare_val_data.txt (OptunaOptimizer.prepare_val_data - PRÉPARATION DONNÉES VALIDATION)
   - Lignes 9459-9469 dans optuna_optimizer.py (11 lignes)
   - FONCTION : Prépare données validation LSTM avec conversion tenseurs PyTorch et logging classes
   - PARAMÈTRES :
     * Fonction interne utilisant X_lstm_val et y_val du contexte parent
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONVERSION FEATURES :** torch.tensor(X_lstm_val, dtype=torch.float32) pour compatibilité LSTM
     * **CONVERSION CIBLES :** torch.tensor(y_val, dtype=torch.long) pour classes 1 et 2 directes
     * **LOGGING CLASSES :** np.unique(y_val) pour vérification classes uniques
     * **LOGGING FORMES :** X_lstm_val.shape et y_val.shape pour validation dimensions
     * **SANS AJUSTEMENT :** Création directe tenseurs sans modification classes
   - RETOUR : Tuple[Tensor, Tensor] - (X_lstm_val_tensor, y_val_tensor)
   - UTILITÉ : Préparation tenseurs PyTorch validation avec validation et logging


71. real_predict_model.txt (OptunaOptimizer.real_predict_model - PRÉDICTION RÉELLE MODÈLE)
   - Lignes 2686-2692 dans optuna_optimizer.py (7 lignes)
   - FONCTION : Effectue prédictions avec modèle entraîné après vérification existence
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * X - Données features pour prédiction
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION MODÈLE :** hasattr(self, 'model_') pour existence modèle entraîné
     * **EXCEPTION SÉCURITÉ :** RuntimeError si modèle non entraîné avec message explicite
     * **PRÉDICTION DIRECTE :** self.model_.predict(X) pour prédictions classes
   - RETOUR : ndarray - Prédictions classes du modèle
   - UTILITÉ : Interface sécurisée prédiction avec validation modèle entraîné


72. real_predict_proba_model.txt (OptunaOptimizer.real_predict_proba_model - PRÉDICTION PROBABILITÉS RÉELLE)
   - Lignes 2694-2704 dans optuna_optimizer.py (11 lignes)
   - FONCTION : Effectue prédictions probabilités avec modèle entraîné après double vérification
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * X - Données features pour prédiction probabilités
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION MODÈLE :** hasattr(self, 'model_') pour existence modèle entraîné
     * **VÉRIFICATION MÉTHODE :** hasattr(self.model_, 'predict_proba') pour support probabilités
     * **EXCEPTIONS SÉCURITÉ :** RuntimeError avec messages spécifiques selon problème
     * **PRÉDICTION PROBABILITÉS :** self.model_.predict_proba(X) pour probabilités classes
   - RETOUR : ndarray - Probabilités prédictions du modèle
   - UTILITÉ : Interface sécurisée prédiction probabilités avec double validation


73. record_diagnostic_alert.txt (OptimizationStatsCollector.record_diagnostic_alert - ENREGISTREMENT ALERTE DIAGNOSTIC)
   - Lignes 13697-13704 dans optuna_optimizer.py (8 lignes)
   - FONCTION : Enregistre alerte diagnostic avec incrémentation compteur spécifique type
   - PARAMÈTRES :
     * self - Instance de la classe OptimizationStatsCollector
     * alert_type (str) - Type alerte ("confiance_basse", "incertitude_elevee", etc.)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INCRÉMENTATION COMPTEUR :** self.diagnostic_alerts[alert_type] += 1
     * **DEFAULTDICT :** Utilise defaultdict(int) pour création automatique compteurs
     * **TYPES ALERTES :** Supporte types personnalisés selon besoins diagnostic
   - RETOUR : None (mise à jour interne compteurs)
   - UTILITÉ : Système tracking alertes diagnostic avec compteurs automatiques par type


74. reset.txt (OptimizationStatsCollector.reset - MÉTHODE RESET)
   - Lignes 13605-13637 dans optuna_optimizer.py (33 lignes)
   - FONCTION : Réinitialise toutes statistiques collectées avec remise à zéro complète
   - PARAMÈTRES :
     * self - Instance de la classe OptimizationStatsCollector
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STATS GÉNÉRALES :** total_predictions=0, wait_predictions=0, non_wait_predictions=0
     * **LISTES DÉTAILLÉES :** decision_thresholds=[], confidence_values=[], uncertainty_values=[]
     * **STATS PHASES :** phase_stats=defaultdict avec structure complète par phase
     * **ALERTES DIAGNOSTIC :** diagnostic_alerts=defaultdict(int) pour compteurs
     * **FLAG RAPPORT :** report_generated=False pour réinitialisation état
     * **STRUCTURE PHASE :** count, wait_count, non_wait_count + listes métriques
   - RETOUR : None (réinitialisation interne)
   - UTILITÉ : Reset complet statistiques pour nouvelle session optimisation


75. reset_to_original.txt (DynamicRangeAdjuster.reset_to_original - RESET PARAMÈTRES ORIGINAUX)
   - Lignes 955-976 dans optuna_optimizer.py (22 lignes)
   - FONCTION : Réinitialise plages hyperparamètres aux valeurs originales sauvegardées
   - PARAMÈTRES :
     * self - Instance de la classe DynamicRangeAdjuster
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION SAUVEGARDE :** if self.original_ranges pour existence valeurs originales
     * **CONSTRUCTION DICT :** reset_ranges avec (param_type, low, high) pour chaque paramètre
     * **MISE À JOUR CONFIG :** _update_config_ranges(reset_ranges) pour restauration config.py
     * **GESTION ÉCHECS :** Warning si unadjusted_params pour paramètres non réinitialisés
     * **NETTOYAGE VARIABLES :** adjusted_ranges={}, study_lock=None pour état propre
     * **FALLBACK :** Warning si aucune sauvegarde originale disponible
   - RETOUR : None (réinitialisation interne)
   - UTILITÉ : Restauration sécurisée plages originales avec gestion erreurs


76. restore_print.txt (SimplifiedTrialPrinter.restore_print - RESTAURATION AFFICHAGE)
   - Lignes 3358-3360 dans optuna_optimizer.py (3 lignes)
   - FONCTION : Méthode vide pour compatibilité interface affichage
   - PARAMÈTRES :
     * self - Instance de la classe SimplifiedTrialPrinter
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTHODE VIDE :** pass pour compatibilité sans implémentation
     * **INTERFACE STANDARD :** Respecte contrat interface sans action
   - RETOUR : None (méthode vide)
   - UTILITÉ : Compatibilité interface affichage sans implémentation spécifique


77. restore_print_1.txt (SimplifiedTrialPrinter.restore_print - RESTAURATION AFFICHAGE - DOUBLON 1)
   - Lignes 11655-11657 dans optuna_optimizer.py (3 lignes)
   - FONCTION : Méthode vide pour compatibilité interface affichage (doublon)
   - PARAMÈTRES :
     * self - Instance de la classe SimplifiedTrialPrinter
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTHODE VIDE :** pass pour compatibilité sans implémentation
     * **DOUBLON IDENTIQUE :** Même implémentation que restore_print original
   - RETOUR : None (méthode vide)
   - UTILITÉ : Doublon compatibilité interface affichage sans implémentation spécifique


78. sample_relative.txt (MetaOptimizer.sample_relative - MÉTHODE ÉCHANTILLONNAGE RELATIF ADAPTATIF)
   - Lignes 14620-14782 dans optuna_optimizer.py (163 lignes)
   - FONCTION : Échantillonne valeurs relatives espace recherche évitant régions problématiques avec échantillonnage adaptatif et historique succès
   - PARAMÈTRES :
     * self - Instance de la classe MetaOptimizer
     * study - Étude Optuna
     * trial - Essai Optuna
     * search_space - Espace de recherche
   - FONCTIONNEMENT DÉTAILLÉ :
     * **ANALYSE ESSAIS :** analyze_trial() pour essais COMPLETE non problématiques
     * **ÉCHANTILLONNAGE ADAPTATIF :** use_adaptive_sampling avec exploitation_factor pour exploration/exploitation
     * **HISTORIQUE SUCCÈS :** use_success_based_sampling si ≥3 successful_trials disponibles
     * **ESPACE RESTREINT :** restricted_search_space avec low/high/current pour distributions restreintes
     * **DISTRIBUTIONS AJUSTÉES :** UniformDistribution, IntUniformDistribution, LogUniformDistribution avec nouvelles limites
     * **ÉCHANTILLONNAGE EXPLOITATIF :** Centré autour mean±std_factor*std des valeurs réussies
     * **LARGEUR MINIMALE :** min_width = range_width * 0.1 pour éviter plages trop étroites
     * **DISTRIBUTIONS CATÉGORIELLES :** Favorise valeur most_frequent_value des essais réussis
     * **ÉVITEMENT PROBLÈMES :** _adjust_distribution() pour chaque paramètre
     * **VALEURS ACTUELLES :** current_values pour premier essai (trial.number == 0)
   - RETOUR : dict - Valeurs échantillonnées avec évitement régions problématiques
   - UTILITÉ : Échantillonnage intelligent avec adaptation historique et évitement zones problématiques pour optimisation efficace


79. save_or_show_plot.txt (OptunaOptimizer.save_or_show_plot - SAUVEGARDE/AFFICHAGE GRAPHIQUE)
   - Lignes 1796-1816 dans optuna_optimizer.py (21 lignes)
   - FONCTION : Sauvegarde ou affiche graphiques avec gestion fichiers et tracking résultats
   - PARAMÈTRES :
     * fig - Figure matplotlib à traiter
     * name - Nom du graphique pour fichier
   - FONCTIONNEMENT DÉTAILLÉ :
     * **SAUVEGARDE CONDITIONNELLE :** if output_file pour sauvegarde fichier
     * **GÉNÉRATION NOM :** f"{base}_{name}{ext}" avec extension .png par défaut
     * **SAUVEGARDE HAUTE QUALITÉ :** fig.savefig(bbox_inches='tight', dpi=300)
     * **TRACKING RÉSULTATS :** results['plots_generated'].append() pour historique
     * **AFFICHAGE CONDITIONNEL :** plt.show() si show_plot sinon plt.close(fig)
   - RETOUR : None (sauvegarde/affichage direct)
   - UTILITÉ : Gestion flexible graphiques avec sauvegarde haute qualité et tracking


80. success_callback.txt (success_callback - fonction locale)
   - Lignes 12995-13002 dans hbp.py
   - FONCTION : Callback local de succès pour optimisation Optuna
   - PARAMÈTRES : best_params (Dict), duration (float)
   - FONCTIONNEMENT :
     * Si UI disponible : planifie _finalize_optuna_optimization(True, best_params, duration, None)
     * Sinon : log succès avec durée et meilleurs paramètres
     * Utilise root.after(0) pour thread-safety UI
   - RETOUR : None
   - UTILITÉ : Gestion succès thread-safe pour optimisation avec finalisation et logging


81. train_consecutive_confidence_calculator.txt (IsolatedMetricsModule.train_consecutive_confidence_calculator - CALCULATEUR CONFIANCE CONSÉCUTIVE)
   - Lignes 130-132 dans optuna_optimizer.py (3 lignes)
   - FONCTION : Délègue calcul confiance consécutive à fonction externe ml_core.py
   - PARAMÈTRES :
     * sequence_history - Historique séquences pour calcul
     * current_position - Position actuelle dans séquence
     * config (optionnel) - Configuration pour calcul
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉLÉGATION DIRECTE :** return train_consecutive_confidence_calculator() externe
     * **ISOLATION MODULE :** Évite dépendances circulaires avec ml_core.py
   - RETOUR : Résultat fonction externe (confiance consécutive)
   - UTILITÉ : Interface isolée calcul confiance avec délégation externe


82. update.txt (MarkovDynamicAdapter.update - MÉTHODE MISE À JOUR)
   - Lignes 12824-12842 dans optuna_optimizer.py (19 lignes)
   - FONCTION : Met à jour lissage Markov selon performances avec adaptation dynamique
   - PARAMÈTRES :
     * self - Instance de la classe MarkovDynamicAdapter
     * performance - Performance actuelle à enregistrer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **HISTORIQUE :** self.performance_history.append(performance) pour tracking
     * **CALCUL TENDANCE :** np.mean(np.diff(performance_history[-3:])) sur 3 derniers
     * **ADAPTATION BAISSE :** Si recent_trend < 0, augmentation lissage *1.2 (max 0.5)
     * **ADAPTATION HAUSSE :** Si recent_trend > 0, réduction lissage *0.8 (min 0.001)
     * **LOGGING ADAPTATIF :** print() pour suivi ajustements lissage
   - RETOUR : float - current_smoothing ajusté
   - UTILITÉ : Adaptation dynamique lissage Markov selon tendances performance


83. validate_best_trial.txt (OptunaOptimizer.validate_best_trial - VALIDATION MEILLEUR ESSAI)
   - Lignes 7318-7359 dans optuna_optimizer.py (42 lignes)
   - FONCTION : Valide meilleur essai phase avec configuration temporaire et évaluation étendue
   - PARAMÈTRES :
     * Fonction interne utilisant best_trial, phase_from, phase_to, subset_indices du contexte
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONFIG TEMPORAIRE :** temp_config = self.config.clone() pour isolation
     * **APPLICATION PARAMS :** setattr(temp_config, param_name, param_value) pour chaque paramètre
     * **ACTIVATION LSTM :** force_lstm=True si phase_to == 2 ou 3
     * **ACTIVATION MARKOV :** force_markov=True si transition vers/depuis phase 'markov'
     * **ACTIVATION DOUBLE :** force_lstm + force_markov si transition Markov vers phase 2/3
     * **ÉVALUATION ÉTENDUE :** _evaluate_config() avec CV 3-folds et subset_indices
     * **LOGGING TRANSITIONS :** Messages détaillés pour chaque type activation
   - RETOUR : Résultat évaluation configuration
   - UTILITÉ : Validation robuste meilleur essai avec adaptation modèles selon phases


TOTAL : 83 méthodes analysées et documentées
- Méthodes trouvées dans descriptif principal: 83
- Méthodes manquantes: 0