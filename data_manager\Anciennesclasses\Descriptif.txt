DESCRIPTIF DÉTAILLÉ DES CLASSES - ANCIENNES CLASSES
================================================================================

Ce fichier contient la description détaillée des classes du système data_manager.py.

ÉTAT DOCUMENTATION : PREMIÈRE VAGUE FONCTIONNELLE
- **Couverture** : Classes principales documentées
- **Niveau** : Documentation fonctionnelle standardisée
- **Qualité** : 15-25 lignes par classe minimum

DOMAINE FONCTIONNEL : DÉFINITIONS DE CLASSES
- Classe BaccaratSequenceManager
- Architecture et responsabilités
- Interfaces et méthodes principales
- Gestion des séquences Baccarat

================================================================================

1. class_BaccaratSequenceManager.txt (BaccaratSequenceManager - CLASSE GESTIONNAIRE SÉQUENCES BACCARAT)
   - Lignes 15-378 dans data_manager.py (364 lignes)
   - FONCTION : Classe gestionnaire de séquences Baccarat modifié avec fenêtre adaptative pour modèles LGBM et LSTM
   - RESPONSABILITÉS :
     * **GESTION SÉQUENCES** : Traitement séquences P/B avec fenêtre adaptative selon commentaire "Utilise toute la séquence disponible jusqu'à i-1"
     * **FILTRAGE DONNÉES** : Application `min_target_hand_index` et garantie manches 31-60 avec `target_round_min = 31`, `target_round_max = 60`
     * **GÉNÉRATION FEATURES** : Interface avec `self.hybrid_feature_creator(input_sequence)` retournant `(features_lgbm, features_lstm_np)`
     * **VALIDATION ROBUSTE** : Contrôles `isinstance()`, `len()`, `shape` et cohérence données avec logging détaillé
     * **AGRÉGATION MULTI-SABOTS** : Traitement `list_of_pb_sequences` avec `current_global_offset += len(shoe_seq)`
     * **ÉVALUATION PERFORMANCE** : Métriques `log_loss()` et `accuracy_score()` de scikit-learn avec gestion NaN
     * **LOGGING DÉTAILLÉ** : `self.logger.info()`, `self.logger.warning()`, `self.logger.error()` pour traçabilité
   - MÉTHODES PRINCIPALES :
     * __init__(sequence_length, min_target_hand_index, hybrid_feature_creator, ...) - Validation `isinstance()` et `ValueError` si invalide
     * _generate_filtered_data_for_shoe(shoe_pb_sequence, game_index_offset) - Boucle `for i in range(1, game_len):` avec filtrage
     * prepare_data_for_model(list_of_pb_sequences) - Conversion `np.array(dtype=np.float64)` et `np.stack().astype(np.float32)`
     * evaluate_performance(y_true, y_pred_proba) - Calculs `log_loss(eps=1e-15)` et `accuracy_score()` avec gestion erreurs
   - ARCHITECTURE :
     * **FENÊTRE ADAPTATIVE** : `input_sequence = shoe_pb_sequence[:i]` utilise toute séquence disponible
     * **SYSTÈME ZERO-BASED** : `label = 1 if actual_outcome == 'banker' else 0` selon standard PyTorch
     * **ROBUSTESSE** : `try/except` avec `exc_info=True` et retours `None` ou `np.nan` si erreurs
     * **PERFORMANCE** : `np.array(dtype=np.float64)` pour LGBM, `np.float32` pour LSTM, logging conditionnel
     * **FLEXIBILITÉ** : Support `Callable[[List[str]], Tuple[Optional[List[float]], Optional[np.ndarray]]]` pour générateurs
   - UTILITÉ : Classe centrale pour gestion séquences Baccarat ML. Essentielle pour préparation données modèles. Critique pour garantie qualité et robustesse données d'entraînement.