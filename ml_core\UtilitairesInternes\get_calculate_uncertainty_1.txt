# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1321 à 1328
# Type: Méthode
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

def get_calculate_uncertainty():
    """
    Importe la méthode calculate_uncertainty depuis hbp.py de manière conditionnelle.

    Returns:
        function: La méthode calculate_uncertainty
    """
    return ModelProvider.get_calculate_uncertainty()