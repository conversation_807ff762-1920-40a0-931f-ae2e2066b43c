# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 368 à 443
# Type: Méthode de la classe MemoryManager

    def optimize_pytorch_memory():
        """
        Configure PyTorch pour utiliser la mémoire disponible de manière optimale.
        Cette fonction doit être appelée au début du programme.
        """
        logger.info("Optimisation de la mémoire PyTorch...")

        # 0. Désactiver la compilation JIT (Just-In-Time) pour éviter l'erreur de compilateur
        os.environ['PYTORCH_JIT'] = '0'
        os.environ['TORCH_COMPILE_DISABLE'] = '1'

        # Désactiver les optimisations qui nécessitent un compilateur C++
        if hasattr(torch._C, '_jit_set_profiling_executor'):
            torch._C._jit_set_profiling_executor(False)
        if hasattr(torch._C, '_jit_set_profiling_mode'):
            torch._C._jit_set_profiling_mode(False)

        # 1. Activer la libération de mémoire inutilisée
        if hasattr(torch.cuda, 'empty_cache'):
            torch.cuda.empty_cache()

        # 2. Désactiver le garbage collector pendant l'entraînement pour éviter les pauses
        gc.disable()

        # 3. Configurer PyTorch pour utiliser la mémoire de manière plus agressive
        # Cette variable d'environnement permet à PyTorch d'allouer plus de mémoire
        os.environ['PYTORCH_NO_CUDA_MEMORY_CACHING'] = '1'

        # 4. Configurer PyTorch pour utiliser le mode de mémoire partagée
        # Cela permet une meilleure utilisation de la mémoire entre les processus
        torch.multiprocessing.set_sharing_strategy('file_system')

        # 5. Configurer PyTorch pour utiliser la mémoire de manière plus efficace
        # Cela permet d'éviter les erreurs de mémoire en utilisant un algorithme plus efficace
        if hasattr(torch, 'backends') and hasattr(torch.backends, 'cudnn'):
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False

        # 6. Configurer PyTorch pour utiliser la mémoire de manière plus agressive
        # Cela permet d'éviter les erreurs de mémoire en utilisant un algorithme plus agressif
        if hasattr(torch, 'set_flush_denormal'):
            torch.set_flush_denormal(True)

        # 7. Configurer PyTorch pour utiliser la mémoire de manière plus efficace
        # Cela permet d'éviter les erreurs de mémoire en utilisant un algorithme plus efficace
        if hasattr(torch, 'set_num_threads'):
            # Utiliser 80% des cœurs disponibles
            num_cores = multiprocessing.cpu_count()
            torch.set_num_threads(max(1, int(num_cores * 0.8)))

        # 8. Configurer PyTorch pour utiliser la mémoire de manière plus efficace
        # Cela permet d'éviter les erreurs de mémoire en utilisant un algorithme plus efficace
        if hasattr(torch, 'set_num_interop_threads'):
            try:
                # Vérifier si nous pouvons définir le nombre de threads d'interopérabilité
                # Cette opération doit être effectuée avant toute opération parallèle
                # Utiliser 80% des cœurs disponibles
                num_cores = multiprocessing.cpu_count()
                torch.set_num_interop_threads(max(1, int(num_cores * 0.8)))
            except RuntimeError as e:
                # Ignorer l'erreur si les threads d'interopérabilité ont déjà été définis
                # ou si un travail parallèle a déjà commencé
                # C'est un avertissement normal et attendu si PyTorch a déjà commencé des opérations parallèles
                logger.debug(f"Impossible de définir le nombre de threads d'interopérabilité: {e}")
                # Ne pas lever d'exception, continuer l'exécution

        # Afficher le nombre de threads configurés
        logger.info(f"Configuration PyTorch: num_threads={torch.get_num_threads()}")

        # Afficher un message pour indiquer que l'optimisation est terminée
        logger.info("Optimisation de la mémoire PyTorch terminée.")

        # Ajouter un message pour expliquer que le message d'erreur sur les threads d'interopérabilité est normal
        logger.debug("Note: Un message 'Impossible de définir le nombre de threads d'interopérabilité' est normal si PyTorch a déjà commencé des opérations parallèles.")

        return True