# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1106 à 1115
# Type: Méthode de la classe TrainOptimizeInterface

    def register_data_source(self, name: str, data_source: Any) -> None:
        """
        Enregistre une source de données dans l'interface.

        Args:
            name: Nom unique de la source de données
            data_source: Source de données à enregistrer
        """
        self.data_sources[name] = data_source
        logger.debug(f"Source de données '{name}' enregistrée dans l'interface")