# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 154 à 192
# Type: Méthode de la classe LoggingManager

    def configure_root_logger(self, level: int = logging.INFO, console: bool = True, file: bool = True) -> None:
        """
        Configure le logger racine.

        Args:
            level: Niveau de journalisation
            console: Si True, ajoute un handler pour la console
            file: Si True, ajoute un handler pour un fichier
        """
        # Configurer le logger racine
        root_logger = logging.getLogger()
        root_logger.setLevel(level)

        # Supprimer les handlers existants
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # Créer un formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        self._formatters['default'] = formatter

        # Ajouter un handler pour la console
        if console:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
            self._handlers['console'] = console_handler

        # Ajouter un handler pour un fichier
        if file:
            log_file = os.path.join(self._log_directory, f'app_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
            self._handlers['file'] = file_handler

        self._loggers['root'] = root_logger

        root_logger.info(f"Logger racine configuré avec niveau {logging.getLevelName(level)}")