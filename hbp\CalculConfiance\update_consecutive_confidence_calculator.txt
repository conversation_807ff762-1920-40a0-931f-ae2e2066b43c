# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 8491 à 8587
# Type: Méthode de la classe HybridBaccaratPredictor

    def update_consecutive_confidence_calculator(self, round_num: int, recommendation: str, actual_outcome: str) -> None:
        """
        Met à jour le calculateur de confiance consécutive pour les recommandations NON-WAIT.
        Cette méthode est utilisée pour suivre les recommandations NON-WAIT valides consécutives,
        particulièrement pour les manches 31-61.

        Args:
            round_num (int): Numéro de la manche actuelle
            recommendation (str): Recommandation faite ('player', 'banker', 'wait')
            actual_outcome (str): <PERSON><PERSON><PERSON><PERSON> réel ('player', 'banker')
        """
        # Récupérer les paramètres de configuration
        target_round_min = getattr(self.config, 'target_round_min', 31)
        target_round_max = getattr(self.config, 'target_round_max', 61)

        # Vérifier si nous sommes dans la plage de manches cibles
        is_target_round = target_round_min <= round_num <= target_round_max

        # Initialiser l'attribut de classe si nécessaire
        if not hasattr(self, 'consecutive_nonwait_valid'):
            self.consecutive_nonwait_valid = 0
            self.max_consecutive_nonwait_valid = 0
            self.total_nonwait_valid = 0
            self.total_nonwait = 0
            self.total_wait_valid = 0
            self.total_wait = 0

        # Mettre à jour le calculateur de confiance consécutive si disponible
        if hasattr(self, 'consecutive_confidence_calculator'):
            try:
                # Mettre à jour les données récentes avec la nouvelle recommandation et son résultat
                self.consecutive_confidence_calculator.update_recent_data(recommendation, actual_outcome)
                logger.debug(f"Manche {round_num}: Données récentes mises à jour dans le calculateur de confiance consécutive")
            except Exception as e_update:
                logger.error(f"Erreur lors de la mise à jour des données récentes: {e_update}")

        # Traiter uniquement si nous sommes dans la plage cible
        if is_target_round:
            if recommendation != 'wait':  # Recommandation NON-WAIT
                self.total_nonwait += 1

                # Vérifier si la recommandation était correcte
                # Convertir en minuscules pour assurer la cohérence
                recommendation_lower = recommendation.lower()
                actual_outcome_lower = actual_outcome.lower()
                is_valid = (recommendation_lower == 'player' and actual_outcome_lower == 'player') or \
                           (recommendation_lower == 'banker' and actual_outcome_lower == 'banker')

                if is_valid:
                    self.total_nonwait_valid += 1
                    self.consecutive_nonwait_valid += 1

                    # Mettre à jour le maximum de recommandations NON-WAIT valides consécutives
                    self.max_consecutive_nonwait_valid = max(
                        self.max_consecutive_nonwait_valid,
                        self.consecutive_nonwait_valid
                    )

                    logger.debug(f"Manche {round_num}: Recommandation NON-WAIT valide. Séquence consécutive: {self.consecutive_nonwait_valid}")
                else:
                    # Réinitialiser le compteur de recommandations NON-WAIT valides consécutives
                    logger.debug(f"Manche {round_num}: Recommandation NON-WAIT invalide. Réinitialisation séquence (était: {self.consecutive_nonwait_valid})")
                    self.consecutive_nonwait_valid = 0
            else:  # Recommandation WAIT
                self.total_wait += 1

                # Ne pas réinitialiser le compteur de recommandations NON-WAIT valides consécutives
                # car les recommandations WAIT n'interrompent pas la séquence

                # Vérifier si la recommandation WAIT était justifiée (incertitude élevée)
                # Note: Ceci est une heuristique simplifiée, vous pourriez avoir une logique plus complexe
                is_valid = True  # Par défaut, considérer WAIT comme valide
                self.total_wait_valid += 1

                logger.debug(f"Manche {round_num}: Recommandation WAIT (considérée valide). Séquence NON-WAIT consécutive maintenue: {self.consecutive_nonwait_valid}")

            # Journaliser les statistiques globales périodiquement
            if round_num % 10 == 0:
                nonwait_accuracy = self.total_nonwait_valid / max(1, self.total_nonwait)
                wait_accuracy = self.total_wait_valid / max(1, self.total_wait)
                wait_ratio = self.total_wait / max(1, self.total_wait + self.total_nonwait)

                logger.info(f"Stats manches {target_round_min}-{target_round_max} (à manche {round_num}):")
                logger.info(f"  NON-WAIT: {self.total_nonwait_valid}/{self.total_nonwait} valides ({nonwait_accuracy:.2%})")
                logger.info(f"  WAIT: {self.total_wait_valid}/{self.total_wait} valides ({wait_accuracy:.2%})")
                logger.info(f"  Ratio WAIT: {wait_ratio:.2%}")
                logger.info(f"  Max NON-WAIT valides consécutives: {self.max_consecutive_nonwait_valid}")

                # Afficher les statistiques du calculateur de confiance consécutive si disponible
                if hasattr(self, 'consecutive_confidence_calculator'):
                    try:
                        wait_ratio = self.consecutive_confidence_calculator.get_current_wait_ratio()
                        confidence_adjustment = self.consecutive_confidence_calculator.get_confidence_adjustment()
                        logger.info(f"  Ratio WAIT récent: {wait_ratio:.2%}")
                        logger.info(f"  Ajustement de confiance: {confidence_adjustment:.4f}")
                    except Exception as e_stats:
                        logger.error(f"Erreur lors de l'affichage des statistiques du calculateur: {e_stats}")