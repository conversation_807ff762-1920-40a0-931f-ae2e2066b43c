DESCRIPTIF DÉTAILLÉ DES MÉTHODES - GESTION DONNÉES
================================================================================

Ce fichier contient la description détaillée des méthodes de gestion des données
du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : GESTION DONNÉES
Méthodes de chargement, préparation, transformation et gestion des données
historiques et en temps réel.

TOTAL : 27 MÉTHODES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
MÉTHODES GESTION DONNÉES
================================================================================

1. _append_session_to_historical_txt.txt (HybridBaccaratPredictor._append_session_to_historical_txt - Sauvegarde session historique)
   - Lignes 12746-12806 dans hbp.py (61 lignes)
   - FONCTION : Ajoute la séquence de session actuelle au fichier historique en format 0/1 avec gestion intelligente des sauts de ligne et validation format
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * filepath (str, optionnel) - Chemin vers le fichier historical_data.txt (défaut: "historical_data.txt")
   - FONCTIONNEMENT DÉTAILLÉ :
     * **PROTECTION THREAD :** Utilise `with self.sequence_lock:` puis `session_to_save = self.sequence[:]` pour copie thread-safe
     * **VALIDATION SÉQUENCE :** Vérifie `if not session_to_save:` avec `logger.info("Séquence de session vide, rien à ajouter.")` et retour `True`
     * **CONVERSION FORMAT :** Transforme séquence avec `converted_sequence = []` puis boucle `for outcome in session_to_save:` :
       - `if outcome == 'player': converted_sequence.append('0')` pour Player → 0
       - `elif outcome == 'banker': converted_sequence.append('1')` pour Banker → 1
       - `else: logger.warning(f"Outcome invalide ignoré: {outcome}")` pour valeurs inconnues
     * **VALIDATION CONVERSION :** Teste `if not converted_sequence:` avec message "Aucun outcome valide trouvé" et retour `True`
     * **GESTION FICHIER :** Ouvre `with open(filepath, 'a+', encoding='utf-8') as f:` en mode append+read pour vérification
     * **VÉRIFICATION SAUT LIGNE :** Utilise `f.seek(0, 2)` pour fin fichier, `file_size = f.tell()` puis si `file_size > 0:` :
       - `f.seek(file_size - 1)` pour dernier caractère
       - `last_char = f.read(1)` et `if last_char != '\n': f.write('\n')` pour assurer saut ligne
     * **ÉCRITURE DONNÉES :** Écrit `line_to_write = ''.join(converted_sequence) + '\n'` puis `f.write(line_to_write)`
     * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Session ajoutée au fichier historique: {len(converted_sequence)} manches")`
     * **GESTION ERREURS :** Capture `Exception as e:` avec `logger.error(f"Erreur lors de l'ajout de la session: {e}", exc_info=True)` et retour `False`
   - RETOUR : bool - True si ajout réussi ou séquence vide, False en cas d'erreur
   - UTILITÉ : Persistance automatique sessions avec format standardisé 0/1, gestion robuste fichiers et validation complète données

2. load_historical_data.txt (HybridBaccaratPredictor.load_historical_data - Chargement données historiques)
   - Lignes 4831-4886 dans hbp.py (56 lignes)
   - FONCTION : Charge les données historiques depuis un fichier .txt avec interface utilisateur et validation complète
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT :** Contrôle qu'aucune tâche ML n'est en cours avant chargement
     * **SÉLECTION FICHIER :** Ouvre dialogue de sélection avec filtres pour fichiers .txt
     * **CHARGEMENT INTERNE :** Utilise _load_historical_txt pour traitement du fichier
     * **CALCUL STATISTIQUES :** Détermine nombre de parties, longueur moyenne, total coups
     * **AFFICHAGE RÉSULTATS :** Présente statistiques détaillées dans messagebox
     * **GESTION SESSION :** Propose réinitialisation de la session en cours si applicable
     * **MISE À JOUR MODÈLES :** Met à jour automatiquement les modèles Markov globaux
     * **GESTION ERREURS :** Affiche messages d'erreur spécifiques selon le type de problème
   - RETOUR : None - Méthode d'interface utilisateur ne retourne rien
   - UTILITÉ : Interface conviviale pour charger l'historique avec validation et feedback utilisateur complet

3. _create_lgbm_features.txt (HybridBaccaratPredictor._create_lgbm_features - Création features LGBM optimisées)
   - Lignes 13748-13801 dans hbp.py (54 lignes)
   - FONCTION : Crée un vecteur de features optimisé pour le modèle LGBM à partir d'une séquence de résultats avec assemblage structuré
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **COMPTEURS BASIQUES :** Calcule `banker_count = sum(1 for outcome in sequence if outcome == 'banker')` et `player_count = sum(1 for outcome in sequence if outcome == 'player')`
     * **CALCUL STREAKS :** Appelle `streak_counts = self._calculate_streaks(sequence)` pour obtenir dictionnaire complet statistiques séries
     * **CALCUL ALTERNANCES :** Appelle `alternate_info = self._calculate_alternates(sequence)` pour patterns d'alternance avec compteurs spécifiques
     * **FEATURES DECAY :** Calcule `banker_decay = self._calculate_decay_feature(sequence, 'banker')` et `player_decay = self._calculate_decay_feature(sequence, 'player')` avec pondération temporelle
     * **ASSEMBLAGE FEATURES BASIQUES :** Initialise `features = [banker_count, player_count, banker_count / max(1, len(sequence)), player_count / max(1, len(sequence)), streak_counts['banker_streaks'], streak_counts['player_streaks'], banker_decay, player_decay]`
     * **STREAKS SPÉCIFIQUES :** Boucle `for length in range(2, 8):` pour ajouter `features.append(streak_counts[f'banker_streak_{length}'])` et `features.append(streak_counts[f'player_streak_{length}'])` (12 features)
     * **INFOS ALTERNANCE :** Étend avec `features.extend([alternate_info['alternate_count_2'], alternate_info['alternate_count_3'], alternate_info['alternate_ratio'], streak_counts['max_banker_streak'], streak_counts['max_player_streak']])`
     * **OPTIMISATION OPTUNA :** Réduit de 28 à 25 features en supprimant 3 features spécifiques selon optimisation hyperparamètres
     * **VALIDATION FINALE :** Assure que `len(features) == 25` pour cohérence avec modèle entraîné
     * **ORDRE FEATURES :** Structure précise : [compteurs(2), ratios(2), streaks_totaux(2), decay(2), streaks_2-7(12), alternances(3), max_streaks(2)] = 25 total
   - RETOUR : List[float] - Vecteur de 25 features normalisées et structurées pour LGBM
   - UTILITÉ : Pipeline complet d'extraction features LGBM avec ordre standardisé et optimisation Optuna intégrée

4. handle_short_sequence.txt (HybridBaccaratPredictor.handle_short_sequence - Gestion séquences courtes LSTM)
   - Lignes 5107-5233 dans hbp.py (127 lignes)
   - FONCTION : Gère séquences trop courtes pour LSTM avec padding intelligent et génération features step-by-step
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (Optional[List[str]]) - Séquence résultats ('player'/'banker') potentiellement None ou courte
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONFIGURATION CIBLE :** Récupère `target_length = self.config.lstm_sequence_length` et `num_features = self.config.lstm_input_size`
     * **VALIDATION MODÈLE :** Vérifie cohérence `self.lstm.input_size != num_features` avec warning si incohérence
     * **CAS SÉQUENCE VIDE :** Si `not sequence`, retourne `np.zeros((target_length, num_features), dtype=np.float32)`
     * **CAS SÉQUENCE LONGUE :** Si `current_length >= target_length`, appelle `self.create_hybrid_features(sequence)` et retourne `lstm_features`
     * **CAS SÉQUENCE COURTE :** Calcule `num_padding = target_length - current_length` et crée `padding_array = np.zeros((num_padding, num_features))`
     * **GÉNÉRATION FEATURES STEP :** Pour chaque `idx in range(current_length)`, calcule 12 features :
       - `pos_norm = (num_padding + idx) / float(target_length)` (position globale normalisée)
       - `is_banker = 1.0 if outcome == 'banker' else 0.0` (encodage binaire)
       - `ratio_banker = sub_sequence.count('banker') / sub_len` (ratio cumulatif)
       - `ratio_player = 1.0 - ratio_banker` (ratio complémentaire)
       - `is_repeat = 1.0 if idx > 0 and sequence[idx] == sequence[idx-1] else 0.0` (détection répétition)
       - `recent_3_count_banker` (comptage banker sur 3 derniers coups)
       - `imbalance = ratio_banker - 0.5` (déséquilibre par rapport à 50/50)
       - `streak_length` (longueur streak actuel avec calcul lookback)
       - `seq_odd_even = (num_padding + idx) % 2` (position paire/impaire)
       - 3 features Optuna par défaut : `0.5, 0.5, 0.5` (confidence, error_pattern_threshold, transition_uncertainty_threshold)
     * **FEATURES CONFIGURABLES :** Utilise `base_features_count = getattr(self.config, 'lstm_base_features_count', 9)` pour limiter features
     * **PADDING FEATURES :** Ajoute `zeros_to_add = num_features - len(current_step_features)` pour compléter à `num_features`
     * **CONCATÉNATION :** Combine `np.concatenate((padding_array, actual_features_array), axis=0)` avec padding au début
     * **VALIDATION FINALE :** Vérifie `padded_sequence_features.shape == (target_length, num_features)` avant retour
   - RETOUR : np.ndarray - Array shape (lstm_sequence_length, lstm_input_size) avec padding zeros au début et features réelles à la fin
   - UTILITÉ : Permet utilisation LSTM même avec séquences insuffisantes via padding intelligent et génération features détaillées

5. create_lstm_sequence_features.txt (HybridBaccaratPredictor.create_lstm_sequence_features - Création features LSTM)
   - Lignes 13570-13745 dans hbp.py (176 lignes)
   - FONCTION : Crée une matrice de features pour le modèle LSTM à partir d'une séquence avec fenêtre adaptative et taille de sortie fixe
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
     * keep_history_length (int, optionnel) - Taille maximale de la matrice de sortie (défaut: config.lstm_sequence_length)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SÉQUENCE :** Vérifie `if not sequence: return None` pour éviter erreurs
     * **DÉTERMINATION LONGUEUR :** Utilise `keep_history_length = self.config.lstm_sequence_length` si None fourni
     * **FENÊTRE ADAPTATIVE :** Utilise `effective_sequence = sequence[:]` (toute la séquence disponible)
     * **INITIALISATION MATRICE :** Crée `sequence_features = np.zeros((keep_history_length, features_count), dtype=np.float32)`
     * **GESTION LONGUEUR :** Si `seq_length <= keep_history_length`, utilise toute séquence avec padding au début ; sinon prend les derniers éléments
     * **CALCUL INDICES :** Détermine `matrix_indices` et `indices_to_use` pour mapping séquence → matrice
     * **GÉNÉRATION FEATURES (12 features par position) :**
       - **Feature 1 :** `(seq_idx + 1) / seq_length` (position relative normalisée)
       - **Feature 2 :** `1 if outcome == 'banker' else 0` (encodage binaire banker/player)
       - **Features 3-4 :** `banker_count / total` et `player_count / total` (ratios cumulatifs)
       - **Feature 5 :** `1 if outcome == previous else 0` (détection répétition)
       - **Feature 6 :** Comptage banker sur 3 derniers coups
       - **Feature 7 :** `banker_count / total - 0.5` (déséquilibre par rapport à 50/50)
       - **Feature 8 :** `alternance_count / seq_idx` (fréquence alternances)
       - **Feature 9 :** Longueur streak actuel avec calcul lookback
       - **Feature 10 :** `(seq_idx + 1) % 2` (position paire/impaire)
       - **Feature 11 :** Proximité du dernier changement de streak normalisée
       - **Feature 12 :** Réservée pour extensions futures
     * **OPTIMISATION MÉMOIRE :** Utilise dtype=np.float32 pour réduire empreinte mémoire
   - RETOUR : Optional[np.ndarray] - Matrice (keep_history_length, lstm_input_size) ou None si erreur
   - UTILITÉ : Génère représentation séquentielle riche avec fenêtre adaptative pour architecture LSTM

6. create_hybrid_features.txt (HybridBaccaratPredictor.create_hybrid_features - Création features hybrides)
   - Lignes 13803-13857 dans hbp.py (55 lignes)
   - FONCTION : Fonction centralisée pour la création de features hybrides (LGBM et LSTM) avec fenêtre adaptative
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SÉQUENCE :** Vérifie `if not sequence or len(sequence) < 2:` avec warning et retour `(None, None)`
     * **INITIALISATION VARIABLES :** Définit `lgbm_features = None` et `lstm_features = None`
     * **CRÉATION FEATURES LGBM :** Appelle `lgbm_features = self._create_lgbm_features(sequence)` avec gestion exception
     * **CRÉATION FEATURES LSTM :**
       - Récupère `lstm_sequence_length = self.config.lstm_sequence_length`
       - Appelle `lstm_features = self.create_lstm_sequence_features(sequence, lstm_sequence_length)`
       - Gestion logging conditionnel pour éviter spam pendant optimisation Optuna
     * **GESTION ERREURS :** Capture exceptions séparément pour LGBM et LSTM avec `logger.error` et `exc_info=True`
     * **VALIDATION FINALE :** Vérifie `if lgbm_features is None and lstm_features is None:` avec warning
     * **FENÊTRE ADAPTATIVE :** Utilise toute la séquence disponible pour calculer probabilités manche N depuis N-1 manches précédentes
   - RETOUR : Tuple[Optional[List[float]], Optional[np.ndarray]] - (features LGBM, features LSTM) ou None pour type en erreur
   - UTILITÉ : Point d'entrée unifié pour génération de features multi-modèles avec gestion robuste des erreurs

7. _extract_lstm_features.txt (HybridBaccaratPredictor._extract_lstm_features - Extraction features LSTM)
   - Lignes 13994-14024 dans hbp.py (31 lignes)
   - FONCTION : Méthode interne wrapper pour extraire les features LSTM avec gestion d'erreurs robuste
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RÉCUPÉRATION CONFIG :** Utilise `lstm_sequence_length = self.config.lstm_sequence_length` pour longueur cible
     * **LOGGING DEBUG :** Enregistre `logger.info(f"_extract_lstm_features: Séquence de longueur {len(sequence)}, lstm_sequence_length={lstm_sequence_length}")`
     * **APPEL PRINCIPAL :** Exécute `features = self.create_lstm_sequence_features(sequence, lstm_sequence_length)` avec gestion exception
     * **VALIDATION RETOUR :** Vérifie `if features is None:` avec logging erreur et création fallback
     * **FALLBACK SÉCURISÉ :** Crée `features = np.zeros((lstm_sequence_length, self.config.lstm_input_size), dtype=np.float32)` si échec
     * **LOGGING SUCCÈS :** Enregistre `logger.info(f"_extract_lstm_features: Features créées avec succès, shape={features.shape}")` si réussite
     * **GESTION EXCEPTION :** Capture toutes exceptions avec `logger.error(f"_extract_lstm_features: Erreur lors de la création des features LSTM: {e}", exc_info=True)`
   - RETOUR : np.ndarray - Matrice features (lstm_sequence_length, lstm_input_size) ou matrice zéros si erreur
   - UTILITÉ : Wrapper sécurisé pour extraction features LSTM avec fallback robuste et logging détaillé

8. _extract_lgbm_features.txt (HybridBaccaratPredictor._extract_lgbm_features - Extraction features LGBM)
   - Lignes 13981-13992 dans hbp.py (12 lignes)
   - FONCTION : Méthode interne wrapper simple pour extraire les features LGBM
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * sequence (List[str]) - Séquence de résultats ('player', 'banker')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **APPEL DIRECT :** Exécute simplement `return self._create_lgbm_features(sequence)` sans traitement supplémentaire
     * **WRAPPER SIMPLE :** Fournit interface cohérente avec _extract_lstm_features pour uniformité API
   - RETOUR : List[float] - Liste des 25 features LGBM normalisées
   - UTILITÉ : Wrapper simple pour extraction features LGBM avec interface uniforme

9. _calculate_sample_weights.txt (HybridBaccaratPredictor._calculate_sample_weights - Calcul poids échantillons)
   - Lignes 4305-4329 dans hbp.py (25 lignes)
   - FONCTION : Calcule poids d'échantillons avec facteur de décroissance temporelle pour privilégier données récentes avec validation robuste
   - PARAMÈTRES :
     * self - Instance de la classe HybridBaccaratPredictor
     * list_of_all_origins (List[int]) - Liste des indices d'origine des échantillons dans données historiques
     * final_num_samples (int) - Nombre total d'échantillons finaux après préparation
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION LOGGER :** Récupère `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))` pour logging adaptatif
     * **INITIALISATION POIDS :** Crée `sample_weights_all = np.ones(final_num_samples, dtype=np.float64)` avec poids uniformes par défaut
     * **RÉCUPÉRATION CONFIG :** Utilise `decay_factor = getattr(self.config, 'sample_weight_decay_factor', 0.9995)` pour facteur décroissance (proche de 1)
     * **CALCUL INDEX MAX :** Détermine `max_origin_index_used = max(list_of_all_origins) if list_of_all_origins else 0` pour référence temporelle
     * **VALIDATION CONDITIONS :** Vérifie `if 0 < decay_factor < 1 and max_origin_index_used > 0 and final_num_samples > 0:` pour application decay
     * **CONSTANTE EPSILON :** Définit `epsilon_decay = 1e-8` pour éviter poids nuls et problèmes numériques
     * **CALCUL POIDS DÉCROISSANTS :** Pour chaque `i, sample_origin_index in enumerate(list_of_all_origins):` :
       - Calcule `time_lag = max(0, max_origin_index_used - sample_origin_index)` pour distance temporelle
       - Applique `weight_raw = decay_factor ** time_lag` pour pondération exponentielle décroissante
       - Sécurise avec `sample_weights_all[i] = max(epsilon_decay, weight_raw) if np.isfinite(weight_raw) else epsilon_decay`
     * **LOGGING SUCCÈS :** Enregistre `logger_instance.info(f"Poids temporels appliqués (decay={decay_factor:.4f}).")` si conditions remplies
     * **FALLBACK UNIFORME :** Si conditions non remplies, log `"Poids temporels non appliqués (decay <= 0, >= 1, ou données insuffisantes)."`
     * **PROTECTION NUMÉRIQUE :** Assure que tous poids sont finis et positifs avec epsilon comme minimum
   - RETOUR : np.ndarray - Array de poids avec dtype=np.float64, décroissance temporelle ou uniformes selon conditions
   - UTILITÉ : Pondération temporelle robuste pour privilégier données récentes dans entraînement avec protection numérique complète

10. _calculate_streaks.txt (HybridBaccaratPredictor._calculate_streaks - Calcul séries consécutives)
    - Lignes 13803-13869 dans hbp.py (67 lignes)
    - FONCTION : Analyse et calcule statistiques complètes des séries consécutives (streaks) pour une séquence
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * sequence (List[str]) - Séquence de résultats ('player', 'banker')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SÉQUENCE :** Retourne dictionnaire vide avec toutes clés à 0 si `not sequence`
      * **INITIALISATION COMPTEURS :** Crée `streak_counts` avec 16 clés : banker_streaks, player_streaks, banker_streak_2 à 7, player_streak_2 à 7, max_banker_streak, max_player_streak
      * **VARIABLES ÉTAT :** Initialise `current_type = None` et `current_length = 0` pour suivi streak actuel
      * **PARCOURS SÉQUENCE :** Pour chaque `outcome` dans séquence :
        - **VALIDATION OUTCOME :** Si `outcome not in ('banker', 'player')`, reset `current_type = None` et `current_length = 0`
        - **DÉTECTION CHANGEMENT :** Si `outcome != current_type`, traite streak précédent et démarre nouveau avec `current_type = outcome` et `current_length = 1`
        - **CONTINUATION STREAK :** Si même type, incrémente `current_length += 1`
      * **TRAITEMENT STREAK PRÉCÉDENT :** Quand changement détecté :
        - Incrémente `streak_counts[f'{current_type}_streaks'] += 1` pour comptage total
        - Met à jour `streak_counts[f'max_{current_type}_streak'] = max(streak_counts[f'max_{current_type}_streak'], current_length)` pour maximum
        - Si `2 <= current_length <= 7`, incrémente `streak_counts[f'{current_type}_streak_{current_length}'] += 1` pour comptage spécifique
      * **TRAITEMENT STREAK FINAL :** Après boucle, traite dernière streak en cours avec même logique
      * **CALCUL MOYENNES :** Calcule moyennes des streaks si compteurs > 0, sinon 0.0
      * **DÉTECTION STREAK ACTUELLE :** Analyse fin séquence pour déterminer `current_streak_length` et `current_streak_type`
    - RETOUR : Dict[str, Union[int, float]] - Dictionnaire avec 16 clés de statistiques streaks complètes
    - UTILITÉ : Analyse patterns de séries consécutives pour features ML avec comptage détaillé par longueur et type

11. _calculate_alternates.txt (HybridBaccaratPredictor._calculate_alternates - Calcul alternances)
    - Lignes 13871-13911 dans hbp.py (41 lignes)
    - FONCTION : Analyse et calcule statistiques des patterns d'alternance dans une séquence avec détection motifs spécifiques
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * sequence (List[str]) - Séquence de résultats ('player', 'banker')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SÉQUENCE :** Retourne `{'alternate_count_2': 0, 'alternate_count_3': 0, 'alternate_ratio': 0.5}` si `not sequence or len(sequence) < 2`
      * **INITIALISATION COMPTEURS :** Définit `alternates = 0` et `last_outcome = sequence[0]` pour suivi alternances basiques
      * **PARCOURS ALTERNANCES :** Pour chaque `outcome` dans `sequence[1:]` :
        - Teste `if outcome != last_outcome and outcome in ('banker', 'player'):` puis `alternates += 1` pour comptage
        - Met à jour `last_outcome = outcome if outcome in ('banker', 'player') else last_outcome` avec validation
      * **DÉFINITION MOTIFS :** Crée patterns spécifiques :
        - `alt_2_pattern = ['banker', 'player', 'banker', 'player']` pour alternance double
        - `alt_3_pattern = ['banker', 'player', 'banker', 'player', 'banker', 'player']` pour alternance triple
      * **INITIALISATION COMPTEURS MOTIFS :** Définit `alt_count_2 = 0` et `alt_count_3 = 0` pour patterns
      * **DÉTECTION MOTIF 2 :** Si `len(sequence) >= 4:`, parcourt `for i in range(len(sequence) - 3):` :
        - Extrait `window = sequence[i:i+4]` pour fenêtre glissante
        - Teste `if window == alt_2_pattern:` puis `alt_count_2 += 1` pour comptage motif
      * **DÉTECTION MOTIF 3 :** Si `len(sequence) >= 6:`, parcourt `for i in range(len(sequence) - 5):` :
        - Extrait `window = sequence[i:i+6]` pour fenêtre glissante
        - Teste `if window == alt_3_pattern:` puis `alt_count_3 += 1` pour comptage motif
      * **CALCUL RATIO :** Calcule `alt_ratio = alternates / (len(sequence) - 1) if len(sequence) > 1 else 0.5` pour ratio global
      * **CONSTRUCTION RETOUR :** Retourne dictionnaire `{'alternate_count_2': alt_count_2, 'alternate_count_3': alt_count_3, 'alternate_ratio': alt_ratio}`
    - RETOUR : Dict[str, float] - Dictionnaire avec compteurs motifs et ratio alternance global
    - UTILITÉ : Détection patterns d'alternance pour features LGBM avec analyse motifs spécifiques et ratio global

12. _load_historical_txt.txt (HybridBaccaratPredictor._load_historical_txt - Chargement historique TXT)
    - Lignes 11157-11238 dans hbp.py (82 lignes)
    - FONCTION : Charge et parse fichiers historiques au format texte avec validation complète et mise à jour Markov global
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * filepath (str) - Chemin vers le fichier historical_data.txt à charger
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION FICHIER :** Vérifie `if not os.path.exists(filepath):` avec logging erreur et mise à jour progress si UI disponible
      * **LECTURE SÉCURISÉE :** Ouvre `with open(filepath, 'r', encoding='utf-8') as f:` pour lecture UTF-8
      * **PARSING LIGNES :** Itère sur `for line_num, line in enumerate(f, 1):` avec nettoyage `line = line.strip()`
      * **VALIDATION FORMAT :** Vérifie que ligne contient uniquement '0', '1', 'B', 'P' avec `if not all(c in '01BP' for c in line):`
      * **CONVERSION STANDARDISÉE :** Convertit format avec `line = line.replace('B', '1').replace('P', '0')` puis `game_sequence = ['banker' if c == '1' else 'player' for c in line]`
      * **FILTRAGE LONGUEUR :** Ignore parties trop courtes avec `if len(game_sequence) < 2:` et logging warning
      * **ACCUMULATION DONNÉES :** Ajoute à `new_historical_data.append(game_sequence)` pour construction dataset
      * **MISE À JOUR THREAD-SAFE :** Utilise `with self.sequence_lock, self.markov_lock:` pour modifier état partagé
      * **ASSIGNATION ÉTAT :** Met à jour `self.historical_data = new_historical_data`, `self.loaded_historical = True`, `self.historical_games_at_startup_or_reset = num_games_loaded`
      * **CALCUL STATISTIQUES :** Détermine `total_rounds = sum(len(g) for g in new_historical_data)` pour logging
      * **MISE À JOUR MARKOV :** Si `self.markov:`, appelle `self.markov.update_global(self.historical_data)` avec gestion d'erreurs
      * **GESTION ERREURS :** Capture `UnicodeDecodeError` pour problèmes encodage et `Exception` générale avec reset compteur
      * **LOGGING DÉTAILLÉ :** Enregistre succès avec `logger.info(f"_load_historical_txt: Succès ({num_games_loaded} parties chargées, {total_rounds} coups totaux).")`
    - RETOUR : bool - True si chargement réussi, False en cas d'erreur
    - UTILITÉ : Import robuste données historiques avec validation format, conversion standardisée et mise à jour Markov global

13. _apply_data_sampling.txt (HybridBaccaratPredictor._apply_data_sampling - Application échantillonnage données)
    - Lignes 4271-4303 dans hbp.py (33 lignes)
    - FONCTION : Applique techniques d'échantillonnage pour équilibrage dataset avec désactivation pour garantir manches 31-60 et optimisation mémoire
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * original_historical_data (List[List[str]]) - Données historiques originales par parties
      * max_games (Optional[int]) - Limite maximale de jeux à traiter (None = pas de limite)
      * sampling_fraction (Optional[float]) - Fraction d'échantillonnage entre 0.0 et 1.0 (None = pas d'échantillonnage)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION LOGGER :** Récupère `logger_instance = getattr(self, 'logger', logging.getLogger(__name__))` pour logging adaptatif
      * **COMPTAGE ORIGINAL :** Calcule `num_original_games = len(original_historical_data)` pour statistiques initiales
      * **COPIE DONNÉES :** Initialise `data_to_process = original_historical_data` comme base de travail
      * **DÉSACTIVATION ÉCHANTILLONNAGE :** Commentaire explicite "MODIFIÉ: Désactive l'échantillonnage pour garantir que toutes les manches 31-60 soient utilisées"
      * **VALIDATION MAX_GAMES :** Si `max_games is not None and max_games > 0 and len(data_to_process) > max_games:` :
        - Applique `data_to_process = data_to_process[:max_games]` pour limitation
        - Log `logger_instance.info(f"Limitation à {max_games} jeux (sur {num_original_games} disponibles)")`
      * **VALIDATION SAMPLING :** Si `sampling_fraction is not None and 0 < sampling_fraction < 1:` :
        - Calcule `target_size = int(len(data_to_process) * sampling_fraction)` pour taille cible
        - Applique `data_to_process = random.sample(data_to_process, target_size)` pour échantillonnage aléatoire
        - Log `logger_instance.info(f"Échantillonnage appliqué: {target_size} jeux sélectionnés")`
      * **STATISTIQUES FINALES :** Calcule `final_num_games = len(data_to_process)` pour comptage final
      * **GÉNÉRATION RAPPORT :** Crée `sampling_info = f"Données traitées: {final_num_games}/{num_original_games} jeux"` avec ratio
      * **LOGGING RÉSULTAT :** Enregistre `logger_instance.info(f"Échantillonnage terminé: {sampling_info}")`
    - RETOUR : Tuple[List[List[str]], str] - (données_échantillonnées, rapport_échantillonnage)
    - UTILITÉ : Optimisation mémoire et performance avec contrôle précis taille dataset tout en préservant intégrité manches cibles

14. _calculate_decay_feature.txt (HybridBaccaratPredictor._calculate_decay_feature - Calcul feature decay)
    - Lignes 13913-13939 dans hbp.py (27 lignes)
    - FONCTION : Calcule feature avec pondération temporelle décroissante pour donner plus de poids aux résultats récents avec normalisation
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * sequence (List[str]) - Séquence de résultats ('banker' ou 'player')
      * target_outcome (str) - Résultat cible à analyser ('banker' ou 'player')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION SÉQUENCE :** Vérifie `if not sequence: return 0.0` pour éviter division par zéro et erreurs
      * **RÉCUPÉRATION CONFIG :** Utilise `decay_factor = self.config.decay_factor` pour facteur de décroissance temporelle (typiquement 0.95-0.99)
      * **INITIALISATION CALCULS :** Définit `weighted_sum = 0.0` et `total_weight = 0.0` pour accumulation pondérée
      * **PARCOURS INVERSE :** Itère `for i, outcome in enumerate(sequence):` pour traitement chronologique
      * **CALCUL POIDS :** Calcule `weight = decay_factor ** (len(sequence) - 1 - i)` pour pondération décroissante (plus récent = poids plus élevé)
      * **ACCUMULATION PONDÉRÉE :** Ajoute `total_weight += weight` pour normalisation puis :
        - Si `outcome == target_outcome:`, ajoute `weighted_sum += weight` pour contribution positive
      * **VALIDATION NORMALISATION :** Vérifie `if total_weight == 0.0: return 0.0` pour éviter division par zéro
      * **CALCUL FINAL :** Retourne `weighted_sum / total_weight` pour feature normalisée entre 0 et 1
      * **INTERPRÉTATION :** Valeur proche de 1 = target_outcome fréquent récemment, proche de 0 = rare récemment
    - RETOUR : float - Feature decay normalisée entre 0.0 et 1.0
    - UTILITÉ : Génère feature temporelle pour modèles ML avec emphasis sur données récentes et normalisation robuste

15. _create_temporal_split.txt (HybridBaccaratPredictor._create_temporal_split - Division temporelle)
    - Lignes 4331-4349 dans hbp.py (19 lignes)
    - FONCTION : Crée division temporelle des données pour validation avec TimeSeriesSplit et sélection du dernier split
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * X_lgbm_all (np.ndarray) - Données features LGBM pour déterminer taille dataset
    - FONCTIONNEMENT DÉTAILLÉ :
      * **RÉCUPÉRATION CONFIG :** Utilise `n_splits = max(2, getattr(self.config, 'lgbm_cv_splits', 5))` pour nombre de splits
      * **INITIALISATION SPLITTER :** Crée `tscv = TimeSeriesSplit(n_splits=n_splits)` pour division temporelle
      * **INITIALISATION LISTES :** Définit `train_indices_list = []` et `val_indices_list = []` pour accumulation
      * **GÉNÉRATION SPLITS :** Itère `for train_idx, val_idx in tscv.split(X_lgbm_all):` pour créer tous les splits possibles
      * **ACCUMULATION INDICES :** Ajoute `train_indices_list.append(train_idx)` et `val_indices_list.append(val_idx)` pour chaque split
      * **VALIDATION GÉNÉRATION :** Vérifie `if not train_indices_list or not val_indices_list:` avec logging erreur et retour `(None, None)`
      * **SÉLECTION DERNIER SPLIT :** Utilise `train_indices = train_indices_list[-1]` et `val_indices = val_indices_list[-1]` pour split le plus récent
      * **LOGIQUE TEMPORELLE :** TimeSeriesSplit assure que validation utilise toujours données plus récentes que entraînement
    - RETOUR : Tuple[np.ndarray, np.ndarray] - (indices_train, indices_validation) ou (None, None) si erreur
    - UTILITÉ : Assure validation temporellement cohérente avec données futures pour test réaliste des modèles

16. _validate_data_shapes.txt (HybridBaccaratPredictor._validate_data_shapes - Validation formes données)
    - Lignes 4351-4384 dans hbp.py (34 lignes)
    - FONCTION : Valide cohérence des dimensions de tous les arrays de données d'entraînement avec vérifications exhaustives
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * X_lgbm_all (np.ndarray) - Features LGBM pour tous échantillons
      * y_labels_all (np.ndarray) - Labels pour tous échantillons
      * X_lstm_all (np.ndarray) - Features LSTM pour tous échantillons
      * sample_weights_all (np.ndarray) - Poids échantillons pour tous échantillons
      * list_of_all_prefix_sequences (List[List[str]]) - Séquences préfixes pour tous échantillons
      * list_of_all_origins (List[int]) - Indices origine pour tous échantillons
      * final_num_samples (int) - Nombre attendu d'échantillons total
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION :** Définit `is_valid = True` et `message = ""` pour accumulation erreurs
      * **VALIDATION X_LGBM :** Vérifie `if X_lgbm_all.shape[0] != final_num_samples:` avec message `f"Incohérence taille X_lgbm (attendu: {final_num_samples}, obtenu: {X_lgbm_all.shape[0]})"`
      * **VALIDATION Y_LABELS :** Vérifie `if y_labels_all.shape[0] != final_num_samples:` avec message `f"Incohérence taille y_labels (attendu: {final_num_samples}, obtenu: {y_labels_all.shape[0]})"`
      * **VALIDATION X_LSTM :** Vérifie `if X_lstm_all.shape[0] != final_num_samples:` avec message `f"Incohérence taille X_lstm (attendu: {final_num_samples}, obtenu: {X_lstm_all.shape[0]})"`
      * **VALIDATION POIDS :** Vérifie `if sample_weights_all.shape[0] != final_num_samples:` avec message `f"Incohérence taille sample_weights (attendu: {final_num_samples}, obtenu: {sample_weights_all.shape[0]})"`
      * **VALIDATION SÉQUENCES :** Vérifie `if len(list_of_all_prefix_sequences) != final_num_samples:` avec message `f"Incohérence taille list_of_all_prefix_sequences (attendu: {final_num_samples}, obtenu: {len(list_of_all_prefix_sequences)})"`
      * **VALIDATION ORIGINES :** Vérifie `if len(list_of_all_origins) != final_num_samples:` avec message `f"Incohérence taille list_of_all_origins (attendu: {final_num_samples}, obtenu: {len(list_of_all_origins)})"`
      * **ACCUMULATION ERREURS :** Chaque erreur ajoute à `message` et met `is_valid = False`
      * **RETOUR CONDITIONNEL :** Si `not is_valid:`, retourne `(False, f"Erreur de validation des shapes: {message}")`, sinon `(True, "")`
    - RETOUR : Tuple[bool, str] - (validation_réussie, message_erreur_ou_vide)
    - UTILITÉ : Prévient erreurs de compatibilité entre modèles avec validation exhaustive dimensions avant entraînement

17. _get_cumulative_new_data.txt (HybridBaccaratPredictor._get_cumulative_new_data - Données nouvelles cumulatives)
    - Lignes 2772-2910 dans hbp.py (139 lignes)
    - FONCTION : Extrait données cumulatives pour entraînement incrémental incluant nouvelles parties historiques et session actuelle
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **PROTECTION THREAD :** Utilise `with self.sequence_lock:` pour accès sécurisé aux données
      * **IDENTIFICATION NOUVELLES PARTIES :** Calcule `current_total_games_in_history = len(self.historical_data)`, `start_index_new_history = self.historical_games_at_startup_or_reset`, `num_new_games_in_history = current_total_games_in_history - start_index_new_history`
      * **AJOUT NOUVELLES PARTIES :** Si `num_new_games_in_history > 0:`, itère `for i in range(start_index_new_history, current_total_games_in_history):` et `combined_new_sequence.extend(self.historical_data[i])`
      * **AJOUT SESSION ACTUELLE :** Si `current_session_len = len(self.sequence) > 0:`, ajoute `combined_new_sequence.extend(self.sequence)`
      * **VALIDATION MINIMUM :** Vérifie `total_new_rounds = len(combined_new_sequence)` contre `min_hist_needed_for_features = self.config.lstm_sequence_length` et `min_rounds_for_feature_gen = 5`
      * **PRÉPARATION CONTEXTE :** Si `start_index_new_history > 0:`, construit contexte historique avec `context_needed_rounds = min_hist_needed_for_features + 5` en itérant à rebours sur historique ancien
      * **GÉNÉRATION FEATURES :** Combine `full_sequence_for_gen = context_sequence + combined_new_sequence` puis itère `for i in range(start_gen_idx_in_full, len(full_sequence_for_gen)):`
      * **VALIDATION FEATURES :** Pour chaque `feat_lgbm, feat_lstm = self.create_hybrid_features(input_sub_sequence):`, vérifie `len(feat_lgbm) == len(self.feature_names)` et `feat_lstm.shape == (self.config.lstm_sequence_length, lstm_expected_num_features)`
      * **ACCUMULATION DONNÉES :** Si features valides, calcule `label = 1 if actual_outcome == 'banker' else 0` et ajoute aux listes `X_lgbm_new`, `y_lgbm_new`, `X_lstm_new`, `y_lstm_new`
      * **CONVERSION NUMPY :** Convertit avec `X_lgbm_np = np.array(X_lgbm_new, dtype=np.float32)`, `X_lstm_np = np.stack(X_lstm_new, axis=0).astype(np.float32)`, `y_lgbm_np = np.array(y_lgbm_new, dtype=np.int64)`
      * **VALIDATION FINALE :** Vérifie cohérence shapes avec `if X_lgbm_np.shape[0] != num_samples_generated:` et gestion d'erreurs complète
    - RETOUR : Tuple[4×Optional[np.ndarray]] - (X_lgbm, y_lgbm, X_lstm, y_lstm) ou (None×4) si erreur
    - UTILITÉ : Entraînement incrémental efficace avec données cumulatives nouvelles et contexte historique pour continuité

18. _get_historical_data_for_refit.txt (HybridBaccaratPredictor._get_historical_data_for_refit - Données historiques pour re-fit)
    - Lignes 4078-4135 dans hbp.py (58 lignes)
    - FONCTION : Récupère et prépare toutes les données historiques pour re-fit des wrappers LGBM avec validation complète
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **PROTECTION THREAD :** Utilise `with self.sequence_lock:` pour accès sécurisé aux données historiques
      * **VALIDATION HISTORIQUE :** Vérifie `if not self.loaded_historical or not self.historical_data:` avec warning et retour `(None, None)` si pas de données
      * **COPIE DONNÉES :** Crée `data_to_process = self.historical_data[:]` pour itération sécurisée sans modification originale
      * **INITIALISATION VARIABLES :** Définit `all_features_lgbm = []`, `all_labels_lgbm = []`, `total_rounds_extracted = 0`, `n_expected_features = len(self.feature_names)`, `min_seq_len_for_features = 5`
      * **PARCOURS PARTIES :** Itère `for game_idx, game in enumerate(data_to_process):` pour traiter chaque partie historique
      * **VALIDATION PARTIE :** Vérifie `if not game or len(game) <= min_seq_len_for_features:` avec warning et continue si partie trop courte
      * **GÉNÉRATION FEATURES :** Pour chaque partie, itère `for i in range(min_seq_len_for_features, len(game_sequence)):` avec `input_sequence = game_sequence[:i]` et `actual_outcome = game_sequence[i]`
      * **APPEL HYBRID FEATURES :** Utilise `features_lgbm, _ = self.create_hybrid_features(input_sequence)` en ignorant partie LSTM
      * **VALIDATION FEATURES :** Vérifie `if features_lgbm is not None and len(features_lgbm) == n_expected_features:` pour cohérence nombre features
      * **ACCUMULATION DONNÉES :** Si valide, calcule `label = 1 if actual_outcome == 'banker' else 0` et ajoute `all_features_lgbm.append(features_lgbm)`, `all_labels_lgbm.append(label)`, `total_rounds_extracted += 1`
      * **GESTION INCOHÉRENCES :** Si `features_lgbm is not None` mais mauvaise taille, warning avec détails partie et coup
      * **VALIDATION FINALE :** Vérifie `if not all_features_lgbm:` avec warning et retour `(None, None)` si aucune feature extraite
      * **CONVERSION NUMPY :** Convertit `X_refit = np.array(all_features_lgbm, dtype=np.float32)` et `y_refit = np.array(all_labels_lgbm, dtype=np.int64)` avec gestion d'erreurs
    - RETOUR : Tuple[Optional[np.ndarray], Optional[np.ndarray]] - (features_LGBM, labels) ou (None, None) si erreur
    - UTILITÉ : Préparation robuste données historiques complètes pour re-fit modèles LGBM avec validation exhaustive et gestion d'erreurs

19. _get_recent_session_data.txt (HybridBaccaratPredictor._get_recent_session_data - Données session récente)
    - Lignes 2984-3076 dans hbp.py (93 lignes)
    - FONCTION : Extrait données récentes pour mise à jour incrémentale avec validation features et gestion historique contextuel
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * min_rounds_for_update (int, optionnel) - Nombre minimum nouvelles manches requises (défaut: 10)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **PROTECTION THREAD :** Utilise `with self.sequence_lock:` pour accès sécurisé aux données de séquence
      * **CALCUL NOUVELLES DONNÉES :** Détermine `current_len = len(self.sequence)`, `start_index = self.last_incremental_update_index`, `num_new_rounds = current_len - start_index`
      * **VALIDATION MINIMUM :** Vérifie `if num_new_rounds < min_rounds_for_update:` avec logging info et retour `(None, None, None, None)` si insuffisant
      * **CALCUL HISTORIQUE CONTEXTUEL :** Détermine `min_hist_needed = self.config.lstm_sequence_length`, `effective_start_for_features = max(0, start_index - min_hist_needed)` pour contexte suffisant
      * **EXTRACTION SOUS-SÉQUENCE :** Récupère `sequence_subset_for_gen = self.sequence[effective_start_for_features:current_len]` avec `offset_in_subset = start_index - effective_start_for_features` pour mapping indices
      * **INITIALISATION STRUCTURES :** Crée `X_lgbm_new, y_lgbm_new = [], []` et `X_lstm_new, y_lstm_new = [], []` pour accumulation données
      * **CONFIGURATION LSTM :** Récupère `lstm_input_feat_size = getattr(self.lstm, 'input_size', 6) if self.lstm else 6` et `expected_lstm_shape = (self.config.lstm_sequence_length, lstm_input_feat_size)`
      * **GÉNÉRATION FEATURES :** Boucle `for i in range(offset_in_subset, len(sequence_subset_for_gen)):` pour nouvelles manches :
        - Calcule `sequence_up_to_i = sequence_subset_for_gen[:i]` pour contexte
        - Appelle `feat_lgbm, feat_lstm = self.create_hybrid_features(sequence_up_to_i)` pour génération
        - Valide `valid_lgbm = feat_lgbm is not None and len(feat_lgbm) == len(self.feature_names)`
        - Valide `valid_lstm = feat_lstm is not None and feat_lstm.shape == expected_lstm_shape`
      * **ACCUMULATION DONNÉES :** Si features valides :
        - Détermine `actual_outcome = sequence_subset_for_gen[i]` et `label = 1 if actual_outcome == 'banker' else 0`
        - Ajoute à listes avec `X_lgbm_new.append(feat_lgbm)`, `y_lgbm_new.append(label)`, etc.
      * **GESTION ERREURS FEATURES :** Si features invalides, collecte problèmes dans `issues = []` et log warning détaillé
      * **VALIDATION RÉSULTATS :** Vérifie `if not X_lgbm_new:` avec warning "Aucune feature valide générée" et retour None
      * **CONVERSION NUMPY :** Convertit avec `X_lgbm_np = np.array(X_lgbm_new, dtype=np.float32)`, `X_lstm_np = np.stack(X_lstm_new, axis=0).astype(np.float32)`, etc.
      * **GESTION ERREURS CONVERSION :** Capture `Exception as e:` avec logging erreur et retour None
      * **LOGGING SUCCÈS :** Enregistre `logger.info(f"Données récentes préparées: LGBM {X_lgbm_np.shape}, LSTM {X_lstm_np.shape}")`
    - RETOUR : Tuple[Optional[np.ndarray], Optional[np.ndarray], Optional[np.ndarray], Optional[np.ndarray]] - (X_lgbm, y_lgbm, X_lstm, y_lstm) ou (None×4) si échec
    - UTILITÉ : Focus sur performance récente pour ajustements incrémentaux avec validation complète et gestion contextuelle historique

20. calculate_lstm_sample_weights.txt (HybridBaccaratPredictor.calculate_lstm_sample_weights - Poids échantillons LSTM)
    - Lignes 10027-10170 dans hbp.py (144 lignes)
    - FONCTION : Calcule les poids d'échantillons pour LSTM basés sur métriques de confiance et incertitude avec focus manches 31-60
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * X_lstm (np.ndarray) - Features LSTM (séquences temporelles)
      * y_lstm (np.ndarray) - Labels correspondants (0=Player, 1=Banker)
      * sequence_positions (np.ndarray, optionnel) - Positions des échantillons dans la séquence
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION DONNÉES :** Vérifie `if X_lstm is None or len(X_lstm) == 0 or y_lstm is None or len(y_lstm) == 0: return None`
      * **INITIALISATION POIDS :** Crée `sample_weights = np.ones(len(X_lstm), dtype=np.float32)` comme base
      * **FACTEUR TEMPOREL :** Calcule `temporal_factor = np.linspace(temporal_factor_min, temporal_factor_max, len(X_lstm))` avec `temporal_factor_min=0.5`, `temporal_factor_max=1.0` pour favoriser échantillons récents
      * **FACTEUR DIFFICULTÉ :** Initialise `difficulty_factor = np.ones_like(sample_weights)` puis si LSTM entraîné :
        - Met modèle en mode évaluation avec `self.lstm.eval()`
        - Convertit features avec `X_tensor = torch.FloatTensor(X_lstm).to(self.device)`
        - Calcule prédictions avec `outputs = self.lstm(X_tensor)` puis `probas = torch.softmax(outputs, dim=1).cpu().numpy()`
        - Détermine confiance avec `confidence = np.abs(pred_probas - 0.5) * 2.0`
        - Inverse pour difficulté avec `difficulty_factor = 1.0 - 0.5 * confidence`
      * **FACTEUR POSITION :** Si `sequence_positions` fourni, extrait `target_round_min=31`, `target_round_max=60`, `late_game_weight_factor`
      * **MASQUE MANCHES CIBLES :** Crée `late_game_mask = (positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max)`
      * **POIDS PROGRESSIFS :** Calcule `normalized_positions = (target_positions - target_round_min) / (target_round_max - target_round_min)` puis `progressive_weights = 1.0 + (np.exp(exponential_factor * normalized_positions) - 1) / (np.exp(exponential_factor) - 1) * (late_game_weight_factor - 1.0)`
      * **ÉQUILIBRAGE CLASSES :** Calcule `class_weights = (len(target_y) / (len(class_counts) * class_counts)) ** 1.5` pour classes minoritaires
      * **FACTEUR TRANSITIONS :** Détecte changements avec `transitions = np.diff(target_y, prepend=target_y[0])` et applique bonus `1.5` aux points de transition
      * **COMBINAISON FINALE :** Multiplie `combined_weights = temporal_factor * difficulty_factor * position_factor`
      * **NORMALISATION :** Applique `normalized_weights = combined_weights * (len(combined_weights) / np.sum(combined_weights))` puis clipping avec `min_sample_weight=0.2`, `max_sample_weight=5.0`
    - RETOUR : np.ndarray - Poids d'échantillons optimisés pour LSTM avec focus manches 31-60
    - UTILITÉ : Optimise apprentissage LSTM avec pondération temporelle, difficulté, et focus spécial sur manches cibles avec équilibrage classes

21. calculate_sample_weights_from_metrics.txt (HybridBaccaratPredictor.calculate_sample_weights_from_metrics - Poids depuis métriques)
    - Lignes 9896-10025 dans hbp.py (130 lignes)
    - FONCTION : Calcule poids échantillons basés sur métriques de confiance et incertitude LGBM avec pondération adaptative
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * X_features (np.ndarray) - Features d'entrée pour calcul métriques
      * y_labels (np.ndarray) - Labels correspondants
      * sequence_positions (np.ndarray, optionnel) - Positions des échantillons dans la séquence
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION MODÈLES :** Vérifie `if self.feature_scaler is None or self.lgbm_base is None: return None`
      * **VÉRIFICATION ENTRAÎNEMENT :** Utilise `check_is_fitted(self.lgbm_base)` pour confirmer modèle LGBM entraîné
      * **INITIALISATION POIDS :** Crée `sample_weights = np.ones(len(X_features), dtype=np.float32)` comme base
      * **CALCUL CONFIANCE :** Si LGBM entraîné, calcule `y_pred_proba = self.lgbm_base.predict_proba(X_features)` puis `confidence_scores = np.abs(y_pred_proba[:, 1] - 0.5) * 2.0`
      * **CALCUL INCERTITUDE :** Si `self.lgbm_uncertainty` disponible :
        - Vérifie entraînement avec `check_is_fitted(self.lgbm_uncertainty)`
        - Extrait prédictions estimateurs avec `estimator_probas = np.array([estimator.predict_proba(X_features)[:, 1] for estimator in self.lgbm_uncertainty.estimators_])`
        - Calcule variance avec `uncertainty_scores = np.var(estimator_probas, axis=0)`
        - Normalise avec `uncertainty_scores = (uncertainty_scores - np.min(uncertainty_scores)) / (np.max(uncertainty_scores) - np.min(uncertainty_scores) + 1e-8)`
      * **PONDÉRATION CONFIANCE :** Applique `confidence_weight_factor = getattr(self.config, 'confidence_weight_factor', 1.5)` avec `confidence_weights = 1.0 + (1.0 - confidence_scores) * confidence_weight_factor`
      * **PONDÉRATION INCERTITUDE :** Applique `uncertainty_weight_factor = getattr(self.config, 'uncertainty_weight_factor', 1.2)` avec `uncertainty_weights = 1.0 + uncertainty_scores * uncertainty_weight_factor`
      * **FACTEUR POSITION :** Si `sequence_positions` fourni, extrait `target_round_min=31`, `target_round_max=60`, `late_game_weight_factor`
      * **MASQUE MANCHES CIBLES :** Crée `late_game_mask = (positions_1_indexed >= target_round_min) & (positions_1_indexed <= target_round_max)`
      * **POIDS PROGRESSIFS :** Pour manches cibles, calcule `normalized_positions = (target_positions - target_round_min) / (target_round_max - target_round_min)` puis `progressive_weights = 1.0 + normalized_positions * (late_game_weight_factor - 1.0)`
      * **COMBINAISON FINALE :** Multiplie `combined_weights = confidence_weights * uncertainty_weights * position_weights`
      * **NORMALISATION :** Applique `normalized_weights = combined_weights * (len(combined_weights) / np.sum(combined_weights))` puis clipping avec `min_sample_weight=0.1`, `max_sample_weight=10.0`
    - RETOUR : np.ndarray - Poids d'échantillons optimisés basés sur métriques LGBM
    - UTILITÉ : Pondération intelligente selon confiance, incertitude et position avec focus manches 31-60 pour optimiser qualité prédictions

22. _extract_features_for_consecutive_calculator.txt (HybridBaccaratPredictor._extract_features_for_consecutive_calculator - Features calculateur consécutif)
    - Lignes 10742-10767 dans hbp.py (26 lignes)
    - FONCTION : Extrait vecteur de features spécialisé pour calculateur de confiance consécutive avec 10 features optimisées
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
    - FONCTIONNEMENT DÉTAILLÉ :
      * **PROTECTION THREAD :** Utilise `with self.sequence_lock:` pour accès sécurisé à la séquence
      * **VALIDATION SÉQUENCE :** Vérifie `if len(self.sequence) < 5:` avec retour features par défaut si insuffisant
      * **SÉQUENCE RÉCENTE :** Extrait `recent_sequence = self.sequence[-10:]` pour analyse patterns récents
      * **FEATURES BASIQUES :** Calcule ratios avec `banker_ratio = recent_sequence.count('banker') / len(recent_sequence)`, `player_ratio = recent_sequence.count('player') / len(recent_sequence)`
      * **FEATURES STREAKS :** Détermine `current_streak_length` et `current_streak_type` en analysant fin de séquence
      * **FEATURES ALTERNANCE :** Calcule `alternation_ratio` en comptant changements consécutifs dans séquence récente
      * **FEATURES PATTERNS :** Détecte patterns répétitifs avec boucles imbriquées `for pattern_length in [2, 3]:` et `for i in range(len(recent_sequence) - 2 * pattern_length):`
      * **NORMALISATION MANCHE :** Ajoute `current_round / getattr(self.config, 'round_normalization_factor', 100.0)` pour position relative
      * **VECTEUR FINAL :** Retourne liste de 10 features : `[banker_ratio, player_ratio, current_streak_length/10.0, current_streak_type, alternation_ratio, volatility, trend_strength, momentum, pattern_strength, normalized_round]`
      * **GESTION ERREURS :** Capture exceptions avec fallback vers `[default_feature_value] * 10` où `default_feature_value = getattr(self.config, 'default_feature_value', 0.5)`
    - RETOUR : List[float] - Vecteur de 10 features normalisées pour calculateur confiance
    - UTILITÉ : Optimise features pour analyse patterns et confiance manches consécutives avec protection complète

23. _extract_pattern_key.txt (ConsecutiveConfidenceCalculator._extract_pattern_key - Extraction clé pattern)
    - Lignes 1429-1440 dans hbp.py (12 lignes)
    - FONCTION : Extrait clé de pattern unique à partir du vecteur de features avec arrondi intelligent pour regroupement
    - PARAMÈTRES :
      * self - Instance de la classe ConsecutiveConfidenceCalculator
      * features_vector (List[float]) - Vecteur de features pour extraction pattern
    - FONCTIONNEMENT DÉTAILLÉ :
      * **INITIALISATION LISTE :** Crée `key_parts = []` pour accumulation des composants de clé
      * **PARCOURS FEATURES :** Itère `for i, feature in enumerate(features_vector[:5]):` pour utiliser les 5 premières features les plus importantes
      * **ARRONDI ADAPTATIF :** Applique arrondi différencié selon type de feature :
        - **RATIOS (indices 0,1,2,3) :** Utilise `round(feature, 1)` pour 1 décimale de précision sur ratios
        - **AUTRES FEATURES (index 4+) :** Utilise `round(feature)` pour 0 décimale sur features non-ratio
      * **FORMATAGE COMPOSANT :** Crée `key_parts.append(f"{i}:{round(feature, 1)}")` ou `key_parts.append(f"{i}:{round(feature)}")` selon type
      * **ASSEMBLAGE CLÉ :** Retourne `"|".join(key_parts)` pour clé unique formatée comme "0:0.7|1:0.3|2:2.0|3:1|4:0"
      * **REGROUPEMENT INTELLIGENT :** L'arrondi permet de regrouper patterns similaires pour statistiques robustes
    - RETOUR : str - Clé pattern unique formatée pour indexation dictionnaire
    - UTILITÉ : Identification et regroupement patterns récurrents avec granularité adaptée pour analyse confiance consécutive

24. _update_pattern_counts.txt (HybridBaccaratPredictor._update_pattern_counts - MAJ compteurs patterns)
    - Lignes 10769-10776 dans hbp.py (8 lignes)
    - FONCTION : Met à jour les compteurs de motifs basés sur les 4 derniers coups avec protection thread-safe
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * last_outcome (str) - Résultat du dernier coup ('player' ou 'banker')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION LONGUEUR :** Vérifie `if len(self.sequence) >= 4:` pour s'assurer d'avoir suffisamment de données
      * **EXTRACTION PATTERN :** Crée `pattern = tuple(self.sequence[-4:])` incluant le dernier coup pour motif de 4 éléments
      * **DÉFINITION CLÉ :** Utilise `dict_key = last_outcome` comme clé de dictionnaire pour indexation
      * **MISE À JOUR COMPTEUR :** Si `dict_key in self.pattern_counts:`, incrémente `self.pattern_counts[dict_key][pattern] += 1`
      * **PROTECTION THREAD :** DOIT être appelée avec `self.sequence_lock` déjà acquis pour éviter conditions de course
      * **STRUCTURE DONNÉES :** Maintient dictionnaire imbriqué {outcome: {pattern: count}} pour statistiques
    - RETOUR : None - Met à jour directement les compteurs internes
    - UTILITÉ : Maintient statistiques patterns à jour pour analyse prédictive et détection motifs récurrents

25. update_recent_data.txt (ConsecutiveConfidenceCalculator.update_recent_data - MAJ données récentes)
    - Lignes 1442-1450 dans hbp.py (9 lignes)
    - FONCTION : Met à jour l'historique récent des recommandations et résultats avec rotation automatique pour maintenir taille fixe
    - PARAMÈTRES :
      * self - Instance de la classe ConsecutiveConfidenceCalculator
      * recommendation (str) - Nouvelle recommandation ('player', 'banker', 'wait')
      * outcome (str) - Résultat réel correspondant ('player', 'banker')
    - FONCTIONNEMENT DÉTAILLÉ :
      * **AJOUT RECOMMANDATION :** Appelle `self.recent_recommendations.append(recommendation)` pour ajouter nouvelle recommandation
      * **AJOUT RÉSULTAT :** Appelle `self.recent_outcomes.append(outcome)` pour ajouter résultat correspondant
      * **VÉRIFICATION TAILLE :** Teste `if len(self.recent_recommendations) > self.max_recent_history:` pour contrôle limite
      * **ROTATION FIFO :** Si dépassement, supprime plus ancien avec `self.recent_recommendations.pop(0)` et `self.recent_outcomes.pop(0)`
      * **SYNCHRONISATION LISTES :** Maintient correspondance exacte entre recommandations et résultats via suppression simultanée
      * **HISTORIQUE LIMITÉ :** Utilise `self.max_recent_history` (défaut: 50) pour limiter mémoire et focus sur données récentes
    - RETOUR : None - Met à jour directement les listes internes
    - UTILITÉ : Maintient historique récent optimisé pour calculs de ratios et statistiques avec rotation automatique FIFO

26. _get_cached_lgbm_prediction.txt (HybridBaccaratPredictor._get_cached_lgbm_prediction - Prédiction LGBM cachée)
    - Lignes 8227-8296 dans hbp.py (70 lignes)
    - FONCTION : Récupère prédiction LGBM depuis cache ou calcule si nécessaire avec système de cache double (deque + dict)
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * lgbm_feat (Optional[List[float]]) - Vecteur de features pour prédiction LGBM
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION FEATURES :** Vérifie `if lgbm_feat is None:` avec retour `{'player': 0.5, 'banker': 0.5}` par défaut
      * **CONVERSION CLÉ :** Convertit `feat_tuple = tuple(lgbm_feat)` pour utilisation comme clé dictionnaire
      * **INITIALISATION CACHE DICT :** Si `not hasattr(self, 'lgbm_cache_dict'):`, convertit cache deque existant en dictionnaire avec `for feat, pred in self.lgbm_cache: self.lgbm_cache_dict[tuple(feat)] = pred`
      * **RECHERCHE CACHE :** Vérifie `if feat_tuple in self.lgbm_cache_dict:` pour hit cache
      * **HIT CACHE :** Si trouvé, récupère `cached_pred = self.lgbm_cache_dict[feat_tuple]`, appelle `self._update_prediction_progress()` et retourne prédiction
      * **MISS CACHE :** Si non trouvé, appelle `self._update_prediction_progress()` puis `lgbm_pred = self.predict_with_lgbm(lgbm_feat)`
      * **MISE À JOUR CACHE :** Ajoute au dictionnaire `self.lgbm_cache_dict[feat_tuple] = lgbm_pred` et à la deque `self.lgbm_cache.append((lgbm_feat, lgbm_pred))`
      * **GESTION TAILLE :** Récupère `max_cache_size = getattr(self.config, 'lgbm_cache_max_size', 1000)` et si `len(self.lgbm_cache) > max_cache_size:`, supprime plus ancien avec `oldest_feat, _ = self.lgbm_cache.popleft()` et `del self.lgbm_cache_dict[tuple(oldest_feat)]`
      * **GESTION ERREURS :** Capture `NotFittedError` avec logging adaptatif selon phase (training/optuna) et `Exception` générale avec logging complet
      * **MESURE PERFORMANCE :** Utilise `start_time = time.time()` et `elapsed = time.time() - start_time` pour monitoring temps exécution
    - RETOUR : Dict[str, float] - Probabilités {'player': float, 'banker': float} depuis cache ou calcul
    - UTILITÉ : Optimisation performance avec cache double (deque FIFO + dict O(1)) pour éviter recalculs coûteux LGBM

27. prepare_training_data.txt (HybridBaccaratPredictor.prepare_training_data - Préparation données entraînement)
    - Lignes 4137-4269 dans hbp.py (133 lignes)
    - FONCTION : Prépare les données d'entraînement via BaccaratSequenceManager avec échantillonnage et validation complète
    - PARAMÈTRES :
      * self - Instance de la classe HybridBaccaratPredictor
      * force_use_historical (bool, optionnel) - Force utilisation données historiques (défaut: False)
      * max_games (Optional[int]) - Limite nombre de jeux à traiter pour optimisation mémoire
      * sampling_fraction (Optional[float]) - Fraction d'échantillonnage des données (0.0-1.0)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **LECTURE CONFIG :** Récupère `min_target_idx = getattr(self.config, 'min_target_hand_index_training', 30)`, `lstm_seq_len_cfg`, `lstm_feat_count_cfg`, `lgbm_feat_count_cfg`
      * **VALIDATION HISTORIQUE :** Vérifie `if not self.loaded_historical or not self.historical_data` avec retour (None×8) si échec
      * **ÉCHANTILLONNAGE :** Appelle `data_to_process, sampling_applied_info = self._apply_data_sampling(original_historical_data, max_games, sampling_fraction)`
      * **MANAGER SÉQUENCES :** Instancie `BaccaratSequenceManager(sequence_length=lstm_seq_len_cfg, min_target_hand_index=min_target_idx, hybrid_feature_creator=self.create_hybrid_features, lgbm_feature_count=lgbm_feat_count_cfg, lstm_seq_len=lstm_seq_len_cfg, lstm_feature_count=lstm_feat_count_cfg)`
      * **GÉNÉRATION DONNÉES :** Appelle `manager_results = manager.prepare_data_for_model(data_to_process)` qui retourne tuple de 5 éléments
      * **UNPACK RÉSULTATS :** Extrait `X_lgbm_all, y_labels_all, X_lstm_all, list_of_all_prefix_sequences, list_of_all_origins = manager_results`
      * **VALIDATION ÉCHANTILLONS :** Vérifie `final_num_samples = X_lgbm_all.shape[0]` contre `min_samples_required = getattr(self.config, 'min_samples_for_training', 100)`
      * **CALCUL POIDS :** Génère `sample_weights_all = self._calculate_sample_weights(list_of_all_origins, final_num_samples)` avec decay factor
      * **SPLIT TEMPOREL :** Crée `train_indices, val_indices = self._create_temporal_split(X_lgbm_all)` via TimeSeriesSplit
      * **VALIDATION SHAPES :** Appelle `(is_valid, message) = self._validate_data_shapes(X_lgbm_all, y_labels_all, X_lstm_all, sample_weights_all, list_of_all_prefix_sequences, list_of_all_origins, final_num_samples)`
      * **LOGGING FINAL :** Enregistre `logger_instance.info(f"_prepare_training_data: Succès! {final_num_samples} échantillons préparés")`
    - RETOUR : Tuple[8] - (X_lgbm, y_labels, X_lstm, sample_weights, train_indices, val_indices, sequences, origins) ou (None×8) si erreur
    - UTILITÉ : Pipeline complet de préparation données avec BaccaratSequenceManager, échantillonnage intelligent, et validation robuste pour entraînement ML
