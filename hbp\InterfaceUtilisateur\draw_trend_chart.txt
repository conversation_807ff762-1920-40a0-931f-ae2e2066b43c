# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 11742 à 11826
# Type: Méthode de la classe HybridBaccaratPredictor

    def draw_trend_chart(self) -> None:
        """Dessine le graphique des tendances des probabilités Player/Banker."""
        # Vérifier que les éléments UI et les couleurs MPL existent
        if not hasattr(self, 'ax') or not hasattr(self, 'canvas') or \
           not hasattr(self, 'bg_color_mpl') or not hasattr(self, 'fg_color_mpl'):
           logger.warning("draw_trend_chart: UI ou couleurs MPL non initialisées.")
           return

        # Utiliser directement les couleurs FIXES MPL stockées dans self
        bg_color_mpl = self.bg_color_mpl
        fg_color_mpl = self.fg_color_mpl

        self.ax.clear()
        self.ax.set_facecolor(bg_color_mpl) # Appliquer fond fixe

        with self.sequence_lock: # Accès à l'historique
             history = self.prediction_history[:]

        if len(history) < 2:
            self.ax.text(0.5, 0.5, 'Pas assez de données pour le graphique',
                         horizontalalignment='center', verticalalignment='center',
                         transform=self.ax.transAxes, color=fg_color_mpl) # Couleur texte fixe
            # Appliquer reset visuel des axes/spines avec couleurs fixes
            self.ax.set_xlabel('')
            self.ax.set_ylabel('')
            self.ax.set_title('')
            self.ax.set_xticks([])
            self.ax.set_yticks([])
            self.ax.spines['bottom'].set_color(fg_color_mpl)
            self.ax.spines['left'].set_color(fg_color_mpl)
            self.ax.spines['top'].set_color(bg_color_mpl)
            self.ax.spines['right'].set_color(bg_color_mpl)
            try:
                self.canvas.draw_idle()
            except Exception as e_draw:
                 logger.error(f"Erreur draw_idle (pas assez de données): {e_draw}")
            return

        # Afficher les N derniers points
        n_points = min(50, len(history))
        plot_data = history[-n_points:]
        x_axis = range(max(0, len(history) - n_points), len(history))
        player_probs = [p.get('player', 0.5) for p in plot_data]
        banker_probs = [p.get('banker', 0.5) for p in plot_data]
        confidence = [1.0 - p.get('uncertainty', 0.5) for p in plot_data]

        # Dessiner les lignes
        self.ax.plot(x_axis, player_probs, label='Player Prob.', color='#0066CC', linewidth=1.5, marker='.', markersize=3)
        self.ax.plot(x_axis, banker_probs, label='Banker Prob.', color='#D83B01', linewidth=1.5, marker='.', markersize=3)
        self.ax.plot(x_axis, confidence, label='Confiance (1-Incert.)', color='green', linestyle=':', linewidth=1.0)
        self.ax.axhline(0.5, color='grey', linestyle='--', linewidth=0.8, label='50%')

        # Configuration des axes et du titre (utilise couleurs FIXES MPL)
        self.ax.set_ylim(0, 1)
        if n_points > 1: self.ax.set_xlim(min(x_axis), max(x_axis))
        elif n_points == 1: self.ax.set_xlim(min(x_axis) - 0.5, max(x_axis) + 0.5)

        self.ax.set_ylabel('Probabilité / Confiance', color=fg_color_mpl)
        self.ax.set_xlabel('Indice de Manche', color=fg_color_mpl)
        self.ax.set_title('Tendance Récente des Prédictions', color=fg_color_mpl, fontsize=10)
        self.ax.tick_params(axis='x', colors=fg_color_mpl)
        self.ax.tick_params(axis='y', colors=fg_color_mpl)
        self.ax.spines['bottom'].set_color(fg_color_mpl)
        self.ax.spines['left'].set_color(fg_color_mpl)
        self.ax.spines['top'].set_color(bg_color_mpl)
        self.ax.spines['right'].set_color(bg_color_mpl)

        # Légende (utilise couleurs FIXES MPL)
        try:
            legend = self.ax.legend(loc='best', fontsize=8,
                                     facecolor=bg_color_mpl,
                                     edgecolor=fg_color_mpl)
            for text in legend.get_texts():
                text.set_color(fg_color_mpl)
        except Exception as e_legend:
             logger.error(f"Erreur lors de la création de la légende: {e_legend}")

        # Grille légère (utilise couleur FIXE MPL)
        self.ax.grid(True, linestyle=':', linewidth=0.5, color=fg_color_mpl, alpha=0.3)

        # Redessiner le canvas
        try:
             self.canvas.draw_idle()
        except Exception as e:
             logger.error(f"Erreur lors du dessin du graphique final: {e}")