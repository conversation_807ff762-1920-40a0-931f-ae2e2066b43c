# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1722 à 1729
# Type: Méthode de la classe ThreadedOptimizer

    def is_optimization_running(self):
        """
        Vérifie si l'optimisation est en cours d'exécution.

        Returns:
            bool: True si l'optimisation est en cours d'exécution, False sinon
        """
        return self.is_running