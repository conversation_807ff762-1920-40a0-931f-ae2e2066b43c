# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\models.py
# Lignes: 72 à 118
# Type: Méthode de la classe EnhancedLSTMModel
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, input_size, hidden_dim, output_size, num_layers=1, dropout_prob=0.2, bidirectional=False,
                 use_attention=True, use_residual=True, dropout_input=0.1, dropout_hidden=0.2, dropout_output=0.15):
        super(EnhancedLSTMModel, self).__init__()
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.bidirectional = bidirectional
        self.input_size = input_size
        self.use_attention = use_attention
        self.use_residual = use_residual

        # Normalisation de l'entrée pour stabiliser l'entraînement et réduire la val_loss
        self.input_norm = nn.LayerNorm(input_size)

        # Dropout spécifique pour l'entrée (modéré pour permettre un bon flux d'information)
        self.input_dropout = nn.Dropout(dropout_input)

        # LSTM principal avec dropout entre les couches
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout_hidden if num_layers > 1 else 0,
            bidirectional=bidirectional
        )

        # Dimension de sortie du LSTM (x2 si bidirectionnel)
        lstm_output_dim = hidden_dim * 2 if bidirectional else hidden_dim

        # Mécanisme d'attention pour se concentrer sur les séquences importantes
        if use_attention:
            self.attention = nn.Sequential(
                nn.Linear(lstm_output_dim, lstm_output_dim // 2),
                nn.Tanh(),
                nn.Linear(lstm_output_dim // 2, 1)
            )

        # Normalisation de couche pour stabiliser l'entraînement
        self.layer_norm = nn.LayerNorm(lstm_output_dim)

        # Couches supplémentaires pour augmenter la capacité du modèle
        self.fc1 = nn.Linear(lstm_output_dim, lstm_output_dim)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(dropout_output)

        # Couche de sortie finale
        self.fc2 = nn.Linear(lstm_output_dim, output_size)