# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 557 à 620
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

    def calculate_performance_based_confidence(self, features, round_num):
        """
        Calcule un score de confiance basé sur les performances d'entraînement.
        Version améliorée qui prend en compte plus de facteurs pour une meilleure précision.

        Args:
            features: Vecteur de features pour la position actuelle
            round_num: Numéro de la manche actuelle

        Returns:
            float: Score de confiance entre 0 et 1
        """
        # Extraire la clé de pattern
        pattern_key = self._extract_pattern_key(features)

        # Récupérer les statistiques pour ce pattern
        pattern_stats = self.pattern_stats.get(pattern_key, {"total": 0, "success": 0, "consecutive_lengths": [], "max_consecutive": 0})

        # Calculer le taux de succès pour ce pattern
        total = pattern_stats["total"]
        success = pattern_stats["success"]
        success_rate = success / total if total >= self.min_occurrences else 0.5

        # Calculer le facteur d'occurrence (plus le pattern a été vu souvent, plus la confiance est élevée)
        occurrence_factor = min(self.max_occurrence_factor, 1.0 + (total / self.occurrence_factor_divisor))

        # Calculer le facteur de séquence consécutive (plus les séquences consécutives sont longues, plus la confiance est élevée)
        consecutive_lengths = pattern_stats["consecutive_lengths"]
        avg_consecutive_length = sum(consecutive_lengths) / len(consecutive_lengths) if consecutive_lengths else 1.0
        max_consecutive_length = pattern_stats.get("max_consecutive", 0)

        # Utiliser à la fois la moyenne et le maximum des séquences consécutives
        consecutive_factor = min(
            self.max_consecutive_factor,
            1.0 + (avg_consecutive_length / self.consecutive_factor_divisor) + (max_consecutive_length / (self.consecutive_factor_divisor * 2))
        )

        # Calculer le facteur de fin de partie (donner plus de poids aux manches 31-60)
        late_game_factor = self.late_game_factor if self.target_round_min <= round_num <= self.target_round_max else 1.0

        # Bonus supplémentaire pour les manches au milieu de la plage cible (où l'objectif 1 est le plus important)
        if self.target_round_min <= round_num <= self.target_round_max:
            # Position relative dans la plage cible (0 au début, 1 à la fin)
            relative_pos = (round_num - self.target_round_min) / (self.target_round_max - self.target_round_min)

            # Bonus en forme de cloche (maximum au milieu de la plage)
            bell_curve_bonus = 1.0 + 0.2 * (1.0 - 4.0 * (relative_pos - 0.5) ** 2)
            late_game_factor *= bell_curve_bonus

        # Bonus pour les séquences consécutives longues
        sequence_bonus = 1.0
        if max_consecutive_length >= self.sequence_bonus_threshold:
            # Bonus proportionnel à la longueur au-delà du seuil
            sequence_bonus += self.sequence_bonus_factor * (max_consecutive_length - self.sequence_bonus_threshold)

        # Calculer le score de confiance final avec pondération des composantes
        confidence = (
            self.success_rate_weight * success_rate +
            self.consecutive_length_weight * (consecutive_factor / self.max_consecutive_factor) +
            self.pattern_frequency_weight * (occurrence_factor / self.max_occurrence_factor)
        ) * late_game_factor * sequence_bonus

        # Limiter la confiance entre 0 et 1
        return min(1.0, max(0.0, confidence))