# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 3330 à 3457
# Type: Méthode de la classe OptunaOptimizer

    def _create_study(self, direction="maximize", study_name=None, sampler=None, use_lhs=False, n_lhs_points=None, search_space=None):
        """
        Crée une étude Optuna avec ajustement dynamique des plages et support pour Latin Hypercube Sampling.

        Args:
            direction: Direction de l'optimisation ("maximize" ou "minimize")
            study_name: Nom de l'étude
            sampler: Échantillonneur Optuna à utiliser
            use_lhs: Utiliser Latin Hypercube Sampling pour générer des points initiaux
            n_lhs_points: Nombre de points LHS à générer
            search_space: Espace de recherche pour LHS

        Returns:
            optuna.study.Study: L'étude Optuna créée
        """
        # Créer un callback personnalisé pour simplifier l'affichage des essais
        class SimplifiedTrialPrinter:
            """
            Callback personnalisé pour simplifier l'affichage des essais Optuna.
            Supprime l'affichage des paramètres dans les messages de fin d'essai.
            """
            def __init__(self):
                pass

            def __call__(self, study, trial):
                # Ce callback ne fait rien, le filtrage est géré par OptunaMessageFilter
                pass

            def restore_print(self):
                # Méthode vide pour compatibilité
                pass

        # Créer l'étude Optuna
        if sampler is None:
            sampler = optuna.samplers.TPESampler(seed=42)

        # Créer le callback pour simplifier l'affichage des essais
        simplified_printer = SimplifiedTrialPrinter()

        study = optuna.create_study(direction=direction, sampler=sampler)

        # Définir le nom de l'étude si fourni
        if study_name:
            study.study_name = study_name

        # Ajuster les plages pour cette étude
        try:
            self.range_adjuster.adjust_ranges_for_study(study)
            logger.info(f"Plages ajustées pour l'étude {study.study_name}")
        except Exception as e:
            logger.error(f"Erreur lors de l'ajustement des plages: {e}")
            import traceback
            logger.error(traceback.format_exc())

        # Générer des points initiaux avec Latin Hypercube Sampling si demandé
        if use_lhs and n_lhs_points and search_space:
            try:
                import numpy as np

                logger.warning(f"Génération de {n_lhs_points} points initiaux avec Latin Hypercube Sampling")

                # Extraire les dimensions de l'espace de recherche
                dimensions = {}
                for param_name, param_spec in search_space.items():
                    if isinstance(param_spec, (list, tuple)) and len(param_spec) >= 3:
                        param_type = param_spec[0]
                        if param_type in ['int', 'float']:
                            low, high = param_spec[1], param_spec[2]
                            dimensions[param_name] = (param_type, low, high)
                        elif param_type == 'categorical':
                            choices = param_spec[1:]
                            dimensions[param_name] = (param_type, choices)

                # Générer des points LHS pour les dimensions continues
                continuous_dims = {name: spec for name, spec in dimensions.items()
                                  if spec[0] in ['int', 'float']}
                n_continuous_dims = len(continuous_dims)

                if n_continuous_dims > 0:
                    # Générer une matrice LHS
                    points = np.zeros((n_lhs_points, n_continuous_dims))

                    # Pour chaque dimension, générer des segments équidistants
                    for i in range(n_continuous_dims):
                        segments = np.linspace(0, 1, n_lhs_points + 1)
                        # Prendre un point aléatoire dans chaque segment
                        points_1d = np.random.uniform(segments[:-1], segments[1:])
                        # Mélanger les points pour cette dimension
                        np.random.shuffle(points_1d)
                        # Ajouter à la matrice de résultats
                        points[:, i] = points_1d

                    # Convertir les points LHS en essais Optuna
                    continuous_names = list(continuous_dims.keys())

                    for i in range(n_lhs_points):
                        # Créer un dictionnaire de paramètres pour cet essai
                        params = {}

                        # Ajouter les paramètres continus
                        for j, name in enumerate(continuous_names):
                            dim_type, low, high = continuous_dims[name]
                            # Convertir la valeur normalisée [0,1] en valeur réelle
                            value = low + points[i, j] * (high - low)
                            # Arrondir à l'entier si nécessaire
                            if dim_type == 'int':
                                value = int(round(value))
                            params[name] = value

                        # Ajouter des valeurs aléatoires pour les paramètres catégoriels
                        for name, dim_info in dimensions.items():
                            if isinstance(dim_info, list) and len(dim_info) > 0 and dim_info[0] == 'categorical':
                                params[name] = np.random.choice(dim_info[1])  # dim_info[1] contient les choix
                            elif isinstance(dim_info, tuple) and len(dim_info) == 2 and dim_info[0] == 'categorical':
                                params[name] = np.random.choice(dim_info[1])  # dim_info[1] contient les choix

                        # Enqueuer l'essai
                        study.enqueue_trial(params)

                    logger.warning(f"{n_lhs_points} points LHS générés et ajoutés à l'étude")
                else:
                    logger.warning("Aucune dimension continue trouvée pour LHS, utilisation de l'échantillonnage standard")
            except Exception as e:
                logger.error(f"Erreur lors de la génération des points LHS: {e}")
                import traceback
                logger.error(traceback.format_exc())

        return study