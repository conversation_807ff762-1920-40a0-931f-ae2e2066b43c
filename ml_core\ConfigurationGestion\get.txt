# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 90 à 105
# Type: Méthode de la classe ConfigManager

    def get(self, key: str, default: Any = None, namespace: str = 'default') -> Any:
        """
        Récupère une valeur de configuration.

        Args:
            key: Clé de configuration
            default: Valeur par défaut si la clé n'existe pas
            namespace: Espace de noms pour la configuration

        Returns:
            La valeur de configuration ou la valeur par défaut
        """
        if namespace not in self._config:
            return default

        return self._config[namespace].get(key, default)