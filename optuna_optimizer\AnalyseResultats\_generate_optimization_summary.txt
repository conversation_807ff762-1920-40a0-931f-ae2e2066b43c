# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 4678 à 4837
# Type: Méthode de la classe OptunaOptimizer

    def _generate_optimization_summary(self, best_trial, adapted_params):
        """
        Génère et affiche un résumé détaillé des performances du meilleur essai d'optimisation.

        Args:
            best_trial: Dictionnaire ou objet Trial contenant les informations sur le meilleur essai
            adapted_params: Dictionnaire contenant les paramètres adaptés pour l'entraînement complet
        """
        logger.warning("\n" + "=" * 80)
        logger.warning("                     RÉSUMÉ FINAL DE L'OPTIMISATION")
        logger.warning("=" * 80 + "\n")

        # Vérifier si best_trial est un dictionnaire ou un objet Trial
        if hasattr(best_trial, 'number') and not isinstance(best_trial, dict):
            # C'est un objet Trial d'Optuna
            trial_number = best_trial.number

            # Extraire les métriques des attributs utilisateur
            metrics = best_trial.user_attrs.get('metrics', {})
            weighted_score = best_trial.value
            # Utiliser les noms standardisés des métriques depuis config.py
            from config import PredictorConfig
            precision_non_wait = metrics.get(PredictorConfig.METRIC_PRECISION_NON_WAIT, 0.0)
            precision_wait = metrics.get(PredictorConfig.METRIC_PRECISION_WAIT, 0.0)
            wait_ratio = metrics.get(PredictorConfig.METRIC_WAIT_RATIO, 0.0)
            recommendation_rate = metrics.get(PredictorConfig.METRIC_RECOMMENDATION_RATE, 0.0)
            wait_count = metrics.get('wait_count', 0)
            non_wait_count = metrics.get('non_wait_count', 0)
            max_consecutive_valid = metrics.get(PredictorConfig.METRIC_MAX_CONSECUTIVE, 'N/A')
            avg_consecutive_valid = metrics.get('avg_max_consecutive', 'N/A')
            current_consecutive_valid = metrics.get('current_consecutive_valid', 'N/A')
            recovery_rate = metrics.get(PredictorConfig.METRIC_RECOVERY_RATE, 'N/A')
            wait_efficiency = metrics.get(PredictorConfig.METRIC_WAIT_EFFICIENCY, 'N/A')
            balance_score = metrics.get('balance_score', 'N/A')

            logger.warning(f"MEILLEUR ESSAI SÉLECTIONNÉ : #{trial_number}\n")
        else:
            # C'est un dictionnaire
            trial_number = best_trial.get('trial_number', 'inconnu')
            weighted_score = best_trial.get('weighted_score', 0.0)
            precision_non_wait = best_trial.get('precision_non_wait', 0.0)
            precision_wait = best_trial.get('precision_wait', 0.0)
            wait_ratio = best_trial.get('wait_ratio', 0.0)
            recommendation_rate = best_trial.get('recommendation_rate', 0.0)
            wait_count = best_trial.get('wait_count', 0)
            non_wait_count = best_trial.get('non_wait_count', 0)
            max_consecutive_valid = best_trial.get('max_consecutive_valid', 'N/A')
            avg_consecutive_valid = best_trial.get('avg_consecutive_valid', 'N/A')
            current_consecutive_valid = best_trial.get('current_consecutive_valid', 'N/A')
            recovery_rate = best_trial.get('recovery_rate', 'N/A')
            wait_efficiency = best_trial.get('wait_efficiency', 'N/A')
            balance_score = best_trial.get('balance_score', 'N/A')

            logger.warning(f"MEILLEUR ESSAI SÉLECTIONNÉ : #{trial_number}\n")

        # Performances principales
        logger.warning("PERFORMANCES PRINCIPALES :")
        logger.warning(f"  • Score global pondéré        : {weighted_score:.4f}")
        logger.warning(f"  • Précision NON-WAIT          : {precision_non_wait:.4f}")
        logger.warning(f"  • Précision WAIT              : {precision_wait:.4f}")
        logger.warning(f"  • Taux de recommandation      : {recommendation_rate:.4f}\n")

        # Recommandations NON-WAIT
        logger.warning("RECOMMANDATIONS NON-WAIT :")
        total_recommendations = non_wait_count + wait_count

        # Calculer les métriques de validité des NON-WAIT
        valid_non_wait_count = int(non_wait_count * precision_non_wait)
        valid_non_wait_percentage = precision_non_wait * 100

        # Si les métriques de séquences ne sont pas disponibles, utiliser des valeurs estimées
        if max_consecutive_valid == 'N/A':
            # Estimation basée sur la précision et le nombre de recommandations
            if precision_non_wait > 0.7 and non_wait_count > 10:
                max_consecutive_valid = "~" + str(int(precision_non_wait * 5))
            else:
                max_consecutive_valid = "Non disponible"

        if avg_consecutive_valid == 'N/A':
            if precision_non_wait > 0:
                avg_consecutive_valid = "~" + str(round(1 / (1 - precision_non_wait) if precision_non_wait < 1 else 5, 1))
            else:
                avg_consecutive_valid = "Non disponible"

        logger.warning(f"  • Total NON-WAIT              : {non_wait_count}")
        logger.warning(f"  • NON-WAIT valides            : {valid_non_wait_count}")
        logger.warning(f"  • % NON-WAIT valides          : {valid_non_wait_percentage:.2f}%")
        logger.warning(f"  • Séquences valides max       : {max_consecutive_valid}")
        logger.warning(f"  • Séquences valides moyenne   : {avg_consecutive_valid}")
        if current_consecutive_valid != 'N/A':
            logger.warning(f"  • Longueur séquence actuelle  : {current_consecutive_valid}\n")
        else:
            logger.warning("\n")

        # Statistiques de prédiction
        logger.warning("STATISTIQUES DE PRÉDICTION :")
        confidence_factor = 1.0 - (1.0 / (1.0 + 0.1 * total_recommendations))

        logger.warning(f"  • Ratio WAIT                  : {wait_ratio:.4f}")
        logger.warning(f"  • Recommandations WAIT        : {wait_count}")
        logger.warning(f"  • Total recommandations       : {total_recommendations}")
        logger.warning(f"  • Facteur de confiance        : {confidence_factor:.4f}\n")

        # Métriques avancées
        logger.warning("MÉTRIQUES AVANCÉES :")
        late_game_precision = precision_non_wait

        # Si les métriques avancées ne sont pas disponibles, utiliser des estimations
        if recovery_rate == 'N/A':
            recovery_rate = "Non disponible"
        if wait_efficiency == 'N/A':
            wait_efficiency = "~" + str(round(precision_wait * (1 - wait_ratio) / max(0.01, wait_ratio), 2)) if precision_wait > 0 else "Non disponible"
        if balance_score == 'N/A':
            balance_score = "~" + str(round((precision_non_wait * (1 - wait_ratio) + precision_wait * wait_ratio) / 2, 4))

        logger.warning(f"  • Taux de récupération        : {recovery_rate}")
        logger.warning(f"  • Efficacité WAIT             : {wait_efficiency}")
        logger.warning(f"  • Score d'équilibre           : {balance_score}")
        logger.warning(f"  • Précision fin de partie     : {late_game_precision:.4f}\n")

        # Hyperparamètres optimaux
        logger.warning("HYPERPARAMÈTRES OPTIMAUX ADAPTÉS POUR L'ENTRAÎNEMENT COMPLET :")

        # Liste des paramètres clés à afficher en priorité
        key_params = [
            'min_confidence_for_recommendation',
            'weight_lstm', 'weight_lgbm', 'weight_markov',
            'consecutive_adjustment_factor', 'consecutive_focus_factor',
            'wait_ratio_tolerance', 'error_pattern_threshold',
            'transition_uncertainty_threshold', 'wait_optimizer_confidence_threshold'
        ]

        # Afficher d'abord les paramètres clés
        for param_name in key_params:
            if param_name in adapted_params:
                param_value = adapted_params[param_name]
                logger.warning(f"  • {param_name.ljust(35)}: {param_value}")

        # Afficher quelques autres paramètres importants
        other_params_shown = 0
        for param_name, param_value in adapted_params.items():
            if param_name not in key_params and not param_name.startswith('_') and not isinstance(param_value, (list, dict)):
                if other_params_shown < 5:  # Limiter à 5 paramètres supplémentaires
                    logger.warning(f"  • {param_name.ljust(35)}: {param_value}")
                    other_params_shown += 1

        logger.warning(f"  • [+ {len(adapted_params) - len(key_params) - other_params_shown} autres paramètres...]\n")

        # Recommandations
        logger.warning("RECOMMANDATIONS :")
        lstm_epochs = adapted_params.get('lstm_epochs', 10)
        batch_size = adapted_params.get('batch_size', 32)
        batch_size_factor = adapted_params.get('batch_size_factor', 1.0)

        logger.warning(f"  • Entraînement complet recommandé avec {lstm_epochs} époques")
        logger.warning(f"  • Utilisation de batch_size={batch_size} (facteur={batch_size_factor})")
        logger.warning(f"  • Ratio WAIT cible: {wait_ratio:.4f}")
        logger.warning(f"  • Seuil de confiance minimum: {adapted_params.get('min_confidence_for_recommendation', 'N/A')}")

        logger.warning("\n" + "=" * 80)