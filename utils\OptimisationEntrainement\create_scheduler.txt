# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 148 à 221
# Type: Méthode

def create_scheduler(optimizer, config, train_loader, epochs):
    """
    Crée un scheduler de learning rate en fonction de la configuration
    Optimisé pour fonctionner par époque plutôt que par batch pour accélérer l'optimisation Optuna

    Args:
        optimizer: Optimiseur PyTorch
        config: Configuration
        train_loader: DataLoader d'entraînement
        epochs: Nombre d'époques

    Returns:
        Scheduler et type de scheduler ('batch' ou 'epoch')
    """
    # Forcer l'utilisation du scheduler par époque pour Optuna
    is_optuna_run = getattr(config, 'is_optuna_run', False)

    # Si c'est un run Optuna, utiliser toujours le scheduler par époque
    if is_optuna_run:
        # ReduceLROnPlateau - réduit le LR quand la métrique stagne
        scheduler = ReduceLROnPlateau(
            optimizer,
            mode='min',  # Minimiser la métrique (val_loss)
            factor=0.5,  # Réduire le LR de 50% (moins agressif pour permettre une meilleure exploration)
            patience=2,  # Attendre 2 époques sans amélioration (moins agressif pour Optuna)
            min_lr=getattr(config, 'lstm_learning_rate', 5e-5) / 100,  # LR minimal moins bas pour éviter une convergence trop lente
            threshold=0.002,  # Seuil légèrement plus élevé pour être moins sensible aux fluctuations
            threshold_mode='rel',  # Mode relatif pour le seuil
            cooldown=1,  # Période de refroidissement après chaque réduction
            verbose=True
        )
        return scheduler, 'epoch'  # Ce scheduler s'applique à chaque époque

    # Pour les runs normaux (non-Optuna), utiliser le comportement original
    scheduler_type = getattr(config, 'scheduler_type', 'plateau')
    total_steps = len(train_loader) * epochs

    if scheduler_type == 'one_cycle':
        # OneCycleLR - excellent pour la convergence rapide
        scheduler = OneCycleLR(
            optimizer,
            max_lr=getattr(config, 'lstm_learning_rate', 5e-5) * 10,  # LR max = 10x le LR de base
            total_steps=total_steps,
            pct_start=0.3,  # 30% du temps pour la phase d'échauffement
            div_factor=25.0,  # LR initiale = max_lr/25
            final_div_factor=10000.0,  # LR finale = LR initiale/10000
            anneal_strategy='cos'  # Décroissance en cosinus
        )
        return scheduler, 'batch'  # Ce scheduler s'applique à chaque batch

    elif scheduler_type == 'cosine_warm':
        # Cosine annealing avec redémarrages - bon pour éviter les minima locaux
        scheduler = CosineAnnealingWarmRestarts(
            optimizer,
            T_0=len(train_loader) // 2,  # Redémarrage deux fois par époque pour des cycles encore plus courts
            T_mult=1,  # Maintenir la même longueur de cycle pour une exploration plus régulière
            eta_min=getattr(config, 'lstm_learning_rate', 5e-5) / 1000  # LR minimale encore plus basse
        )
        return scheduler, 'batch'  # Ce scheduler s'applique à chaque batch

    else:  # 'plateau' par défaut
        # ReduceLROnPlateau - réduit le LR quand la métrique stagne
        scheduler = ReduceLROnPlateau(
            optimizer,
            mode='min',  # Minimiser la métrique (val_loss)
            factor=getattr(config, 'scheduler_factor', 0.2),  # Utiliser le facteur de réduction configuré (par défaut 0.2)
            patience=getattr(config, 'scheduler_patience', 1),  # Utiliser la patience configurée (par défaut 1)
            min_lr=getattr(config, 'lstm_learning_rate', 5e-5) / 5000,  # Réduire davantage le LR minimal
            threshold=0.0005,  # Seuil très bas pour détecter les plateaux plus rapidement
            threshold_mode='rel',  # Mode relatif pour le seuil
            cooldown=0,  # Pas de période de refroidissement pour réagir immédiatement
            verbose=True
        )
        return scheduler, 'epoch'  # Ce scheduler s'applique à chaque époque