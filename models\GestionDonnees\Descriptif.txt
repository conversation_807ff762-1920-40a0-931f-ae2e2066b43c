DESCRIPTIF DÉTAILLÉ DES MÉTHODES - GESTION DONNÉES
================================================================================

Ce fichier contient la description détaillée des méthodes de gestion des données du système models.py.

ÉTAT DOCUMENTATION : PREMIÈRE VAGUE FONCTIONNELLE
- **Couverture** : Méthodes de gestion des données documentées
- **Niveau** : Documentation fonctionnelle standardisée
- **Qualité** : 15-25 lignes par méthode minimum

DOMAINE FONCTIONNEL : GESTION DONNÉES
- Gestion du dataset Baccarat
- Chargement et préparation des données
- Optimisations mémoire et performance

================================================================================

1. __init__.txt (BaccaratDataset.__init__ - INITIALISATION DATASET BACCARAT)
   - Lignes 20-53 dans models.py (34 lignes)
   - FONCTION : Initialise un dataset PyTorch optimisé pour les données de séquences Baccarat avec gestion mémoire avancée et validation des étiquettes
   - PARAMÈTRES :
     * self - Instance de BaccaratDataset
     * sequences (np.ndarray) - Tableau numpy des séquences d'entrée
     * targets (np.ndarray) - Tableau numpy des étiquettes cibles (0=Player, 1=Banker)
     * weights (Optional[np.ndarray]) - Poids optionnels pour l'échantillonnage pondéré
     * sequence_positions (Optional[np.ndarray]) - Positions optionnelles des séquences
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ASSERTION** : `assert len(sequences) == len(targets), "Sequences and targets must have the same length"` vérification stricte
     * **CONVERSION SEQUENCES** : `self.sequences = torch.from_numpy(sequences).float().share_memory_()` conversion directe avec partage mémoire
     * **VALIDATION ÉTIQUETTES** : `unique_targets = np.unique(targets)` puis vérification `set(unique_targets).issubset({0, 1})`
     * **EXCEPTION ÉTIQUETTES** : `raise ValueError(f"Invalid target values found: {unique_targets}. Only 0 (Player) and 1 (Banker) are allowed.")` si invalides
     * **CONVERSION TARGETS** : `self.targets = torch.from_numpy(targets).long().share_memory_()` avec type long pour classification
     * **SYSTÈME ZERO-BASED** : Commentaire explicite "0 = Player, 1 = Banker" selon standard PyTorch
     * **FLAGS OPTIONNELS** : `self.has_weights = weights is not None` et `self.has_sequence_positions = sequence_positions is not None`
     * **CONVERSION WEIGHTS** : `if self.has_weights: self.weights = torch.from_numpy(weights).float().share_memory_()`
     * **CONVERSION POSITIONS** : `if self.has_sequence_positions: self.sequence_positions = torch.from_numpy(sequence_positions).long().share_memory_()`
     * **PRÉALLOCATION INDICES** : `self.indices = torch.arange(len(sequences)).share_memory_()` pour accès rapide
     * **OPTIMISATION PERFORMANCE** : Commentaire "Pas de log de diagnostic pour éviter de surcharger les logs"
     * **PARTAGE MÉMOIRE** : `.share_memory_()` sur tous tenseurs pour transfert CPU->GPU efficace
   - RETOUR : None (constructeur)
   - UTILITÉ : Méthode fondamentale pour créer un dataset PyTorch optimisé. Essentielle pour l'entraînement des modèles ML avec données Baccarat. Critique pour les performances et la validation des données.

2. __getitem__.txt (BaccaratDataset.__getitem__ - ACCÈS INDEXÉ AUX DONNÉES)
   - Lignes 58-68 dans models.py (11 lignes)
   - FONCTION : Fournit l'accès indexé ultra-optimisé aux éléments du dataset avec retour conditionnel selon les données disponibles
   - PARAMÈTRES :
     * self - Instance de BaccaratDataset
     * idx (int) - Index de l'élément à récupérer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **COMMENTAIRE PERFORMANCE** : "Retour ultra-optimisé des données sans vérifications supplémentaires"
     * **COMMENTAIRE VECTORISATION** : "Utilisation d'indexation vectorisée pour un accès plus rapide"
     * **CONDITION COMPLÈTE** : `if self.has_weights and self.has_sequence_positions:` test booléen double
     * **RETOUR COMPLET** : `return self.sequences[idx], self.targets[idx], self.weights[idx], self.sequence_positions[idx]` tuple 4 éléments
     * **CONDITION WEIGHTS** : `elif self.has_weights:` test booléen simple
     * **RETOUR WEIGHTS** : `return self.sequences[idx], self.targets[idx], self.weights[idx]` tuple 3 éléments
     * **CONDITION POSITIONS** : `elif self.has_sequence_positions:` test booléen simple
     * **RETOUR POSITIONS** : `return self.sequences[idx], self.targets[idx], self.sequence_positions[idx]` tuple 3 éléments
     * **RETOUR MINIMAL** : `else: return self.sequences[idx], self.targets[idx]` tuple 2 éléments
     * **INDEXATION DIRECTE** : `self.sequences[idx]` accès vectorisé sans vérification bounds
     * **OPTIMISATION ZERO-COPY** : Accès direct aux tenseurs préalloués sans copie mémoire
     * **LOGIQUE CONDITIONNELLE** : Structure if/elif/else pour performance optimale
     * **AUCUNE VALIDATION** : Pas de vérification idx pour vitesse maximale
   - RETOUR : tuple - Tuple variable selon données disponibles (sequences, targets, [weights], [positions])
   - UTILITÉ : Méthode essentielle pour l'interface PyTorch Dataset. Critique pour les performances d'entraînement. Permet l'accès rapide aux données pendant les boucles d'entraînement.

3. __len__.txt (BaccaratDataset.__len__ - TAILLE DU DATASET)
   - Lignes 55-56 dans models.py (2 lignes)
   - FONCTION : Retourne la taille totale du dataset pour l'interface PyTorch Dataset standard
   - PARAMÈTRES :
     * self - Instance de BaccaratDataset
   - FONCTIONNEMENT DÉTAILLÉ :
     * **SIGNATURE MÉTHODE** : `def __len__(self) -> int:` avec type hint explicite
     * **RETOUR DIRECT** : `return len(self.sequences)` appel fonction len() sur tenseur PyTorch
     * **INTERFACE PYTORCH** : Implémente la méthode __len__ requise par l'interface Dataset
     * **PERFORMANCE OPTIMALE** : Accès direct à la propriété length du tenseur sequences
     * **COHÉRENCE GARANTIE** : La taille est cohérente car validée dans __init__ avec assert
     * **UTILISATION DATALOADER** : Permet au DataLoader de connaître la taille pour batching
     * **SIMPLICITÉ MAXIMALE** : Une seule ligne de code pour performance optimale
     * **FIABILITÉ** : Pas de calcul complexe, juste accès à la propriété native du tenseur
     * **STANDARD PYTORCH** : Respecte exactement l'interface attendue par torch.utils.data.Dataset
     * **TYPE HINT** : Annotation `-> int` pour clarté et vérification statique
   - RETOUR : int - Nombre total d'éléments dans le dataset
   - UTILITÉ : Méthode obligatoire pour l'interface PyTorch Dataset. Essentielle pour le fonctionnement des DataLoaders. Permet la gestion automatique des batches et de l'itération.