# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 592 à 602
# Type: Méthode de la classe LSTMMemoryContext
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, model, training=False):
        """
        Initialise le gestionnaire de contexte.

        Args:
            model: Le modèle LSTM à optimiser
            training: Si True, configure le modèle pour l'entraînement, sinon pour l'inférence
        """
        self.model = model
        self.training = training
        self.previous_training_state = None