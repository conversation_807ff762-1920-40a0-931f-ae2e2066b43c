DESCRIPTIF DÉTAILLÉ DES MÉTHODES - DEUXIÈME VAGUE TECHNIQUE
================================================================================

Ce fichier contient la description technique complète de toutes les méthodes du système config.py.

ÉTAT DOCUMENTATION : DEUXIÈME VAGUE COMPLÈTE
- **Couverture** : 100% des méthodes enrichies techniquement
- **Niveau** : Code source réel intégré, détails d'implémentation
- **Qualité** : 25-40 lignes par méthode avec précision technique

STRUCTURE DU SYSTÈME :
- **LoggingMonitoring** : Filtrage et gestion des logs Optuna (2 méthodes)
- **ConfigurationGestion** : Configuration et gestion système (4 méthodes)
- **Anciennesclasses** : Définitions de classes (2 classes)

TOTAL : 8 MÉTHODES ANALYSÉES

================================================================================
SECTION 1 : LOGGINGMONITORING
================================================================================

1. __init__.txt (SilentOptunaFilter.__init__ - INITIALISATION FILTRE LOGGING)
   - Lignes 24-66 dans config.py (43 lignes)
   - FONCTION : Initialise le filtre de logging pour supprimer les messages répétitifs d'Optuna et du système ML, configurant les patterns de filtrage et les compteurs
   - PARAMÈTRES :
     * self - Instance de SilentOptunaFilter
   - FONCTIONNEMENT DÉTAILLÉ :
     * **APPEL PARENT** : `super().__init__()` initialise logging.Filter avec héritage complet
     * **LISTE PATTERNS** : `self.repetitive_patterns = [...]` contient exactement 35 chaînes de caractères
     * **PATTERNS INITIALISATION** : Chaînes exactes "Auto-update désactivé par défaut", "PersistentMarkov initialisé", "Initialisation de l'optimiseur de placement des WAIT"
     * **PATTERNS DIAGNOSTIC** : "DIAGNOSTIC TRAIN:", "DIAGNOSTIC EVAL", "DIAGNOSTIC CONVERSION:", "DIAGNOSTIC DATASET:"
     * **PATTERNS TECHNIQUE** : "Gradient norm élevé:", "Nettoyage GC/CUDA effectué", "Incertitude détaillée:", "Poids: confidence_weight="
     * **PATTERNS MODÈLES** : "Modèle 'calibrated_lgbm' non initialisé. Retour 50/50", "Modèle LSTM non initialisé. Retour 50/50"
     * **PATTERNS CACHE** : "Cache LGBM vidé après annulation", "Incohérence shape features LSTM"
     * **PATTERNS MÉTRIQUES** : "Mise à jour des métriques cibles", "Métriques consécutives:", "Facteur de difficulté calculé"
     * **PATTERNS POIDS** : "Poids LSTM calculés:", "Poids d'échantillons calculés:"
     * **PATTERNS CALLBACK** : "_auto_update_callback: Cette méthode n'est plus utilisée"
     * **PATTERNS ÉCHEC** : "Échec car modèle 'calibrated_lgbm' ou scaler non 'fit'", "Modèle LGBM non entraîné dans"
     * **COMPTEUR DICT** : `self.repetitive_counts = {}` dictionnaire vide pour tracking occurrences par message
     * **STRUCTURE COMPLÈTE** : 35 patterns string exactement définis pour filtrage précis
   - RETOUR : None (constructeur)
   - UTILITÉ : Méthode essentielle pour maintenir des logs propres pendant l'optimisation Optuna. Réduit drastiquement le volume de logs répétitifs. Critique pour la lisibilité des logs et les performances de logging.

2. filter.txt (SilentOptunaFilter.filter - FILTRAGE MESSAGES LOGGING)
   - Lignes 68-118 dans config.py (51 lignes)
   - FONCTION : Filtre les messages de logging selon des règles complexes pour réduire le bruit tout en préservant les informations importantes, avec gestion spéciale des threads Optuna
   - PARAMÈTRES :
     * self - Instance de SilentOptunaFilter
     * record - Objet LogRecord contenant les informations du message de log
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉTECTION THREAD** : `is_optuna_thread = "OptunaOptimization" in threading.current_thread().name` vérification string exacte
     * **EXTRACTION MESSAGE** : `message = record.getMessage()` récupération contenu log formaté
     * **VÉRIFICATION RÉPÉTITION** : `is_repetitive = any(pattern in message for pattern in self.repetitive_patterns)` test inclusion patterns
     * **CORRECTION NIVEAU CRITICAL** : `if "DIAGNOSTIC" in message and record.levelno == logging.CRITICAL:` détection niveau incorrect
     * **RÉTROGRADATION** : `record.levelno = logging.INFO; record.levelname = "INFO"` modification directe attributs LogRecord
     * **FILTRAGE VERBEUX IMMÉDIAT** : Conditions exactes `"DIAGNOSTIC TRAIN:" in message or "Gradient norm élevé:" in message or "Incertitude détaillée" in message`
     * **GESTION PREMIÈRE OCCURRENCE** : `if message not in self.repetitive_counts:` test existence clé dictionnaire
     * **AJOUT NOTE** : `record.msg += " (messages similaires seront filtrés)"` modification message original
     * **INITIALISATION COMPTEUR** : `self.repetitive_counts[message] = 1` création entrée dictionnaire
     * **INCRÉMENTATION** : `self.repetitive_counts[message] += 1` puis `return False` pour filtrer répétitions
     * **PRÉSERVATION WARNING** : `if record.levelno >= logging.WARNING:` test niveau numérique exact
     * **EXCEPTION SPÉCIFIQUE** : `if "Erreur lors de l'optimisation de la mémoire PyTorch" in message: return False` filtrage ciblé
     * **MOTS-CLÉS IMPORTANTS** : Liste exacte `["VIABLE", "ESSAI", "OPTIMISATION", "WAIT", "NON-WAIT", "VAGUE", "Progression", "Epoch", "Val Loss", "Val Accuracy", "Train Loss", "Train Accuracy", "Objectif 1", "Objectif 2", "Score composite", "Early stopping"]`
     * **TEST INCLUSION** : `if any(keyword in message for keyword in important_keywords): return True` préservation prioritaire
     * **FILTRAGE FINAL OPTUNA** : `return not is_optuna_thread` suppression autres logs threads optimisation
   - RETOUR : bool - True pour afficher le message, False pour le filtrer
   - UTILITÉ : Méthode critique pour maintenir des logs lisibles pendant l'optimisation. Équilibre entre réduction du bruit et préservation des informations essentielles. Indispensable pour le debugging efficace d'Optuna.

================================================================================
SECTION 2 : CONFIGURATIONGESTION
================================================================================

3. __init___1.txt (PredictorConfig.__init__ - INITIALISATION CONFIGURATION PRINCIPALE)
   - Lignes 206-939 dans config.py (734 lignes)
   - FONCTION : Initialise une instance de PredictorConfig avec tous les paramètres de configuration du système de prédiction ML, incluant les hyperparamètres pour LSTM, LGBM, Markov et les seuils de décision
   - PARAMÈTRES :
     * self - Instance de PredictorConfig
     * config_overrides (optionnel) - Dictionnaire de surcharges de configuration pour personnaliser les valeurs par défaut
   - FONCTIONNEMENT DÉTAILLÉ :
     * **POIDS MODÈLES EXACTS** : `self.initial_weights: Dict[str, float] = {'lgbm': 0.20, 'lstm': 0.60, 'markov': 0.20}` répartition précise
     * **PARAMÈTRES ENTRAÎNEMENT** : `self.min_rounds_per_game: int = 15`, `self.min_training_samples: int = 500`, `self.early_stopping_patience: int = 8`
     * **BATCH SIZES** : `self.batch_sizes: Dict[str, int] = {'small': 512, 'medium': 1024, 'large': 2048}` tailles exactes
     * **FEATURES LSTM** : `self.lstm_streak_normalization_factor: float = 8.0`, `self.lstm_recent_window_size: int = 4`, `self.lstm_avg_game_length: int = 80`
     * **SEUILS DÉCISION** : `self.decision_threshold_min: float = 0.3`, `self.decision_threshold_max: float = 0.6`, `self.transition_uncertainty_threshold: float = 0.6`
     * **CONFIANCE** : `self.min_confidence_for_recommendation = 0.30`, `self.adaptive_confidence_threshold: bool = True`, `self.confidence_adjustment_step: float = 0.05`
     * **FACTEURS ÉQUILIBRAGE** : `self.wait_bias_factor: float = 0.9`, `self.fallback_penalty_factor: float = 0.14039566669804696`
     * **LSTM HYPERPARAMÈTRES** : `self.lstm_hidden_size: int = 512`, `self.lstm_num_layers: int = 1`, `self.lstm_dropout: float = 0.27`, `self.lstm_learning_rate: float = 8.57e-05`
     * **LGBM HYPERPARAMÈTRES** : `self.lgbm_learning_rate: float = 0.008`, `self.lgbm_max_depth: int = 3`, `self.lgbm_num_leaves: int = 26`, `self.lgbm_n_estimators: int = 109`
     * **MARKOV HYPERPARAMÈTRES** : `self.markov_max_order: int = 1`, `self.markov_smoothing: float = 0.15`, `self.markov_global_weight: float = 0.15`
     * **NORMALISATION** : `self.normalization: str = 'robust_scaler'`, `self.feature_engineering_methods: List[str] = ['temporal_features', 'statistical_moments', 'frequency_domain_features']`
     * **OPTUNA ESPACE** : `self.optuna_search_space: Dict[str, tuple]` avec 100+ paramètres optimisables et leurs plages
     * **NORMALISATION POIDS** : Calcul `total = sum(self.initial_weights.values())` puis normalisation `{k: v/total for k, v in self.initial_weights.items()}`
     * **DÉTECTION RESSOURCES** : Appel conditionnel `self._detect_resources_with_psutil()` si variables environnement absentes
   - RETOUR : None (constructeur)
   - UTILITÉ : Méthode fondamentale pour initialiser toute la configuration du système de prédiction. Centralise tous les hyperparamètres optimisables et non-optimisables. Essentielle pour la cohérence des paramètres entre entraînement et optimisation Optuna.

4. clone.txt (PredictorConfig.clone - CLONAGE CONFIGURATION)
   - Lignes 976-993 dans config.py (18 lignes)
   - FONCTION : Crée une copie profonde complète de l'instance de configuration actuelle, préservant tous les attributs et leurs valeurs pour permettre des modifications indépendantes
   - PARAMÈTRES :
     * self - Instance de PredictorConfig à cloner
   - FONCTIONNEMENT DÉTAILLÉ :
     * **IMPORT DYNAMIQUE** : `import copy` importation locale du module copy pour deepcopy
     * **CRÉATION INSTANCE** : `new_config = PredictorConfig()` instanciation nouvelle configuration avec __init__ complet
     * **ITÉRATION ATTRIBUTS** : `for attr_name, attr_value in vars(self).items():` parcours dictionnaire __dict__ instance
     * **COPIE PROFONDE** : `setattr(new_config, attr_name, copy.deepcopy(attr_value))` copie récursive complète objets mutables
     * **LOGGING DEBUG** : `logger.debug(f"Configuration clonée avec {len(vars(new_config))} attributs")` comptage exact attributs
     * **RETOUR INSTANCE** : `return new_config` nouvelle instance complètement indépendante
     * **PRÉSERVATION TYPES** : deepcopy préserve types Dict, List, float, int, bool, str avec références indépendantes
     * **ÉVITEMENT ALIAS** : Aucune référence partagée entre instance originale et clone
   - RETOUR : PredictorConfig - Nouvelle instance avec attributs identiques mais indépendants
   - UTILITÉ : Méthode essentielle pour créer des configurations dérivées sans affecter l'original. Critique pour les tests d'optimisation Optuna et les expérimentations de paramètres. Permet la sauvegarde d'états de configuration.

5. get_min_sequence_length.txt (PredictorConfig.get_min_sequence_length - CALCUL LONGUEUR SÉQUENCE MINIMALE)
   - Lignes 995-1031 dans config.py (37 lignes)
   - FONCTION : Détermine la longueur minimale de séquence requise pour tous les modèles ML en calculant les besoins de chaque modèle et en retournant le maximum pour assurer la compatibilité
   - PARAMÈTRES :
     * self - Instance de PredictorConfig
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RÉCUPÉRATION MARKOV** : `markov_max_order = getattr(self, 'markov_max_order', 5)` avec fallback valeur 5
     * **CONTEXTE MARKOV** : `markov_context_length = getattr(self, 'markov_context_length', markov_max_order * 2)` calcul dynamique
     * **MINIMUM MARKOV** : `markov_min_length = max(markov_max_order, markov_context_length)` prend le maximum des deux
     * **RÉCUPÉRATION LSTM** : `lstm_sequence_length = getattr(self, 'lstm_sequence_length', 20)` avec fallback valeur 20
     * **CONTEXTE LSTM** : `lstm_context_length = getattr(self, 'lstm_context_length', lstm_sequence_length)` utilise sequence_length par défaut
     * **MINIMUM LSTM** : `lstm_min_length = lstm_context_length` assignation directe
     * **RÉCUPÉRATION LGBM** : `lgbm_feature_window = getattr(self, 'lgbm_feature_window', 10)` avec fallback valeur 10
     * **CONTEXTE LGBM** : `lgbm_context_length = getattr(self, 'lgbm_context_length', lgbm_feature_window * 2)` multiplication par 2
     * **MINIMUM LGBM** : `lgbm_min_length = lgbm_context_length` assignation directe
     * **MAXIMUM GLOBAL** : `min_sequence_length = max(markov_min_length, lstm_min_length, lgbm_min_length)` fonction max sur trois valeurs
     * **VÉRIFICATION CACHE** : `if not hasattr(self.__class__, '_last_min_lengths') or self.__class__._last_min_lengths != (markov_min_length, lstm_min_length, lgbm_min_length, min_sequence_length):` test existence et égalité tuple
     * **LOGGING CONDITIONNEL** : `logger.debug(f"Longueurs minimales de séquence: Markov={markov_min_length}, LSTM={lstm_min_length}, LGBM={lgbm_min_length}, Global={min_sequence_length}")` uniquement si changement
     * **MISE À JOUR CACHE** : `self.__class__._last_min_lengths = (markov_min_length, lstm_min_length, lgbm_min_length, min_sequence_length)` stockage tuple au niveau classe
     * **RETOUR VALEUR** : `return min_sequence_length` valeur entière calculée
   - RETOUR : int - Longueur minimale de séquence requise pour tous les modèles
   - UTILITÉ : Méthode critique pour assurer la compatibilité des données entre tous les modèles ML. Évite les erreurs de longueur insuffisante. Essentielle pour la validation des données d'entrée et la préparation des séquences.

6. _detect_resources_with_psutil.txt (PredictorConfig._detect_resources_with_psutil - DÉTECTION RESSOURCES SYSTÈME)
   - Lignes 941-974 dans config.py (34 lignes)
   - FONCTION : Détecte automatiquement les ressources système disponibles (CPU et mémoire) en utilisant psutil avec fallback vers des valeurs par défaut en cas d'échec
   - PARAMÈTRES :
     * self - Instance de PredictorConfig
   - FONCTIONNEMENT DÉTAILLÉ :
     * **TRY GLOBAL** : `try:` bloc principal pour capture toutes exceptions
     * **IMPORT CONDITIONNEL** : `try: import psutil except ImportError: psutil = None` gestion absence module
     * **VÉRIFICATION PSUTIL** : `if psutil:` test existence module importé
     * **FLAG DÉTECTION** : `detected = False` variable booléenne pour tracking succès
     * **TRY DÉTECTION** : `try:` bloc pour capture exceptions psutil
     * **DÉTECTION CPU** : `cores = psutil.cpu_count(logical=True)` nombre cœurs logiques incluant hyperthreading
     * **DÉTECTION MÉMOIRE** : `mem_bytes = psutil.virtual_memory().total` mémoire totale en bytes
     * **VALIDATION CPU** : `if cores:` vérification valeur non-nulle
     * **CALCUL CPU** : `self.default_cpu_cores = max(1, cores)` minimum 1 cœur garanti
     * **CALCUL MÉMOIRE** : `calculated_mem_gb = max(2, int(mem_bytes / (1024**3) * 0.8))` conversion bytes vers GB avec 80% utilisation
     * **MINIMUM MÉMOIRE** : `self.default_max_memory_gb = max(28, calculated_mem_gb)` plancher 28GB pour ML
     * **FLAG SUCCÈS** : `detected = True` marquage détection réussie
     * **EXCEPT PSUTIL** : `except Exception as e_psutil: pass` capture silencieuse erreurs psutil
     * **FALLBACK DÉTECTION** : `if not detected:` test échec détection
     * **VALEURS DÉFAUT PSUTIL** : `self.default_cpu_cores = 4; self.default_max_memory_gb = 28` si psutil échoue
     * **FALLBACK IMPORT** : `else:` si psutil absent, mêmes valeurs par défaut
     * **EXCEPT GLOBAL** : `except Exception as e_overall:` capture toutes autres exceptions
     * **VALEURS DÉFAUT FINALES** : Dernière ligne de défense avec valeurs sûres
   - RETOUR : None (modifie attributs de l'instance)
   - UTILITÉ : Méthode essentielle pour l'adaptation automatique aux ressources système. Optimise les performances selon le matériel disponible. Critique pour éviter les configurations inadaptées et les problèmes de mémoire.

================================================================================
SECTION 3 : ANCIENNESCLASSES
================================================================================

7. class_PredictorConfig.txt (PredictorConfig - CLASSE CONFIGURATION PRINCIPALE)
   - Lignes 157-1031 dans config.py (875 lignes)
   - FONCTION : Classe principale de configuration du système de prédiction ML contenant tous les hyperparamètres, seuils de décision et espaces de recherche Optuna
   - RESPONSABILITÉS :
     * **CONSTANTES MÉTRIQUES** : `METRIC_PRECISION_NON_WAIT = 'precision_non_wait_late_game'`, `METRIC_WAIT_RATIO = 'wait_ratio'`, `OPTIMAL_WAIT_RATIO = 0.4`
     * **HYPERPARAMÈTRES LSTM** : `lstm_hidden_size: int = 512`, `lstm_num_layers: int = 1`, `lstm_dropout: float = 0.27`, `lstm_learning_rate: float = 8.57e-05`
     * **HYPERPARAMÈTRES LGBM** : `lgbm_learning_rate: float = 0.008`, `lgbm_max_depth: int = 3`, `lgbm_num_leaves: int = 26`, `lgbm_n_estimators: int = 109`
     * **HYPERPARAMÈTRES MARKOV** : `markov_max_order: int = 1`, `markov_smoothing: float = 0.15`, `markov_global_weight: float = 0.15`
     * **SEUILS DÉCISION** : `decision_threshold_min: float = 0.3`, `decision_threshold_max: float = 0.6`, `transition_uncertainty_threshold: float = 0.6`
     * **POIDS MODÈLES** : `initial_weights: Dict[str, float] = {'lgbm': 0.20, 'lstm': 0.60, 'markov': 0.20}` avec normalisation automatique
     * **ESPACE OPTUNA** : `optuna_search_space: Dict[str, tuple]` avec 100+ paramètres et plages exactes `('float', min, max)`, `('int', min, max)`, `('categorical', [options])`
     * **FEATURES SÉLECTIONNÉES** : `selected_features: List[str]` avec 28 features exactes incluant 'banker_count', 'player_count', 'confidence'
     * **PARAMÈTRES PERFORMANCE** : `use_advanced_cache: bool = True`, `max_advanced_cache_size_gb: float = 8.0`, `historical_data_sample_percentage: float = 0.10`
     * **VIABILITÉ** : `viability_min_accuracy: float = 0.55`, `viability_min_wait_ratio: float = 0.2`, `viability_max_wait_ratio: float = 0.5`
   - MÉTHODES PRINCIPALES :
     * __init__(config_overrides=None) - Initialisation complète avec 200+ attributs
     * clone() - Création copie profonde avec copy.deepcopy()
     * get_min_sequence_length() - Calcul max(markov_min, lstm_min, lgbm_min)
     * _detect_resources_with_psutil() - Détection CPU/RAM avec fallbacks
   - ARCHITECTURE :
     * **CENTRALISATION** : Point unique pour 200+ paramètres configurables
     * **OPTIMISATION OPTUNA** : Intégration native avec espaces de recherche définis
     * **FLEXIBILITÉ** : Support config_overrides et variables environnement
     * **COHÉRENCE** : Synchronisation garantie entre entraînement et optimisation
     * **ROBUSTESSE** : Fallbacks multiples pour détection ressources et valeurs par défaut
   - UTILITÉ : Classe centrale du système de configuration. Essentielle pour maintenir la cohérence des paramètres. Critique pour l'optimisation Optuna et la reproductibilité des expériences ML.

8. class_SilentOptunaFilter.txt (SilentOptunaFilter - CLASSE FILTRE LOGGING OPTUNA)
   - Lignes 21-118 dans config.py (98 lignes)
   - FONCTION : Classe de filtrage héritant de logging.Filter pour supprimer les messages répétitifs et verbeux d'Optuna et du système ML
   - RESPONSABILITÉS :
     * **HÉRITAGE FILTER** : `class SilentOptunaFilter(logging.Filter):` étend classe de base Python
     * **PATTERNS RÉPÉTITIFS** : `self.repetitive_patterns = [...]` liste exacte de 35 chaînes de caractères
     * **COMPTEUR OCCURRENCES** : `self.repetitive_counts = {}` dictionnaire pour tracking messages vus
     * **DÉTECTION THREADS** : `"OptunaOptimization" in threading.current_thread().name` identification threads optimisation
     * **CORRECTION NIVEAUX** : `record.levelno = logging.INFO; record.levelname = "INFO"` rétrogradation CRITICAL vers INFO
     * **FILTRAGE IMMÉDIAT** : Conditions exactes `"DIAGNOSTIC TRAIN:" in message or "Gradient norm élevé:" in message`
     * **MOTS-CLÉS IMPORTANTS** : Liste exacte `["VIABLE", "ESSAI", "OPTIMISATION", "WAIT", "NON-WAIT", "VAGUE", "Progression", "Epoch", "Val Loss", "Val Accuracy", "Train Loss", "Train Accuracy", "Objectif 1", "Objectif 2", "Score composite", "Early stopping"]`
     * **GESTION PREMIÈRE OCCURRENCE** : `record.msg += " (messages similaires seront filtrés)"` modification message original
     * **PRÉSERVATION WARNING** : `if record.levelno >= logging.WARNING:` test niveau numérique exact
     * **EXCEPTION SPÉCIFIQUE** : `if "Erreur lors de l'optimisation de la mémoire PyTorch" in message: return False`
   - MÉTHODES PRINCIPALES :
     * __init__() - `super().__init__()` puis initialisation patterns et compteurs
     * filter(record) - Logique complète avec 10+ conditions de filtrage
   - ARCHITECTURE :
     * **PATTERN MATCHING** : Utilise `any(pattern in message for pattern in self.repetitive_patterns)` pour détection O(n)
     * **COMPTAGE STATEFUL** : `self.repetitive_counts[message] = 1` puis incrémentation pour tracking
     * **FILTRAGE INTELLIGENT** : Équilibre entre réduction bruit et préservation information critique
     * **THREAD-AWARE** : `return not is_optuna_thread` suppression finale logs threads optimisation
     * **MODIFICATION DYNAMIQUE** : Altération directe attributs LogRecord pour correction niveaux
   - UTILITÉ : Classe essentielle pour maintenir des logs lisibles pendant l'optimisation Optuna. Réduit drastiquement le volume de logs sans perdre d'informations critiques. Indispensable pour le debugging efficace des optimisations longues.
