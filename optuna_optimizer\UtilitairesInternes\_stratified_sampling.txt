# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 5172 à 5403
# Type: Méthode de la classe OptunaOptimizer

    def _stratified_sampling(self, sequences, max_samples, method='lhs'):
        """
        Effectue un échantillonnage stratifié des séquences basé sur le ratio P/B et d'autres caractéristiques.
        Supporte plusieurs méthodes d'échantillonnage, dont Latin Hypercube Sampling (LHS).

        Args:
            sequences: Liste des séquences à échantillonner
            max_samples: Nombre maximum d'échantillons à retourner
            method: Méthode d'échantillonnage ('stratified', 'lhs', 'random')

        Returns:
            numpy.ndarray: Indices des séquences échantillonnées
        """
        import random
        import numpy as np

        # Vérifier si nous avons assez de séquences
        if len(sequences) <= max_samples:
            return np.arange(len(sequences))

        # Méthode d'échantillonnage Latin Hypercube (LHS)
        if method == 'lhs':
            try:
                # Extraire des caractéristiques des séquences pour LHS
                features = []
                for i, seq in enumerate(sequences):
                    # Extraire les caractéristiques de la séquence
                    if isinstance(seq, tuple) and len(seq) == 3:
                        # Format (X_lgbm, X_lstm, y)
                        y_seq = seq[2]
                        if hasattr(y_seq, 'target_sequence'):
                            target_seq = y_seq.target_sequence
                        else:
                            # Fallback si la structure est différente
                            target_seq = str(y_seq)
                    elif isinstance(seq, dict) and 'target_sequence' in seq:
                        # Format dictionnaire avec target_sequence
                        target_seq = seq['target_sequence']
                    else:
                        # Fallback pour les autres formats
                        target_seq = str(seq)

                    # Calculer les caractéristiques
                    if isinstance(target_seq, str):
                        p_count = target_seq.count('P')
                        b_count = target_seq.count('B')
                        seq_len = len(target_seq)
                        p_ratio = p_count / seq_len if seq_len > 0 else 0
                        b_ratio = b_count / seq_len if seq_len > 0 else 0
                        pb_ratio = p_count / (b_count + 1e-10)  # Éviter division par zéro
                    else:
                        # Fallback pour les autres types
                        p_ratio = 0
                        b_ratio = 0
                        pb_ratio = 1

                    features.append([p_ratio, b_ratio, pb_ratio])

                # Convertir en tableau numpy
                features = np.array(features)

                # Normaliser les caractéristiques
                for j in range(features.shape[1]):
                    if np.max(features[:, j]) > np.min(features[:, j]):
                        features[:, j] = (features[:, j] - np.min(features[:, j])) / (np.max(features[:, j]) - np.min(features[:, j]))

                # Appliquer LHS
                try:
                    # Essayer d'utiliser scikit-optimize pour LHS
                    from skopt.sampler import Lhs
                    from skopt.space import Space

                    # Définir l'espace des caractéristiques
                    space = Space([(0.0, 1.0) for _ in range(features.shape[1])])

                    # Créer l'échantillonneur LHS
                    lhs = Lhs(lhs_type="classic", criterion="maximin")

                    # Générer les points LHS
                    lhs_points = lhs.generate(space.dimensions, max_samples)

                    # Normaliser les features pour éviter les problèmes numériques
                    try:
                        features_normalized = self._normalize_features(features)
                        logger.warning(f"Features normalisées pour le calcul des distances (shape: {features_normalized.shape})")
                    except Exception as e:
                        logger.warning(f"Erreur lors de la normalisation des features: {e}")
                        features_normalized = features

                    # Trouver les indices des séquences les plus proches des points LHS
                    selected_indices = []
                    for point in lhs_points:
                        # Calculer la distance euclidienne entre le point et toutes les caractéristiques
                        # Utiliser une méthode robuste pour éviter les dépassements numériques
                        try:
                            # Méthode 1: Utiliser np.linalg.norm qui est plus stable numériquement
                            distances = np.array([np.linalg.norm(f - point) for f in features_normalized])
                        except Exception as e:
                            logger.warning(f"Erreur lors du calcul des distances avec np.linalg.norm: {e}")

                            try:
                                # Méthode 2: Calculer la distance manuellement avec une protection contre les dépassements
                                # Limiter les différences pour éviter les dépassements lors de l'élévation au carré
                                diff = np.clip(features_normalized - point, -1e6, 1e6)
                                distances = np.sqrt(np.sum(diff * diff, axis=1))
                            except Exception as e:
                                logger.warning(f"Erreur lors du calcul manuel des distances: {e}")

                                # Méthode 3: Fallback - utiliser une distance approximative
                                # Utiliser la somme des valeurs absolues des différences (distance de Manhattan)
                                distances = np.sum(np.abs(features_normalized - point), axis=1)

                        # Trouver l'indice de la séquence la plus proche
                        closest_idx = np.argmin(distances)

                        # Ajouter l'indice à la liste des sélectionnés
                        selected_indices.append(closest_idx)

                        # Mettre à jour les distances pour éviter de sélectionner la même séquence
                        # Utiliser une grande valeur finie au lieu de l'infini pour éviter les problèmes de conversion
                        # Vérifier le type de données pour éviter les erreurs de cast
                        max_value = np.finfo(features_normalized.dtype).max
                        features_normalized[closest_idx] = np.ones_like(features_normalized[closest_idx]) * max_value

                    logger.warning(f"Échantillonnage LHS: {len(selected_indices)} séquences sélectionnées avec scikit-optimize")
                    return np.array(selected_indices)

                except ImportError:
                    # Fallback vers notre implémentation simplifiée de LHS
                    logger.warning("scikit-optimize non disponible, utilisation de l'implémentation simplifiée de LHS")
                    selected_indices = self._simple_lhs(features, max_samples)
                    logger.warning(f"Échantillonnage LHS simplifié: {len(selected_indices)} séquences sélectionnées")
                    return np.array(selected_indices)

            except Exception as e:
                # Vérifier si l'erreur est liée à la conversion d'infini en entier
                if "cannot convert float infinity to integer" in str(e):
                    logger.warning(f"Erreur lors de l'échantillonnage LHS: {e}, fallback vers l'échantillonnage stratifié")
                    logger.warning("Cette erreur est due à une limitation de scikit-optimize avec les valeurs infinies.")
                    logger.warning("Utilisation de l'échantillonnage stratifié comme alternative robuste.")
                else:
                    logger.warning(f"Erreur lors de l'échantillonnage LHS: {e}, fallback vers l'échantillonnage stratifié")
                method = 'stratified'

        # Méthode d'échantillonnage stratifié classique
        if method == 'stratified':
            # Regrouper les séquences par ratio P/B
            groups = {}
            for i, seq in enumerate(sequences):
                # Extraire la séquence cible
                if isinstance(seq, tuple) and len(seq) == 3:
                    # Format (X_lgbm, X_lstm, y)
                    y_seq = seq[2]
                    if hasattr(y_seq, 'target_sequence'):
                        target_seq = y_seq.target_sequence
                    else:
                        # Fallback si la structure est différente
                        target_seq = str(y_seq)
                elif isinstance(seq, dict) and 'target_sequence' in seq:
                    # Format dictionnaire avec target_sequence
                    target_seq = seq['target_sequence']
                else:
                    # Fallback pour les autres formats
                    target_seq = str(seq)

                # Calculer le ratio P/B
                if isinstance(target_seq, str):
                    p_count = target_seq.count('P')
                    seq_len = len(target_seq)
                    ratio = round(p_count / seq_len, 1) if seq_len > 0 else 0  # Arrondir à 0.1 près
                else:
                    ratio = 0

                if ratio not in groups:
                    groups[ratio] = []
                groups[ratio].append(i)  # Stocker l'indice au lieu de la séquence

            # Calculer le nombre d'échantillons par groupe
            total_groups = len(groups)
            samples_per_group = max_samples // total_groups if total_groups > 0 else 0

            # Sélectionner les échantillons de chaque groupe
            result_indices = []
            for ratio, group_indices in groups.items():
                if len(group_indices) <= samples_per_group:
                    result_indices.extend(group_indices)
                else:
                    result_indices.extend(random.sample(group_indices, samples_per_group))

            # Si nous n'avons pas assez d'échantillons, ajouter des échantillons supplémentaires
            remaining = max_samples - len(result_indices)
            if remaining > 0:
                # Créer une liste de tous les indices non sélectionnés
                all_indices = set(range(len(sequences)))
                selected_indices = set(result_indices)
                remaining_indices = list(all_indices - selected_indices)

                if remaining_indices:
                    # Ajouter des indices supplémentaires aléatoirement
                    additional = random.sample(remaining_indices, min(remaining, len(remaining_indices)))
                    result_indices.extend(additional)

            logger.warning(f"Échantillonnage stratifié: {len(result_indices)} séquences sélectionnées à partir de {len(sequences)} séquences")

            # Vérifier la diversité des groupes
            if len(groups) < 2:
                logger.warning("=" * 80)
                logger.warning(f"ALERTE CRITIQUE: DIVERSITÉ INSUFFISANTE DANS LES DONNÉES")
                logger.warning(f"Seulement {len(groups)} groupes de ratios P/B différents détectés")
                logger.warning(f"Cela affectera significativement la qualité de l'optimisation et la fiabilité des résultats.")
                logger.warning(f"RECOMMANDATION: Utilisez un jeu de données plus diversifié avec au moins 3-5 groupes différents.")
                logger.warning(f"Solutions possibles:")
                logger.warning(f"1. Augmentez la taille de l'échantillon de données historiques")
                logger.warning(f"2. Utilisez des données provenant de différentes sessions ou périodes")
                logger.warning(f"3. Incluez des séquences avec différents ratios P/B (idéalement entre 0.3 et 0.7)")
                logger.warning("=" * 80)
            else:
                logger.warning(f"Distribution des ratios P/B: {len(groups)} groupes différents")

            return np.array(result_indices)

        # Méthode d'échantillonnage aléatoire
        if method == 'random':
            indices = np.arange(len(sequences))
            np.random.shuffle(indices)
            return indices[:max_samples]

        # Fallback: échantillonnage aléatoire
        logger.warning(f"Méthode d'échantillonnage '{method}' non reconnue, utilisation de l'échantillonnage aléatoire")
        indices = np.arange(len(sequences))
        np.random.shuffle(indices)
        return indices[:max_samples]