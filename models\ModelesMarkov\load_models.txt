# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\models.py
# Lignes: 636 à 800
# Type: Méthode de la classe PersistentMarkov

    def load_models(self, data: Optional[Dict[str, Any]]) -> bool:
         """
         Charge l'état des modèles (global et session) et la configuration
         depuis un dictionnaire (typiquement issu d'une désérialisation).
         Met à jour l'instance actuelle.

         Args:
             data (Optional[Dict[str, Any]]): Le dictionnaire contenant les données
                                              à charger (format attendu de `export_models`).
                                              Si None ou invalide, la fonction échoue.

         Returns:
             bool: True si le chargement a réussi, False sinon.
         """
         if not isinstance(data, dict):
             logger.error("load_models: Données invalides fournies (pas un dictionnaire ou None).")
             return False

         with self.lock:
            logger.info("Chargement des modèles Markov depuis les données fournies...")
            try:
                # --- Charger et vérifier la Configuration ---
                loaded_config = data.get('config')
                if isinstance(loaded_config, dict):
                    prev_max_order = self.max_order
                    prev_smoothing = self.smoothing

                    loaded_max_order = loaded_config.get('max_order', self.max_order)
                    loaded_smoothing = loaded_config.get('smoothing', self.smoothing)

                    # Valider loaded_max_order (doit être un entier)
                    if isinstance(loaded_max_order, float):
                        logger.warning(f"max_order chargé comme flottant ({loaded_max_order}), conversion en entier.")
                        loaded_max_order = int(loaded_max_order)

                    if not isinstance(loaded_max_order, int) or loaded_max_order < 1:
                        logger.warning(f"max_order chargé invalide ({loaded_max_order}), utilisation de la valeur actuelle ({self.max_order}).")
                        loaded_max_order = self.max_order

                    # Garantir une valeur entre 1 et 12
                    loaded_max_order = max(1, min(12, loaded_max_order))
                    if not isinstance(loaded_smoothing, (int, float)) or loaded_smoothing < 0:
                        logger.warning(f"smoothing chargé invalide ({loaded_smoothing}), utilisation de la valeur actuelle ({self.smoothing}).")
                        loaded_smoothing = self.smoothing

                    self.max_order = loaded_max_order
                    self.smoothing = loaded_smoothing

                    if prev_max_order != self.max_order or prev_smoothing != self.smoothing:
                          logger.warning(f"Configuration Markov chargée différente des valeurs actuelles: "
                                       f"MaxOrder={prev_max_order}->{self.max_order}, "
                                       f"Smoothing={prev_smoothing:.4f}->{self.smoothing:.4f}")

                    # Calculer max_order_int pour les structures de données (égal à max_order car c'est un entier)
                    self.max_order_int = self.max_order

                    # Redimensionner les listes de modèles si max_order_int a changé
                    expected_len = self.max_order_int + 1
                    if len(self.global_models) != expected_len:
                         logger.warning(f"Redimensionnement des listes de modèles Markov pour max_order={self.max_order}")
                         self.global_models = [defaultdict(lambda: defaultdict(int)) for _ in range(expected_len)]
                         self.session_models = [defaultdict(lambda: defaultdict(int)) for _ in range(expected_len)]
                else:
                     logger.warning("Aucune section 'config' valide trouvée dans les données Markov chargées. Utilisation des valeurs actuelles.")

                # --- Charger les Modèles Globaux ---
                loaded_global = data.get('global')
                # Vérifier si la liste chargée contient des données valides
                if isinstance(loaded_global, list) and len(loaded_global) > 0:
                     # Utiliser self.max_order_int pour les structures de données (déjà un entier)

                     # Réinitialiser les modèles actuels avant de charger
                     self.global_models = [defaultdict(lambda: defaultdict(int)) for _ in range(self.max_order_int + 1)]
                     # Charger chaque ordre disponible, jusqu'à max_order
                     loaded_orders = 0
                     for order_idx, model_dict in enumerate(loaded_global):
                         if order_idx >= self.max_order:
                             logger.info(f"Chargement Global: Ignoré les ordres > {self.max_order} car max_order actuel = {self.max_order}")
                             break  # Ne pas dépasser max_order

                         order = order_idx + 1 # L'index 0 de la liste correspond à l'ordre 1
                         if not isinstance(model_dict, dict):
                              logger.warning(f"Chargement Global: Données invalides pour l'ordre {order}. Ignoré.")
                              continue

                         loaded_orders += 1
                         for state_key, outcomes_dict in model_dict.items():
                              # Assurer que state_key est un tuple (problème potentiel avec JSON)
                              if not isinstance(state_key, tuple):
                                    try:
                                         state_tuple = tuple(state_key)
                                         logger.debug(f"Clé état convertie en tuple: {state_key} -> {state_tuple}")
                                    except TypeError:
                                         logger.warning(f"Chargement Global Ordre {order}: Impossible de convertir la clé état '{state_key}' en tuple. Ignoré.")
                                         continue
                              else:
                                   state_tuple = state_key

                              if not isinstance(outcomes_dict, dict):
                                  logger.warning(f"Chargement Global Ordre {order}: Données d'outcomes invalides pour état {state_tuple}. Ignoré.")
                                  continue

                              # Important: Reconstruire avec defaultdict(int) pour les outcomes
                              self.global_models[order][state_tuple] = defaultdict(int, outcomes_dict)

                     logger.info(f"Modèles Markov globaux chargés pour les ordres 1 à {loaded_orders} (max_order actuel = {self.max_order}).")
                else:
                     logger.warning(f"Données Markov 'global' non trouvées ou invalides. Les modèles globaux sont laissés vides.")
                     # Garder les modèles globaux vides (réinitialisés juste avant)

                # --- Charger les Modèles de Session ---
                loaded_session = data.get('session')
                if isinstance(loaded_session, list) and len(loaded_session) > 0:
                     # Utiliser max_order_int pour les structures de données
                     max_order_int = int(self.max_order) + 1 if self.max_order > int(self.max_order) else int(self.max_order)

                     # Réinitialiser les modèles de session avant de charger
                     self.session_models = [defaultdict(lambda: defaultdict(int)) for _ in range(max_order_int + 1)]
                     # Charger chaque ordre disponible, jusqu'à max_order
                     loaded_orders = 0
                     for order_idx, model_dict in enumerate(loaded_session):
                         if order_idx >= int(self.max_order):
                             logger.info(f"Chargement Session: Ignoré les ordres > {int(self.max_order)} car max_order actuel = {self.max_order}")
                             break  # Ne pas dépasser la partie entière de max_order

                         order = order_idx + 1
                         if not isinstance(model_dict, dict):
                             logger.warning(f"Chargement Session: Données invalides pour l'ordre {order}. Ignoré.")
                             continue

                         loaded_orders += 1
                         for state_key, outcomes_dict in model_dict.items():
                              if not isinstance(state_key, tuple):
                                  try:
                                      state_tuple = tuple(state_key)
                                      logger.debug(f"Session: Clé état convertie en tuple: {state_key} -> {state_tuple}")
                                  except TypeError:
                                      logger.warning(f"Chargement Session Ordre {order}: Impossible de convertir la clé état '{state_key}' en tuple. Ignoré.")
                                      continue
                              else:
                                  state_tuple = state_key

                              if not isinstance(outcomes_dict, dict):
                                  logger.warning(f"Chargement Session Ordre {order}: Données d'outcomes invalides pour état {state_tuple}. Ignoré.")
                                  continue

                              self.session_models[order][state_tuple] = defaultdict(int, outcomes_dict)
                     logger.info(f"Modèles Markov de session chargés pour les ordres 1 à {loaded_orders} (max_order actuel = {self.max_order}).")
                else:
                    logger.warning(f"Données Markov 'session' non trouvées ou invalides. Les modèles de session sont réinitialisés (vides).")
                    # Utiliser max_order_int pour les structures de données
                    max_order_int = int(self.max_order) + 1 if self.max_order > int(self.max_order) else int(self.max_order)

                    # Assurer que session est vide si non chargé correctement
                    self.session_models = [defaultdict(lambda: defaultdict(int)) for _ in range(max_order_int + 1)]


                logger.info("Chargement Markov terminé.")
                return True # Succès du chargement

            except Exception as e:
                logger.error(f"Erreur majeure pendant le chargement des modèles Markov: {e}", exc_info=True)
                # En cas d'erreur, réinitialiser complètement pour éviter un état corrompu
                self.reset(reset_type='hard') # Réinitialise global et session
                return False # Échec du chargement