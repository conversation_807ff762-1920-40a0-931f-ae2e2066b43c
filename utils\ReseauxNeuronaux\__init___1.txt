# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 66 à 106
# Type: Méthode de la classe AdvancedLSTM
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, config, input_size=None):
        super(AdvancedLSTM, self).__init__()

        # Récupérer les paramètres depuis la configuration
        self.input_size = input_size if input_size is not None else getattr(config, 'lstm_input_size', 12)
        self.hidden_size = getattr(config, 'lstm_hidden_dim', 320)
        self.num_layers = getattr(config, 'lstm_num_layers', 2)
        self.bidirectional = getattr(config, 'lstm_bidirectional', True)
        self.use_attention = getattr(config, 'lstm_use_attention', True)
        self.use_residual = getattr(config, 'lstm_use_residual', True)

        # Dropout différencié
        self.dropout_input = getattr(config, 'lstm_dropout_input', 0.1)
        self.dropout_hidden = getattr(config, 'lstm_dropout_hidden', 0.2)
        self.dropout_output = getattr(config, 'lstm_dropout_output', 0.15)

        # Facteur multiplicatif pour la direction
        self.direction_factor = 2 if self.bidirectional else 1

        # Couches de dropout
        self.dropout_in = nn.Dropout(self.dropout_input)
        self.dropout_out = nn.Dropout(self.dropout_output)

        # LSTM avec dropout entre les couches
        self.lstm = nn.LSTM(
            input_size=self.input_size,
            hidden_size=self.hidden_size,
            num_layers=self.num_layers,
            batch_first=True,
            bidirectional=self.bidirectional,
            dropout=self.dropout_hidden if self.num_layers > 1 else 0
        )

        # Couche d'attention
        if self.use_attention:
            self.attention = AttentionLayer(self.hidden_size * self.direction_factor)

        # Couches de classification
        self.fc1 = nn.Linear(self.hidden_size * self.direction_factor, self.hidden_size)
        self.bn1 = nn.BatchNorm1d(self.hidden_size)
        self.fc2 = nn.Linear(self.hidden_size, 2)  # 2 classes: banker et player