# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 955 à 970
# Type: Méthode de la classe ConfidenceCalculator

    def calculate_calibrated_confidence(probabilities, calibration_method='isotonic'):
        """
        Calcule une confiance calibrée à partir des probabilités brutes.
        Nouvelle fonctionnalité pour améliorer la fiabilité des scores de confiance.

        Args:
            probabilities: Probabilités brutes (après softmax)
            calibration_method: Méthode de calibration ('isotonic' ou 'platt')

        Returns:
            Probabilités calibrées
        """
        # Cette méthode nécessiterait une implémentation plus complète avec sklearn
        # Pour l'instant, nous retournons simplement les probabilités d'origine
        logger.warning("Calibration de confiance non implémentée, retour des probabilités brutes")
        return probabilities