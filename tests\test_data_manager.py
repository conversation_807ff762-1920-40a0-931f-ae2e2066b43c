#!/usr/bin/env python3
"""
Tests complets pour le gestionnaire de données
"""

import sys
import os
import unittest
import tempfile
import shutil
import numpy as np
from pathlib import Path

# Ajouter le dossier du programme au path
PROG_DIR = r"C:\Users\<USER>\Desktop\travail\plateforme\notes_travail\prog"
sys.path.insert(0, PROG_DIR)

try:
    from data_manager import BaccaratSequenceManager
    from config import PredictorConfig
except ImportError as e:
    print(f"ERREUR: Impossible d'importer modules: {e}")
    sys.exit(1)


class TestDataManager(unittest.TestCase):
    """Tests du gestionnaire de données"""

    def setUp(self):
        """Initialisation avant chaque test"""
        self.config = PredictorConfig()

        # Créer fonction factice pour hybrid_feature_creator
        def dummy_feature_creator(sequence):
            if len(sequence) < 2:
                return None, None
            lgbm_features = [0.5] * 10  # 10 features LGBM
            lstm_features = np.random.random((5, 3))  # 5 timesteps, 3 features
            return lgbm_features, lstm_features

        self.data_manager = BaccaratSequenceManager(
            sequence_length=5,
            min_target_hand_index=10,
            hybrid_feature_creator=dummy_feature_creator,
            lgbm_feature_count=10,
            lstm_seq_len=5,
            lstm_feature_count=3
        )

        # Créer données de test
        self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 10  # 100 éléments

    def test_data_manager_creation(self):
        """Test création gestionnaire de données"""
        self.assertIsNotNone(self.data_manager)
        self.assertIsInstance(self.data_manager, BaccaratSequenceManager)

    def test_prepare_data_basic(self):
        """Test préparation données de base"""
        # Créer séquences de test
        test_sequences = [
            ['player', 'banker', 'player', 'banker'] * 15,  # 60 éléments
            ['banker', 'player', 'banker', 'player'] * 15   # 60 éléments
        ]

        try:
            # Tester préparation
            result = self.data_manager.prepare_data_for_model(test_sequences)

            if result[0] is not None:  # Si données générées
                X_lgbm, y, X_lstm, prefixes, origins = result
                self.assertIsNotNone(X_lgbm)
                self.assertIsNotNone(y)
                self.assertIsNotNone(X_lstm)

        except Exception as e:
            self.fail(f"Échec préparation données: {e}")

    def test_evaluate_performance(self):
        """Test évaluation performance"""
        # Créer données factices
        y_true = np.array([0, 1, 0, 1, 1])
        y_pred_proba = np.array([0.3, 0.8, 0.2, 0.9, 0.7])

        try:
            result = self.data_manager.evaluate_performance(y_true, y_pred_proba)

            self.assertIsInstance(result, dict)
            self.assertIn('loss', result)
            self.assertIn('accuracy', result)

        except Exception as e:
            self.fail(f"Échec évaluation performance: {e}")

    def test_sequence_manager_attributes(self):
        """Test attributs du gestionnaire de séquences"""
        self.assertEqual(self.data_manager.sequence_length, 5)
        self.assertEqual(self.data_manager.min_target_hand_index, 10)
        self.assertEqual(self.data_manager.lgbm_feature_count, 10)
        self.assertEqual(self.data_manager.lstm_seq_len, 5)
        self.assertEqual(self.data_manager.lstm_feature_count, 3)

    def test_empty_sequences(self):
        """Test avec séquences vides"""
        empty_sequences = []

        try:
            result = self.data_manager.prepare_data_for_model(empty_sequences)
            # Devrait retourner None pour toutes les valeurs
            self.assertEqual(result, (None, None, None, None, None))

        except Exception as e:
            self.fail(f"Échec test séquences vides: {e}")

    def test_short_sequences(self):
        """Test avec séquences trop courtes"""
        short_sequences = [
            ['player', 'banker'],  # Seulement 2 éléments
            ['banker']             # Seulement 1 élément
        ]

        try:
            result = self.data_manager.prepare_data_for_model(short_sequences)
            # Peut retourner None ou données limitées
            self.assertTrue(True)  # Test réussi si pas d'exception

        except Exception as e:
            self.fail(f"Échec test séquences courtes: {e}")


class TestDataManagerFeatures(unittest.TestCase):
    """Tests des fonctionnalités avancées du gestionnaire de données"""

    def setUp(self):
        self.config = PredictorConfig()

        # Créer fonction factice pour hybrid_feature_creator
        def dummy_feature_creator(sequence):
            if len(sequence) < 2:
                return None, None
            lgbm_features = [0.5] * 15  # 15 features LGBM
            lstm_features = np.random.random((8, 4))  # 8 timesteps, 4 features
            return lgbm_features, lstm_features

        self.data_manager = BaccaratSequenceManager(
            sequence_length=8,
            min_target_hand_index=5,
            hybrid_feature_creator=dummy_feature_creator,
            lgbm_feature_count=15,
            lstm_seq_len=8,
            lstm_feature_count=4
        )

    def test_advanced_data_preparation(self):
        """Test préparation données avancée"""
        # Créer séquences plus complexes
        complex_sequences = [
            ['player'] * 30 + ['banker'] * 30,  # 60 éléments alternés
            ['banker'] * 20 + ['player'] * 40   # 60 éléments variés
        ]

        try:
            result = self.data_manager.prepare_data_for_model(complex_sequences)

            if result[0] is not None:
                X_lgbm, y, X_lstm, prefixes, origins = result

                # Vérifier shapes
                self.assertEqual(X_lgbm.shape[1], 15)  # 15 features LGBM
                self.assertEqual(X_lstm.shape[1], 8)   # 8 timesteps
                self.assertEqual(X_lstm.shape[2], 4)   # 4 features par timestep

        except Exception as e:
            self.fail(f"Échec préparation avancée: {e}")

    def test_performance_evaluation_edge_cases(self):
        """Test évaluation performance cas limites"""
        # Test avec une seule classe
        y_true_single = np.array([1, 1, 1, 1])
        y_pred_single = np.array([0.8, 0.9, 0.7, 0.6])

        try:
            result = self.data_manager.evaluate_performance(y_true_single, y_pred_single)
            self.assertIsInstance(result, dict)
            # Loss peut être NaN pour une seule classe

        except Exception as e:
            self.fail(f"Échec évaluation cas limite: {e}")

    def test_invalid_feature_creator(self):
        """Test avec créateur de features défaillant"""
        def failing_feature_creator(sequence):
            # Simuler échec
            return None, None

        failing_manager = BaccaratSequenceManager(
            sequence_length=5,
            min_target_hand_index=5,
            hybrid_feature_creator=failing_feature_creator,
            lgbm_feature_count=10,
            lstm_seq_len=5,
            lstm_feature_count=3
        )

        test_sequences = [['player', 'banker'] * 30]

        try:
            result = failing_manager.prepare_data_for_model(test_sequences)
            # Devrait retourner None car features échouent
            self.assertEqual(result, (None, None, None, None, None))

        except Exception as e:
            self.fail(f"Échec test créateur défaillant: {e}")


class TestDataManagerPerformance(unittest.TestCase):
    """Tests de performance du gestionnaire de données"""

    def setUp(self):
        self.config = PredictorConfig()

        # Créer fonction factice rapide
        def fast_feature_creator(sequence):
            if len(sequence) < 2:
                return None, None
            lgbm_features = [0.5] * 5  # 5 features LGBM
            lstm_features = np.ones((3, 2))  # 3 timesteps, 2 features
            return lgbm_features, lstm_features

        self.data_manager = BaccaratSequenceManager(
            sequence_length=3,
            min_target_hand_index=2,
            hybrid_feature_creator=fast_feature_creator,
            lgbm_feature_count=5,
            lstm_seq_len=3,
            lstm_feature_count=2
        )

    def test_large_data_handling(self):
        """Test gestion grandes quantités de données"""
        # Créer dataset volumineux
        large_sequences = []
        for i in range(10):  # 10 séquences de 60 éléments
            sequence = ['player' if j % 2 == 0 else 'banker' for j in range(60)]
            large_sequences.append(sequence)

        # Test préparation
        start_time = time.time()
        result = self.data_manager.prepare_data_for_model(large_sequences)
        end_time = time.time()

        # Devrait être rapide (< 5 secondes)
        self.assertLess(end_time - start_time, 5.0)
        # Peut retourner None ou données selon implémentation

    def test_memory_usage(self):
        """Test utilisation mémoire"""
        import psutil
        import os

        process = psutil.Process(os.getpid())
        memory_before = process.memory_info().rss

        # Traiter données volumineuses
        large_sequences = []
        for i in range(20):  # 20 séquences
            sequence = ['player', 'banker'] * 30  # 60 éléments chacune
            large_sequences.append(sequence)

        result = self.data_manager.prepare_data_for_model(large_sequences)

        memory_after = process.memory_info().rss
        memory_increase = memory_after - memory_before

        # Augmentation mémoire devrait être raisonnable (< 100MB)
        self.assertLess(memory_increase, 100 * 1024 * 1024)


if __name__ == '__main__':
    import time

    print("=== TESTS DATA MANAGER ===")
    print(f"Répertoire programme: {PROG_DIR}")

    # Créer suite de tests
    suite = unittest.TestSuite()

    # Ajouter tests
    suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestDataManager))
    suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestDataManagerFeatures))
    suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestDataManagerPerformance))

    # Exécuter tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Afficher résultats
    print(f"\n=== RÉSULTATS DATA MANAGER ===")
    print(f"Tests exécutés: {result.testsRun}")
    print(f"Échecs: {len(result.failures)}")
    print(f"Erreurs: {len(result.errors)}")

    if result.failures:
        print("\nÉCHECS:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")

    if result.errors:
        print("\nERREURS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")

    sys.exit(0 if result.wasSuccessful() else 1)
