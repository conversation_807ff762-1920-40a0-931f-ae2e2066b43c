#!/usr/bin/env python3
"""
Tests complets pour le gestionnaire de données
"""

import sys
import os
import unittest
import tempfile
import shutil
import numpy as np
from pathlib import Path

# Ajouter le dossier du programme au path
PROG_DIR = r"C:\Users\<USER>\Desktop\travail\plateforme\notes_travail\prog"
sys.path.insert(0, PROG_DIR)

try:
    from data_manager import DataManager
    from config import PredictorConfig
except ImportError as e:
    print(f"ERREUR: Impossible d'importer modules: {e}")
    sys.exit(1)


class TestDataManager(unittest.TestCase):
    """Tests du gestionnaire de données"""
    
    def setUp(self):
        """Initialisation avant chaque test"""
        self.config = PredictorConfig()
        self.data_manager = DataManager(self.config)
        
        # Créer données de test
        self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 10  # 100 éléments
        
    def test_data_manager_creation(self):
        """Test création gestionnaire de données"""
        self.assertIsNotNone(self.data_manager)
        self.assertIsInstance(self.data_manager, DataManager)
        
    def test_load_historical_data(self):
        """Test chargement données historiques"""
        # Créer fichier temporaire avec données test
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(','.join(map(str, self.test_data)))
            temp_file = f.name
            
        try:
            # Tester chargement
            data = self.data_manager.load_historical_data(temp_file)
            
            self.assertIsNotNone(data)
            self.assertEqual(len(data), len(self.test_data))
            self.assertEqual(data, self.test_data)
            
        finally:
            os.unlink(temp_file)
            
    def test_load_historical_data_file_not_found(self):
        """Test chargement fichier inexistant"""
        with self.assertRaises(FileNotFoundError):
            self.data_manager.load_historical_data("fichier_inexistant.txt")
            
    def test_validate_data_format(self):
        """Test validation format données"""
        # Données valides
        valid_data = [0, 1, 0, 1, 1, 0]
        self.assertTrue(self.data_manager.validate_data_format(valid_data))
        
        # Données invalides - valeurs non binaires
        invalid_data = [0, 1, 2, 1, 0]
        self.assertFalse(self.data_manager.validate_data_format(invalid_data))
        
        # Données invalides - types incorrects
        invalid_data2 = [0, 1, "a", 1, 0]
        self.assertFalse(self.data_manager.validate_data_format(invalid_data2))
        
    def test_split_data(self):
        """Test division données train/test"""
        data = list(range(100))
        train_ratio = 0.8
        
        train_data, test_data = self.data_manager.split_data(data, train_ratio)
        
        self.assertEqual(len(train_data), 80)
        self.assertEqual(len(test_data), 20)
        self.assertEqual(len(train_data) + len(test_data), len(data))
        
    def test_create_sequences(self):
        """Test création séquences pour LSTM"""
        data = list(range(20))
        sequence_length = 5
        
        sequences = self.data_manager.create_sequences(data, sequence_length)
        
        self.assertIsNotNone(sequences)
        self.assertEqual(len(sequences), len(data) - sequence_length)
        
        # Vérifier première séquence
        expected_first = data[:sequence_length]
        self.assertEqual(sequences[0], expected_first)
        
    def test_create_sequences_insufficient_data(self):
        """Test création séquences avec données insuffisantes"""
        data = [0, 1, 0]  # Seulement 3 éléments
        sequence_length = 5
        
        sequences = self.data_manager.create_sequences(data, sequence_length)
        
        # Devrait retourner liste vide ou None
        self.assertTrue(len(sequences) == 0 or sequences is None)


class TestDataManagerFeatures(unittest.TestCase):
    """Tests des fonctionnalités avancées du gestionnaire de données"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.data_manager = DataManager(self.config)
        
    def test_calculate_statistics(self):
        """Test calcul statistiques données"""
        data = [0, 1, 0, 1, 1, 0, 1, 0]
        
        stats = self.data_manager.calculate_statistics(data)
        
        self.assertIsInstance(stats, dict)
        self.assertIn('mean', stats)
        self.assertIn('std', stats)
        self.assertIn('count_0', stats)
        self.assertIn('count_1', stats)
        
        # Vérifier calculs
        self.assertEqual(stats['count_0'], 4)
        self.assertEqual(stats['count_1'], 4)
        self.assertAlmostEqual(stats['mean'], 0.5, places=2)
        
    def test_detect_patterns(self):
        """Test détection patterns dans données"""
        # Données avec pattern évident
        data = [0, 1, 0, 1, 0, 1, 0, 1]
        
        patterns = self.data_manager.detect_patterns(data)
        
        self.assertIsInstance(patterns, dict)
        # Devrait détecter alternance
        
    def test_clean_data(self):
        """Test nettoyage données"""
        # Données avec valeurs aberrantes
        dirty_data = [0, 1, -1, 1, 0, 2, 1, 0]
        
        clean_data = self.data_manager.clean_data(dirty_data)
        
        # Vérifier que seules valeurs 0 et 1 restent
        for value in clean_data:
            self.assertIn(value, [0, 1])
            
    def test_augment_data(self):
        """Test augmentation données"""
        original_data = [0, 1, 0, 1]
        
        augmented_data = self.data_manager.augment_data(original_data)
        
        # Données augmentées devraient être plus longues
        self.assertGreaterEqual(len(augmented_data), len(original_data))


class TestDataManagerPerformance(unittest.TestCase):
    """Tests de performance du gestionnaire de données"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.data_manager = DataManager(self.config)
        
    def test_large_data_handling(self):
        """Test gestion grandes quantités de données"""
        # Créer dataset volumineux
        large_data = [i % 2 for i in range(10000)]
        
        # Test chargement
        start_time = time.time()
        stats = self.data_manager.calculate_statistics(large_data)
        end_time = time.time()
        
        # Devrait être rapide (< 1 seconde)
        self.assertLess(end_time - start_time, 1.0)
        self.assertIsNotNone(stats)
        
    def test_memory_usage(self):
        """Test utilisation mémoire"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_before = process.memory_info().rss
        
        # Traiter données volumineuses
        large_data = [i % 2 for i in range(50000)]
        sequences = self.data_manager.create_sequences(large_data, 10)
        
        memory_after = process.memory_info().rss
        memory_increase = memory_after - memory_before
        
        # Augmentation mémoire devrait être raisonnable (< 100MB)
        self.assertLess(memory_increase, 100 * 1024 * 1024)


if __name__ == '__main__':
    import time
    
    print("=== TESTS DATA MANAGER ===")
    print(f"Répertoire programme: {PROG_DIR}")
    
    # Créer suite de tests
    suite = unittest.TestSuite()
    
    # Ajouter tests
    suite.addTest(unittest.makeSuite(TestDataManager))
    suite.addTest(unittest.makeSuite(TestDataManagerFeatures))
    suite.addTest(unittest.makeSuite(TestDataManagerPerformance))
    
    # Exécuter tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Afficher résultats
    print(f"\n=== RÉSULTATS DATA MANAGER ===")
    print(f"Tests exécutés: {result.testsRun}")
    print(f"Échecs: {len(result.failures)}")
    print(f"Erreurs: {len(result.errors)}")
    
    if result.failures:
        print("\nÉCHECS:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
            
    if result.errors:
        print("\nERREURS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
            
    sys.exit(0 if result.wasSuccessful() else 1)
