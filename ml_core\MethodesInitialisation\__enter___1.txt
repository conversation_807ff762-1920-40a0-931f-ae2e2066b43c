# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 604 à 615
# Type: Méthode de la classe LSTMMemoryContext
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __enter__(self):
        """
        Appelé à l'entrée du bloc with.
        Optimise la mémoire du modèle LSTM et configure son mode.
        """
        # Sauvegarder l'état d'entraînement précédent
        self.previous_training_state = self.model.training

        # Optimiser la mémoire du modèle LSTM
        MemoryManager.optimize_lstm_memory(self.model, training_mode=self.training)

        return self