# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1533 à 1562
# Type: Méthode de la classe ThreadedTrainer

    def stop(self):
        """
        Arrête l'entraînement en cours.

        Returns:
            bool: True si l'entraînement a été arrêté, False sinon
        """
        if not self.is_running:
            logger.warning("Aucun entraînement en cours d'exécution")
            return False

        # Définir l'événement d'arrêt
        self.stop_event.set()

        # Définir le flag d'arrêt dans l'instance d'entraînement
        if hasattr(self.trainer_instance, 'stop_training'):
            self.trainer_instance.stop_training = True

        # Attendre que le thread se termine (avec un timeout)
        if self.thread:
            self.thread.join(timeout=5.0)

            # Si le thread ne s'est pas terminé, le forcer à s'arrêter
            if self.thread.is_alive():
                logger.warning("Le thread d'entraînement ne s'est pas arrêté proprement, forçage de l'arrêt")
                # Nous ne pouvons pas forcer l'arrêt d'un thread en Python, mais nous pouvons le marquer comme terminé
                self.is_running = False

        logger.info("Entraînement arrêté")
        return True