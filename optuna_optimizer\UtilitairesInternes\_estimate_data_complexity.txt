# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 12395 à 12444
# Type: Méthode de la classe OptunaOptimizer

    def _estimate_data_complexity(self, sample_data=None):
        """
        Estime la complexité des données en analysant un échantillon.

        Args:
            sample_data: Échantillon de données à analyser (optionnel)

        Returns:
            float: Indicateur de complexité des données (1.0 = complexité moyenne)
        """
        # Si aucun échantillon n'est fourni, utiliser les données disponibles
        if sample_data is None and hasattr(self, 'sequences'):
            sample_data = self.sequences[:min(1000, len(self.sequences))]

        # Si aucune donnée n'est disponible, retourner la complexité par défaut
        if sample_data is None or not sample_data:
            return 1.0

        try:
            # Calculer la diversité des patterns (ratio P/B)
            pattern_diversity = 0.0
            pattern_counts = {}

            for seq in sample_data:
                if isinstance(seq, dict) and 'target_sequence' in seq:
                    target_seq = seq['target_sequence']

                    # Compter les occurrences de chaque pattern de longueur 3
                    for i in range(len(target_seq) - 2):
                        pattern = target_seq[i:i+3]
                        if pattern not in pattern_counts:
                            pattern_counts[pattern] = 0
                        pattern_counts[pattern] += 1

            # Calculer la diversité des patterns (nombre de patterns uniques / nombre total possible)
            if pattern_counts:
                unique_patterns = len(pattern_counts)
                max_possible_patterns = 2**3  # Pour des séquences binaires de longueur 3
                pattern_diversity = unique_patterns / max_possible_patterns

            # Calculer la complexité en fonction de la diversité des patterns
            # Plus la diversité est élevée, plus les données sont complexes
            complexity = 0.5 + pattern_diversity

            logger.info(f"Complexité des données estimée: {complexity:.2f}")
            return complexity

        except Exception as e:
            logger.error(f"Erreur lors de l'estimation de la complexité des données: {e}")
            return 1.0