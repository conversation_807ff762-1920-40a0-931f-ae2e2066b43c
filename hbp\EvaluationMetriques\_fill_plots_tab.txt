# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 7526 à 7547
# Type: Méthode de la classe HybridBaccaratPredictor

    def _fill_plots_tab(self, parent_frame):
        """Remplit l'onglet des graphiques."""
        # Créer un cadre pour les boutons de graphiques
        buttons_frame = ttk.Frame(parent_frame)
        buttons_frame.pack(fill=tk.X, padx=10, pady=5)

        # Ajouter des boutons pour afficher différents graphiques
        ttk.But<PERSON>(buttons_frame, text="Courbes d'Apprentissage LGBM",
                  command=self._show_lgbm_learning_curves).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(buttons_frame, text="Courbes d'Apprentissage LSTM",
                  command=self._show_lstm_learning_curves).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(buttons_frame, text="Importance des Caractéristiques",
                  command=self._show_feature_importance).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(buttons_frame, text="Matrices de Confusion",
                  command=self._show_confusion_matrices).pack(side=tk.LEFT, padx=5, pady=5)

        # Créer un cadre pour afficher les graphiques
        self.plot_display_frame = ttk.LabelFrame(parent_frame, text="Affichage des Graphiques")
        self.plot_display_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Ajouter un message initial
        ttk.Label(self.plot_display_frame, text="Cliquez sur un bouton ci-dessus pour afficher un graphique").pack(pady=50)