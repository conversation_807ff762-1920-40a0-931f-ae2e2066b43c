# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 8113 à 8225
# Type: Méthode de la classe HybridBaccaratPredictor

    def predict_with_lstm(self, lstm_features: Optional[np.ndarray]) -> Dict[str, float]:
        """Effectue une prédiction en utilisant le modèle LSTM avec une approche optimisée pour réduire la latence."""
        # Importer NotFittedError ici pour éviter les problèmes d'importation circulaire
        from sklearn.exceptions import NotFittedError
        import time

        # Mesurer le temps d'exécution
        start_time = time.time()

        # Vérifier si nous sommes en phase d'entraînement ou d'optimisation Optuna
        is_training_phase = getattr(self, '_is_training', False)
        is_optuna_phase = getattr(self, 'is_optuna_running', False)
        log_method = logger.debug if (is_training_phase or is_optuna_phase) else logger.warning

        if self.lstm is None:
            log_method("predict_with_lstm: Modèle LSTM non initialisé. Retour valeurs par défaut.")
            default_player_prob = getattr(self.config, 'default_player_prob', 0.5)
            default_banker_prob = getattr(self.config, 'default_banker_prob', 0.5)
            return {'player': default_player_prob, 'banker': default_banker_prob}

        expected_shape = (self.config.lstm_sequence_length, self.lstm.input_size)
        input_array = None

        if lstm_features is not None and lstm_features.shape == expected_shape:
            input_array = lstm_features
        else:
            if lstm_features is not None:
                log_method(f"predict_with_lstm: Features fournies invalides (shape {lstm_features.shape}, attendu {expected_shape}). Tentative avec handle_short_sequence.")
            with self.sequence_lock:
                input_array = self.handle_short_sequence(self.sequence)

        if input_array is None or input_array.shape != expected_shape:
            log_method(f"predict_with_lstm: Impossible d'obtenir des features LSTM valides (attendu {expected_shape}). Retour valeurs par défaut.")
            default_player_prob = getattr(self.config, 'default_player_prob', 0.5)
            default_banker_prob = getattr(self.config, 'default_banker_prob', 0.5)
            return {'player': default_player_prob, 'banker': default_banker_prob}

        # Vérifier si nous avons un cache LSTM
        if not hasattr(self, 'lstm_cache'):
            self.lstm_cache = {}
            logger.info("Initialisation du cache LSTM pour accélérer les prédictions")

        # Créer une clé de cache basée sur le hash des features
        cache_key = hash(input_array.tobytes())

        # Vérifier si la prédiction est dans le cache
        if cache_key in self.lstm_cache:
            prediction = self.lstm_cache[cache_key]
            # Mettre à jour la progression au lieu d'afficher un log
            self._update_prediction_progress()
            return prediction

        with self.model_lock:
            try:
                self.lstm.eval()

                # Approche directe sans DataLoader pour réduire la latence
                with torch.no_grad():
                    # Convertir les features en tensor
                    inputs_tensor = torch.FloatTensor(input_array).unsqueeze(0)

                    # Déplacer sur le device approprié
                    inputs_tensor = inputs_tensor.to(self.device)

                    # Effectuer la prédiction directement
                    outputs = self.lstm(inputs_tensor)

                    # Calculer les probabilités
                    probabilities = torch.softmax(outputs, dim=1)

                    # Convertir en numpy
                    probs_cpu = probabilities.squeeze(0).cpu().numpy()

                    if len(probs_cpu) != 2:
                        logger.error(f"Prédiction LSTM: Sortie de probabilités inattendue. Shape: {probs_cpu.shape}, Valeurs: {probs_cpu}")
                        return {'player': 0.5, 'banker': 0.5}

                    # Vérifier si les indices sont correctement alignés
                    if len(probs_cpu) == 2:
                        # Créer le dictionnaire de résultats
                        # Utiliser UNIQUEMENT le système zero-based: 0 = Player, 1 = Banker (indices standards PyTorch)
                        # Importer la fonction de conversion depuis pytorch_standard_extensions
                        from optuna_optimizer import class_to_label

                        # Utiliser les indices standards de PyTorch
                        result = {'player': float(probs_cpu[0]), 'banker': float(probs_cpu[1])}
                    else:
                        # Cas d'erreur, mais pas besoin de log répétitif
                        result = {'player': 0.5, 'banker': 0.5}

                    # Pas besoin de vérifier si les probabilités sont trop proches de 0.5

                    # Stocker dans le cache
                    self.lstm_cache[cache_key] = result

                    # Limiter la taille du cache
                    max_cache_size = getattr(self.config, 'lstm_cache_max_size', 1000)
                    if len(self.lstm_cache) > max_cache_size:
                        # Supprimer une entrée aléatoire
                        try:
                            key_to_remove = next(iter(self.lstm_cache))
                            del self.lstm_cache[key_to_remove]
                        except Exception as e_cache:
                            logger.debug(f"Erreur lors de la suppression d'une entrée du cache LSTM: {e_cache}")

                    # Mettre à jour la progression au lieu d'afficher un log
                    self._update_prediction_progress()
                    return result

            except Exception as e:
                elapsed = time.time() - start_time
                logger.error(f"Erreur lors de la prédiction LSTM (temps: {elapsed*1000:.1f}ms): {e}", exc_info=True)
                return {'player': 0.5, 'banker': 0.5}