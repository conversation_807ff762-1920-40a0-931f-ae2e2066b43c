# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 4305 à 4329
# Type: Méthode de la classe HybridBaccaratPredictor

    def _calculate_sample_weights(self, list_of_all_origins: List[int], final_num_samples: int) -> np.ndarray:
        """Extrait le calcul des poids d'échantillons dans une fonction séparée."""
        logger_instance = getattr(self, 'logger', logging.getLogger(__name__))
        sample_weights_all = np.ones(final_num_samples, dtype=np.float64)

        decay_factor = getattr(self.config, 'sample_weight_decay_factor', 0.9995)
        max_origin_index_used = max(list_of_all_origins) if list_of_all_origins else 0

        if 0 < decay_factor < 1 and max_origin_index_used > 0 and final_num_samples > 0:
            epsilon_decay = 1e-8
            for i, sample_origin_index in enumerate(list_of_all_origins):
                time_lag = max(0, max_origin_index_used - sample_origin_index)
                weight_raw = decay_factor ** time_lag
                sample_weights_all[i] = max(epsilon_decay, weight_raw) if np.isfinite(weight_raw) else epsilon_decay

            logger_instance.info(f"Poids temporels appliqués (decay={decay_factor:.4f}).")
        else:
            logger_instance.info("Poids temporels non appliqués (decay <= 0, >= 1, ou données insuffisantes).")

        mean_weight_final = np.mean(sample_weights_all)
        if mean_weight_final > 1e-9:
            sample_weights_all /= mean_weight_final
        logger_instance.info(f"Poids finaux normalisés (Min: {np.min(sample_weights_all):.4f}, Max: {np.max(sample_weights_all):.4f}, Mean: {np.mean(sample_weights_all):.4f}).")

        return sample_weights_all