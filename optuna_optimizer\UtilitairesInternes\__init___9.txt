# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 13548 à 13551
# Type: Méthode de la classe OptimizerAdapter
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

            def __init__(self, optimizer_instance, n_trials, progress_callback=None):
                self.optimizer_instance = optimizer_instance
                self.n_trials = n_trials
                self.progress_callback = progress_callback