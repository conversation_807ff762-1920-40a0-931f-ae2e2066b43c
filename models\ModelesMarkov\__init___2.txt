# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\models.py
# Lignes: 195 à 227
# Type: Méthode de la classe PersistentMarkov
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, max_order: int, smoothing: float):
        """
        Initialise le gestionnaire de modèles Markov.

        Args:
            max_order (int): L'ordre maximal des chaînes de Markov à considérer (1-12).
                            Doit être un entier car représente un nombre discret de manches.
            smoothing (float): Le facteur de lissage de Laplace (alpha) à appliquer
                               lors du calcul des probabilités pour éviter les probas nulles.
        """
        # Assurer que max_order est un entier
        if isinstance(max_order, float):
            logger.warning(f"max_order reçu comme flottant ({max_order}), conversion en entier.")
            max_order = int(max_order)

        # Garantir une valeur entre 1 et 12
        self.max_order = max(1, min(12, max_order))

        # Valider le paramètre smoothing
        if not isinstance(smoothing, (int, float)) or smoothing < 0:
            raise ValueError("smoothing doit être un nombre >= 0")
        self.smoothing: float = smoothing

        # Pour les structures de données, utiliser directement max_order
        self.max_order_int = self.max_order

        # Utilise une liste où l'index correspond à l'ordre (index 0 non utilisé pour les modèles)
        # global_models[order][state_tuple] = defaultdict(int) -> {'outcome': count}
        self.global_models: List[defaultdict] = [defaultdict(lambda: defaultdict(int)) for _ in range(self.max_order_int + 1)]
        self.session_models: List[defaultdict] = [defaultdict(lambda: defaultdict(int)) for _ in range(self.max_order_int + 1)]
        self.lock: threading.RLock = threading.RLock()
        logger.info(f"PersistentMarkov initialisé avec max_order={self.max_order} (max_order_int={self.max_order_int}), smoothing={self.smoothing}")
        logger.info(f"Taille des modèles: global_models={len(self.global_models)}, session_models={len(self.session_models)}")