# Suite de Tests Complète - Plateforme Baccarat

## Vue d'ensemble

Cette suite de tests complète évalue tous les aspects critiques de la plateforme de prédiction ML hybride pour Baccarat, avec un focus particulier sur l'entraînement et l'optimisation.

## Structure des Tests

### 📁 Fichiers de Test

| Fichier | Description | Focus |
|---------|-------------|-------|
| `test_config.py` | Tests de configuration | Validation paramètres, cohérence |
| `test_data_manager.py` | Tests gestionnaire données | Chargement, validation, préparation |
| `test_training.py` | **Tests entraînement** | **LGBM, LSTM, Markov, Pipeline complet** |
| `test_optimization.py` | **Tests optimisation** | **Optuna, paramètres, convergence** |
| `test_threading.py` | Tests threading | Verrous, concurrence, deadlocks |
| `test_performance.py` | Tests performance | Mémoire, vitesse, scalabilité |
| `test_integration.py` | Tests intégration | Workflow complet, robustesse |
| `run_all_tests.py` | **Script principal** | **Exécution complète + rapport** |

### 🎯 Tests Prioritaires (Entraînement & Optimisation)

#### Tests d'Entraînement (`test_training.py`)
- **TestTrainingBasic** : Initialisation, préparation données, création modèles
- **TestTrainingLGBM** : Features LGBM, entraînement, prédictions
- **TestTrainingLSTM** : Séquences LSTM, entraînement, prédictions  
- **TestTrainingMarkov** : Chaînes Markov, construction, prédictions
- **TestTrainingIntegration** : Pipeline complet, prédictions hybrides, performance

#### Tests d'Optimisation (`test_optimization.py`)
- **TestOptimizationBasic** : Création études Optuna, fonctions objectif
- **TestOptimizationParameters** : Poids modèles, paramètres LSTM/LGBM
- **TestOptimizationExecution** : Exécution trials, timeout, données réelles
- **TestOptimizationResults** : Extraction paramètres, historique, convergence

## 🚀 Utilisation

### Exécution Complète
```bash
cd tests
python run_all_tests.py
```

### Exécution Tests Spécifiques

#### Tests d'Entraînement
```bash
python test_training.py
```

#### Tests d'Optimisation  
```bash
python test_optimization.py
```

#### Tests Individuels
```bash
python test_config.py
python test_data_manager.py
python test_threading.py
python test_performance.py
python test_integration.py
```

## 📊 Rapport de Résultats

Le script principal génère :

### Rapport Console
- Résumé par suite de tests
- Détails des échecs et erreurs
- Recommandations d'amélioration
- Score global de stabilité

### Rapport JSON (`test_results.json`)
```json
{
  "execution_time": 120.5,
  "total_tests": 89,
  "total_failures": 12,
  "total_errors": 3,
  "test_suites": {
    "ENTRAÎNEMENT": {
      "tests_run": 15,
      "failures": 2,
      "errors": 1,
      "success": false
    }
  }
}
```

## 🔍 Données de Test

### Données Réelles
- **Fichier** : `C:\Users\<USER>\Desktop\travail\plateforme\notes_travail\prog\historical_data.txt`
- **Format** : Valeurs séparées par virgules (0,1,1,0,1...)
- **Utilisation** : Tests d'entraînement et optimisation avec vraies données

### Données Synthétiques
- **Fallback** : Données générées si fichier réel indisponible
- **Pattern** : `[0,1,1,0,1,0,1,1,0,1] * N` répétitions
- **Tailles** : Variables selon le test (100-2000 éléments)

## ⚠️ Points d'Attention

### Tests d'Entraînement
- **Timeout** : 5 minutes maximum par entraînement
- **Mémoire** : Surveillance utilisation < 500MB
- **Données** : Validation format binaire (0,1)
- **Modèles** : Vérification création et prédictions

### Tests d'Optimisation
- **Trials** : Limités (2-5) pour tests rapides
- **Timeout** : 60-120 secondes maximum
- **Paramètres** : Validation plages et contraintes
- **Convergence** : Vérification amélioration progressive

### Tests Threading
- **Deadlocks** : Détection automatique avec timeout
- **Concurrence** : Tests accès simultané cache/modèles
- **Performance** : Mesure overhead threading

## 📈 Métriques de Succès

### Critères de Validation
- **Taux de succès** : > 80% pour validation
- **Performance** : Entraînement < 5 min, prédiction < 1 sec
- **Mémoire** : Utilisation < 500MB, pas de fuites
- **Threading** : Pas de deadlocks, accès concurrent sûr

### Seuils d'Alerte
- **🔴 Critique** : < 60% succès - Refonte nécessaire
- **🟠 Moyen** : 60-80% succès - Corrections importantes
- **🟡 Bon** : 80-95% succès - Améliorations mineures
- **🟢 Excellent** : > 95% succès - Système stable

## 🛠️ Dépendances

### Modules Python Requis
```python
import unittest
import numpy as np
import psutil
import optuna
import threading
import time
import json
```

### Modules Système Testés
```python
from hbp import HybridBaccaratPredictor
from config import PredictorConfig
from data_manager import DataManager  # Si disponible
```

## 📝 Personnalisation

### Ajout Nouveaux Tests
1. Créer classe héritant `unittest.TestCase`
2. Ajouter méthodes `test_*`
3. Importer dans `run_all_tests.py`
4. Ajouter à la suite appropriée

### Modification Paramètres
- **Timeouts** : Ajuster selon performance machine
- **Tailles données** : Réduire pour tests plus rapides
- **Trials Optuna** : Augmenter pour optimisation réelle

## 🎯 Objectifs Tests

### Validation Fonctionnelle
- ✅ Tous les composants s'initialisent correctement
- ✅ Entraînement fonctionne avec données réelles
- ✅ Optimisation améliore les paramètres
- ✅ Prédictions sont cohérentes et rapides

### Validation Non-Fonctionnelle  
- ✅ Performance acceptable sous charge
- ✅ Utilisation mémoire contrôlée
- ✅ Threading sûr sans deadlocks
- ✅ Robustesse face aux erreurs

### Validation Intégration
- ✅ Workflow complet fonctionnel
- ✅ Interaction modèles cohérente
- ✅ Récupération après erreurs
- ✅ Session complète simulée

---

**Note** : Cette suite de tests est conçue pour identifier les défauts critiques du système avant déploiement en production. Les résultats doivent être analysés en conjonction avec le rapport d'analyse des défauts pour une évaluation complète.
