# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 11252 à 11331
# Type: Méthode de la classe OptunaOptimizer

    def _stratified_sampling_for_early_phases(self, indices, phase):
        """
        Effectue un échantillonnage stratifié pour les phases initiales.

        Cette méthode garantit que même avec 100% des données, l'exploration initiale
        reste efficace en se concentrant sur des patterns importants.

        Args:
            indices: Indices complets des données
            phase: Numéro de la phase (0, 1, 2, 3)

        Returns:
            np.ndarray: Indices échantillonnés de manière stratifiée
        """
        if phase in [0, 1]:  # Phases d'exploration initiale
            # Extraire les caractéristiques pour la stratification
            X = self.X_lgbm_full[indices]
            y = self.y_full[indices]

            # Identifier les patterns rares ou importants
            pattern_features = []

            # 1. Longues séquences de Player ou Banker
            streak_cols = [col for col in range(X.shape[1]) if hasattr(self, 'lgbm_feature_names') and col < len(self.lgbm_feature_names) and 'streak' in self.lgbm_feature_names[col]]
            if streak_cols:
                streak_values = X[:, streak_cols].max(axis=1)
                pattern_features.append((streak_values > np.percentile(streak_values, 90)))

            # 2. Alternances fréquentes
            alt_cols = [col for col in range(X.shape[1]) if hasattr(self, 'lgbm_feature_names') and col < len(self.lgbm_feature_names) and 'alternate' in self.lgbm_feature_names[col]]
            if alt_cols:
                alt_values = X[:, alt_cols].max(axis=1)
                pattern_features.append((alt_values > np.percentile(alt_values, 90)))

            # 3. Distributions équilibrées/déséquilibrées
            if hasattr(self, 'lgbm_feature_names') and 'p_ratio' in self.lgbm_feature_names:
                p_ratio_col = self.lgbm_feature_names.index('p_ratio')
                p_ratio = X[:, p_ratio_col]
                pattern_features.append((p_ratio < 0.35) | (p_ratio > 0.65))

            # Combiner les patterns
            is_interesting = np.zeros(len(indices), dtype=bool)
            for pattern in pattern_features:
                is_interesting = is_interesting | pattern

            # Assurer un minimum de 30% d'échantillons intéressants
            interesting_indices = indices[is_interesting]
            regular_indices = indices[~is_interesting]

            # Si aucun pattern intéressant n'a été trouvé, retourner tous les indices
            if len(interesting_indices) == 0:
                logger.warning("Aucun pattern intéressant trouvé pour l'échantillonnage stratifié")
                return indices

            # Sélectionner 30% d'échantillons intéressants et 70% réguliers
            sample_size = len(indices)
            interesting_sample_size = min(int(0.3 * sample_size), len(interesting_indices))
            regular_sample_size = sample_size - interesting_sample_size

            # Ajuster si nécessaire
            if regular_sample_size > len(regular_indices):
                regular_sample_size = len(regular_indices)
                interesting_sample_size = sample_size - regular_sample_size

            # Échantillonner
            sampled_interesting = np.random.choice(interesting_indices, size=interesting_sample_size, replace=False)
            sampled_regular = np.random.choice(regular_indices, size=regular_sample_size, replace=False)

            # Combiner et mélanger
            stratified_sample = np.concatenate([sampled_interesting, sampled_regular])
            np.random.shuffle(stratified_sample)

            logger.warning(f"Échantillonnage stratifié pour la phase {phase}: {len(stratified_sample)} échantillons")
            logger.warning(f"  - {interesting_sample_size} échantillons intéressants ({interesting_sample_size/sample_size*100:.1f}%)")
            logger.warning(f"  - {regular_sample_size} échantillons réguliers ({regular_sample_size/sample_size*100:.1f}%)")

            return stratified_sample

        # Pour les autres phases, retourner tous les indices
        return indices