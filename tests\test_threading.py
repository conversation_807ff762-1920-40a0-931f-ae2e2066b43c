#!/usr/bin/env python3
"""
Tests complets pour les mécanismes de threading
"""

import sys
import os
import unittest
import threading
import time
import queue
from pathlib import Path

# Ajouter le dossier du programme au path
PROG_DIR = r"C:\Users\<USER>\Desktop\travail\plateforme\notes_travail\prog"
sys.path.insert(0, PROG_DIR)

try:
    from hbp import HybridBaccaratPredictor
    from config import PredictorConfig
except ImportError as e:
    print(f"ERREUR: Impossible d'importer modules: {e}")
    sys.exit(1)


class TestThreadingBasic(unittest.TestCase):
    """Tests de base pour le threading"""
    
    def setUp(self):
        """Initialisation avant chaque test"""
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 20
        
    def test_thread_creation(self):
        """Test création de threads"""
        try:
            # Créer thread simple
            def simple_task():
                time.sleep(0.1)
                return True
                
            thread = threading.Thread(target=simple_task)
            thread.start()
            thread.join(timeout=5)
            
            self.assertFalse(thread.is_alive())
            
        except Exception as e:
            self.fail(f"Échec création thread: {e}")
            
    def test_thread_communication(self):
        """Test communication entre threads"""
        try:
            result_queue = queue.Queue()
            
            def worker_task(q):
                q.put("test_result")
                
            thread = threading.Thread(target=worker_task, args=(result_queue,))
            thread.start()
            thread.join(timeout=5)
            
            # Vérifier résultat
            self.assertFalse(result_queue.empty())
            result = result_queue.get()
            self.assertEqual(result, "test_result")
            
        except Exception as e:
            self.fail(f"Échec communication threads: {e}")
            
    def test_thread_timeout(self):
        """Test timeout des threads"""
        start_time = time.time()
        
        try:
            def long_task():
                time.sleep(10)  # Tâche longue
                
            thread = threading.Thread(target=long_task)
            thread.start()
            thread.join(timeout=2)  # Timeout court
            
            end_time = time.time()
            elapsed = end_time - start_time
            
            # Devrait se terminer proche de 2 secondes
            self.assertLess(elapsed, 3)
            self.assertTrue(thread.is_alive())  # Thread encore actif
            
        except Exception as e:
            self.fail(f"Échec test timeout: {e}")


class TestThreadingLocks(unittest.TestCase):
    """Tests des verrous threading"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        self.shared_resource = 0
        self.lock = threading.Lock()
        
    def test_lock_acquisition(self):
        """Test acquisition/libération verrous"""
        try:
            # Test acquisition simple
            acquired = self.lock.acquire(timeout=1)
            self.assertTrue(acquired)
            
            # Libération
            self.lock.release()
            
        except Exception as e:
            self.fail(f"Échec acquisition verrou: {e}")
            
    def test_lock_contention(self):
        """Test contention sur verrous"""
        results = []
        
        def worker_with_lock(worker_id):
            try:
                with self.lock:
                    # Section critique
                    old_value = self.shared_resource
                    time.sleep(0.01)  # Simuler travail
                    self.shared_resource = old_value + 1
                    results.append(f"worker_{worker_id}_done")
            except Exception as e:
                results.append(f"worker_{worker_id}_error: {e}")
                
        # Créer plusieurs threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker_with_lock, args=(i,))
            threads.append(thread)
            
        # Démarrer tous les threads
        for thread in threads:
            thread.start()
            
        # Attendre fin
        for thread in threads:
            thread.join(timeout=5)
            
        # Vérifier résultats
        self.assertEqual(self.shared_resource, 5)
        self.assertEqual(len(results), 5)
        
    def test_deadlock_detection(self):
        """Test détection de deadlocks potentiels"""
        lock1 = threading.Lock()
        lock2 = threading.Lock()
        deadlock_detected = threading.Event()
        
        def task1():
            try:
                with lock1:
                    time.sleep(0.1)
                    acquired = lock2.acquire(timeout=0.5)
                    if not acquired:
                        deadlock_detected.set()
                    else:
                        lock2.release()
            except Exception:
                deadlock_detected.set()
                
        def task2():
            try:
                with lock2:
                    time.sleep(0.1)
                    acquired = lock1.acquire(timeout=0.5)
                    if not acquired:
                        deadlock_detected.set()
                    else:
                        lock1.release()
            except Exception:
                deadlock_detected.set()
                
        # Démarrer threads susceptibles de deadlock
        thread1 = threading.Thread(target=task1)
        thread2 = threading.Thread(target=task2)
        
        thread1.start()
        thread2.start()
        
        # Attendre détection ou timeout
        detected = deadlock_detected.wait(timeout=2)
        
        thread1.join(timeout=1)
        thread2.join(timeout=1)
        
        # Un deadlock devrait être détecté
        self.assertTrue(detected)


class TestThreadingPredictor(unittest.TestCase):
    """Tests threading spécifiques au prédicteur"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 50
        
    def test_concurrent_training(self):
        """Test entraînement concurrent"""
        results = []
        
        def train_worker(worker_id):
            try:
                # Simuler entraînement
                result = self.predictor.train_all_models(self.test_data)
                results.append(f"worker_{worker_id}: {result}")
            except Exception as e:
                results.append(f"worker_{worker_id}_error: {e}")
                
        # Créer threads d'entraînement
        threads = []
        for i in range(2):  # Seulement 2 pour éviter surcharge
            thread = threading.Thread(target=train_worker, args=(i,))
            threads.append(thread)
            
        # Démarrer
        for thread in threads:
            thread.start()
            
        # Attendre avec timeout
        for thread in threads:
            thread.join(timeout=60)  # 1 minute max
            
        # Vérifier que tous ont terminé
        for thread in threads:
            self.assertFalse(thread.is_alive())
            
        # Vérifier résultats
        self.assertEqual(len(results), 2)
        
    def test_concurrent_prediction(self):
        """Test prédictions concurrentes"""
        # Entraîner d'abord
        try:
            self.predictor.train_all_models(self.test_data)
        except:
            self.skipTest("Entraînement échoué")
            
        results = []
        
        def predict_worker(worker_id):
            try:
                recent_data = self.test_data[-20:]
                prediction = self.predictor.predict_hybrid(recent_data)
                results.append(f"worker_{worker_id}: {prediction}")
            except Exception as e:
                results.append(f"worker_{worker_id}_error: {e}")
                
        # Créer threads de prédiction
        threads = []
        for i in range(3):
            thread = threading.Thread(target=predict_worker, args=(i,))
            threads.append(thread)
            
        # Démarrer
        for thread in threads:
            thread.start()
            
        # Attendre
        for thread in threads:
            thread.join(timeout=30)
            
        # Vérifier résultats
        self.assertEqual(len(results), 3)
        
    def test_thread_safety_cache(self):
        """Test thread-safety du cache"""
        cache_errors = []
        
        def cache_worker(worker_id):
            try:
                for i in range(10):
                    # Simuler accès cache
                    key = f"test_key_{i}"
                    value = f"test_value_{worker_id}_{i}"
                    
                    # Accès concurrent au cache
                    if hasattr(self.predictor, 'cache_set'):
                        self.predictor.cache_set(key, value)
                        retrieved = self.predictor.cache_get(key)
                        
                        if retrieved != value:
                            cache_errors.append(f"Cache corruption: {key}")
                            
            except Exception as e:
                cache_errors.append(f"Cache error worker_{worker_id}: {e}")
                
        # Créer threads d'accès cache
        threads = []
        for i in range(3):
            thread = threading.Thread(target=cache_worker, args=(i,))
            threads.append(thread)
            
        # Démarrer
        for thread in threads:
            thread.start()
            
        # Attendre
        for thread in threads:
            thread.join(timeout=10)
            
        # Vérifier pas d'erreurs cache
        if cache_errors:
            self.fail(f"Erreurs cache détectées: {cache_errors}")


class TestThreadingStress(unittest.TestCase):
    """Tests de stress pour le threading"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        
    def test_many_threads(self):
        """Test avec beaucoup de threads"""
        num_threads = 10
        results = []
        
        def simple_worker(worker_id):
            try:
                time.sleep(0.1)
                results.append(f"worker_{worker_id}_done")
            except Exception as e:
                results.append(f"worker_{worker_id}_error: {e}")
                
        # Créer beaucoup de threads
        threads = []
        for i in range(num_threads):
            thread = threading.Thread(target=simple_worker, args=(i,))
            threads.append(thread)
            
        start_time = time.time()
        
        # Démarrer tous
        for thread in threads:
            thread.start()
            
        # Attendre tous
        for thread in threads:
            thread.join(timeout=5)
            
        end_time = time.time()
        elapsed = end_time - start_time
        
        # Vérifier performance
        self.assertLess(elapsed, 2)  # Devrait être rapide
        self.assertEqual(len(results), num_threads)
        
    def test_thread_memory_usage(self):
        """Test utilisation mémoire avec threads"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_before = process.memory_info().rss
        
        def memory_worker():
            # Créer données temporaires
            data = [0] * 10000
            time.sleep(0.1)
            del data
            
        # Créer threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=memory_worker)
            threads.append(thread)
            
        # Exécuter
        for thread in threads:
            thread.start()
            
        for thread in threads:
            thread.join(timeout=5)
            
        memory_after = process.memory_info().rss
        memory_increase = memory_after - memory_before
        
        # Augmentation mémoire devrait être raisonnable
        self.assertLess(memory_increase, 50 * 1024 * 1024)  # < 50MB


if __name__ == '__main__':
    print("=== TESTS THREADING ===")
    print(f"Répertoire programme: {PROG_DIR}")
    
    # Créer suite de tests
    suite = unittest.TestSuite()
    
    # Ajouter tests
    suite.addTest(unittest.makeSuite(TestThreadingBasic))
    suite.addTest(unittest.makeSuite(TestThreadingLocks))
    suite.addTest(unittest.makeSuite(TestThreadingPredictor))
    suite.addTest(unittest.makeSuite(TestThreadingStress))
    
    # Exécuter tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Afficher résultats
    print(f"\n=== RÉSULTATS THREADING ===")
    print(f"Tests exécutés: {result.testsRun}")
    print(f"Échecs: {len(result.failures)}")
    print(f"Erreurs: {len(result.errors)}")
    
    if result.failures:
        print("\nÉCHECS:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
            
    if result.errors:
        print("\nERREURS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
            
    sys.exit(0 if result.wasSuccessful() else 1)
