# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 2984 à 3076
# Type: Méthode de la classe HybridBaccaratPredictor

    def _get_recent_session_data(self, min_rounds_for_update: int = 10) -> Tuple[Optional[np.ndarray], Optional[np.ndarray], Optional[np.ndarray], Optional[np.ndarray]]:
        """
        Extrait les données (features et labels) pour LGBM et LSTM basées sur les
        manches ajoutées à la séquence DEPUIS la dernière mise à jour rapide.

        Args:
            min_rounds_for_update: Nombre minimum de NOUVELLES manches requises.

        Returns:
            Tuple[X_lgbm, y_lgbm, X_lstm, y_lstm] ou <PERSON>ple[None, None, None, None] si pas assez de données.
        """
        with self.sequence_lock:
            current_len = len(self.sequence)
            start_index = self.last_incremental_update_index
            num_new_rounds = current_len - start_index

            if num_new_rounds < min_rounds_for_update:
                logger.info(f"_get_recent_session_data: Pas assez de nouvelles manches ({num_new_rounds} < {min_rounds_for_update}).")
                return None, None, None, None

            # Extraire la portion pertinente de la séquence pour générer les features
            # On a besoin d'assez d'historique *avant* le début des nouvelles données pour les premières features
            min_hist_needed = self.config.lstm_sequence_length
            effective_start_for_features = max(0, start_index - min_hist_needed) # Point de départ pour l'historique
            sequence_subset_for_gen = self.sequence[effective_start_for_features:current_len]
            # Index dans sequence_subset_for_gen correspondant au début réel des *nouvelles* prédictions
            offset_in_subset = start_index - effective_start_for_features

            logger.info(f"Extraction des données pour mise à jour rapide: {num_new_rounds} nouvelles manches "
                        f"(indices {start_index} à {current_len-1}). Utilisation historique depuis index {effective_start_for_features}.")

            X_lgbm_new, y_lgbm_new = [], []
            X_lstm_new, y_lstm_new = [], []

            lstm_input_feat_size = getattr(self.lstm, 'input_size', 6) if self.lstm else 6
            expected_lstm_shape = (self.config.lstm_sequence_length, lstm_input_feat_size)

            # Boucler sur les NOUVELLES manches pour générer les paires (features, label)
            # L'indice 'i' ici est relatif à la `sequence_subset_for_gen`
            # On commence à générer les features pour prédire l'outcome à l'indice 'offset_in_subset'
            # (qui correspond à l'indice 'start_index' dans la séquence globale)
            required_len_for_lstm = self.config.lstm_sequence_length
            for i in range(offset_in_subset, len(sequence_subset_for_gen)):
                # Indices dans la séquence *globale* pour cette itération:
                # Input sequence: self.sequence[0 : effective_start_for_features + i]
                # Target outcome: self.sequence[effective_start_for_features + i]

                # Séquence d'entrée pour créer les features
                input_sub_sequence = sequence_subset_for_gen[:i]
                # Résultat réel à prédire
                actual_outcome = sequence_subset_for_gen[i]

                if len(input_sub_sequence) < required_len_for_lstm:
                    # logger.debug(f"  Skipping index {i} in subset (global {effective_start_for_features+i}): not enough history ({len(input_sub_sequence)} < {required_len_for_lstm})")
                    continue # Pas assez d'historique pour cette manche

                # Générer les features basées sur la sous-séquence jusqu'à i-1
                feat_lgbm, feat_lstm = self.create_hybrid_features(input_sub_sequence)

                # --- Validation des features générées ---
                valid_lgbm = feat_lgbm is not None and len(feat_lgbm) == len(self.feature_names)
                valid_lstm = feat_lstm is not None and feat_lstm.shape == expected_lstm_shape

                if valid_lgbm and valid_lstm:
                    label = 1 if actual_outcome == 'banker' else 0
                    X_lgbm_new.append(feat_lgbm)
                    y_lgbm_new.append(label)
                    X_lstm_new.append(feat_lstm)
                    y_lstm_new.append(label)
                else:
                     issues = []
                     if feat_lgbm is None: issues.append("LGBM features = None")
                     elif not valid_lgbm: issues.append(f"LGBM features len mismatch (got {len(feat_lgbm)}, expected {len(self.feature_names)})")
                     if feat_lstm is None: issues.append("LSTM features = None")
                     elif not valid_lstm: issues.append(f"LSTM features shape mismatch (got {feat_lstm.shape}, expected {expected_lstm_shape})")
                     # Log seulement si un problème est détecté
                     if issues: logger.warning(f"  Features non générées/invalides pour indice subset {i}: {', '.join(issues)}. Ignoré.")


            if not X_lgbm_new: # Si aucune feature n'a pu être générée
                 logger.warning("Aucune feature valide n'a pu être générée pour la mise à jour rapide.")
                 return None, None, None, None

        try:
            X_lgbm_np = np.array(X_lgbm_new, dtype=np.float32)
            y_lgbm_np = np.array(y_lgbm_new, dtype=np.int64)
            X_lstm_np = np.stack(X_lstm_new, axis=0).astype(np.float32)
            y_lstm_np = np.array(y_lstm_new, dtype=np.int64)
            logger.info(f"Données récentes préparées: LGBM {X_lgbm_np.shape}, LSTM {X_lstm_np.shape}")
            return X_lgbm_np, y_lgbm_np, X_lstm_np, y_lstm_np
        except Exception as e:
            logger.error(f"Erreur lors de la conversion NumPy des données récentes: {e}", exc_info=True)
            return None, None, None, None