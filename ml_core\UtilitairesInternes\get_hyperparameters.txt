# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1175 à 1191
# Type: Méthode de la classe TrainOptimizeInterface

    def get_hyperparameters(self, model_name: str) -> Dict[str, Any]:
        """
        Récupère les hyperparamètres pour un modèle.

        Args:
            model_name: Nom du modèle

        Returns:
            Dictionnaire des hyperparamètres

        Raises:
            KeyError: Si les hyperparamètres ne sont pas définis pour ce modèle
        """
        if model_name not in self.hyperparameters:
            raise KeyError(f"Hyperparamètres non définis pour le modèle '{model_name}'")

        return self.hyperparameters[model_name]