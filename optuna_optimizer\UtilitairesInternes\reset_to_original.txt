# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 955 à 976
# Type: Méthode de la classe DynamicRangeAdjuster

    def reset_to_original(self) -> None:
        """Réinitialise les plages à leurs valeurs originales."""
        if self.original_ranges:
            # Construire un dictionnaire pour _update_config_ranges
            reset_ranges = {}
            for param_name, (param_type, low, high) in self.original_ranges.items():
                reset_ranges[param_name] = (param_type, low, high)

            # Mettre à jour config.py avec les plages originales
            success, unadjusted_params = self._update_config_ranges(reset_ranges)

            # Avertir si certains paramètres n'ont pas pu être réinitialisés
            if unadjusted_params:
                logger.warning(f"ATTENTION: Les paramètres suivants n'ont pas pu être réinitialisés: {unadjusted_params}")

            # Réinitialiser les variables
            self.adjusted_ranges = {}
            self.study_lock = None

            logger.info("Plages réinitialisées aux valeurs originales")
        else:
            logger.warning("Aucune plage originale sauvegardée. Impossible de réinitialiser.")