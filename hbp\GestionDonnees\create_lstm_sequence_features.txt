# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 13570 à 13745
# Type: Méthode de la classe HybridBaccaratPredictor

    def create_lstm_sequence_features(self, sequence: List[str], keep_history_length: int = None) -> Optional[np.ndarray]:
        """
        Crée une matrice de features pour le modèle LSTM à partir d'une séquence.
        Utilise une fenêtre adaptative (toute la séquence disponible) mais garantit
        une taille de sortie fixe pour le modèle LSTM.

        Pour calculer les probabilités de la manche N, utilise les N-1 manches précédentes,
        exactement comme LGBM.

        Args:
            sequence (List[str]): Séquence de résultats ('player', 'banker')
            keep_history_length (int, optional): Taille maximale de la matrice de sortie.
                                               Si None, utilise la valeur de config appropriée.

        Returns:
            Optional[np.ndarray]: Matrice de features (keep_history_length, lstm_input_size) ou None si erreur
        """
        if not sequence:
            return None

        # Déterminer la longueur de séquence à utiliser pour la sortie
        if keep_history_length is None:
            # Utiliser toujours lstm_sequence_length pour uniformiser le code
            keep_history_length = self.config.lstm_sequence_length
            # Ne pas afficher de message de débogage pendant l'optimisation Optuna
            if (not hasattr(self, 'is_optuna_running') or not self.is_optuna_running) and hasattr(self, 'first_run') and self.first_run:
                logger.debug(f"create_lstm_sequence_features: Utilisation de lstm_sequence_length ({keep_history_length}) pour tous les modèles")
                # Désactiver les messages futurs
                self.first_run = False

        # Utiliser toute la séquence disponible (fenêtre adaptative)
        effective_sequence = sequence[:]
        seq_length = len(effective_sequence)
        features_count = self.config.lstm_input_size  # Nombre attendu de features

        # Initialiser matrice de features avec des valeurs par défaut (zéros)
        sequence_features = np.zeros((keep_history_length, features_count), dtype=np.float32)

        # Initialiser les compteurs et variables nécessaires
        banker_count = 0
        player_count = 0
        current_streak_type = None
        current_streak_length = 0
        alternance_count = 0
        previous_outcome = None

        # Récupérer tous les paramètres de configuration
        use_adaptive_window = getattr(self.config, 'lstm_use_adaptive_window', True)
        streak_norm_factor = getattr(self.config, 'lstm_streak_normalization_factor', 10.0)
        recent_window_size = getattr(self.config, 'lstm_recent_window_size', 5)
        avg_game_length = getattr(self.config, 'lstm_avg_game_length', 80)

        # Paramètres supplémentaires qui pourraient être utiles
        banker_weight = getattr(self.config, 'lstm_banker_weight', 1.0)
        player_weight = getattr(self.config, 'lstm_player_weight', 1.0)
        streak_weight = getattr(self.config, 'lstm_streak_weight', 1.0)
        alternance_weight = getattr(self.config, 'lstm_alternance_weight', 1.0)
        imbalance_scale = getattr(self.config, 'lstm_imbalance_scale', 2.0)

        # Approche fenêtre adaptative: utiliser les N derniers éléments de la séquence
        # Si la séquence est plus courte que keep_history_length, on utilise toute la séquence avec padding au début
        # Si la séquence est plus longue, on prend les keep_history_length derniers éléments
        if use_adaptive_window:
            if seq_length <= keep_history_length:
                # Séquence plus courte que la taille requise: utiliser toute la séquence avec padding au début
                indices_to_use = list(range(seq_length))
                # Ajuster les indices pour le remplissage de la matrice (pour tenir compte du padding)
                matrix_indices = list(range(keep_history_length - len(indices_to_use), keep_history_length))
            else:
                # Séquence plus longue que la taille requise: prendre les keep_history_length derniers éléments
                start_idx = seq_length - keep_history_length
                indices_to_use = list(range(start_idx, seq_length))
                matrix_indices = list(range(keep_history_length))
        else:
            # Approche originale avec échantillonnage
            if seq_length <= keep_history_length:
                # Utiliser les indices directs
                indices_to_use = list(range(seq_length))
                # Ajuster les indices pour le remplissage de la matrice
                matrix_indices = list(range(len(indices_to_use)))
            else:
                # Échantillonner la séquence pour la faire tenir dans keep_history_length
                step = seq_length / keep_history_length
                indices_to_use = [min(seq_length - 1, int(i * step)) for i in range(keep_history_length)]
                matrix_indices = list(range(keep_history_length))

        # Remplir la matrice avec les données disponibles
        for i, (matrix_idx, seq_idx) in enumerate(zip(matrix_indices, indices_to_use)):
            outcome = effective_sequence[seq_idx]

            # Feature 1: Position relative (normalisée) dans la séquence
            sequence_features[matrix_idx, 0] = (seq_idx + 1) / seq_length

            # Feature 2: Est-ce un banker (1) ou player (0)
            # Harmonisation: 0 = Player, 1 = Banker (indices standards PyTorch)
            is_banker = 1 if outcome == 'banker' else 0
            sequence_features[matrix_idx, 1] = is_banker

            # Calculer les compteurs pour cette position (utiliser la séquence jusqu'à seq_idx inclus)
            sub_sequence = effective_sequence[:seq_idx+1]
            banker_count = sum(1 for x in sub_sequence if x == 'banker')
            player_count = sum(1 for x in sub_sequence if x == 'player')

            # Features 3-4: Ratios cumulatifs banker et player
            total = banker_count + player_count
            if total > 0:
                sequence_features[matrix_idx, 2] = banker_count / total
                sequence_features[matrix_idx, 3] = player_count / total

            # Feature 5: Est-ce une répétition du résultat précédent
            if seq_idx > 0:
                is_repeat = 1 if outcome == effective_sequence[seq_idx-1] else 0
                sequence_features[matrix_idx, 4] = is_repeat

                # Tracking des alternances pour Feature 8
                if outcome != previous_outcome:
                    alternance_count += 1
            else:
                sequence_features[matrix_idx, 4] = 0  # Pas de précédent pour le premier élément

            previous_outcome = outcome

            # Feature 6: Banker count dans les N derniers coups (normalisé)
            window_size = min(recent_window_size, seq_idx+1)
            if window_size > 0:
                last_n = effective_sequence[seq_idx+1-window_size:seq_idx+1]
                banker_in_last_n = sum(1 for x in last_n if x == 'banker')
                sequence_features[matrix_idx, 5] = banker_in_last_n / window_size

            # Features 7-8: Gestion des streaks
            # Calculer le streak actuel à cette position
            current_streak_type = outcome
            current_streak_length = 1
            for j in range(seq_idx-1, -1, -1):
                if effective_sequence[j] == current_streak_type:
                    current_streak_length += 1
                else:
                    break

            # Longueur du streak actuel (normalisé)
            sequence_features[matrix_idx, 6] = min(current_streak_length / streak_norm_factor, 1.0)  # Capping à 1.0
            # Type du streak actuel (banker=1, player=0)
            # Harmonisation: 0 = Player, 1 = Banker (indices standards PyTorch)
            sequence_features[matrix_idx, 7] = 1 if current_streak_type == 'banker' else 0

            # Feature 8: Ratio d'alternance
            if seq_idx > 0:
                sequence_features[matrix_idx, 8] = alternance_count / seq_idx

            # Feature 9: Phase du jeu (début, milieu, fin)
            game_position_ratio = min((seq_idx + 1) / avg_game_length, 1.0)
            sequence_features[matrix_idx, 9] = game_position_ratio

            # Feature 10: Déséquilibre banker-player récent (N derniers coups)
            window_size = min(recent_window_size, seq_idx+1)
            if window_size > 0:
                window = effective_sequence[max(0, seq_idx+1-window_size):seq_idx+1]
                banker_in_window = sum(1 for x in window if x == 'banker')
                imbalance = (banker_in_window / window_size) - 0.5  # -0.5 à 0.5
                sequence_features[matrix_idx, 10] = imbalance * imbalance_scale  # Mise à l'échelle configurable

            # Feature 11: Proximité du dernier changement de streak
            if seq_idx > 0 and effective_sequence[seq_idx-1] != outcome:
                sequence_features[matrix_idx, 11] = 0  # Changement immédiat
            elif seq_idx > 1:
                # Chercher le dernier changement
                for j in range(seq_idx-1, 0, -1):
                    if effective_sequence[j] != effective_sequence[j-1]:
                        # Normaliser par la taille de séquence
                        sequence_features[matrix_idx, 11] = (seq_idx - j) / seq_length
                        break

            # Les 3 features spécifiques à l'optimisation Optuna ont été supprimées
            # pour réduire le nombre de features de 15 à 12

        return sequence_features