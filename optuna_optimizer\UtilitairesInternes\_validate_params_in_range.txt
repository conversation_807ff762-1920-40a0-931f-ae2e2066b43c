# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 4623 à 4676
# Type: Méthode de la classe OptunaOptimizer

    def _validate_params_in_range(self, params, search_space=None):
        """
        Vérifie si les paramètres sont dans les plages définies et les ajuste si nécessaire.

        Args:
            params: Dictionnaire des paramètres à valider
            search_space: Espace de recherche à utiliser (si None, utilise self.config.optuna_search_space)

        Returns:
            dict: Paramètres validés et ajustés si nécessaire
        """
        # Si aucun espace de recherche n'est fourni, utiliser celui de la configuration
        if search_space is None:
            search_space = getattr(self.config, 'optuna_search_space', {})

        if not search_space:
            logger.warning("Aucun espace de recherche défini pour la validation des paramètres")
            return params

        # Copier les paramètres pour ne pas modifier l'original
        validated_params = params.copy()

        # Parcourir tous les paramètres
        for param_name, param_value in params.items():
            # Vérifier si le paramètre est dans l'espace de recherche
            if param_name in search_space:
                param_type, *param_args = search_space[param_name]

                # Traiter selon le type de paramètre
                if param_type == 'int':
                    min_val, max_val = param_args[0], param_args[1]
                    if param_value < min_val:
                        logger.warning(f"Paramètre '{param_name}' avec valeur {param_value} est inférieur à la limite minimale {min_val}. Ajusté à {min_val}.")
                        validated_params[param_name] = min_val
                    elif param_value > max_val:
                        logger.warning(f"Paramètre '{param_name}' avec valeur {param_value} est supérieur à la limite maximale {max_val}. Ajusté à {max_val}.")
                        validated_params[param_name] = max_val

                elif param_type == 'float':
                    min_val, max_val = param_args[0], param_args[1]
                    if param_value < min_val:
                        logger.warning(f"Paramètre '{param_name}' avec valeur {param_value} est inférieur à la limite minimale {min_val}. Ajusté à {min_val}.")
                        validated_params[param_name] = min_val
                    elif param_value > max_val:
                        logger.warning(f"Paramètre '{param_name}' avec valeur {param_value} est supérieur à la limite maximale {max_val}. Ajusté à {max_val}.")
                        validated_params[param_name] = max_val

                elif param_type == 'categorical':
                    valid_values = param_args[0]
                    if param_value not in valid_values:
                        logger.warning(f"Paramètre '{param_name}' avec valeur {param_value} n'est pas dans les valeurs valides {valid_values}. Ajusté à {valid_values[0]}.")
                        validated_params[param_name] = valid_values[0]

        return validated_params