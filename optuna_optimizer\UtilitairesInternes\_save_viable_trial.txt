# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 4839 à 4987
# Type: Méthode de la classe OptunaOptimizer

    def _save_viable_trial(self, trial_info):
        """
        Sauvegarde un essai viable dans un fichier pour pouvoir le récupérer en cas d'erreur.
        Sauvegarde également les modèles entraînés si l'essai est final.

        Args:
            trial_info: Informations sur l'essai viable à sauvegarder
        """
        try:
            import json
            import os
            import joblib

            # Créer le dossier de sauvegarde s'il n'existe pas
            save_dir = os.path.join(os.getcwd(), "viable_trials")
            os.makedirs(save_dir, exist_ok=True)

            # Sauvegarder l'essai viable
            trial_number = trial_info.get('trial_number', 'unknown')
            save_path = os.path.join(save_dir, f"viable_trial_{trial_number}.json")

            # Convertir les objets non sérialisables avec gestion améliorée
            serializable_info = {}
            for k, v in trial_info.items():
                if k == 'params':
                    # Traitement spécial pour les paramètres de configuration
                    param_dict = {}
                    for param, value in v.items():
                        # Vérifier si c'est un paramètre booléen
                        if param.startswith(('use_', 'lstm_use_', 'lgbm_use_')) or isinstance(value, bool):
                            # Les paramètres booléens sont maintenant correctement gérés à la source
                            # Traitement simplifié pour les chaînes 'true'/'false'
                            if isinstance(value, str) and value.lower() in ('true', 'false'):
                                # Convertir les chaînes 'true'/'false' en booléens
                                param_dict[param] = value.lower() == 'true'
                            else:
                                # Pour les autres valeurs, utiliser la conversion standard
                                param_dict[param] = bool(value)
                        elif isinstance(value, (int, float, bool, str, type(None))):
                            param_dict[param] = value
                        elif isinstance(value, np.ndarray):
                            # Convertir les tableaux numpy en listes
                            param_dict[param] = value.tolist()
                        elif isinstance(value, (list, tuple)):
                            # Convertir les listes et tuples en JSON
                            try:
                                param_dict[param] = list(value)
                            except:
                                param_dict[param] = str(value)
                        elif hasattr(value, '__dict__'):
                            # Tenter de sérialiser les objets avec __dict__
                            try:
                                param_dict[param] = value.__dict__
                            except:
                                param_dict[param] = str(value)
                        else:
                            # Fallback pour les autres types
                            param_dict[param] = str(value)
                    serializable_info[k] = param_dict
                elif isinstance(v, dict):
                    # Traitement similaire pour les dictionnaires
                    dict_values = {}
                    for sk, sv in v.items():
                        if isinstance(sv, (int, float, bool, str, type(None))):
                            dict_values[sk] = sv
                        elif isinstance(sv, np.ndarray):
                            dict_values[sk] = sv.tolist()
                        elif isinstance(sv, (list, tuple)):
                            try:
                                dict_values[sk] = list(sv)
                            except:
                                dict_values[sk] = str(sv)
                        else:
                            dict_values[sk] = str(sv)
                    serializable_info[k] = dict_values
                elif isinstance(v, np.ndarray):
                    serializable_info[k] = v.tolist()
                elif isinstance(v, (list, tuple)):
                    try:
                        serializable_info[k] = list(v)
                    except:
                        serializable_info[k] = str(v)
                else:
                    serializable_info[k] = str(v) if not isinstance(v, (int, float, bool, str, type(None))) else v

            with open(save_path, 'w') as f:
                json.dump(serializable_info, f, indent=2)

            logger.warning("=" * 80)
            logger.warning(f"ESSAI VIABLE SAUVEGARDÉ DANS {save_path}")
            logger.warning("Cet essai pourra être récupéré automatiquement en cas d'erreur")
            logger.warning("=" * 80)

            # Afficher les paramètres clés de l'essai viable
            logger.warning("PARAMÈTRES CLÉS DE L'ESSAI VIABLE SAUVEGARDÉ:")
            for param_name in ['min_confidence_for_recommendation', 'wait_ratio_min_threshold',
                              'error_pattern_threshold', 'transition_uncertainty_threshold',
                              'wait_optimizer_confidence_threshold']:
                if param_name in trial_info.get('params', {}):
                    logger.warning(f"  {param_name}: {trial_info['params'][param_name]}")
            logger.warning("=" * 80)

            # Si c'est un essai final ou une vague finale, sauvegarder également les modèles entraînés
            is_final = trial_info.get('final', False)
            is_last_wave = trial_info.get('wave', 0) == 3  # Supposons que la vague 3 est la dernière

            if is_final or is_last_wave:
                try:
                    # Obtenir une instance de HybridBaccaratPredictor
                    hbp_instance = get_hbp_instance(trial_id=trial_number)

                    if hbp_instance:
                        # Créer un dossier pour les modèles
                        models_dir = os.path.join(save_dir, f"models_{trial_number}")
                        os.makedirs(models_dir, exist_ok=True)

                        # Chemin du fichier de sauvegarde des modèles
                        models_save_path = os.path.join(models_dir, f"optimized_models_score_{trial_info.get('score', 0.0):.4f}.joblib")

                        # Sauvegarder les modèles
                        logger.warning(f"Sauvegarde des modèles entraînés pour l'essai {trial_number} dans {models_save_path}")

                        # Utiliser la méthode _perform_save de HybridBaccaratPredictor
                        if hasattr(hbp_instance, '_perform_save'):
                            save_success = hbp_instance._perform_save(models_save_path)

                            if save_success:
                                logger.warning(f"Modèles entraînés sauvegardés avec succès dans {models_save_path}")

                                # Ajouter le chemin du fichier de modèles à l'essai viable
                                with open(save_path, 'r') as f:
                                    updated_info = json.load(f)

                                updated_info['models_path'] = models_save_path

                                with open(save_path, 'w') as f:
                                    json.dump(updated_info, f, indent=2)

                                logger.warning(f"Chemin des modèles ajouté à l'essai viable: {models_save_path}")
                            else:
                                logger.error(f"Échec de la sauvegarde des modèles entraînés pour l'essai {trial_number}")
                        else:
                            logger.error(f"La méthode _perform_save n'est pas disponible dans l'instance HBP")
                    else:
                        logger.error(f"Impossible d'obtenir une instance HBP pour sauvegarder les modèles")
                except Exception as e:
                    logger.error(f"Erreur lors de la sauvegarde des modèles entraînés: {e}", exc_info=True)
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde de l'essai viable: {e}", exc_info=True)