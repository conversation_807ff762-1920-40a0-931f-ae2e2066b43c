# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 223 à 282
# Type: Méthode

def mixup_data(x, y, alpha=0.2, device=None, adaptive=True):
    """
    Applique l'augmentation Mixup aux données avec des améliorations.

    Cette version améliorée inclut:
    - Mixup adaptatif qui ajuste le paramètre lambda en fonction de la difficulté des exemples
    - Gestion améliorée des appareils (CPU/GPU)
    - Vérifications de sécurité pour éviter les erreurs

    Args:
        x: Données d'entrée (batch)
        y: Étiquettes
        alpha: Paramètre alpha pour la distribution Beta
        device: Device PyTorch
        adaptive: Si True, ajuste le mixup en fonction de la difficulté des exemples

    Returns:
        Tuple (données mixées, étiquettes a, étiquettes b, lambda)
    """
    # Vérification de sécurité
    if x.size(0) <= 1:
        return x, y, y, 1.0  # Pas de mixup possible avec un seul exemple

    # Génération du paramètre lambda
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)

        # Mixup adaptatif: ajuste lambda pour être plus conservateur sur les exemples difficiles
        if adaptive:
            # On considère que les exemples avec des probabilités proches de 0.5 sont plus difficiles
            # Pour ces exemples, on réduit l'intensité du mixup (lambda plus proche de 1)
            if len(x.shape) >= 2 and x.shape[1] > 1:
                # Estimation de la difficulté basée sur la variance des features
                # Plus la variance est élevée, plus l'exemple est considéré comme difficile
                feature_variance = torch.var(x, dim=1)
                difficulty = torch.sigmoid(5 * (0.1 - feature_variance.mean()))

                # Ajuster lambda pour être plus proche de 1 pour les exemples difficiles
                # difficulty proche de 1 = exemple difficile, lambda plus proche de 1
                lam = lam * (1 - difficulty.item() * 0.5) + difficulty.item() * 0.5
    else:
        lam = 1.0

    batch_size = x.size()[0]

    # Gestion améliorée des appareils
    if device is None and x.is_cuda:
        device = x.device

    # Génération des indices de permutation
    if device is not None:
        index = torch.randperm(batch_size).to(device)
    else:
        index = torch.randperm(batch_size)

    # Application du mixup
    mixed_x = lam * x + (1 - lam) * x[index, :]
    y_a, y_b = y, y[index]

    return mixed_x, y_a, y_b, lam