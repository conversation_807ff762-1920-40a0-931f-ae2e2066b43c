# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 7269 à 7494
# Type: Méthode de la classe OptunaOptimizer

    def _parallel_phase_transition(self, best_trial, phase_from, phase_to, subset_indices):
        """
        Exécute la transition entre deux phases en parallèle.
        Cette méthode permet d'accélérer la phase intermédiaire entre les phases.

        Args:
            best_trial: Meilleur essai de la phase précédente
            phase_from: Phase de départ (0, 1, 2, 'markov')
            phase_to: Phase d'arrivée (1, 2, 3, 'markov')
            subset_indices: Indices du sous-ensemble à utiliser

        Returns:
            dict: Paramètres optimisés pour la phase suivante
        """
        import time
        import concurrent.futures
        import numpy as np

        # Mesurer le temps de transition
        start_time = time.time()

        logger.warning(f"Transition parallèle de la phase {phase_from} à la phase {phase_to}")

        # Créer un espace de recherche restreint basé sur le meilleur essai
        search_space = {}
        for param_name, param_value in best_trial.params.items():
            if isinstance(param_value, int):
                # Pour les paramètres entiers, définir une plage de ±10% (min 1)
                min_val = max(int(param_value * 0.9), param_value - 1)
                max_val = int(param_value * 1.1) + 1
                search_space[param_name] = {
                    'low': min_val,
                    'high': max_val,
                    'type': 'int'
                }
            elif isinstance(param_value, float):
                # Pour les paramètres flottants, définir une plage de ±10%
                min_val = param_value * 0.9
                max_val = param_value * 1.1
                search_space[param_name] = {
                    'low': min_val,
                    'high': max_val,
                    'type': 'float'
                }

        # Diviser les tâches en sous-tâches parallèles
        tasks = []

        # Tâche 1: Validation du meilleur essai sur un sous-ensemble plus grand
        def validate_best_trial():
            logger.warning(f"Validation du meilleur essai de la phase {phase_from} sur un sous-ensemble plus grand")

            # Créer une configuration temporaire avec les paramètres du meilleur essai
            temp_config = self.config.clone()
            for param_name, param_value in best_trial.params.items():
                setattr(temp_config, param_name, param_value)

            # Déterminer si nous devons forcer l'entraînement LSTM ou Markov
            force_lstm = False
            force_markov = False

            # Si nous passons à la phase 2 ou 3, activer LSTM
            if phase_to == 2 or phase_to == 3:
                force_lstm = True
                logger.warning(f"Activation forcée de LSTM pour la transition vers la phase {phase_to}")

            # Si nous passons à la phase Markov ou si nous venons de la phase Markov, activer Markov
            if phase_to == 'markov' or phase_from == 'markov':
                force_markov = True
                logger.warning(f"Activation forcée de Markov pour la transition de/vers la phase Markov")

            # Si nous passons à la phase 2 ou 3 après la phase Markov, activer à la fois LSTM et Markov
            if (phase_to == 2 or phase_to == 3) and phase_from == 'markov':
                force_lstm = True
                force_markov = True
                logger.warning(f"Activation forcée de LSTM et Markov pour la transition de la phase Markov vers la phase {phase_to}")

            # Évaluer la configuration sur un sous-ensemble plus grand
            result = self._evaluate_config(
                temp_config,
                is_viability_check=True,
                force_lstm_training=force_lstm,
                force_markov_training=force_markov,
                subset_indices=subset_indices,
                enable_cv=True,
                n_folds=3,
                phase_from=phase_from,
                phase_to=phase_to
            )

            return result

        # Tâche 2: Préparation des données pour la phase suivante avec parallélisation interne
        def prepare_data_for_next_phase():
            logger.warning(f"Préparation des données pour la phase {phase_to} avec parallélisation optimisée")

            # Sous-tâches pour la préparation des données
            def prepare_lgbm_features():
                return self.X_lgbm_full[subset_indices]

            def prepare_lstm_features():
                return self.X_lstm_full[subset_indices]

            def prepare_targets():
                return self.y_full[subset_indices]

            # Utiliser un pool de threads interne pour paralléliser la préparation des données
            with concurrent.futures.ThreadPoolExecutor(max_workers=3) as data_executor:
                future_lgbm = data_executor.submit(prepare_lgbm_features)
                future_lstm = data_executor.submit(prepare_lstm_features)
                future_y = data_executor.submit(prepare_targets)

                # Attendre que toutes les sous-tâches soient terminées
                concurrent.futures.wait([future_lgbm, future_lstm, future_y])

                # Récupérer les résultats
                X_lgbm_subset = future_lgbm.result()
                X_lstm_subset = future_lstm.result()
                y_subset = future_y.result()

            logger.warning(f"Préparation des données terminée: {len(subset_indices)} exemples traités")

            # Mettre en cache les features
            self._cache_features(subset_indices, X_lgbm_subset, X_lstm_subset, y_subset)

            # Journaliser des informations sur l'apprentissage par curriculum
            if phase_to == 1 or phase_to == 2 or phase_to == 3 or phase_to == 'markov':
                # Calculer la difficulté moyenne des exemples (si applicable)
                if hasattr(self, 'difficulty_scores') and self.difficulty_scores is not None:
                    subset_difficulties = self.difficulty_scores[subset_indices]
                    avg_difficulty = np.mean(subset_difficulties)
                    logger.info(f"Apprentissage par curriculum activé: {len(subset_indices)} exemples avec difficulté moyenne {avg_difficulty:.4f}")

                # Si nous passons à la phase Markov, préparer des données spécifiques pour Markov
                if phase_to == 'markov':
                    logger.warning(f"Préparation des données spécifiques pour la phase Markov")
                    # Ici, nous pourrions ajouter une préparation spécifique pour Markov si nécessaire

            return (X_lgbm_subset.shape, X_lstm_subset.shape, y_subset.shape)

        # Tâche 3: Création des variations du meilleur essai
        def create_variations():
            logger.warning(f"Création des variations du meilleur essai de la phase {phase_from}")

            # Créer plusieurs variations du meilleur essai
            variations = []

            # Liste des paramètres booléens qui doivent être traités spécialement
            boolean_params = [
                'lstm_use_adaptive_window', 'use_markov_model', 'lstm_use_attention',
                'lstm_use_residual', 'use_advanced_features', 'use_advanced_lstm',
                'use_ensemble', 'use_mixup', 'use_focal_loss', 'adaptive_confidence_threshold',
                'lstm_bidirectional', 'force_markov_training'
            ]

            # Variation 1: Légère variation positive
            variation1 = {}
            for param_name, param_value in best_trial.params.items():
                # Vérifier si c'est un paramètre booléen
                if param_name in boolean_params or param_name.startswith(('use_', 'lstm_use_', 'lgbm_use_')) or param_value is True or param_value is False:
                    # Ne pas modifier les paramètres booléens
                    logger.warning(f"Paramètre booléen '{param_name}' conservé sans variation: {param_value}")
                    variation1[param_name] = param_value
                elif isinstance(param_value, int):
                    variation = max(1, int(param_value * 0.05))
                    variation1[param_name] = param_value + variation
                elif isinstance(param_value, float):
                    variation = param_value * 0.05
                    variation1[param_name] = param_value + variation
                else:
                    variation1[param_name] = param_value

            # Variation 2: Légère variation négative
            variation2 = {}
            for param_name, param_value in best_trial.params.items():
                # Vérifier si c'est un paramètre booléen
                if param_name in boolean_params or param_name.startswith(('use_', 'lstm_use_', 'lgbm_use_')) or param_value is True or param_value is False:
                    # Ne pas modifier les paramètres booléens
                    logger.warning(f"Paramètre booléen '{param_name}' conservé sans variation: {param_value}")
                    variation2[param_name] = param_value
                elif isinstance(param_value, int):
                    variation = max(1, int(param_value * 0.05))
                    variation2[param_name] = max(1, param_value - variation)
                elif isinstance(param_value, float):
                    variation = param_value * 0.05
                    variation2[param_name] = max(0.0, param_value - variation)
                else:
                    variation2[param_name] = param_value

            variations.append(variation1)
            variations.append(variation2)

            return variations

        # Exécuter les tâches en parallèle avec 8 workers pour optimiser la phase transitoire
        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            future_validate = executor.submit(validate_best_trial)
            future_prepare = executor.submit(prepare_data_for_next_phase)
            future_variations = executor.submit(create_variations)

            # Attendre que toutes les tâches soient terminées
            concurrent.futures.wait([future_validate, future_prepare, future_variations])

            logger.warning(f"Phase transitoire exécutée avec 8 workers pour optimiser les performances")

            # Récupérer les résultats
            validation_result = future_validate.result()
            data_shapes = future_prepare.result()
            variations = future_variations.result()

        # Calculer le temps de transition
        elapsed_time = time.time() - start_time
        logger.warning(f"Transition parallèle terminée en {elapsed_time:.2f} secondes")

        # Afficher les résultats
        logger.warning(f"Résultat de la validation: {validation_result}")
        logger.warning(f"Formes des données: {data_shapes}")
        logger.warning(f"Nombre de variations créées: {len(variations)}")

        # Retourner les paramètres optimisés pour la phase suivante
        return {
            'best_params': best_trial.params,
            'variations': variations,
            'validation_result': validation_result,
            'data_shapes': data_shapes
        }