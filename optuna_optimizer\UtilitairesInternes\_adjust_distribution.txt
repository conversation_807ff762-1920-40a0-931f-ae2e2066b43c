# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 14419 à 14618
# Type: Méthode de la classe MetaOptimizer

    def _adjust_distribution(self, name: str, distribution: optuna.distributions.BaseDistribution) -> optuna.distributions.BaseDistribution:
        """
        Ajuste une distribution pour éviter les régions problématiques.
        Utilise une approche adaptative basée sur l'historique des essais.

        Args:
            name: Nom du paramètre
            distribution: Distribution originale

        Returns:
            optuna.distributions.BaseDistribution: Distribution ajustée
        """
        # Ne pas ajuster si pas assez d'essais analysés ou pas de régions problématiques
        if name not in self.problematic_params or self.analyzed_trials_count < 5:
            return distribution

        if isinstance(distribution, optuna.distributions.UniformDistribution):
            # Pour les distributions uniformes (float)
            low = distribution.low
            high = distribution.high
            range_width = high - low

            # Ajuster la distribution pour éviter les régions problématiques
            problematic_regions = (
                self.problematic_params[name]['too_few_wait'] +
                self.problematic_params[name]['too_many_wait']
            )

            # Paramètres spécifiques qui nécessitent un traitement particulier
            if name == 'min_confidence_for_recommendation':
                # Si trop de WAIT, réduire le seuil de confiance
                if self.problematic_params[name]['too_many_wait']:
                    new_low = max(0.15, low * 0.8)
                    new_high = min(0.4, high * 0.8)
                    logger.warning(f"Distribution ajustée pour '{name}' (trop de WAIT): {low:.4f}-{high:.4f} -> {new_low:.4f}-{new_high:.4f}")
                    return optuna.distributions.UniformDistribution(new_low, new_high)

                # Si trop peu de WAIT, augmenter le seuil de confiance
                if self.problematic_params[name]['too_few_wait']:
                    new_low = min(0.6, low * 1.2)
                    new_high = min(0.8, high * 1.2)
                    logger.warning(f"Distribution ajustée pour '{name}' (trop peu de WAIT): {low:.4f}-{high:.4f} -> {new_low:.4f}-{new_high:.4f}")
                    return optuna.distributions.UniformDistribution(new_low, new_high)

            elif name == 'transition_uncertainty_threshold':
                # Si trop de WAIT, augmenter le seuil d'incertitude
                if self.problematic_params[name]['too_many_wait']:
                    new_low = min(0.7, low * 1.1)
                    new_high = min(0.9, high * 1.1)
                    logger.warning(f"Distribution ajustée pour '{name}' (trop de WAIT): {low:.4f}-{high:.4f} -> {new_low:.4f}-{new_high:.4f}")
                    return optuna.distributions.UniformDistribution(new_low, new_high)

                # Si trop peu de WAIT, réduire le seuil d'incertitude
                if self.problematic_params[name]['too_few_wait']:
                    new_low = max(0.3, low * 0.9)
                    new_high = max(0.5, high * 0.9)
                    logger.warning(f"Distribution ajustée pour '{name}' (trop peu de WAIT): {low:.4f}-{high:.4f} -> {new_low:.4f}-{new_high:.4f}")
                    return optuna.distributions.UniformDistribution(new_low, new_high)

            elif name == 'global_uncertainty_factor':
                # Si trop de WAIT, réduire le facteur d'incertitude global
                if self.problematic_params[name]['too_many_wait']:
                    new_low = max(0.7, low * 0.9)
                    new_high = max(0.9, high * 0.9)
                    logger.warning(f"Distribution ajustée pour '{name}' (trop de WAIT): {low:.4f}-{high:.4f} -> {new_low:.4f}-{new_high:.4f}")
                    return optuna.distributions.UniformDistribution(new_low, new_high)

                # Si trop peu de WAIT, augmenter le facteur d'incertitude global
                if self.problematic_params[name]['too_few_wait']:
                    new_low = min(1.1, low * 1.1)
                    new_high = min(1.3, high * 1.1)
                    logger.warning(f"Distribution ajustée pour '{name}' (trop peu de WAIT): {low:.4f}-{high:.4f} -> {new_low:.4f}-{new_high:.4f}")
                    return optuna.distributions.UniformDistribution(new_low, new_high)

            # Pour les autres paramètres, utiliser une approche adaptative avancée
            # Identifier les zones prometteuses en analysant les essais réussis
            successful_values = []
            if hasattr(self, 'successful_trials') and self.successful_trials:
                for trial in self.successful_trials:
                    if name in trial['params']:
                        successful_values.append(trial['params'][name])

            # Si nous avons des valeurs réussies, les utiliser pour guider la recherche
            if len(successful_values) >= 3:
                # Calculer la moyenne et l'écart-type des valeurs réussies
                mean_value = np.mean(successful_values)
                std_value = np.std(successful_values)

                # Créer une distribution centrée autour de la moyenne des valeurs réussies
                # avec une largeur proportionnelle à l'écart-type
                new_low = max(low, mean_value - 2 * std_value)
                new_high = min(high, mean_value + 2 * std_value)

                # S'assurer que la plage n'est pas trop étroite (au moins 20% de la plage originale)
                min_width = range_width * 0.2
                if new_high - new_low < min_width:
                    center = (new_high + new_low) / 2
                    new_low = max(low, center - min_width / 2)
                    new_high = min(high, center + min_width / 2)

                logger.warning(f"Distribution ajustée pour '{name}' (basée sur {len(successful_values)} essais réussis): {low:.4f}-{high:.4f} -> {new_low:.4f}-{new_high:.4f}")
                return optuna.distributions.UniformDistribution(new_low, new_high)

            # Approche générique améliorée: échantillonnage adaptatif
            # Diviser l'espace en segments et évaluer chaque segment
            num_segments = 10
            segment_width = range_width / num_segments
            segment_scores = np.zeros(num_segments)

            # Évaluer chaque segment
            for i in range(num_segments):
                segment_low = low + i * segment_width
                segment_high = segment_low + segment_width
                segment_center = (segment_low + segment_high) / 2

                # Vérifier si le centre du segment est dans une région problématique
                if self._is_in_problematic_region(name, segment_center):
                    segment_scores[i] = -1  # Marquer comme problématique
                else:
                    segment_scores[i] = 1  # Marquer comme non problématique

            # Trouver le plus grand segment continu non problématique
            best_start = 0
            best_length = 0
            current_start = 0
            current_length = 0

            for i in range(num_segments):
                if segment_scores[i] > 0:
                    if current_length == 0:
                        current_start = i
                    current_length += 1
                else:
                    if current_length > best_length:
                        best_start = current_start
                        best_length = current_length
                    current_length = 0

            # Vérifier le dernier segment
            if current_length > best_length:
                best_start = current_start
                best_length = current_length

            # Si nous avons trouvé un segment non problématique
            if best_length > 0:
                new_low = low + best_start * segment_width
                new_high = new_low + best_length * segment_width

                logger.warning(f"Distribution ajustée pour '{name}' (segment non problématique): {low:.4f}-{high:.4f} -> {new_low:.4f}-{new_high:.4f}")
                return optuna.distributions.UniformDistribution(new_low, new_high)

            # Si tous les segments sont problématiques, essayer une approche aléatoire
            for _ in range(15):  # Augmenter le nombre d'essais à 15
                # Générer une valeur aléatoire dans la plage
                value = np.random.uniform(low, high)

                # Vérifier si la valeur est dans une région problématique
                if not self._is_in_problematic_region(name, value):
                    # Créer une nouvelle distribution centrée autour de cette valeur
                    # avec une largeur adaptative basée sur la densité des essais
                    width_factor = 0.3 - min(0.2, len(self.problematic_trials) / 50.0)
                    new_low = max(low, value - range_width * width_factor)
                    new_high = min(high, value + range_width * width_factor)

                    logger.warning(f"Distribution ajustée pour '{name}' (aléatoire): {low:.4f}-{high:.4f} -> {new_low:.4f}-{new_high:.4f}")
                    return optuna.distributions.UniformDistribution(new_low, new_high)

            # Si aucune région non problématique n'a été trouvée, élargir légèrement la plage
            # pour encourager l'exploration de nouvelles régions
            new_low = max(0, low - range_width * 0.15)
            new_high = min(1, high + range_width * 0.15)

            logger.warning(f"Distribution élargie pour '{name}' (exploration): {low:.4f}-{high:.4f} -> {new_low:.4f}-{new_high:.4f}")
            return optuna.distributions.UniformDistribution(new_low, new_high)

        elif isinstance(distribution, optuna.distributions.IntUniformDistribution):
            # Adapter la logique pour les distributions entières
            low = distribution.low
            high = distribution.high

            # Approche simplifiée pour les distributions entières
            # Éviter les régions problématiques en élargissant légèrement la plage
            new_low = max(0, low - 1)
            new_high = high + 1

            return optuna.distributions.IntUniformDistribution(new_low, new_high)

        elif isinstance(distribution, optuna.distributions.LogUniformDistribution):
            # Adapter la logique pour les distributions log-uniformes
            low = distribution.low
            high = distribution.high

            # Approche simplifiée pour les distributions log-uniformes
            # Éviter les régions problématiques en élargissant légèrement la plage
            new_low = max(1e-5, low * 0.9)
            new_high = high * 1.1

            return optuna.distributions.LogUniformDistribution(new_low, new_high)

        return distribution