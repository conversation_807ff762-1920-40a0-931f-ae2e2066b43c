# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 1796 à 1816
# Type: Méthode de la classe OptunaOptimizer

        def save_or_show_plot(fig, name):
            if output_file:
                # C<PERSON>er un nom de fichier basé sur le type de graphique
                base, ext = os.path.splitext(output_file)
                if not ext:
                    ext = '.png'
                file_path = f"{base}_{name}{ext}"

                # Sauvegarder le graphique
                fig.savefig(file_path, bbox_inches='tight', dpi=300)
                logger.warning(f"Graphique '{name}' sauvegardé dans {file_path}")

                results['plots_generated'].append({
                    'name': name,
                    'path': file_path
                })

            if show_plot:
                plt.show()
            else:
                plt.close(fig)