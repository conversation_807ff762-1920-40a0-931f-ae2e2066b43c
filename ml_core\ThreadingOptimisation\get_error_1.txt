# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1713 à 1720
# Type: Méthode de la classe ThreadedOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def get_error(self):
        """
        Récupère l'erreur de l'optimisation.

        Returns:
            L'erreur de l'optimisation ou None si l'optimisation n'a pas échoué
        """
        return self.error