DESCRIPTIF DÉTAILLÉ DES MÉTHODES - DEUXIÈME VAGUE TECHNIQUE
================================================================================

Ce fichier contient la description technique complète de toutes les méthodes du système.

ÉTAT DOCUMENTATION : DEUXIÈME VAGUE COMPLÈTE
- **Couverture** : 100% des méthodes enrichies techniquement
- **Niveau** : Code source réel intégré, détails d'implémentation
- **Qualité** : 25-40 lignes par méthode avec précision technique

STRUCTURE DU SYSTÈME :
- **SECTION 1 : LoggingMonitoring** : Gestion système logging et monitoring (2 méthodes)
- **SECTION 2 : ConfigurationGestion** : Gestionnaire configuration centralisé (4 méthodes)
- **SECTION 3 : CalculConfiance** : Calculs confiance et incertitude (6 méthodes)
- **SECTION 4 : UtilitairesInternes** : Fonctions utilitaires et helpers (19 méthodes)
- **SECTION 5 : MethodesInitialisation** : Constructeurs et initialisations (15 méthodes)
- **SECTION 6 : GestionMemoire** : Optimisation mémoire PyTorch (8 méthodes)
- **SECTION 7 : ThreadingOptimisation** : Threading et optimisation asynchrone (12 méthodes)
- **SECTION 8 : InterfacesModeles** : Interfaces et abstractions modèles (13 méthodes)
- **SECTION 9 : Anciennesclasses** : Classes principales du système (12 classes)

TOTAL : 91 MÉTHODES ANALYSÉES

================================================================================
SECTION 1 : LOGGINGMONITORING
================================================================================

1. configure_root_logger.txt (LoggingManager.configure_root_logger - CONFIGURATION LOGGER RACINE)
   - Lignes 154-192 dans ml_core.py (39 lignes)
   - FONCTION : Configure le logger racine du système avec handlers personnalisés pour console et fichier, formatage standardisé et gestion complète des niveaux de journalisation
   - PARAMÈTRES :
     * self - Instance de LoggingManager
     * level (int, défaut=logging.INFO) - Niveau de journalisation (DEBUG, INFO, WARNING, ERROR, CRITICAL)
     * console (bool, défaut=True) - Active/désactive l'affichage des logs sur la console
     * file (bool, défaut=True) - Active/désactive l'écriture des logs dans un fichier
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RÉCUPÉRATION LOGGER** : Obtient le logger racine avec logging.getLogger() sans paramètres
     * **CONFIGURATION NIVEAU** : Définit le niveau de journalisation avec root_logger.setLevel(level)
     * **NETTOYAGE HANDLERS** : Supprime tous les handlers existants pour éviter les doublons
     * **CRÉATION FORMATTER** : Instancie un formatter avec timestamp, nom, niveau et message
     * **STOCKAGE FORMATTER** : Sauvegarde le formatter dans self._formatters['default']
     * **HANDLER CONSOLE** : `console_handler = logging.StreamHandler(); root_logger.addHandler(console_handler)` si console=True
     * **HANDLER FICHIER** : `file_handler = logging.FileHandler(f"ml_core_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")` si file=True
     * **APPLICATION FORMATTER** : `handler.setFormatter(formatter)` application format à tous handlers
     * **STOCKAGE HANDLERS** : `self._handlers['console'] = console_handler; self._handlers['file'] = file_handler` référence
     * **STOCKAGE LOGGER** : `self._loggers['root'] = root_logger` enregistrement logger configuré
     * **LOGGING CONFIRMATION** : `root_logger.info("Logger racine configuré avec succès")` confirmation démarrage
     * **TYPE HINTS** : `level: int = logging.INFO`, `console: bool = True`, `file: bool = True` validation statique
   - RETOUR : None (configuration en place, pas de retour)
   - UTILITÉ : Méthode fondamentale pour initialiser le système de journalisation. Essentielle pour tracer l'exécution, déboguer et monitorer l'application. Permet la journalisation simultanée console/fichier avec formatage professionnel.

2. get_logger.txt (LoggingManager.get_logger - RÉCUPÉRATION LOGGER NOMMÉ)
   - Lignes 194-212 dans ml_core.py (19 lignes)
   - FONCTION : Récupère ou crée un logger avec un nom spécifique et un niveau de journalisation optionnel, avec enregistrement automatique dans le gestionnaire pour suivi et réutilisation
   - PARAMÈTRES :
     * self - Instance de LoggingManager
     * name (str) - Nom unique du logger à récupérer ou créer
     * level (Optional[int], défaut=None) - Niveau de journalisation spécifique, si None utilise le niveau parent
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RÉCUPÉRATION/CRÉATION** : `logger = logging.getLogger(name)` obtention logger existant ou création nouveau
     * **CONFIGURATION NIVEAU** : `if level is not None: logger.setLevel(level)` application niveau personnalisé si fourni
     * **HÉRITAGE NIVEAU** : Si level=None, logger hérite niveau parent (logger racine) automatiquement
     * **ENREGISTREMENT GESTIONNAIRE** : `self._loggers[name] = logger` stockage dans dictionnaire pour référence future
     * **RETOUR LOGGER** : `return logger` retourne instance logger configurée et enregistrée
     * **RÉUTILISATION** : Appels ultérieurs avec même nom retournent même logger (pattern Singleton)
     * **HIÉRARCHIE LOGGERS** : Respecte hiérarchie loggers Python (ex: 'app.module.submodule')
     * **TYPE HINTS** : `name: str`, `level: Optional[int] = None`, `-> logging.Logger` validation statique
     * **DOCSTRING COMPLÈTE** : Args, Returns avec descriptions détaillées
   - RETOUR : logging.Logger - Instance du logger configuré et enregistré
   - UTILITÉ : Méthode essentielle pour créer des loggers spécialisés par module ou composant. Permet la journalisation granulaire avec niveaux différents par zone de l'application. Facilite le debugging ciblé et la surveillance modulaire.

================================================================================
SECTION 2 : CONFIGURATIONGESTION
================================================================================

3. get.txt (ConfigManager.get - RÉCUPÉRATION VALEUR CONFIGURATION)
   - Lignes 90-105 dans ml_core.py (16 lignes)
   - FONCTION : Récupère une valeur de configuration depuis un espace de noms spécifique avec gestion des valeurs par défaut et validation d'existence
   - PARAMÈTRES :
     * self - Instance de ConfigManager
     * key (str) - Clé de configuration à récupérer
     * default (Any, défaut=None) - Valeur par défaut si la clé n'existe pas
     * namespace (str, défaut='default') - Espace de noms pour organiser les configurations
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION NAMESPACE** : Contrôle si l'espace de noms existe dans self._config
     * **RETOUR DÉFAUT NAMESPACE** : Si le namespace n'existe pas, retourne immédiatement la valeur par défaut
     * **RÉCUPÉRATION SÉCURISÉE** : `return self._config[namespace].get(key, default)` méthode get avec fallback
     * **GESTION HIÉRARCHIQUE** : Organisation configurations par espaces de noms avec dictionnaires imbriqués
     * **TYPE HINTS** : `key: str`, `default=None`, `namespace: str = 'default'`, `-> Any` validation statique
     * **DOCSTRING COMPLÈTE** : Args et Returns avec descriptions détaillées
     * **PERFORMANCE O(1)** : Accès direct dictionnaire avec fallback automatique
     * **VALEUR DÉFAUT** : Retourne default si clé ou namespace inexistants
   - RETOUR : Any - La valeur de configuration trouvée ou la valeur par défaut
   - UTILITÉ : Méthode centrale pour accéder aux configurations de l'application. Permet la récupération sécurisée avec fallbacks. Essentielle pour la configuration modulaire et la gestion des paramètres par domaine fonctionnel.

4. set.txt (ConfigManager.set - DÉFINITION VALEUR CONFIGURATION)
   - Lignes 107-121 dans ml_core.py (15 lignes)
   - FONCTION : Définit une valeur de configuration dans un espace de noms spécifique avec création automatique du namespace et logging des modifications
   - PARAMÈTRES :
     * self - Instance de ConfigManager
     * key (str) - Clé de configuration à définir
     * value (Any) - Valeur de configuration à stocker
     * namespace (str, défaut='default') - Espace de noms pour organiser les configurations
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CRÉATION NAMESPACE** : Si le namespace n'existe pas, crée automatiquement self._config[namespace] = {}
     * **STOCKAGE VALEUR** : Enregistre la valeur avec self._config[namespace][key] = value
     * **ÉCRASEMENT POSSIBLE** : Si la clé existe déjà, la valeur est remplacée silencieusement
     * **LOGGING DEBUG** : Enregistre un message de debug avec la clé, valeur et namespace pour traçabilité
     * **GESTION HIÉRARCHIQUE** : Permet l'organisation des configurations par espaces de noms
     * **CRÉATION DYNAMIQUE** : Crée les structures de données nécessaires à la volée
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour configurer l'application dynamiquement. Permet la modification des paramètres à l'exécution. Essentielle pour la configuration modulaire et l'adaptation des comportements par domaine fonctionnel.

5. __new__.txt (ConfigManager.__new__ - CRÉATION SINGLETON CONFIGURATION)
   - Lignes 43-47 dans ml_core.py (5 lignes)
   - FONCTION : Méthode spéciale qui implémente le pattern Singleton pour ConfigManager, garantissant une seule instance de gestionnaire de configuration dans l'application
   - PARAMÈTRES :
     * cls - Référence à la classe ConfigManager
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION INSTANCE** : Contrôle si cls._instance est None pour déterminer si une instance existe déjà
     * **CRÉATION UNIQUE** : Si aucune instance n'existe, crée une nouvelle instance avec super(ConfigManager, cls).__new__(cls)
     * **INITIALISATION** : Appelle cls._instance._initialize() pour configurer l'instance nouvellement créée
     * **RETOUR INSTANCE** : Retourne toujours la même instance (cls._instance) pour garantir l'unicité
     * **PATTERN SINGLETON** : Assure qu'une seule instance de ConfigManager existe dans toute l'application
   - RETOUR : ConfigManager - L'instance unique de ConfigManager (nouvelle ou existante)
   - UTILITÉ : Méthode fondamentale pour implémenter le pattern Singleton dans la gestion de configuration. Garantit une configuration centralisée et cohérente dans toute l'application. Essentielle pour éviter les conflits de configuration et assurer la cohérence des paramètres.

6. _initialize.txt (ConfigManager._initialize - INITIALISATION GESTIONNAIRE CONFIGURATION)
   - Lignes 49-54 dans ml_core.py (6 lignes)
   - FONCTION : Initialise les structures de données internes du gestionnaire de configuration pour stocker les configurations, sources, validateurs et valeurs par défaut
   - PARAMÈTRES :
     * self - Instance de ConfigManager
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONFIGURATION PRINCIPALE** : Initialise self._config = {} pour stocker les configurations par namespace
     * **SOURCES CONFIGURATION** : Initialise self._config_sources = {} pour tracer l'origine des configurations
     * **VALIDATEURS** : Initialise self._validators = {} pour stocker les fonctions de validation par clé
     * **VALEURS DÉFAUT** : Initialise self._default_values = {} pour stocker les valeurs par défaut
     * **STRUCTURES VIDES** : Crée des dictionnaires vides prêts à recevoir les données de configuration
     * **PRÉPARATION SYSTÈME** : Prépare toutes les structures nécessaires au fonctionnement du gestionnaire
   - RETOUR : None (méthode d'initialisation, pas de retour)
   - UTILITÉ : Méthode d'initialisation fondamentale pour préparer le gestionnaire de configuration. Crée toutes les structures de données nécessaires au stockage et à la gestion des configurations. Essentielle pour le bon fonctionnement du pattern Singleton de ConfigManager.

================================================================================
SECTION 3 : CALCULCONFIANCE
================================================================================

7. calculate_calibrated_confidence.txt (ConfidenceCalculator.calculate_calibrated_confidence - CALIBRATION CONFIANCE)
   - Lignes 955-970 dans ml_core.py (16 lignes)
   - FONCTION : Calcule une confiance calibrée à partir des probabilités brutes pour améliorer la fiabilité des scores de confiance, avec support pour différentes méthodes de calibration (isotonic, platt)
   - PARAMÈTRES :
     * probabilities - Probabilités brutes après softmax à calibrer
     * calibration_method (str, défaut='isotonic') - Méthode de calibration ('isotonic' pour régression isotonique ou 'platt' pour scaling de Platt)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTHODE PLACEHOLDER** : Implémentation actuelle est un placeholder en attente de développement complet
     * **CALIBRATION ISOTONIC** : Méthode par défaut utilisant la régression isotonique pour calibration monotone
     * **CALIBRATION PLATT** : Alternative utilisant le Platt scaling (régression logistique) pour calibration
     * **DÉPENDANCE SKLEARN** : Nécessiterait sklearn.calibration.CalibratedClassifierCV pour implémentation complète
     * **WARNING LOGGING** : `logger.warning("Calibration de confiance non implémentée, retour des probabilités brutes")` message explicite
     * **RETOUR DIRECT** : `return probabilities` retourne probabilités sans modification
     * **TYPE HINTS COMPLETS** : `probabilities`, `calibration_method='isotonic'` paramètres avec défaut
     * **DOCSTRING COMPLÈTE** : Args et Returns avec descriptions détaillées
     * **MÉTHODE STATIQUE** : `@staticmethod` pour utilisation sans instance classe
     * **PATTERN PLACEHOLDER** : Implémentation temporaire en attente développement sklearn
     * **FONCTIONNALITÉ FUTURE** : Prévue pour améliorer significativement la fiabilité des scores de confiance
   - RETOUR : Probabilités (actuellement non calibrées, identiques à l'entrée)
   - UTILITÉ : Fonctionnalité avancée pour améliorer la fiabilité des prédictions de confiance. Essentielle pour les applications critiques nécessitant des probabilités bien calibrées. Actuellement en développement, retourne les probabilités brutes.

8. get_confidence_from_probabilities.txt (ConfidenceCalculator.get_confidence_from_probabilities - EXTRACTION CONFIANCE)
   - Lignes 800-832 dans ml_core.py (33 lignes)
   - FONCTION : Extrait la confiance pour une classe prédite spécifique à partir des probabilités, avec gestion robuste des différents formats de données et validation des indices
   - PARAMÈTRES :
     * probabilities - Tenseur PyTorch, liste ou array numpy contenant les probabilités après softmax
     * predicted_class - Indice 0-based de la classe prédite (0=Player, 1=Banker)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION INDICE** : Vérifie que predicted_class est dans la plage valide [0, 1]
     * **CORRECTION AUTOMATIQUE** : Si l'indice est invalide, le corrige avec max(0, min(1, predicted_class))
     * **WARNING LOGGING** : Enregistre un avertissement si un indice invalide est détecté
     * **DÉTECTION FORMAT** : Identifie le type de données (tenseur PyTorch 2D, 1D, liste, array numpy)
     * **TENSEUR 2D** : Pour probabilities.dim() > 1, accède avec probabilities[0][predicted_class].item()
     * **TENSEUR 1D** : Pour tenseur 1D, accède avec probabilities[predicted_class].item()
     * **LISTE/ARRAY** : Pour autres formats, utilise float(probabilities[predicted_class])
     * **EXTRACTION SÉCURISÉE** : Utilise .item() pour les tenseurs PyTorch pour conversion en float Python
     * **GESTION UNIVERSELLE** : Compatible avec tous les formats de données ML courants
   - RETOUR : float - Valeur de confiance pour la classe prédite (probabilité entre 0 et 1)
   - UTILITÉ : Méthode centrale pour extraire la confiance des prédictions ML. Essentielle pour l'évaluation de la fiabilité des modèles. Robuste aux différents formats de données et erreurs d'indices. Critique pour les systèmes de prise de décision basés sur la confiance.

9. get_confidence_from_probabilities_1.txt (get_confidence_from_probabilities - WRAPPER EXTRACTION CONFIANCE - DOUBLON)
   - Lignes 1330-1341 dans ml_core.py (12 lignes)
   - FONCTION : Fonction wrapper qui délègue l'extraction de confiance à la méthode statique ConfidenceCalculator.get_confidence_from_probabilities, fournissant une interface simplifiée
   - PARAMÈTRES :
     * probabilities - Tenseur de probabilités après softmax
     * predicted_class - Indice 0-based de la classe prédite (0 ou 1)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉLÉGATION DIRECTE** : Appelle immédiatement ConfidenceCalculator.get_confidence_from_probabilities avec les mêmes paramètres
     * **INTERFACE SIMPLIFIÉE** : Permet d'accéder à l'extraction de confiance sans référencer explicitement ConfidenceCalculator
     * **TRANSPARENCE TOTALE** : Transmet tous les paramètres sans modification ni traitement supplémentaire
     * **MÊME COMPORTEMENT** : Produit exactement le même résultat que la méthode originale de ConfidenceCalculator
   - RETOUR : float - Valeur de confiance pour la classe prédite (retour direct de ConfidenceCalculator.get_confidence_from_probabilities)
   - UTILITÉ : Fonction de commodité pour l'extraction de confiance sans nécessiter de connaître l'architecture interne. Particulièrement utile pour les utilisateurs qui veulent extraire rapidement la confiance d'une prédiction sans se soucier de l'implémentation sous-jacente.

10. train_consecutive_confidence_calculator.txt (ConfidenceCalculator.train_consecutive_confidence_calculator - CALCUL CONFIANCE RECOMMANDATIONS CONSÉCUTIVES)
   - Lignes 835-952 dans ml_core.py (118 lignes)
   - FONCTION : Calcule la confiance pour les recommandations consécutives basée sur l'analyse de motifs dans l'historique des séquences avec gestion des manches cibles
   - PARAMÈTRES :
     * sequence_history (List[str]) - Historique des résultats ('banker' ou 'player')
     * current_position (int) - Position actuelle dans la séquence
     * config (optionnel) - Configuration avec target_round_min/max et wait_threshold
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION HISTORIQUE** : `if not sequence_history or current_position < 10:` vérification suffisance données
     * **RETOUR DÉFAUT** : `return {"confidence": 0.5, "expected_consecutive": 0, ...}` si historique insuffisant
     * **DÉTECTION MANCHES CIBLES** : `is_target_round = (current_position % 10) in [0, 1, 2, 3, 4]` calcul modulo pour manches cibles
     * **RETOUR HORS CIBLES** : `if not is_target_round: return {"confidence": 0.5, ...}` confiance neutre hors manches
     * **ANALYSE PATTERNS** : Recherche patterns similaires dans historique avec fenêtre glissante et seuil similarité
     * **COMPTAGE PATTERNS** : `banker_count = sum(1 for result in similar_patterns if result == 'banker')` comptage occurrences banker
     * **CALCUL CONFIANCE** : `confidence = max(banker_count, player_count) / similar_patterns_count` proportion majoritaire
     * **EXPECTED CONSECUTIVE** : `expected_consecutive = min(5, int(confidence * 5))` calcul recommandations consécutives
     * **SEUIL WAIT** : `wait_threshold = getattr(config, 'wait_threshold', 0.65) if config else 0.65` paramètre configurable
     * **RECOMMANDATION WAIT** : `wait_recommendation = confidence < wait_threshold` logique décision binaire
     * **FORCES RECOMMANDATION** : `wait_recommendation_strength = max(0.0, wait_threshold - confidence) / wait_threshold` calcul intensité wait
     * **RETOUR COMPLET** : Dictionnaire structuré avec confidence, expected_consecutive, similar_patterns_count, success_rate, etc.
   - RETOUR : Dict[str, float] - Métriques complètes (confidence, expected_consecutive, similar_patterns_count, success_rate, is_target_round, wait_recommendation_strength, non_wait_recommendation_strength, wait_recommendation, wait_reason)
   - UTILITÉ : Méthode sophistiquée pour analyser les motifs de séquences et calculer la confiance des recommandations. Essentielle pour les systèmes de prédiction basés sur l'analyse de motifs historiques. Critique pour optimiser les stratégies de recommandation avec gestion des risques.

11. train_consecutive_confidence_calculator_1.txt (train_consecutive_confidence_calculator - WRAPPER CALCUL CONFIANCE CONSÉCUTIVE - DOUBLON)
   - Lignes 1343-1355 dans ml_core.py (13 lignes)
   - FONCTION : Fonction wrapper qui délègue le calcul de confiance consécutive à ConfidenceCalculator.train_consecutive_confidence_calculator, fournissant une interface simplifiée
   - PARAMÈTRES :
     * sequence_history - Historique des résultats (liste de 'banker' ou 'player')
     * current_position - Position actuelle dans la séquence
     * config (optionnel) - Configuration optionnelle
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉLÉGATION DIRECTE** : Appelle immédiatement ConfidenceCalculator.train_consecutive_confidence_calculator avec les mêmes paramètres
     * **INTERFACE SIMPLIFIÉE** : Permet d'accéder au calcul de confiance consécutive sans référencer explicitement ConfidenceCalculator
     * **TRANSPARENCE TOTALE** : Transmet tous les paramètres sans modification ni traitement supplémentaire
     * **MÊME COMPORTEMENT** : Produit exactement le même résultat que la méthode originale de ConfidenceCalculator
   - RETOUR : Dict[str, float] - Dictionnaire contenant les métriques de confiance (retour direct de ConfidenceCalculator.train_consecutive_confidence_calculator)
   - UTILITÉ : Fonction de commodité pour le calcul de confiance consécutive sans nécessiter de connaître l'architecture interne. Particulièrement utile pour les utilisateurs qui veulent calculer rapidement la confiance des recommandations consécutives sans se soucier de l'implémentation sous-jacente.

12. get_confidence_from_probabilities.txt (ConfidenceCalculator.get_confidence_from_probabilities - EXTRACTION CONFIANCE DEPUIS PROBABILITÉS)
   - Lignes 800-832 dans ml_core.py (33 lignes)
   - FONCTION : Extrait la confiance pour une classe prédite à partir des probabilités avec gestion robuste des formats de données et validation des indices
   - PARAMÈTRES :
     * probabilities - Tenseur de probabilités (après softmax) ou liste/array de probabilités
     * predicted_class (int) - Classe prédite (indice 0-based, 0=Player ou 1=Banker)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION INDICE** : `if predicted_class < 0 or predicted_class > 1:` contrôle bornes [0, 1]
     * **LOGGING WARNING** : `logger.warning(f"ATTENTION: Indice invalide détecté dans predicted_class: {predicted_class}")` avertissement formaté
     * **CORRECTION AUTOMATIQUE** : `predicted_class = max(0, min(1, predicted_class))` clamp entre 0 et 1
     * **GESTION TENSEUR 2D** : `if hasattr(probabilities, 'dim') and probabilities.dim() > 1: confidence = probabilities[0][predicted_class].item()` PyTorch 2D
     * **GESTION TENSEUR 1D** : `elif hasattr(probabilities, 'item'): confidence = probabilities[predicted_class].item()` PyTorch 1D
     * **GESTION ARRAY/LISTE** : `else: confidence = float(probabilities[predicted_class])` numpy/liste Python
     * **CONVERSION FLOAT** : `float()` assure type Python standard pour compatibilité
     * **DOCSTRING COMPLÈTE** : Args, Returns avec descriptions détaillées types et indices
     * **ROBUSTESSE FORMATS** : Support tenseurs PyTorch, arrays numpy, listes Python
     * **GESTION ERREURS** : Validation et correction automatique indices invalides
   - RETOUR : float - Confiance pour la classe prédite (valeur entre 0 et 1)
   - UTILITÉ : Méthode essentielle pour extraire la confiance depuis différents formats de probabilités. Permet la robustesse face aux variations de format de données. Critique pour les systèmes ML utilisant différents frameworks (PyTorch, NumPy, listes).

================================================================================
SECTION 4 : UTILITAIRESINTERNES
================================================================================

13. get_instance.txt (ModuleInterface.get_instance - RÉCUPÉRATION INSTANCE ENREGISTRÉE)
   - Lignes 321-341 dans ml_core.py (21 lignes)
   - FONCTION : Récupère une instance enregistrée dans l'interface avec support pour le chargement paresseux et gestion d'erreurs appropriée
   - PARAMÈTRES :
     * self - Instance de ModuleInterface
     * name (str) - Nom de l'instance à récupérer depuis le registre
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION PRIMAIRE** : `if name not in self._instances:` contrôle existence directe dans dictionnaire
     * **CHARGEMENT PARESSEUX CONDITIONNEL** : `if name in self._lazy_loaders:` vérifie disponibilité loader différé
     * **EXÉCUTION LOADER** : `self._lazy_loaders[name]()` appelle fonction de chargement sans paramètres
     * **DOUBLE VÉRIFICATION POST-LOADER** : `if name not in self._instances:` contrôle à nouveau après tentative de chargement
     * **EXCEPTION EXPLICITE** : `raise KeyError(f"Instance '{name}' non enregistrée dans l'interface")` avec message formaté
     * **RETOUR SÉCURISÉ** : `return self._instances[name]` accès direct au dictionnaire après validation
     * **PATTERN LAZY LOADING** : Séquence if-check → lazy-load → if-check → error/return pour optimisation mémoire
     * **THREAD-SAFETY** : Accès atomique aux dictionnaires self._instances et self._lazy_loaders
     * **TYPE HINTS** : `name: str` et `-> Any` pour validation statique et documentation
     * **DOCSTRING COMPLÈTE** : Args, Returns, Raises avec descriptions détaillées
   - RETOUR : Any - L'instance enregistrée (type dépend de l'instance stockée)
   - RAISES : KeyError - Si l'instance n'est pas enregistrée et aucun loader paresseux disponible
   - UTILITÉ : Méthode essentielle pour accéder aux instances enregistrées avec chargement paresseux. Permet l'optimisation mémoire et la gestion flexible des dépendances. Critique pour les systèmes modulaires avec chargement à la demande.

14. register_instance.txt (ModuleInterface.register_instance - ENREGISTREMENT INSTANCE)
   - Lignes 310-319 dans ml_core.py (10 lignes)
   - FONCTION : Enregistre une instance dans l'interface avec un nom unique pour permettre la récupération ultérieure et la gestion centralisée des instances
   - PARAMÈTRES :
     * self - Instance de ModuleInterface
     * name (str) - Nom unique de l'instance pour identification et récupération
     * instance (Any) - Instance à enregistrer (peut être n'importe quel type d'objet)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE ATOMIQUE** : `self._instances[name] = instance` assignation directe dans dictionnaire
     * **ÉCRASEMENT SILENCIEUX** : Si name existe déjà, remplace l'instance précédente sans avertissement
     * **LOGGING DEBUG** : `logger.debug(f"Instance '{name}' enregistrée dans l'interface")` avec f-string formatée
     * **TYPE HINTS COMPLETS** : `name: str`, `instance: Any`, `-> None` pour validation statique
     * **GESTION UNIVERSELLE** : Any accepte classes, fonctions, objets, primitives, collections
     * **RÉFÉRENCE DIRECTE** : Stocke pointeur vers instance originale, pas de copie/clone
     * **THREAD-SAFETY** : Opération atomique sur dictionnaire Python (GIL protection)
     * **DOCSTRING STANDARD** : Args avec descriptions pour name et instance
     * **PATTERN REGISTRY** : Implémente registre centralisé avec clé-valeur simple
     * **PERFORMANCE** : O(1) pour insertion dans dictionnaire hash
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour construire un registre d'instances centralisé. Permet la gestion de multiples instances avec accès par nom. Essentielle pour les systèmes modulaires utilisant l'injection de dépendances et le pattern Registry.

15. get_class.txt (ModuleInterface.get_class - RÉCUPÉRATION CLASSE ENREGISTRÉE)
   - Lignes 288-308 dans ml_core.py (21 lignes)
   - FONCTION : Récupère une classe enregistrée dans l'interface avec support pour le chargement paresseux et gestion d'erreurs appropriée
   - PARAMÈTRES :
     * self - Instance de ModuleInterface
     * name (str) - Nom de la classe à récupérer depuis le registre
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION PRIMAIRE** : `if name not in self._classes:` contrôle existence directe dans dictionnaire
     * **CHARGEMENT PARESSEUX CONDITIONNEL** : `if name in self._lazy_loaders:` vérifie disponibilité loader différé
     * **EXÉCUTION LOADER** : `self._lazy_loaders[name]()` appelle fonction de chargement sans paramètres
     * **DOUBLE VÉRIFICATION POST-LOADER** : `if name not in self._classes:` contrôle à nouveau après tentative de chargement
     * **EXCEPTION EXPLICITE** : `raise KeyError(f"Classe '{name}' non enregistrée dans l'interface")` avec message formaté
     * **RETOUR SÉCURISÉ** : `return self._classes[name]` accès direct au dictionnaire après validation
     * **PATTERN LAZY LOADING** : Séquence if-check → lazy-load → if-check → error/return identique à get_instance
     * **TYPE HINTS COMPLETS** : `name: str` et `-> Type` pour validation statique et documentation
     * **THREAD-SAFETY** : Accès atomique aux dictionnaires self._classes et self._lazy_loaders
     * **DOCSTRING COMPLÈTE** : Args, Returns, Raises avec descriptions détaillées
   - RETOUR : Type - La classe enregistrée (type Python)
   - RAISES : KeyError - Si la classe n'est pas enregistrée et aucun loader paresseux disponible
   - UTILITÉ : Méthode essentielle pour accéder aux classes enregistrées avec chargement paresseux. Permet l'optimisation mémoire et la gestion flexible des dépendances de classes. Critique pour les systèmes modulaires avec chargement dynamique de classes.

16. register_class.txt (ModuleInterface.register_class - ENREGISTREMENT CLASSE)
   - Lignes 277-286 dans ml_core.py (10 lignes)
   - FONCTION : Enregistre une classe dans l'interface avec un nom unique pour permettre la récupération ultérieure et la gestion centralisée des classes
   - PARAMÈTRES :
     * self - Instance de ModuleInterface
     * name (str) - Nom unique de la classe pour identification et récupération
     * cls (Type) - Classe Python à enregistrer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE ATOMIQUE** : `self._classes[name] = cls` assignation directe dans dictionnaire
     * **ÉCRASEMENT SILENCIEUX** : Si name existe déjà, remplace la classe précédente sans avertissement
     * **LOGGING DEBUG** : `logger.debug(f"Classe '{name}' enregistrée dans l'interface")` avec f-string formatée
     * **TYPE HINTS COMPLETS** : `name: str`, `cls: Type`, `-> None` pour validation statique
     * **GESTION TYPES** : Type accepte classes, métaclasses, types génériques, ABC
     * **RÉFÉRENCE DIRECTE** : Stocke pointeur vers classe originale, pas de copie/clone
     * **THREAD-SAFETY** : Opération atomique sur dictionnaire Python (GIL protection)
     * **DOCSTRING STANDARD** : Args avec descriptions pour name et cls
     * **PATTERN REGISTRY** : Implémente registre centralisé avec clé-valeur simple
     * **PERFORMANCE** : O(1) pour insertion dans dictionnaire hash
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour construire un registre de classes centralisé. Permet la gestion de multiples classes avec accès par nom. Essentielle pour les systèmes modulaires utilisant l'injection de dépendances et le pattern Registry pour les classes.

17. get_function.txt (ModuleInterface.get_function - RÉCUPÉRATION FONCTION ENREGISTRÉE)
   - Lignes 255-275 dans ml_core.py (21 lignes)
   - FONCTION : Récupère une fonction enregistrée dans l'interface avec support pour le chargement paresseux et gestion d'erreurs appropriée
   - PARAMÈTRES :
     * self - Instance de ModuleInterface
     * name (str) - Nom de la fonction à récupérer depuis le registre
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION PRIMAIRE** : `if name not in self._functions:` contrôle existence directe dans dictionnaire
     * **CHARGEMENT PARESSEUX CONDITIONNEL** : `if name in self._lazy_loaders:` vérifie disponibilité loader différé
     * **EXÉCUTION LOADER** : `self._lazy_loaders[name]()` appelle fonction de chargement sans paramètres
     * **DOUBLE VÉRIFICATION POST-LOADER** : `if name not in self._functions:` contrôle à nouveau après tentative de chargement
     * **EXCEPTION EXPLICITE** : `raise KeyError(f"Fonction '{name}' non enregistrée dans l'interface")` avec message formaté
     * **RETOUR SÉCURISÉ** : `return self._functions[name]` accès direct au dictionnaire après validation
     * **PATTERN LAZY LOADING** : Séquence if-check → lazy-load → if-check → error/return identique aux autres get_*
     * **TYPE HINTS COMPLETS** : `name: str` et `-> Callable` pour validation statique et documentation
     * **THREAD-SAFETY** : Accès atomique aux dictionnaires self._functions et self._lazy_loaders
     * **DOCSTRING COMPLÈTE** : Args, Returns, Raises avec descriptions détaillées
   - RETOUR : Callable - La fonction enregistrée (objet callable)
   - RAISES : KeyError - Si la fonction n'est pas enregistrée et aucun loader paresseux disponible
   - UTILITÉ : Méthode essentielle pour accéder aux fonctions enregistrées avec chargement paresseux. Permet l'optimisation mémoire et la gestion flexible des dépendances de fonctions. Critique pour les systèmes modulaires avec chargement dynamique de fonctions.

18. register_function.txt (ModuleInterface.register_function - ENREGISTREMENT FONCTION)
   - Lignes 244-253 dans ml_core.py (10 lignes)
   - FONCTION : Enregistre une fonction dans l'interface avec un nom unique pour permettre la récupération ultérieure et la gestion centralisée des fonctions
   - PARAMÈTRES :
     * self - Instance de ModuleInterface
     * name (str) - Nom unique de la fonction pour identification et récupération
     * function (Callable) - Fonction Python à enregistrer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE ATOMIQUE** : `self._functions[name] = function` assignation directe dans dictionnaire
     * **ÉCRASEMENT SILENCIEUX** : Si name existe déjà, remplace la fonction précédente sans avertissement
     * **LOGGING DEBUG** : `logger.debug(f"Fonction '{name}' enregistrée dans l'interface")` avec f-string formatée
     * **TYPE HINTS COMPLETS** : `name: str`, `function: Callable`, `-> None` pour validation statique
     * **GESTION CALLABLES** : Callable accepte fonctions, méthodes, lambdas, classes avec __call__
     * **RÉFÉRENCE DIRECTE** : Stocke pointeur vers fonction originale, pas de copie/clone
     * **THREAD-SAFETY** : Opération atomique sur dictionnaire Python (GIL protection)
     * **DOCSTRING STANDARD** : Args avec descriptions pour name et function
     * **PATTERN REGISTRY** : Implémente registre centralisé avec clé-valeur simple
     * **PERFORMANCE** : O(1) pour insertion dans dictionnaire hash
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour construire un registre de fonctions centralisé. Permet la gestion de multiples fonctions avec accès par nom. Essentielle pour les systèmes modulaires utilisant l'injection de dépendances et le pattern Registry pour les fonctions.

19. get_hbp_instance.txt (ModelProvider.get_hbp_instance - RÉCUPÉRATION INSTANCE HBP SINGLETON)
   - Lignes 649-746 dans ml_core.py (98 lignes)
   - FONCTION : Implémente le pattern Singleton pour HybridBaccaratPredictor avec configuration avancée, initialisation complète et gestion thread-safe
   - PARAMÈTRES :
     * cls - Référence à la classe ModelProvider (méthode de classe)
     * trial_id (optionnel) - Identifiant de l'essai Optuna pour collecteur de statistiques
     * config (optionnel) - Configuration à utiliser pour l'instance HBP
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION INSTANCE** : Contrôle si cls._hbp_instance existe déjà pour éviter la recréation
     * **MISE À JOUR CONFIG** : Si config fournie et différente, met à jour les attributs de configuration existants
     * **THREAD-SAFETY** : Utilise cls._hbp_instance_lock pour éviter les problèmes de concurrence
     * **DOUBLE-CHECK LOCKING** : Vérifie à nouveau l'instance après acquisition du verrou
     * **IMPORTATION CONDITIONNELLE** : Importe HybridBaccaratPredictor seulement quand nécessaire pour éviter les cycles
     * **CONFIGURATION DÉFAUT** : Crée PredictorConfig() si aucune configuration fournie
     * **DÉSACTIVATION AUTO-UPDATE** : Désactive auto_update_enabled pour stabilité pendant optimisation
     * **COLLECTEUR STATS** : Initialise OptimizationStatsCollector si disponible pour trial_id
     * **FLAG OPTUNA** : Définit is_optuna_running=True pour indiquer phase d'optimisation
     * **INITIALISATION SÉQUENCE** : Crée sequence=[] vide pour éviter les erreurs
     * **MODÈLE MARKOV** : Initialise PersistentMarkov avec paramètres de configuration validés
     * **GESTION ERREURS** : Capture exceptions Markov et désactive use_markov_model si échec
   - RETOUR : HybridBaccaratPredictor - L'instance unique configurée et initialisée
   - UTILITÉ : Méthode centrale pour accès singleton HBP avec configuration complète. Essentielle pour optimisation Optuna et gestion centralisée des instances. Critique pour éviter les conflits de ressources et garantir la cohérence.

20. register_lazy_loader.txt (ModuleInterface.register_lazy_loader - ENREGISTREMENT CHARGEUR PARESSEUX)
   - Lignes 343-352 dans ml_core.py (10 lignes)
   - FONCTION : Enregistre un chargeur paresseux qui sera exécuté uniquement lorsque la dépendance sera demandée, implémentant le pattern Lazy Loading pour optimisation mémoire
   - PARAMÈTRES :
     * self - Instance de ModuleInterface
     * name (str) - Nom de la dépendance à charger paresseusement
     * loader (Callable) - Fonction qui charge la dépendance à la demande
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE LOADER** : Enregistre le chargeur dans self._lazy_loaders[name] avec le nom comme clé
     * **EXÉCUTION DIFFÉRÉE** : Le loader ne sera appelé que lors de la première demande de la dépendance
     * **ÉCRASEMENT POSSIBLE** : Si un loader avec le même nom existe, il sera remplacé silencieusement
     * **LOGGING DEBUG** : Enregistre un message de debug confirmant l'enregistrement du chargeur paresseux
     * **PATTERN LAZY LOADING** : Implémente le chargement paresseux pour optimisation mémoire et performance
     * **FONCTION CALLABLE** : Accepte toute fonction callable qui peut charger la dépendance
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour implémenter le chargement paresseux de dépendances. Permet l'optimisation mémoire en chargeant les ressources seulement quand nécessaire. Essentielle pour les systèmes modulaires avec dépendances lourdes ou optionnelles.

21. load_from_python_file.txt (ConfigManager.load_from_python_file - CHARGEMENT CONFIGURATION FICHIER PYTHON)
   - Lignes 56-88 dans ml_core.py (33 lignes)
   - FONCTION : Charge la configuration depuis un fichier Python en extrayant les attributs publics et en les stockant dans un namespace spécifique
   - PARAMÈTRES :
     * self - Instance de ConfigManager
     * file_path (str) - Chemin vers le fichier Python contenant la configuration
     * namespace (str, défaut='default') - Espace de noms pour organiser la configuration
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CRÉATION SPEC** : `spec = importlib.util.spec_from_file_location("config_module", file_path)` création spécification module
     * **INSTANCIATION MODULE** : `config_module = importlib.util.module_from_spec(spec)` création objet module
     * **EXÉCUTION MODULE** : `spec.loader.exec_module(config_module)` exécution code Python du fichier
     * **EXTRACTION ATTRIBUTS** : `config_dict = {key: value for key, value in vars(config_module).items() if not key.startswith('_') and not callable(value)}` filtrage attributs publics
     * **STOCKAGE CONFIG** : `self._config[namespace] = config_dict` assignation dans dictionnaire configuration
     * **TRAÇABILITÉ SOURCE** : `self._config_sources[namespace] = file_path` stockage chemin source pour debugging
     * **LOGGING SUCCÈS** : `logger.info(f"Configuration chargée depuis {file_path} dans l'espace de noms '{namespace}'")` confirmation
     * **GESTION ERREURS** : `try-except` capture toutes exceptions avec logging détaillé
     * **TYPE HINTS** : `file_path: str`, `namespace: str = 'default'`, `-> bool` validation statique
     * **RETOUR SUCCÈS** : `return True` en cas de succès, `return False` en cas d'erreur
   - RETOUR : bool - True si le chargement a réussi, False en cas d'erreur
   - UTILITÉ : Méthode avancée pour charger la configuration depuis des fichiers Python. Permet la configuration dynamique et flexible. Essentielle pour les systèmes nécessitant des configurations complexes avec logique Python.

22. get_metric.txt (TrainOptimizeInterface.get_metric - RÉCUPÉRATION MÉTRIQUE ENREGISTRÉE)
   - Lignes 1146-1162 dans ml_core.py (17 lignes)
   - FONCTION : Récupère une métrique enregistrée dans l'interface avec validation d'existence et gestion d'erreurs appropriée
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * name (str) - Nom de la métrique à récupérer depuis le registre
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION EXISTENCE** : `if name not in self.metrics:` contrôle existence directe dans dictionnaire
     * **EXCEPTION EXPLICITE** : `raise KeyError(f"Métrique '{name}' non enregistrée dans l'interface")` avec message formaté
     * **RETOUR SÉCURISÉ** : `return self.metrics[name]` accès direct au dictionnaire après validation
     * **TYPE HINTS COMPLETS** : `name: str` et `-> Any` pour validation statique et documentation
     * **DOCSTRING COMPLÈTE** : Args, Returns, Raises avec descriptions détaillées
     * **THREAD-SAFETY** : Accès atomique au dictionnaire self.metrics (GIL protection)
     * **PERFORMANCE** : O(1) pour vérification et récupération dans dictionnaire hash
     * **GESTION ERREURS** : Message d'erreur explicite incluant nom métrique pour debugging
   - RETOUR : Any - La métrique enregistrée (type dépend de la métrique stockée)
   - RAISES : KeyError - Si la métrique demandée n'est pas enregistrée dans l'interface
   - UTILITÉ : Méthode essentielle pour accéder aux métriques enregistrées dans le système. Permet la récupération sécurisée avec validation d'existence. Critique pour les systèmes d'évaluation utilisant des métriques personnalisées.

23. register_metric.txt (TrainOptimizeInterface.register_metric - ENREGISTREMENT MÉTRIQUE)
   - Lignes 1135-1144 dans ml_core.py (10 lignes)
   - FONCTION : Enregistre une métrique dans l'interface avec un nom unique pour permettre la récupération ultérieure et la gestion centralisée des métriques
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * name (str) - Nom unique de la métrique pour identification et récupération
     * metric (Any) - Métrique à enregistrer (peut être une fonction, classe ou objet métrique)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE ATOMIQUE** : `self.metrics[name] = metric` assignation directe dans dictionnaire
     * **ÉCRASEMENT SILENCIEUX** : Si name existe déjà, remplace la métrique précédente sans avertissement
     * **LOGGING DEBUG** : `logger.debug(f"Métrique '{name}' enregistrée dans l'interface")` avec f-string formatée
     * **TYPE HINTS COMPLETS** : `name: str`, `metric: Any`, `-> None` pour validation statique
     * **GESTION UNIVERSELLE** : Any accepte fonctions, classes, objets métriques, callables
     * **RÉFÉRENCE DIRECTE** : Stocke pointeur vers métrique originale, pas de copie/clone
     * **THREAD-SAFETY** : Opération atomique sur dictionnaire Python (GIL protection)
     * **DOCSTRING STANDARD** : Args avec descriptions pour name et metric
     * **PATTERN REGISTRY** : Implémente registre centralisé avec clé-valeur simple
     * **PERFORMANCE** : O(1) pour insertion dans dictionnaire hash
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour construire un registre de métriques centralisé. Permet la gestion de multiples métriques avec accès par nom. Essentielle pour les systèmes d'évaluation utilisant des métriques personnalisées et configurables.

24. get_result_1.txt (ThreadedTrainer.get_result - RÉCUPÉRATION RÉSULTAT ENTRAÎNEMENT - HOMONYME)
   - Lignes 1564-1571 dans ml_core.py (8 lignes)
   - FONCTION : Récupère le résultat de l'entraînement threadé pour inspection et analyse des performances
   - PARAMÈTRES :
     * self - Instance de ThreadedTrainer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RETOUR DIRECT** : Retourne self.result sans traitement supplémentaire
     * **ÉTAT RÉSULTAT** : Contient le résultat de l'entraînement ou None si non terminé/échoué
     * **INSPECTION** : Permet l'inspection du résultat pour analyse et debugging
     * **GETTER SIMPLE** : Méthode getter pure sans effets de bord
     * **THREAD-SAFE** : Lecture atomique d'une référence d'objet
   - RETOUR : Any ou None - Le résultat de l'entraînement ou None si l'entraînement n'est pas terminé ou a échoué
   - UTILITÉ : Méthode essentielle pour récupérer les résultats d'entraînement threadé. Permet l'inspection des performances et l'analyse des résultats. Critique pour les interfaces utilisateur affichant les résultats et les systèmes d'analyse de performance.

25. get_result_2.txt (ThreadedOptimizer.get_result - RÉCUPÉRATION RÉSULTAT OPTIMISATION - HOMONYME)
   - Lignes 1704-1711 dans ml_core.py (8 lignes)
   - FONCTION : Récupère le résultat de l'optimisation threadée pour inspection et analyse des hyperparamètres optimaux
   - PARAMÈTRES :
     * self - Instance de ThreadedOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RETOUR DIRECT** : Retourne self.result sans traitement supplémentaire
     * **ÉTAT RÉSULTAT** : Contient le résultat de l'optimisation ou None si non terminée/échouée
     * **INSPECTION** : Permet l'inspection du résultat pour analyse et debugging
     * **GETTER SIMPLE** : Méthode getter pure sans effets de bord
     * **THREAD-SAFE** : Lecture atomique d'une référence d'objet
   - RETOUR : Any ou None - Le résultat de l'optimisation ou None si l'optimisation n'est pas terminée ou a échoué
   - UTILITÉ : Méthode essentielle pour récupérer les résultats d'optimisation threadée. Permet l'inspection des hyperparamètres optimaux et l'analyse des résultats. Critique pour les interfaces utilisateur affichant les résultats d'optimisation et les systèmes d'analyse de performance.

26. get_hyperparameters.txt (TrainOptimizeInterface.get_hyperparameters - RÉCUPÉRATION HYPERPARAMÈTRES MODÈLE)
   - Lignes 1175-1191 dans ml_core.py (17 lignes)
   - FONCTION : Récupère les hyperparamètres pour un modèle spécifique avec validation d'existence et gestion d'erreurs appropriée
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * model_name (str) - Nom du modèle pour lequel récupérer les hyperparamètres
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION EXISTENCE** : Contrôle si model_name existe dans self.hyperparameters
     * **GESTION ERREUR** : Lève KeyError avec message explicite si les hyperparamètres ne sont pas définis
     * **MESSAGE EXPLICITE** : Inclut le nom du modèle dans le message d'erreur pour debugging
     * **RÉCUPÉRATION DIRECTE** : Retourne self.hyperparameters[model_name] après validation
     * **VALIDATION PRÉALABLE** : Assure que seuls les hyperparamètres valides et définis sont retournés
   - RETOUR : Dict[str, Any] - Dictionnaire des hyperparamètres pour le modèle spécifié
   - RAISES : KeyError - Si les hyperparamètres ne sont pas définis pour le modèle demandé
   - UTILITÉ : Méthode essentielle pour accéder aux hyperparamètres de modèles dans le système. Permet la récupération sécurisée avec validation. Critique pour les systèmes d'optimisation et de configuration de modèles ML.

27. set_hyperparameters.txt (TrainOptimizeInterface.set_hyperparameters - DÉFINITION HYPERPARAMÈTRES MODÈLE)
   - Lignes 1164-1173 dans ml_core.py (10 lignes)
   - FONCTION : Définit les hyperparamètres pour un modèle spécifique avec stockage centralisé et logging des modifications
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * model_name (str) - Nom du modèle pour lequel définir les hyperparamètres
     * hyperparameters (Dict[str, Any]) - Dictionnaire des hyperparamètres à stocker
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE ATOMIQUE** : `self.hyperparameters[model_name] = hyperparameters` assignation directe dans dictionnaire
     * **ÉCRASEMENT SILENCIEUX** : Si hyperparamètres existent déjà pour ce modèle, ils seront remplacés sans avertissement
     * **LOGGING DEBUG** : `logger.debug(f"Hyperparamètres définis pour le modèle '{model_name}'")` avec f-string formatée
     * **TYPE HINTS COMPLETS** : `model_name: str`, `hyperparameters: Dict[str, Any]`, `-> None` validation statique
     * **GESTION UNIVERSELLE** : Dict[str, Any] accepte tous types d'hyperparamètres (int, float, str, bool, listes)
     * **RÉFÉRENCE DIRECTE** : Stocke pointeur vers dictionnaire original, pas de copie/clone
     * **THREAD-SAFETY** : Opération atomique sur dictionnaire Python (GIL protection)
     * **DOCSTRING STANDARD** : Args avec descriptions pour model_name et hyperparameters
     * **PATTERN REGISTRY** : Implémente registre centralisé avec clé-valeur simple
     * **PERFORMANCE** : O(1) pour insertion dans dictionnaire hash
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour configurer les hyperparamètres de modèles dans le système. Permet la gestion centralisée des configurations de modèles. Essentielle pour les systèmes d'optimisation et de configuration dynamique de modèles ML.

28. get_data_source.txt (TrainOptimizeInterface.get_data_source - RÉCUPÉRATION SOURCE DONNÉES)
   - Lignes 1117-1133 dans ml_core.py (17 lignes)
   - FONCTION : Récupère une source de données enregistrée dans l'interface avec validation d'existence et gestion d'erreurs appropriée
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * name (str) - Nom de la source de données à récupérer depuis le registre
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION EXISTENCE** : `if name not in self.data_sources:` contrôle existence directe dans dictionnaire
     * **EXCEPTION EXPLICITE** : `raise KeyError(f"Source de données '{name}' non enregistrée dans l'interface")` avec message formaté
     * **RETOUR SÉCURISÉ** : `return self.data_sources[name]` accès direct au dictionnaire après validation
     * **TYPE HINTS COMPLETS** : `name: str` et `-> Any` pour validation statique et documentation
     * **DOCSTRING COMPLÈTE** : Args, Returns, Raises avec descriptions détaillées
     * **THREAD-SAFETY** : Accès atomique au dictionnaire self.data_sources (GIL protection)
     * **PERFORMANCE** : O(1) pour vérification et récupération dans dictionnaire hash
     * **GESTION ERREURS** : Message d'erreur explicite incluant nom source pour debugging
     * **PATTERN REGISTRY** : Accès sécurisé aux sources de données enregistrées par nom
   - RETOUR : Any - La source de données enregistrée (type dépend de la source stockée)
   - RAISES : KeyError - Si la source de données demandée n'est pas enregistrée dans l'interface
   - UTILITÉ : Méthode essentielle pour accéder aux sources de données enregistrées dans le système. Permet la récupération sécurisée avec validation d'existence. Critique pour les systèmes utilisant multiples sources de données avec gestion centralisée.

29. register_data_source.txt (TrainOptimizeInterface.register_data_source - ENREGISTREMENT SOURCE DONNÉES)
   - Lignes 1106-1115 dans ml_core.py (10 lignes)
   - FONCTION : Enregistre une source de données dans l'interface avec un nom unique pour permettre la récupération ultérieure et la gestion centralisée des sources
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * name (str) - Nom unique de la source de données pour identification et récupération
     * data_source (Any) - Source de données à enregistrer (peut être DataFrame, fichier, API, etc.)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE ATOMIQUE** : `self.data_sources[name] = data_source` assignation directe dans dictionnaire
     * **ÉCRASEMENT SILENCIEUX** : Si source avec même nom existe, elle sera remplacée sans avertissement
     * **LOGGING DEBUG** : `logger.debug(f"Source de données '{name}' enregistrée dans l'interface")` avec f-string formatée
     * **TYPE HINTS COMPLETS** : `name: str`, `data_source: Any`, `-> None` pour validation statique
     * **GESTION UNIVERSELLE** : Any accepte DataFrames, fichiers, connexions DB, APIs, streams
     * **RÉFÉRENCE DIRECTE** : Stocke pointeur vers source originale, pas de copie/clone
     * **THREAD-SAFETY** : Opération atomique sur dictionnaire Python (GIL protection)
     * **DOCSTRING STANDARD** : Args avec descriptions pour name et data_source
     * **PATTERN REGISTRY** : Implémente registre centralisé avec clé-valeur simple
     * **PERFORMANCE** : O(1) pour insertion dans dictionnaire hash
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour construire un registre de sources de données centralisé. Permet la gestion de multiples sources avec accès par nom. Essentielle pour les systèmes ML utilisant diverses sources de données avec gestion centralisée.

30. get_hbp_instance_1.txt (get_hbp_instance - WRAPPER INSTANCE HBP - DOUBLON)
   - Lignes 1308-1319 dans ml_core.py (12 lignes)
   - FONCTION : Fonction wrapper qui délègue la création d'instance HBP à ModelProvider.get_hbp_instance, fournissant une interface simplifiée pour l'accès singleton
   - PARAMÈTRES :
     * trial_id (optionnel) - Identifiant de l'essai Optuna pour collecteur de statistiques
     * config (optionnel) - Configuration à utiliser pour l'instance HBP
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉLÉGATION DIRECTE** : `return ModelProvider.get_hbp_instance(trial_id, config)` appel méthode statique avec paramètres
     * **INTERFACE SIMPLIFIÉE** : Permet accès instance HBP sans référencer explicitement ModelProvider
     * **TRANSPARENCE TOTALE** : Transmet tous paramètres sans modification ni traitement supplémentaire
     * **MÊME COMPORTEMENT** : Produit exactement même résultat que méthode originale ModelProvider
     * **IMPORTATION CONDITIONNELLE** : Évite importations circulaires en déléguant à ModelProvider
     * **DOCSTRING COMPLÈTE** : Args, Returns avec descriptions détaillées
     * **PATTERN FACADE** : Simplifie accès à fonctionnalité complexe ModelProvider
     * **TYPE HINTS** : `trial_id=None`, `config=None`, `-> HybridBaccaratPredictor` validation statique
   - RETOUR : HybridBaccaratPredictor - Instance singleton configurée (retour direct de ModelProvider.get_hbp_instance)
   - UTILITÉ : Fonction de commodité pour l'accès à l'instance HBP sans nécessiter de connaître l'architecture interne. Particulièrement utile pour les utilisateurs qui veulent accéder rapidement à l'instance HBP sans se soucier du pattern Singleton sous-jacent.

31. get_calculate_uncertainty_1.txt (get_calculate_uncertainty - WRAPPER CALCUL INCERTITUDE - DOUBLON)
   - Lignes 1321-1328 dans ml_core.py (8 lignes)
   - FONCTION : Fonction wrapper qui délègue l'accès à la méthode calculate_uncertainty à ModelProvider.get_calculate_uncertainty, fournissant une interface simplifiée
   - PARAMÈTRES : Aucun paramètre
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉLÉGATION DIRECTE** : `return ModelProvider.get_calculate_uncertainty()` appel méthode statique sans paramètres
     * **INTERFACE SIMPLIFIÉE** : Permet accès fonction calculate_uncertainty sans référencer explicitement ModelProvider
     * **TRANSPARENCE TOTALE** : Retourne directement résultat méthode déléguée sans traitement supplémentaire
     * **MÊME COMPORTEMENT** : Produit exactement même résultat que méthode originale ModelProvider
     * **IMPORTATION CONDITIONNELLE** : Évite importations circulaires en déléguant à ModelProvider
     * **DOCSTRING COMPLÈTE** : Returns avec description détaillée
     * **PATTERN FACADE** : Simplifie accès à fonctionnalité complexe ModelProvider
     * **TYPE HINTS** : `-> function` validation statique pour retour fonction
   - RETOUR : function - La méthode calculate_uncertainty de HybridBaccaratPredictor (retour direct de ModelProvider.get_calculate_uncertainty)
   - UTILITÉ : Fonction de commodité pour l'accès à la fonction de calcul d'incertitude sans nécessiter de connaître l'architecture interne. Particulièrement utile pour les utilisateurs qui veulent calculer l'incertitude rapidement sans se soucier de l'implémentation sous-jacente.

================================================================================
SECTION 5 : METHODESINITIALISATION
================================================================================

32. __init__.txt (PyTorchMemoryContext.__init__ - INITIALISATION CONTEXTE MÉMOIRE PYTORCH)
   - Lignes 540-549 dans ml_core.py (10 lignes)
   - FONCTION : Initialise le gestionnaire de contexte pour la gestion automatique de la mémoire PyTorch avec configuration des comportements d'optimisation et de nettoyage
   - PARAMÈTRES :
     * self - Instance de PyTorchMemoryContext
     * optimize_on_enter (bool, défaut=True) - Active l'optimisation mémoire à l'entrée du contexte
     * cleanup_on_exit (bool, défaut=True) - Active le nettoyage mémoire à la sortie du contexte
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE ATOMIQUE** : `self.optimize_on_enter = optimize_on_enter` assignation directe paramètre
     * **STOCKAGE NETTOYAGE** : `self.cleanup_on_exit = cleanup_on_exit` assignation directe paramètre
     * **PARAMÈTRES DÉFAUT** : `optimize_on_enter=True, cleanup_on_exit=True` valeurs par défaut optimales
     * **TYPE HINTS IMPLICITES** : Paramètres booléens pour contrôle comportement contexte
     * **DOCSTRING COMPLÈTE** : Args avec descriptions détaillées pour optimize_on_enter et cleanup_on_exit
     * **CONFIGURATION FLEXIBLE** : Permet désactivation sélective optimisation ou nettoyage
     * **PRÉPARATION CONTEXTE** : Prépare instance pour utilisation avec statements 'with'
     * **PATTERN CONTEXT MANAGER** : Initialise variables contrôle pour __enter__ et __exit__
     * **PERFORMANCE** : Initialisation O(1) avec assignations simples
   - RETOUR : None (constructeur, initialisation en place)
   - UTILITÉ : Constructeur essentiel pour créer des gestionnaires de contexte mémoire PyTorch configurables. Permet l'optimisation automatique de la mémoire dans les blocs 'with'. Critique pour la gestion efficace des ressources dans les applications ML intensives.

33. __init___1.txt (LSTMMemoryContext.__init__ - INITIALISATION CONTEXTE MÉMOIRE LSTM - HOMONYME)
   - Lignes 592-602 dans ml_core.py (11 lignes)
   - FONCTION : Initialise le gestionnaire de contexte spécialisé pour l'optimisation mémoire des modèles LSTM avec gestion des états d'entraînement et sauvegarde de l'état précédent
   - PARAMÈTRES :
     * self - Instance de LSTMMemoryContext
     * model - Le modèle LSTM PyTorch à optimiser et gérer
     * training (bool, défaut=False) - Mode d'utilisation (True pour entraînement, False pour inférence)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE MODÈLE** : Sauvegarde la référence du modèle dans self.model pour manipulation ultérieure
     * **STOCKAGE MODE** : Enregistre le mode training dans self.training pour configuration appropriée
     * **ÉTAT PRÉCÉDENT** : Initialise self.previous_training_state à None pour sauvegarder l'état original
     * **PRÉPARATION CONTEXTE** : Configure l'instance pour utilisation avec les statements 'with' spécialisés LSTM
     * **GESTION ÉTAT** : Prépare la sauvegarde/restauration de l'état d'entraînement du modèle
   - RETOUR : None (constructeur, initialisation en place)
   - UTILITÉ : Constructeur spécialisé pour la gestion contextuelle des modèles LSTM. Permet l'optimisation automatique et la restauration d'état pour les modèles LSTM. Essentiel pour les applications utilisant des modèles LSTM avec changements temporaires de mode.

34. __enter__.txt (PyTorchMemoryContext.__enter__ - ENTRÉE CONTEXTE MÉMOIRE PYTORCH)
   - Lignes 551-558 dans ml_core.py (8 lignes)
   - FONCTION : Méthode spéciale appelée automatiquement à l'entrée du bloc 'with' pour initialiser le contexte de gestion mémoire PyTorch avec optimisation conditionnelle
   - PARAMÈTRES :
     * self - Instance de PyTorchMemoryContext
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION CONDITIONNELLE** : `if self.optimize_on_enter:` contrôle flag optimisation
     * **OPTIMISATION GLOBALE** : `MemoryManager.optimize_pytorch_memory()` appel méthode statique optimisation complète
     * **RETOUR SELF** : `return self` retourne instance pour utilisation avec 'as' dans statement 'with'
     * **PATTERN CONTEXT MANAGER** : Implémente protocole gestionnaire contexte Python (__enter__/__exit__)
     * **DOCSTRING COMPLÈTE** : Documentation détaillée du comportement et usage
     * **OPTIMISATION SYSTÈME** : Applique toutes optimisations PyTorch (threads, cache, GC, CUDA)
     * **PERFORMANCE** : Exécution O(1) avec appel conditionnel optimisation
     * **THREAD-SAFETY** : Méthode thread-safe via MemoryManager statique
   - RETOUR : self - Instance du contexte pour utilisation avec 'as' dans 'with'
   - UTILITÉ : Méthode essentielle du protocole de gestionnaire de contexte pour l'optimisation automatique de la mémoire PyTorch. Permet l'utilisation avec 'with PyTorchMemoryContext() as ctx:' pour optimisation transparente.

35. __exit__.txt (PyTorchMemoryContext.__exit__ - SORTIE CONTEXTE MÉMOIRE PYTORCH)
   - Lignes 560-567 dans ml_core.py (8 lignes)
   - FONCTION : Méthode spéciale appelée automatiquement à la sortie du bloc 'with' pour finaliser le contexte de gestion mémoire PyTorch avec nettoyage conditionnel
   - PARAMÈTRES :
     * self - Instance de PyTorchMemoryContext
     * exc_type - Type d'exception si une exception s'est produite dans le bloc 'with'
     * exc_val - Valeur de l'exception si une exception s'est produite
     * exc_tb - Traceback de l'exception si une exception s'est produite
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION CONDITIONNELLE** : `if self.cleanup_on_exit:` contrôle flag nettoyage
     * **NETTOYAGE GLOBAL** : `MemoryManager.cleanup_pytorch_memory()` appel méthode statique nettoyage complet
     * **RETOUR FALSE** : `return False` ne supprime pas exceptions, les laisse se propager
     * **PATTERN CONTEXT MANAGER** : Implémente protocole gestionnaire contexte Python (__enter__/__exit__)
     * **DOCSTRING COMPLÈTE** : Documentation détaillée paramètres exception et comportement
     * **NETTOYAGE SYSTÈME** : Libère mémoire GPU/CPU, force GC, nettoie caches PyTorch
     * **GESTION EXCEPTIONS** : Paramètres exc_type, exc_val, exc_tb pour gestion erreurs
     * **PERFORMANCE** : Exécution O(1) avec appel conditionnel nettoyage
     * **THREAD-SAFETY** : Méthode thread-safe via MemoryManager statique
   - RETOUR : False - Pour propager les exceptions au lieu de les supprimer
   - UTILITÉ : Méthode essentielle du protocole de gestionnaire de contexte pour le nettoyage automatique de la mémoire PyTorch. Assure la libération des ressources à la fin du bloc 'with' même en cas d'exception.

36. __enter___1.txt (LSTMMemoryContext.__enter__ - ENTRÉE CONTEXTE MÉMOIRE LSTM - HOMONYME)
   - Lignes 604-615 dans ml_core.py (12 lignes)
   - FONCTION : Méthode spéciale appelée automatiquement à l'entrée du bloc 'with' pour initialiser le contexte de gestion mémoire LSTM avec sauvegarde d'état et optimisation spécialisée
   - PARAMÈTRES :
     * self - Instance de LSTMMemoryContext
   - FONCTIONNEMENT DÉTAILLÉ :
     * **SAUVEGARDE ÉTAT** : Enregistre l'état d'entraînement actuel dans self.previous_training_state pour restauration ultérieure
     * **ÉTAT TRAINING** : Capture self.model.training (True/False) pour pouvoir le restaurer à la sortie
     * **OPTIMISATION LSTM** : Appelle MemoryManager.optimize_lstm_memory avec le modèle et le mode training configuré
     * **MODE SPÉCIALISÉ** : Utilise self.training pour déterminer le mode d'optimisation (entraînement ou inférence)
     * **RETOUR SELF** : Retourne self pour permettre l'utilisation avec 'as' dans le statement 'with'
     * **PROTOCOLE CONTEXTE** : Implémente le protocole de gestionnaire de contexte Python pour LSTM
   - RETOUR : self - Instance du contexte pour utilisation avec 'as' dans 'with'
   - UTILITÉ : Méthode essentielle du protocole de gestionnaire de contexte pour l'optimisation automatique de la mémoire LSTM. Permet l'utilisation avec 'with LSTMMemoryContext(model) as ctx:' pour optimisation transparente avec sauvegarde d'état.

37. __exit___1.txt (LSTMMemoryContext.__exit__ - SORTIE CONTEXTE MÉMOIRE LSTM - HOMONYME)
   - Lignes 617-632 dans ml_core.py (16 lignes)
   - FONCTION : Méthode spéciale appelée automatiquement à la sortie du bloc 'with' pour finaliser le contexte de gestion mémoire LSTM avec restauration d'état et nettoyage
   - PARAMÈTRES :
     * self - Instance de LSTMMemoryContext
     * exc_type - Type d'exception si une exception s'est produite dans le bloc 'with'
     * exc_val - Valeur de l'exception si une exception s'est produite
     * exc_tb - Traceback de l'exception si une exception s'est produite
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT** : Contrôle si self.previous_training_state n'est pas None avant restauration
     * **RESTAURATION TRAINING** : Si previous_training_state=True, appelle self.model.train() pour mode entraînement
     * **RESTAURATION EVAL** : Si previous_training_state=False, appelle self.model.eval() pour mode évaluation
     * **NETTOYAGE MÉMOIRE** : Appelle MemoryManager.cleanup_pytorch_memory() pour libération ressources
     * **GESTION EXCEPTIONS** : Reçoit les informations d'exception mais ne les supprime pas
     * **RETOUR FALSE** : Retourne False pour propager les exceptions au lieu de les supprimer
     * **PROTOCOLE CONTEXTE** : Complète le protocole de gestionnaire de contexte Python pour LSTM
   - RETOUR : False - Pour propager les exceptions au lieu de les supprimer
   - UTILITÉ : Méthode essentielle du protocole de gestionnaire de contexte pour la restauration automatique d'état LSTM. Assure que le modèle retrouve son état original à la fin du bloc 'with' même en cas d'exception.

38. __init___2.txt (ThreadedTrainer.__init__ - INITIALISATION ENTRAÎNEUR THREADÉ - HOMONYME)
   - Lignes 1367-1386 dans ml_core.py (20 lignes)
   - FONCTION : Initialise l'entraîneur threadé avec instance de prédicteur, callbacks et gestion d'état pour l'entraînement asynchrone de modèles ML
   - PARAMÈTRES :
     * self - Instance de ThreadedTrainer
     * trainer_instance - Instance de HybridBaccaratPredictor à utiliser pour l'entraînement
     * callback (optionnel) - Fonction à appeler lorsque l'entraînement est terminé avec succès
     * error_callback (optionnel) - Fonction à appeler en cas d'erreur pendant l'entraînement
     * progress_callback (optionnel) - Fonction à appeler pour mettre à jour la progression
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE INSTANCE** : Sauvegarde trainer_instance pour utilisation dans le thread d'entraînement
     * **CONFIGURATION CALLBACKS** : Enregistre callback, error_callback et progress_callback pour notifications
     * **INITIALISATION THREAD** : Définit self.thread = None pour stockage du thread d'exécution
     * **ÉVÉNEMENT ARRÊT** : Crée self.stop_event = threading.Event() pour signalisation d'arrêt coopératif
     * **ÉTAT EXÉCUTION** : Initialise self.is_running = False pour suivi de l'état d'exécution
     * **STOCKAGE RÉSULTATS** : Prépare self.result = None et self.error = None pour stockage des résultats
     * **HORODATAGE** : Initialise self.start_time = None pour mesure de durée d'exécution
   - RETOUR : None (constructeur, initialisation en place)
   - UTILITÉ : Constructeur fondamental pour créer des entraîneurs threadés configurables. Permet l'entraînement asynchrone avec callbacks et contrôle d'arrêt. Essentiel pour les interfaces utilisateur non-bloquantes et la surveillance de progression.

39. __init___3.txt (ThreadedOptimizer.__init__ - INITIALISATION OPTIMISEUR THREADÉ - HOMONYME)
   - Lignes 1597-1614 dans ml_core.py (18 lignes)
   - FONCTION : Initialise l'optimiseur threadé avec classe d'optimiseur et callbacks pour l'optimisation asynchrone d'hyperparamètres
   - PARAMÈTRES :
     * self - Instance de ThreadedOptimizer
     * optimizer_class - Classe d'optimiseur à utiliser (ex: OptunaOptimizer, GridSearchOptimizer)
     * callback (optionnel) - Fonction à appeler lorsque l'optimisation est terminée avec succès
     * error_callback (optionnel) - Fonction à appeler en cas d'erreur pendant l'optimisation
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE CLASSE** : Sauvegarde optimizer_class pour instanciation ultérieure dans le thread
     * **INSTANCE DIFFÉRÉE** : Initialise self.optimizer_instance = None (sera créée au démarrage)
     * **CONFIGURATION CALLBACKS** : Enregistre callback et error_callback pour notifications
     * **INITIALISATION THREAD** : Définit self.thread = None pour stockage du thread d'exécution
     * **ÉVÉNEMENT ARRÊT** : Crée self.stop_event = threading.Event() pour signalisation d'arrêt coopératif
     * **ÉTAT EXÉCUTION** : Initialise self.is_running = False pour suivi de l'état d'exécution
     * **STOCKAGE RÉSULTATS** : Prépare self.result = None et self.error = None pour stockage des résultats
   - RETOUR : None (constructeur, initialisation en place)
   - UTILITÉ : Constructeur fondamental pour créer des optimiseurs threadés configurables. Permet l'optimisation asynchrone d'hyperparamètres avec callbacks. Essentiel pour les processus d'optimisation longs (Optuna, GridSearch) sans bloquer l'interface utilisateur.

40. __new___1.txt (LoggingManager.__new__ - CRÉATION SINGLETON LOGGING - HOMONYME)
   - Lignes 138-142 dans ml_core.py (5 lignes)
   - FONCTION : Méthode spéciale qui implémente le pattern Singleton pour LoggingManager, garantissant une seule instance de gestionnaire de logging dans l'application
   - PARAMÈTRES :
     * cls - Référence à la classe LoggingManager
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION INSTANCE** : `if cls._instance is None:` contrôle existence instance unique
     * **CRÉATION UNIQUE** : `cls._instance = super(LoggingManager, cls).__new__(cls)` création nouvelle instance si nécessaire
     * **INITIALISATION** : `cls._instance._initialize()` configuration instance nouvellement créée
     * **RETOUR INSTANCE** : `return cls._instance` retourne toujours même instance pour garantir unicité
     * **PATTERN SINGLETON** : Assure qu'une seule instance LoggingManager existe dans toute application
     * **VARIABLE CLASSE** : `cls._instance` stockage instance au niveau classe
     * **MÉTHODE SPÉCIALE** : `__new__` contrôle création objet avant `__init__`
   - RETOUR : LoggingManager - L'instance unique de LoggingManager (nouvelle ou existante)
   - UTILITÉ : Méthode fondamentale pour implémenter le pattern Singleton dans la gestion de logging. Garantit un logging centralisé et cohérent dans toute l'application. Essentielle pour éviter les conflits de logging et assurer la cohérence des messages.

41. _initialize_1.txt (LoggingManager._initialize - INITIALISATION GESTIONNAIRE LOGGING - HOMONYME)
   - Lignes 144-152 dans ml_core.py (9 lignes)
   - FONCTION : Initialise les structures de données internes du gestionnaire de logging pour stocker les loggers, handlers, formatters et créer le répertoire de logs
   - PARAMÈTRES :
     * self - Instance de LoggingManager
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INIT LOGGERS** : `self._loggers = {}` dictionnaire stockage instances logger par nom
     * **INIT HANDLERS** : `self._handlers = {}` dictionnaire stockage gestionnaires sortie (fichier, console)
     * **INIT FORMATTERS** : `self._formatters = {}` dictionnaire stockage formateurs messages log
     * **RÉPERTOIRE LOGS** : `self._log_directory = 'logs'` définition répertoire par défaut
     * **CRÉATION RÉPERTOIRE** : `os.makedirs(self._log_directory, exist_ok=True)` création répertoire si inexistant
     * **STRUCTURES VIDES** : Dictionnaires vides prêts recevoir composants logging
     * **MÉTHODE PRIVÉE** : `_initialize` méthode interne appelée uniquement par __new__
     * **PATTERN SINGLETON** : Initialisation unique lors création singleton
   - RETOUR : None (méthode d'initialisation, pas de retour)
   - UTILITÉ : Méthode d'initialisation fondamentale pour préparer le gestionnaire de logging. Crée toutes les structures nécessaires au stockage et à la gestion des composants de logging. Essentielle pour le bon fonctionnement du pattern Singleton de LoggingManager.

42. __new___2.txt (ModuleInterface.__new__ - CRÉATION SINGLETON MODULE INTERFACE - HOMONYME)
   - Lignes 230-234 dans ml_core.py (5 lignes)
   - FONCTION : Méthode spéciale qui implémente le pattern Singleton pour ModuleInterface, garantissant une seule instance d'interface modulaire dans l'application
   - PARAMÈTRES :
     * cls - Référence à la classe ModuleInterface
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION INSTANCE** : Contrôle si cls._instance est None pour déterminer si une instance existe déjà
     * **CRÉATION UNIQUE** : Si aucune instance n'existe, crée une nouvelle instance avec super(ModuleInterface, cls).__new__(cls)
     * **INITIALISATION** : Appelle cls._instance._initialize() pour configurer l'instance nouvellement créée
     * **RETOUR INSTANCE** : Retourne toujours la même instance (cls._instance) pour garantir l'unicité
     * **PATTERN SINGLETON** : Assure qu'une seule instance de ModuleInterface existe dans toute l'application
   - RETOUR : ModuleInterface - L'instance unique de ModuleInterface (nouvelle ou existante)
   - UTILITÉ : Méthode fondamentale pour implémenter le pattern Singleton dans l'interface modulaire. Garantit une gestion centralisée et cohérente des modules. Essentielle pour éviter les conflits d'enregistrement et assurer la cohérence des dépendances.

43. __new___3.txt (TrainOptimizeInterface.__new__ - CRÉATION SINGLETON TRAIN OPTIMIZE - HOMONYME)
   - Lignes 1062-1066 dans ml_core.py (5 lignes)
   - FONCTION : Méthode spéciale qui implémente le pattern Singleton pour TrainOptimizeInterface, garantissant une seule instance d'interface d'entraînement et d'optimisation dans l'application
   - PARAMÈTRES :
     * cls - Référence à la classe TrainOptimizeInterface
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION INSTANCE** : Contrôle si cls._instance est None pour déterminer si une instance existe déjà
     * **CRÉATION UNIQUE** : Si aucune instance n'existe, crée une nouvelle instance avec super(TrainOptimizeInterface, cls).__new__(cls)
     * **INITIALISATION** : Appelle cls._instance._initialize() pour configurer l'instance nouvellement créée
     * **RETOUR INSTANCE** : Retourne toujours la même instance (cls._instance) pour garantir l'unicité
     * **PATTERN SINGLETON** : Assure qu'une seule instance de TrainOptimizeInterface existe dans toute l'application
   - RETOUR : TrainOptimizeInterface - L'instance unique de TrainOptimizeInterface (nouvelle ou existante)
   - UTILITÉ : Méthode fondamentale pour implémenter le pattern Singleton dans l'interface d'entraînement et d'optimisation. Garantit une gestion centralisée des modèles et métriques. Essentielle pour éviter les conflits de configuration et assurer la cohérence des processus ML.

44. _initialize_2.txt (ModuleInterface._initialize - INITIALISATION INTERFACE MODULAIRE - HOMONYME)
   - Lignes 236-242 dans ml_core.py (7 lignes)
   - FONCTION : Initialise les dictionnaires de dépendances de l'interface modulaire pour stocker les fonctions, classes, instances, factories et chargeurs paresseux
   - PARAMÈTRES :
     * self - Instance de ModuleInterface
   - FONCTIONNEMENT DÉTAILLÉ :
     * **FONCTIONS** : Initialise self._functions = {} pour stocker les fonctions enregistrées par nom
     * **CLASSES** : Initialise self._classes = {} pour stocker les classes enregistrées par nom
     * **INSTANCES** : Initialise self._instances = {} pour stocker les instances enregistrées par nom
     * **FACTORIES** : Initialise self._factories = {} pour stocker les factories de création d'objets
     * **CHARGEURS PARESSEUX** : Initialise self._lazy_loaders = {} pour stocker les fonctions de chargement différé
     * **STRUCTURES VIDES** : Crée des dictionnaires vides prêts à recevoir les dépendances modulaires
   - RETOUR : None (méthode d'initialisation, pas de retour)
   - UTILITÉ : Méthode d'initialisation fondamentale pour préparer l'interface modulaire. Crée toutes les structures nécessaires au stockage et à la gestion des dépendances. Essentielle pour le bon fonctionnement du pattern Singleton et de l'injection de dépendances.

45. _initialize_3.txt (TrainOptimizeInterface._initialize - INITIALISATION INTERFACE TRAIN OPTIMIZE - HOMONYME)
   - Lignes 1068-1075 dans ml_core.py (8 lignes)
   - FONCTION : Initialise les dictionnaires de l'interface d'entraînement et d'optimisation pour stocker les modèles, sources de données, métriques, préprocesseurs, hyperparamètres et résultats
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MODÈLES** : Initialise self.models = {} pour stocker les modèles ML enregistrés par nom
     * **SOURCES DONNÉES** : Initialise self.data_sources = {} pour stocker les sources de données par nom
     * **MÉTRIQUES** : Initialise self.metrics = {} pour stocker les fonctions de métrique par nom
     * **PRÉPROCESSEURS** : Initialise self.preprocessors = {} pour stocker les préprocesseurs de données par nom
     * **HYPERPARAMÈTRES** : Initialise self.hyperparameters = {} pour stocker les configurations d'hyperparamètres par modèle
     * **RÉSULTATS** : Initialise self.results = {} pour stocker les résultats d'évaluation par modèle
   - RETOUR : None (méthode d'initialisation, pas de retour)
   - UTILITÉ : Méthode d'initialisation fondamentale pour préparer l'interface d'entraînement et d'optimisation. Crée toutes les structures nécessaires au stockage des composants ML. Essentielle pour le bon fonctionnement des processus d'entraînement et d'optimisation de modèles.

46. __enter__.txt (PyTorchMemoryContext.__enter__ - ENTRÉE CONTEXTE MÉMOIRE PYTORCH)
   - Lignes 551-558 dans ml_core.py (8 lignes)
   - FONCTION : Méthode spéciale appelée à l'entrée du bloc with pour initialiser le contexte de gestion mémoire PyTorch avec optimisation conditionnelle
   - PARAMÈTRES :
     * self - Instance de PyTorchMemoryContext
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION FLAG** : `if self.optimize_on_enter:` contrôle flag optimisation
     * **OPTIMISATION CONDITIONNELLE** : `MemoryManager.optimize_pytorch_memory()` appel méthode statique si flag True
     * **NETTOYAGE MÉMOIRE** : Libère mémoire PyTorch non utilisée (cache GPU, fragmentation)
     * **RETOUR SELF** : `return self` retourne instance pour utilisation avec 'as' dans 'with'
     * **PATTERN CONTEXT MANAGER** : Implémente protocole gestionnaire contexte Python (__enter__/__exit__)
     * **DOCSTRING COMPLÈTE** : Documentation détaillée optimisation conditionnelle
     * **MÉTHODE SPÉCIALE** : `__enter__` appelée automatiquement à entrée bloc 'with'
   - RETOUR : PyTorchMemoryContext - L'instance elle-même pour utilisation dans le bloc with
   - UTILITÉ : Méthode essentielle pour l'entrée dans un contexte de gestion mémoire PyTorch. Permet l'optimisation automatique de la mémoire au début d'un bloc de code. Critique pour les systèmes PyTorch nécessitant une gestion mémoire précise.

================================================================================
SECTION 6 : GESTIONMEMOIRE
================================================================================

47. optimize_lstm_memory.txt (MemoryManager.optimize_lstm_memory - OPTIMISATION MÉMOIRE LSTM)
   - Lignes 470-507 dans ml_core.py (38 lignes)
   - FONCTION : Optimise la mémoire utilisée par un modèle LSTM en configurant le mode d'exécution et en appliquant des optimisations spécifiques selon le contexte d'utilisation (entraînement ou inférence)
   - PARAMÈTRES :
     * lstm_model - Le modèle LSTM PyTorch à optimiser pour la mémoire
     * training_mode (bool, défaut=False) - Détermine si le modèle est en mode entraînement (True) ou inférence (False)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONDITION TRAINING** : `if training_mode:` branchement conditionnel selon mode
     * **MODE TRAIN** : `lstm_model.train()` activation mode entraînement avec gradients
     * **ACTIVATION GRADIENTS** : `for param in lstm_model.parameters(): param.requires_grad = True` activation rétropropagation
     * **MODE EVAL** : `lstm_model.eval()` activation mode évaluation sans gradients
     * **DÉSACTIVATION GRADIENTS** : `for param in lstm_model.parameters(): param.requires_grad = False` économie mémoire
     * **OPTIMISATION LSTM** : `for module in lstm_model.modules(): if hasattr(module, 'flatten_parameters'): module.flatten_parameters()` optimisation disposition mémoire
     * **GESTION ERREURS** : `try-except` capture exceptions avec `logger.warning` sans interruption
     * **LOGGING SUCCÈS** : `logger.info(f"Modèle LSTM optimisé pour la mémoire (mode {'entraînement' if training_mode else 'inférence'})")` traçabilité
     * **RETOUR MODÈLE** : `return lstm_model` retour objet modifié en place
   - RETOUR : Le modèle LSTM optimisé (même objet modifié en place)
   - UTILITÉ : Essentiel pour la gestion efficace de la mémoire GPU/CPU lors de l'utilisation de modèles LSTM, particulièrement important pour les modèles volumineux ou les environnements à ressources limitées. Permet d'adapter automatiquement l'utilisation mémoire selon le contexte d'usage.

48. optimize_lstm_memory_1.txt (optimize_lstm_memory - WRAPPER OPTIMISATION LSTM - DOUBLON)
   - Lignes 1295-1306 dans ml_core.py (12 lignes)
   - FONCTION : Fonction wrapper qui délègue l'optimisation mémoire LSTM à la méthode statique MemoryManager.optimize_lstm_memory, servant d'interface simplifiée pour l'optimisation mémoire
   - PARAMÈTRES :
     * lstm_model - Le modèle LSTM PyTorch à optimiser
     * training_mode (bool, défaut=False) - Mode d'exécution (entraînement ou inférence)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉLÉGATION DIRECTE** : Appelle immédiatement MemoryManager.optimize_lstm_memory avec les mêmes paramètres
     * **INTERFACE SIMPLIFIÉE** : Fournit un point d'accès direct sans avoir à instancier ou référencer MemoryManager
     * **TRANSPARENCE TOTALE** : Transmet tous les paramètres sans modification ni traitement supplémentaire
     * **MÊME COMPORTEMENT** : Produit exactement le même résultat que la méthode originale de MemoryManager
   - RETOUR : Le modèle LSTM optimisé (retour direct de MemoryManager.optimize_lstm_memory)
   - UTILITÉ : Fonction de commodité qui simplifie l'accès à l'optimisation mémoire LSTM sans nécessiter de connaître l'architecture interne. Utile pour les utilisateurs qui veulent optimiser rapidement un modèle LSTM sans se soucier de l'implémentation sous-jacente.

49. optimize_pytorch_memory.txt (MemoryManager.optimize_pytorch_memory - OPTIMISATION GLOBALE PYTORCH)
   - Lignes 368-443 dans ml_core.py (76 lignes)
   - FONCTION : Configure PyTorch pour utiliser la mémoire disponible de manière optimale en appliquant une série d'optimisations système et de configuration avancées pour maximiser les performances et minimiser l'utilisation mémoire
   - PARAMÈTRES : Aucun paramètre (méthode statique)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉSACTIVATION JIT** : Désactive la compilation Just-In-Time (PYTORCH_JIT=0, TORCH_COMPILE_DISABLE=1) pour éviter les erreurs de compilateur
     * **PROFILING DÉSACTIVÉ** : Désactive le profiling executor et mode pour réduire l'overhead mémoire
     * **NETTOYAGE CACHE CUDA** : Vide le cache CUDA avec torch.cuda.empty_cache() pour libérer la mémoire GPU inutilisée
     * **GARBAGE COLLECTOR** : Désactive le garbage collector Python (gc.disable()) pour éviter les pauses pendant l'entraînement
     * **CACHE MÉMOIRE CUDA** : Configure PYTORCH_NO_CUDA_MEMORY_CACHING=1 pour allocation mémoire plus agressive
     * **STRATÉGIE PARTAGE** : Configure torch.multiprocessing.set_sharing_strategy('file_system') pour meilleure utilisation mémoire inter-processus
     * **OPTIMISATION CUDNN** : Active benchmark=True et deterministic=False pour performances optimales
     * **FLUSH DENORMAL** : Active torch.set_flush_denormal(True) pour optimisation arithmétique
     * **CONFIGURATION THREADS** : Configure num_threads et num_interop_threads à 80% des cœurs CPU disponibles
     * **GESTION ERREURS THREADS** : Capture et ignore les RuntimeError pour threads d'interopérabilité déjà configurés
     * **LOGGING DÉTAILLÉ** : Enregistre la configuration finale et les messages d'information/debug
   - RETOUR : True (booléen indiquant le succès de l'optimisation)
   - UTILITÉ : Méthode critique à appeler au début du programme pour optimiser globalement PyTorch. Essentielle pour les applications ML intensives, particulièrement avec GPU. Améliore significativement les performances et réduit les erreurs de mémoire.

50. optimize_pytorch_memory_1.txt (optimize_pytorch_memory - WRAPPER OPTIMISATION PYTORCH - DOUBLON)
   - Lignes 1275-1283 dans ml_core.py (9 lignes)
   - FONCTION : Fonction wrapper qui délègue l'optimisation globale PyTorch à la méthode statique MemoryManager.optimize_pytorch_memory, fournissant une interface simplifiée pour l'optimisation système
   - PARAMÈTRES : Aucun paramètre
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉLÉGATION DIRECTE** : Appelle immédiatement MemoryManager.optimize_pytorch_memory() sans paramètres
     * **INTERFACE SIMPLIFIÉE** : Permet d'accéder à l'optimisation PyTorch sans référencer explicitement MemoryManager
     * **TRANSPARENCE TOTALE** : Retourne directement le résultat de la méthode déléguée sans traitement supplémentaire
     * **MÊME COMPORTEMENT** : Applique exactement les mêmes optimisations que la méthode originale de MemoryManager
   - RETOUR : bool - True si l'optimisation a réussi, False sinon (retour direct de MemoryManager.optimize_pytorch_memory)
   - UTILITÉ : Fonction de commodité pour l'optimisation globale PyTorch sans nécessiter de connaître l'architecture interne. Particulièrement utile pour les scripts d'initialisation ou les utilisateurs qui veulent optimiser rapidement PyTorch au démarrage de l'application.

51. cleanup_pytorch_memory.txt (MemoryManager.cleanup_pytorch_memory - NETTOYAGE MÉMOIRE PYTORCH)
   - Lignes 446-467 dans ml_core.py (22 lignes)
   - FONCTION : Nettoie la mémoire PyTorch après utilisation en libérant les ressources inutilisées et en forçant la collecte des déchets, optimisée pour être appelée à la fin de chaque époque d'entraînement
   - PARAMÈTRES : Aucun paramètre (méthode statique)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VIDAGE CACHE CUDA** : `if hasattr(torch.cuda, 'empty_cache'): torch.cuda.empty_cache()` libération mémoire GPU conditionnelle
     * **RÉACTIVATION GC** : `gc.enable()` réactivation garbage collector après désactivation optimisation
     * **COLLECTION FORCÉE** : `gc.collect()` force collecte déchets immédiate pour libération mémoire Python
     * **DÉTACHEMENT TENSEURS** : `for obj in gc.get_objects(): if torch.is_tensor(obj): obj.detach_()` parcours objets Python
     * **GESTION ERREURS SILENCIEUSE** : `try-except` bloc protection pour ignorer erreurs détachement (objets déjà libérés)
     * **RETOUR SUCCÈS** : `return True` indication nettoyage terminé avec succès
     * **DOCSTRING COMPLÈTE** : Documentation détaillée usage fin époque entraînement
     * **MÉTHODE STATIQUE** : `@staticmethod` pour utilisation sans instance classe
     * **NETTOYAGE COMPLET** : Combine nettoyage GPU, CPU et Python pour libération maximale mémoire
   - RETOUR : True (booléen indiquant le succès du nettoyage)
   - UTILITÉ : Essentiel pour éviter les fuites mémoire lors d'entraînements longs. Doit être appelé régulièrement, particulièrement à la fin de chaque époque. Critique pour les environnements à ressources limitées ou les modèles volumineux.

52. cleanup_pytorch_memory_1.txt (cleanup_pytorch_memory - WRAPPER NETTOYAGE PYTORCH - DOUBLON)
   - Lignes 1285-1293 dans ml_core.py (9 lignes)
   - FONCTION : Fonction wrapper qui délègue le nettoyage mémoire PyTorch à la méthode statique MemoryManager.cleanup_pytorch_memory, fournissant une interface simplifiée pour le nettoyage système
   - PARAMÈTRES : Aucun paramètre
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉLÉGATION DIRECTE** : Appelle immédiatement MemoryManager.cleanup_pytorch_memory() sans paramètres
     * **INTERFACE SIMPLIFIÉE** : Permet d'accéder au nettoyage PyTorch sans référencer explicitement MemoryManager
     * **TRANSPARENCE TOTALE** : Retourne directement le résultat de la méthode déléguée sans traitement supplémentaire
     * **MÊME COMPORTEMENT** : Applique exactement les mêmes opérations de nettoyage que la méthode originale de MemoryManager
   - RETOUR : bool - True si le nettoyage a réussi, False sinon (retour direct de MemoryManager.cleanup_pytorch_memory)
   - UTILITÉ : Fonction de commodité pour le nettoyage mémoire PyTorch sans nécessiter de connaître l'architecture interne. Particulièrement utile pour les boucles d'entraînement ou les utilisateurs qui veulent nettoyer rapidement la mémoire à la fin d'une époque.

53. register_memory_hooks.txt (MemoryManager.register_memory_hooks - HOOKS SURVEILLANCE MÉMOIRE)
   - Lignes 510-524 dans ml_core.py (15 lignes)
   - FONCTION : Enregistre des hooks sur le modèle pour surveiller et optimiser automatiquement l'utilisation de la mémoire en libérant les tenseurs intermédiaires après chaque passage forward
   - PARAMÈTRES :
     * cls - Référence à la classe MemoryManager (méthode de classe)
     * model - Le modèle PyTorch sur lequel enregistrer les hooks de surveillance mémoire
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉFINITION HOOK** : Crée une fonction hook_fn interne qui sera appelée après chaque forward pass
     * **LIBÉRATION AUTOMATIQUE** : Le hook vérifie si l'output a une méthode detach et l'appelle pour libérer les gradients
     * **DÉTACHEMENT SÉCURISÉ** : Utilise hasattr(output, 'detach') pour vérifier la disponibilité avant d'appeler detach_()
     * **ENREGISTREMENT GLOBAL** : Parcourt tous les modules du modèle avec model.modules() pour une couverture complète
     * **HOOKS FORWARD** : Utilise register_forward_hook() pour intercepter les sorties de chaque module
     * **SURVEILLANCE CONTINUE** : Les hooks restent actifs pendant toute la durée de vie du modèle
     * **OPTIMISATION AUTOMATIQUE** : Libère automatiquement la mémoire des tenseurs intermédiaires sans intervention manuelle
   - RETOUR : Le modèle modifié avec les hooks enregistrés (même objet avec hooks ajoutés)
   - UTILITÉ : Fonctionnalité avancée pour la gestion automatique de la mémoire pendant l'entraînement. Particulièrement utile pour les modèles complexes avec de nombreuses couches intermédiaires. Réduit significativement l'utilisation mémoire sans impact sur les performances.

54. hook_fn.txt (MemoryManager.hook_fn - FONCTION HOOK INTERNE MÉMOIRE)
   - Lignes 515-518 dans ml_core.py (4 lignes)
   - FONCTION : Fonction hook interne utilisée par register_memory_hooks pour libérer automatiquement la mémoire des tenseurs intermédiaires après chaque passage forward dans un module
   - PARAMÈTRES :
     * module - Le module PyTorch qui a exécuté le forward pass
     * input - Les tenseurs d'entrée du module (non utilisés dans cette implémentation)
     * output - Les tenseurs de sortie du module à traiter pour libération mémoire
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION SÉCURISÉE** : `if hasattr(output, 'detach'):` contrôle existence méthode detach sur output
     * **DÉTACHEMENT AUTOMATIQUE** : `output.detach_()` libération gradients et références tenseur sortie
     * **LIBÉRATION IMMÉDIATE** : Exécutée automatiquement après chaque forward pass module associé
     * **GESTION TRANSPARENTE** : Fonctionne sans intervention utilisateur une fois hook enregistré
     * **OPTIMISATION CIBLÉE** : Se concentre uniquement sur tenseurs sortie qui peuvent être détachés
     * **FONCTION INTERNE** : Définie à l'intérieur de register_memory_hooks comme closure
     * **SIGNATURE HOOK** : `def hook_fn(module, input, output):` signature standard PyTorch hook
     * **PERFORMANCE** : O(1) vérification et détachement par tenseur
   - RETOUR : Aucun retour (fonction void, modification en place)
   - UTILITÉ : Composant interne essentiel du système de gestion automatique de la mémoire. Permet la libération continue et transparente des tenseurs intermédiaires pendant l'exécution du modèle, réduisant l'empreinte mémoire sans affecter les calculs.

================================================================================
SECTION 7 : THREADINGOPTIMISATION
================================================================================

55. start.txt (ThreadedTrainer.start - DÉMARRAGE ENTRAÎNEMENT THREADÉ)
   - Lignes 1388-1430 dans ml_core.py (43 lignes)
   - FONCTION : Démarre l'entraînement de modèles ML (LGBM et LSTM) dans un thread séparé pour permettre l'exécution asynchrone et non-bloquante avec gestion complète des états et erreurs
   - PARAMÈTRES :
     * self - Instance de ThreadedTrainer
     * X_lgbm - Features/données d'entrée pour le modèle LGBM
     * y_lgbm - Labels/cibles pour le modèle LGBM
     * X_lstm - Features/données d'entrée pour le modèle LSTM
     * y_lstm - Labels/cibles pour le modèle LSTM
     * config_override (optionnel) - Configuration personnalisée pour remplacer la configuration par défaut
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT** : Contrôle si un entraînement est déjà en cours avec self.is_running pour éviter les conflits
     * **RÉINITIALISATION** : Nettoie les événements (stop_event.clear()) et remet à zéro les résultats et erreurs
     * **HORODATAGE** : Enregistre l'heure de début avec time.time() pour suivi de durée
     * **VALIDATION INSTANCE** : Vérifie que trainer_instance possède la méthode '_train_models_async' requise
     * **GESTION ERREURS** : Appelle error_callback si l'instance est invalide et retourne False
     * **CRÉATION THREAD** : `self.thread = threading.Thread(target=self._run_training, args=(X_lgbm, y_lgbm, X_lstm, y_lstm, config_override))` instanciation
     * **CONFIG THREAD** : `name="ThreadedTrainer", daemon=True` configuration thread daemon
     * **DÉMARRAGE THREAD** : `self.thread.start(); self.is_running = True` lancement et mise à jour état
     * **LOGGING SUCCÈS** : `logger.info("Entraînement démarré avec succès")` traçabilité démarrage
     * **ERROR CALLBACK** : `if self.error_callback: self.error_callback(message)` notification erreurs validation
     * **RETOUR CONDITIONNEL** : `return True` si succès, `return False` si erreur ou déjà en cours
   - RETOUR : bool - True si l'entraînement a démarré avec succès, False en cas d'erreur ou si déjà en cours
   - UTILITÉ : Point d'entrée principal pour l'entraînement asynchrone de modèles ML. Essentiel pour les interfaces utilisateur non-bloquantes et les applications nécessitant un entraînement en arrière-plan. Permet la surveillance et le contrôle de l'entraînement.

56. start_1.txt (ThreadedOptimizer.start - DÉMARRAGE OPTIMISATION THREADÉE - HOMONYME)
   - Lignes 1616-1652 dans ml_core.py (37 lignes)
   - FONCTION : Démarre l'optimisation d'hyperparamètres dans un thread séparé pour permettre l'exécution asynchrone et non-bloquante avec création dynamique de l'instance d'optimiseur
   - PARAMÈTRES :
     * self - Instance de ThreadedOptimizer
     * *args - Arguments positionnels variables à transmettre au constructeur de l'optimiseur
     * **kwargs - Arguments nommés variables à transmettre au constructeur de l'optimiseur
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT** : Contrôle si une optimisation est déjà en cours avec self.is_running pour éviter les conflits
     * **RÉINITIALISATION** : Nettoie les événements (stop_event.clear()) et remet à zéro les résultats et erreurs
     * **CRÉATION DYNAMIQUE** : Instancie self.optimizer_class avec les arguments fournis (*args, **kwargs)
     * **GESTION ERREURS CRÉATION** : Capture les exceptions lors de l'instanciation et appelle error_callback
     * **LOGGING ERREURS** : Enregistre les erreurs de création d'optimiseur pour debugging
     * **CRÉATION THREAD** : Instancie threading.Thread avec target=_run_optimization (sans paramètres)
     * **CONFIGURATION DAEMON** : Définit daemon=True pour arrêt automatique avec le programme principal
     * **DÉMARRAGE ASYNCHRONE** : Lance thread.start() et marque is_running=True
     * **LOGGING SUCCÈS** : Enregistre le succès du démarrage pour traçabilité
   - RETOUR : bool - True si l'optimisation a démarré avec succès, False en cas d'erreur ou si déjà en cours
   - UTILITÉ : Point d'entrée principal pour l'optimisation asynchrone d'hyperparamètres. Essentiel pour les processus d'optimisation longs (Optuna, GridSearch) sans bloquer l'interface utilisateur. Permet la création flexible d'optimiseurs avec paramètres variables.

57. stop.txt (ThreadedTrainer.stop - ARRÊT ENTRAÎNEMENT THREADÉ)
   - Lignes 1533-1562 dans ml_core.py (30 lignes)
   - FONCTION : Arrête proprement l'entraînement en cours d'exécution dans le thread séparé avec gestion des timeouts et signalisation d'arrêt gracieux
   - PARAMÈTRES :
     * self - Instance de ThreadedTrainer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT** : Contrôle si un entraînement est en cours avec self.is_running avant tentative d'arrêt
     * **WARNING PRÉVENTIF** : Log un avertissement si aucun entraînement n'est en cours et retourne False
     * **SIGNALISATION ARRÊT** : Active l'événement d'arrêt avec self.stop_event.set() pour signaler au thread
     * **FLAG INSTANCE** : Définit trainer_instance.stop_training=True si l'attribut existe pour arrêt coopératif
     * **ATTENTE GRACIEUSE** : Utilise thread.join(timeout=5.0) pour attendre l'arrêt propre du thread
     * **VÉRIFICATION SURVIE** : Contrôle si le thread est encore vivant après le timeout avec thread.is_alive()
     * **ARRÊT FORCÉ** : Si le thread ne s'arrête pas, marque is_running=False et log un avertissement
     * **LIMITATION PYTHON** : Reconnaît qu'on ne peut pas forcer l'arrêt d'un thread en Python
     * **LOGGING SUCCÈS** : Enregistre l'arrêt réussi pour traçabilité
   - RETOUR : bool - True si l'arrêt a été initié avec succès, False si aucun entraînement en cours
   - UTILITÉ : Méthode essentielle pour l'arrêt contrôlé de l'entraînement asynchrone. Permet l'interruption propre des processus longs et la libération des ressources. Critique pour les interfaces utilisateur avec boutons d'arrêt.

58. stop_1.txt (ThreadedOptimizer.stop - ARRÊT OPTIMISATION THREADÉE - HOMONYME)
   - Lignes 1677-1702 dans ml_core.py (26 lignes)
   - FONCTION : Arrête proprement l'optimisation d'hyperparamètres en cours d'exécution dans le thread séparé avec gestion des timeouts et signalisation d'arrêt gracieux
   - PARAMÈTRES :
     * self - Instance de ThreadedOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION ÉTAT** : Contrôle si une optimisation est en cours avec self.is_running avant tentative d'arrêt
     * **WARNING PRÉVENTIF** : Log un avertissement si aucune optimisation n'est en cours et retourne False
     * **SIGNALISATION ARRÊT** : Active l'événement d'arrêt avec self.stop_event.set() pour signaler au thread d'optimisation
     * **ATTENTE GRACIEUSE** : Utilise thread.join(timeout=5.0) pour attendre l'arrêt propre du thread d'optimisation
     * **VÉRIFICATION SURVIE** : Contrôle si le thread est encore vivant après le timeout avec thread.is_alive()
     * **ARRÊT FORCÉ** : Si le thread ne s'arrête pas, marque is_running=False et log un avertissement
     * **LIMITATION PYTHON** : Reconnaît qu'on ne peut pas forcer l'arrêt d'un thread en Python
     * **LOGGING SUCCÈS** : Enregistre l'arrêt réussi de l'optimisation pour traçabilité
   - RETOUR : bool - True si l'arrêt a été initié avec succès, False si aucune optimisation en cours
   - UTILITÉ : Méthode essentielle pour l'arrêt contrôlé de l'optimisation asynchrone d'hyperparamètres. Permet l'interruption propre des processus d'optimisation longs (Optuna, GridSearch) et la libération des ressources. Critique pour les interfaces utilisateur avec contrôle d'arrêt.

59. is_training_running.txt (ThreadedTrainer.is_training_running - VÉRIFICATION ÉTAT ENTRAÎNEMENT)
   - Lignes 1582-1589 dans ml_core.py (8 lignes)
   - FONCTION : Vérifie l'état d'exécution de l'entraînement threadé en retournant la valeur du flag interne is_running pour surveillance et contrôle d'état
   - PARAMÈTRES :
     * self - Instance de ThreadedTrainer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **LECTURE ÉTAT** : Accède directement à l'attribut self.is_running sans modification
     * **RETOUR IMMÉDIAT** : Retourne la valeur booléenne sans traitement supplémentaire
     * **ÉTAT SYNCHRONISÉ** : Reflète l'état réel du thread d'entraînement (mis à jour par start/stop)
     * **VÉRIFICATION SIMPLE** : Méthode getter pure sans effets de bord
     * **THREAD-SAFE** : Lecture atomique d'un booléen, pas de synchronisation complexe requise
   - RETOUR : bool - True si l'entraînement est actuellement en cours d'exécution, False sinon
   - UTILITÉ : Méthode de surveillance essentielle pour les interfaces utilisateur et la logique de contrôle. Permet de vérifier l'état avant de démarrer/arrêter l'entraînement. Utile pour l'affichage d'indicateurs d'état et la validation des opérations.

60. is_optimization_running.txt (ThreadedOptimizer.is_optimization_running - VÉRIFICATION ÉTAT OPTIMISATION)
   - Lignes 1722-1729 dans ml_core.py (8 lignes)
   - FONCTION : Vérifie l'état d'exécution de l'optimisation threadée en retournant la valeur du flag interne is_running pour surveillance et contrôle d'état
   - PARAMÈTRES :
     * self - Instance de ThreadedOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **LECTURE ÉTAT** : Accède directement à l'attribut self.is_running sans modification
     * **RETOUR IMMÉDIAT** : Retourne la valeur booléenne sans traitement supplémentaire
     * **ÉTAT SYNCHRONISÉ** : Reflète l'état réel du thread d'optimisation (mis à jour par start/stop)
     * **VÉRIFICATION SIMPLE** : Méthode getter pure sans effets de bord
     * **THREAD-SAFE** : Lecture atomique d'un booléen, pas de synchronisation complexe requise
   - RETOUR : bool - True si l'optimisation est actuellement en cours d'exécution, False sinon
   - UTILITÉ : Méthode de surveillance essentielle pour les interfaces utilisateur et la logique de contrôle d'optimisation. Permet de vérifier l'état avant de démarrer/arrêter l'optimisation d'hyperparamètres. Utile pour l'affichage d'indicateurs d'état et la validation des opérations d'optimisation.

61. _run_training.txt (ThreadedTrainer._run_training - EXÉCUTION ENTRAÎNEMENT THREADÉ INTERNE)
   - Lignes 1432-1531 dans ml_core.py (100 lignes)
   - FONCTION : Méthode interne complexe qui exécute l'entraînement dans le thread séparé avec gestion avancée des callbacks, arrêt coopératif et restauration d'état
   - PARAMÈTRES :
     * self - Instance de ThreadedTrainer
     * X_lgbm - Features pour le modèle LGBM
     * y_lgbm - Labels pour le modèle LGBM
     * X_lstm - Features pour le modèle LSTM
     * y_lstm - Labels pour le modèle LSTM
     * config_override (optionnel) - Configuration personnalisée pour l'entraînement
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONFIGURATION ARRÊT** : Configure stop_training dynamiquement pour vérifier stop_event.is_set()
     * **SAUVEGARDE ORIGINALE** : Sauvegarde les méthodes originales avant modification temporaire
     * **PROPRIÉTÉ DYNAMIQUE** : Crée check_stop_training qui combine flag et événement d'arrêt
     * **CALLBACK PROGRESSION** : Configure observe_progress pour éviter la récursion infinie
     * **OBSERVATEUR PROGRESSION** : Ajoute _progress_observer à l'instance pour callbacks
     * **EXÉCUTION PRINCIPALE** : Appelle trainer_instance._train_models_async avec tous les paramètres
     * **VÉRIFICATION INTERRUPTION** : Contrôle stop_event.is_set() après entraînement
     * **RÉSULTAT STRUCTURÉ** : Crée dictionnaire avec success, message, duration
     * **GESTION ERREURS** : Capture exceptions avec logging détaillé et callbacks d'erreur
     * **RESTAURATION FINALE** : Restaure méthodes originales et supprime observateurs dans finally
     * **NETTOYAGE ÉTAT** : Marque is_running=False pour indiquer fin d'exécution
   - RETOUR : None (méthode interne, résultats stockés dans self.result)
   - UTILITÉ : Cœur de l'exécution threadée avec gestion sophistiquée des états et callbacks. Essentielle pour l'entraînement asynchrone robuste avec contrôle d'arrêt et surveillance de progression. Critique pour les applications nécessitant un entraînement interruptible.

62. observe_progress.txt (ThreadedTrainer.observe_progress - OBSERVATEUR PROGRESSION INTERNE)
   - Lignes 1473-1477 dans ml_core.py (5 lignes)
   - FONCTION : Fonction interne d'observation de progression créée dynamiquement pour éviter la récursion infinie lors des callbacks de progression dans l'entraînement threadé
   - PARAMÈTRES :
     * progress - Valeur de progression (pourcentage ou étape actuelle)
     * message - Message descriptif de l'état de progression
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION CALLBACK** : Contrôle si self.progress_callback existe avant appel
     * **DÉLÉGATION SÉCURISÉE** : Appelle progress_callback avec les mêmes paramètres sans modification
     * **ÉVITEMENT RÉCURSION** : Conçue pour éviter les boucles infinies de callbacks
     * **FONCTION INTERNE** : Créée dynamiquement dans _run_training pour contexte spécifique
     * **OBSERVATEUR PATTERN** : Implémente le pattern Observer pour surveillance de progression
   - RETOUR : None (fonction de callback, pas de retour)
   - UTILITÉ : Composant interne essentiel pour la surveillance de progression sans récursion. Permet les callbacks de progression sécurisés dans l'entraînement threadé. Critique pour les interfaces utilisateur avec barres de progression en temps réel.

63. _run_optimization.txt (ThreadedOptimizer._run_optimization - EXÉCUTION OPTIMISATION THREADÉE INTERNE)
   - Lignes 1654-1675 dans ml_core.py (22 lignes)
   - FONCTION : Méthode interne qui exécute l'optimisation d'hyperparamètres dans le thread séparé avec gestion des callbacks et des erreurs
   - PARAMÈTRES :
     * self - Instance de ThreadedOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXÉCUTION OPTIMISATION** : Appelle self.optimizer_instance.optimize(stop_event=self.stop_event) pour lancer l'optimisation
     * **TRANSMISSION STOP_EVENT** : Passe l'événement d'arrêt à l'optimiseur pour permettre l'interruption coopérative
     * **STOCKAGE RÉSULTAT** : Enregistre le résultat dans self.result pour récupération ultérieure
     * **VÉRIFICATION INTERRUPTION** : Contrôle si stop_event.is_set() pour détecter les arrêts demandés
     * **CALLBACK SUCCÈS** : Appelle self.callback(self.result) si l'optimisation se termine normalement
     * **GESTION ERREURS** : Capture les exceptions avec logging détaillé et stockage dans self.error
     * **CALLBACK ERREUR** : Appelle self.error_callback(e) si une erreur survient pendant l'optimisation
     * **NETTOYAGE FINAL** : Marque is_running=False dans le bloc finally pour indiquer la fin d'exécution
   - RETOUR : None (méthode interne, résultats stockés dans self.result)
   - UTILITÉ : Cœur de l'exécution d'optimisation threadée avec gestion robuste des états et callbacks. Essentielle pour l'optimisation asynchrone d'hyperparamètres (Optuna, GridSearch). Critique pour les processus d'optimisation longs avec contrôle d'arrêt.

64. check_stop_training.txt (ThreadedTrainer.check_stop_training - VÉRIFICATION ARRÊT ENTRAÎNEMENT INTERNE)
   - Lignes 1456-1457 dans ml_core.py (2 lignes)
   - FONCTION : Fonction interne créée dynamiquement pour vérifier si l'entraînement doit s'arrêter en combinant le flag local et l'événement d'arrêt threadé
   - PARAMÈTRES :
     * self_instance - Instance de l'entraîneur pour vérifier son flag stop_training
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DOUBLE VÉRIFICATION** : Combine self_instance.stop_training (flag local) et self.stop_event.is_set() (événement threadé)
     * **LOGIQUE OU** : Retourne True si l'une des deux conditions d'arrêt est activée
     * **ARRÊT LOCAL** : Vérifie self_instance.stop_training pour arrêt demandé par l'instance d'entraînement
     * **ARRÊT THREADÉ** : Vérifie self.stop_event.is_set() pour arrêt demandé par le thread manager
     * **FONCTION DYNAMIQUE** : Créée à la volée dans _run_training pour contexte spécifique
     * **ARRÊT COOPÉRATIF** : Permet l'arrêt gracieux depuis multiple sources
   - RETOUR : bool - True si l'entraînement doit s'arrêter, False sinon
   - UTILITÉ : Composant interne essentiel pour l'arrêt coopératif de l'entraînement threadé. Permet la vérification combinée des conditions d'arrêt depuis multiple sources. Critique pour l'arrêt gracieux et la responsivité des interfaces utilisateur.

65. get_error.txt (ThreadedTrainer.get_error - RÉCUPÉRATION ERREUR ENTRAÎNEMENT)
   - Lignes 1573-1580 dans ml_core.py (8 lignes)
   - FONCTION : Récupère l'erreur survenue pendant l'entraînement threadé pour diagnostic et gestion d'erreurs
   - PARAMÈTRES :
     * self - Instance de ThreadedTrainer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RETOUR DIRECT** : Retourne self.error sans traitement supplémentaire
     * **ÉTAT ERREUR** : Contient l'exception capturée pendant l'exécution ou None si pas d'erreur
     * **DIAGNOSTIC** : Permet l'inspection de l'erreur pour debugging et gestion
     * **GETTER SIMPLE** : Méthode getter pure sans effets de bord
     * **THREAD-SAFE** : Lecture atomique d'une référence d'objet
   - RETOUR : Exception ou None - L'erreur de l'entraînement ou None si l'entraînement n'a pas échoué
   - UTILITÉ : Méthode essentielle pour la gestion d'erreurs dans l'entraînement threadé. Permet le diagnostic des problèmes et la gestion appropriée des échecs. Critique pour les interfaces utilisateur affichant les erreurs et les systèmes de logging.

66. get_error_1.txt (ThreadedOptimizer.get_error - RÉCUPÉRATION ERREUR OPTIMISATION - HOMONYME)
   - Lignes 1713-1720 dans ml_core.py (8 lignes)
   - FONCTION : Récupère l'erreur survenue pendant l'optimisation threadée pour diagnostic et gestion d'erreurs
   - PARAMÈTRES :
     * self - Instance de ThreadedOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RETOUR DIRECT** : Retourne self.error sans traitement supplémentaire
     * **ÉTAT ERREUR** : Contient l'exception capturée pendant l'optimisation ou None si pas d'erreur
     * **DIAGNOSTIC** : Permet l'inspection de l'erreur pour debugging et gestion
     * **GETTER SIMPLE** : Méthode getter pure sans effets de bord
     * **THREAD-SAFE** : Lecture atomique d'une référence d'objet
   - RETOUR : Exception ou None - L'erreur de l'optimisation ou None si l'optimisation n'a pas échoué
   - UTILITÉ : Méthode essentielle pour la gestion d'erreurs dans l'optimisation threadée. Permet le diagnostic des problèmes d'optimisation et la gestion appropriée des échecs. Critique pour les interfaces utilisateur affichant les erreurs d'optimisation et les systèmes de logging.

================================================================================
SECTION 8 : INTERFACESMODELES
================================================================================

67. get_model.txt (TrainOptimizeInterface.get_model - RÉCUPÉRATION MODÈLE ENREGISTRÉ)
   - Lignes 1088-1104 dans ml_core.py (17 lignes)
   - FONCTION : Récupère un modèle enregistré dans l'interface par son nom avec validation d'existence et gestion d'erreurs appropriée
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * name (str) - Nom du modèle à récupérer depuis le registre des modèles
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION EXISTENCE** : `if name not in self.models:` contrôle existence directe dans dictionnaire
     * **EXCEPTION EXPLICITE** : `raise KeyError(f"Modèle '{name}' non enregistré dans l'interface")` avec message formaté
     * **RETOUR SÉCURISÉ** : `return self.models[name]` accès direct au dictionnaire après validation
     * **TYPE HINTS COMPLETS** : `name: str` et `-> Any` pour validation statique et documentation
     * **DOCSTRING COMPLÈTE** : Args, Returns, Raises avec descriptions détaillées
     * **THREAD-SAFETY** : Accès atomique au dictionnaire self.models (GIL protection)
     * **PERFORMANCE** : O(1) pour vérification et récupération dans dictionnaire hash
     * **GESTION ERREURS** : Message d'erreur explicite incluant nom modèle pour debugging
   - RETOUR : Any - Le modèle enregistré (type dépend du modèle stocké)
   - RAISES : KeyError - Si le modèle demandé n'est pas enregistré dans l'interface
   - UTILITÉ : Méthode essentielle pour accéder aux modèles enregistrés dans le système. Permet la récupération sécurisée avec validation d'existence. Critique pour les systèmes utilisant plusieurs modèles avec gestion centralisée.

68. register_model.txt (TrainOptimizeInterface.register_model - ENREGISTREMENT MODÈLE)
   - Lignes 1077-1086 dans ml_core.py (10 lignes)
   - FONCTION : Enregistre un modèle dans l'interface avec un nom unique pour permettre la récupération ultérieure et la gestion centralisée des modèles
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * name (str) - Nom unique du modèle pour identification et récupération
     * model (Any) - Instance du modèle à enregistrer (peut être n'importe quel type de modèle ML)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE DIRECT** : Enregistre le modèle dans self.models[name] avec le nom comme clé
     * **ÉCRASEMENT POSSIBLE** : Si un modèle avec le même nom existe, il sera remplacé silencieusement
     * **LOGGING DEBUG** : Enregistre un message de debug confirmant l'enregistrement avec le nom du modèle
     * **GESTION UNIVERSELLE** : Accepte tout type de modèle (PyTorch, scikit-learn, TensorFlow, etc.)
     * **RÉFÉRENCE DIRECTE** : Stocke une référence directe au modèle, pas une copie
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour construire un registre de modèles centralisé. Permet la gestion de multiples modèles avec accès par nom. Essentielle pour les systèmes complexes utilisant plusieurs algorithmes ML simultanément.

69. predict.txt (ModelInterface.predict - PRÉDICTION MODÈLE INTERFACE)
   - Lignes 999-1010 dans ml_core.py (12 lignes)
   - FONCTION : Méthode d'interface abstraite pour la prédiction de labels à partir de features, définissant le contrat standard que doivent implémenter toutes les classes de modèles
   - PARAMÈTRES :
     * self - Instance de ModelInterface
     * X - Features/données d'entrée pour la prédiction (format dépend de l'implémentation)
     * **kwargs - Arguments supplémentaires variables pour personnalisation de la prédiction
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTHODE ABSTRAITE** : Implémentation vide (pass) car c'est une interface à implémenter par les sous-classes
     * **CONTRAT STANDARD** : Définit la signature standard que doivent respecter toutes les implémentations
     * **FLEXIBILITÉ PARAMÈTRES** : Utilise **kwargs pour permettre des paramètres spécifiques à chaque type de modèle
     * **POLYMORPHISME** : Permet l'utilisation uniforme de différents types de modèles via la même interface
     * **DOCUMENTATION INTERFACE** : Fournit la documentation standard pour toutes les implémentations
   - RETOUR : Prédictions (format dépend de l'implémentation concrète)
   - UTILITÉ : Interface fondamentale pour standardiser les prédictions de modèles ML. Permet le polymorphisme et l'interchangeabilité des modèles. Essentielle pour les architectures modulaires où différents types de modèles doivent être utilisés de manière uniforme.

70. load.txt (ModelInterface.load - CHARGEMENT MODÈLE INTERFACE)
   - Lignes 1041-1052 dans ml_core.py (12 lignes)
   - FONCTION : Méthode d'interface abstraite pour le chargement de modèles depuis un chemin de fichier, définissant le contrat standard que doivent implémenter toutes les classes de modèles
   - PARAMÈTRES :
     * self - Instance de ModelInterface
     * path - Chemin de fichier depuis lequel charger le modèle (format dépend de l'implémentation)
     * **kwargs - Arguments supplémentaires variables pour personnalisation du chargement
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTHODE ABSTRAITE** : Implémentation vide (pass) car c'est une interface à implémenter par les sous-classes
     * **CONTRAT STANDARD** : Définit la signature standard que doivent respecter toutes les implémentations
     * **FLEXIBILITÉ PARAMÈTRES** : Utilise **kwargs pour permettre des paramètres spécifiques à chaque type de modèle
     * **POLYMORPHISME** : Permet le chargement uniforme de différents types de modèles via la même interface
     * **DOCUMENTATION INTERFACE** : Fournit la documentation standard pour toutes les implémentations
     * **PATTERN FLUENT** : Retourne self pour permettre le chaînage de méthodes
   - RETOUR : self - Instance du modèle pour permettre le chaînage de méthodes
   - UTILITÉ : Interface fondamentale pour standardiser le chargement de modèles ML. Permet le polymorphisme et l'interchangeabilité des modèles. Essentielle pour les architectures modulaires où différents types de modèles doivent être chargés de manière uniforme.

71. save.txt (ModelInterface.save - SAUVEGARDE MODÈLE INTERFACE)
   - Lignes 1027-1038 dans ml_core.py (12 lignes)
   - FONCTION : Méthode d'interface abstraite pour la sauvegarde de modèles vers un chemin de fichier, définissant le contrat standard que doivent implémenter toutes les classes de modèles
   - PARAMÈTRES :
     * self - Instance de ModelInterface
     * path - Chemin de fichier vers lequel sauvegarder le modèle (format dépend de l'implémentation)
     * **kwargs - Arguments supplémentaires variables pour personnalisation de la sauvegarde
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTHODE ABSTRAITE** : Implémentation vide (pass) car c'est une interface à implémenter par les sous-classes
     * **CONTRAT STANDARD** : Définit la signature standard que doivent respecter toutes les implémentations
     * **FLEXIBILITÉ PARAMÈTRES** : Utilise **kwargs pour permettre des paramètres spécifiques à chaque type de modèle
     * **POLYMORPHISME** : Permet la sauvegarde uniforme de différents types de modèles via la même interface
     * **DOCUMENTATION INTERFACE** : Fournit la documentation standard pour toutes les implémentations
     * **RETOUR BOOLÉEN** : Spécifie que l'implémentation doit retourner True/False pour indiquer le succès
   - RETOUR : bool - True si la sauvegarde a réussi, False sinon (selon l'implémentation concrète)
   - UTILITÉ : Interface fondamentale pour standardiser la sauvegarde de modèles ML. Permet le polymorphisme et l'interchangeabilité des modèles. Essentielle pour les architectures modulaires où différents types de modèles doivent être sauvegardés de manière uniforme.

72. fit.txt (ModelInterface.fit - ENTRAÎNEMENT MODÈLE INTERFACE)
   - Lignes 984-996 dans ml_core.py (13 lignes)
   - FONCTION : Méthode d'interface abstraite pour l'entraînement de modèles sur des données, définissant le contrat standard que doivent implémenter toutes les classes de modèles
   - PARAMÈTRES :
     * self - Instance de ModelInterface
     * X - Features/données d'entraînement (format dépend de l'implémentation)
     * y - Labels/cibles d'entraînement (format dépend de l'implémentation)
     * **kwargs - Arguments supplémentaires variables pour personnalisation de l'entraînement
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTHODE ABSTRAITE** : Implémentation vide (pass) car c'est une interface à implémenter par les sous-classes
     * **CONTRAT STANDARD** : Définit la signature standard que doivent respecter toutes les implémentations
     * **FLEXIBILITÉ PARAMÈTRES** : Utilise **kwargs pour permettre des paramètres spécifiques à chaque type de modèle
     * **POLYMORPHISME** : Permet l'entraînement uniforme de différents types de modèles via la même interface
     * **DOCUMENTATION INTERFACE** : Fournit la documentation standard pour toutes les implémentations
     * **PATTERN FLUENT** : Retourne self pour permettre le chaînage de méthodes
   - RETOUR : self - Instance du modèle pour permettre le chaînage de méthodes
   - UTILITÉ : Interface fondamentale pour standardiser l'entraînement de modèles ML. Permet le polymorphisme et l'interchangeabilité des modèles. Essentielle pour les architectures modulaires où différents types de modèles doivent être entraînés de manière uniforme.

73. factory.txt (ModelProvider.factory - FACTORY CRÉATION MODÈLE)
   - Lignes 782-785 dans ml_core.py (4 lignes)
   - FONCTION : Fonction factory interne qui crée des instances de modèles en combinant les paramètres par défaut avec les paramètres fournis, implémentant le pattern Factory pour la création flexible de modèles
   - PARAMÈTRES :
     * **kwargs - Arguments nommés variables à combiner avec les paramètres par défaut
   - FONCTIONNEMENT DÉTAILLÉ :
     * **FUSION PARAMÈTRES** : Combine default_params avec kwargs en utilisant {**default_params, **kwargs}
     * **PRIORITÉ KWARGS** : Les paramètres fournis dans kwargs écrasent les paramètres par défaut
     * **CRÉATION INSTANCE** : Appelle model_class(**params) pour instancier le modèle avec les paramètres fusionnés
     * **PATTERN FACTORY** : Implémente le design pattern Factory pour création standardisée
     * **FLEXIBILITÉ** : Permet la personnalisation tout en conservant des valeurs par défaut sensées
   - RETOUR : Instance du modèle créé avec les paramètres fusionnés
   - UTILITÉ : Fonction factory essentielle pour la création flexible de modèles avec paramètres par défaut. Permet la standardisation de la création tout en autorisant la personnalisation. Critique pour les systèmes de modèles configurables.

74. get_result.txt (TrainOptimizeInterface.get_result - RÉCUPÉRATION RÉSULTAT MÉTRIQUE)
   - Lignes 1208-1228 dans ml_core.py (21 lignes)
   - FONCTION : Récupère un résultat spécifique pour un modèle et une métrique donnés avec validation d'existence et gestion d'erreurs détaillée
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * model_name (str) - Nom du modèle pour lequel récupérer le résultat
     * metric_name (str) - Nom de la métrique à récupérer (ex: 'accuracy', 'loss', 'f1_score')
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION MODÈLE** : Vérifie si model_name existe dans self.results
     * **ERREUR MODÈLE** : Lève KeyError avec message explicite si le modèle n'est pas trouvé
     * **VALIDATION MÉTRIQUE** : Vérifie si metric_name existe dans self.results[model_name]
     * **ERREUR MÉTRIQUE** : Lève KeyError avec message détaillé incluant modèle et métrique si non trouvée
     * **RÉCUPÉRATION SÉCURISÉE** : Retourne self.results[model_name][metric_name] après validation complète
     * **MESSAGES EXPLICITES** : Inclut les noms de modèle et métrique dans les messages d'erreur pour debugging
   - RETOUR : float - Valeur numérique du résultat pour la métrique demandée
   - RAISES : KeyError - Si le modèle ou la métrique n'existe pas dans les résultats
   - UTILITÉ : Méthode essentielle pour accéder aux résultats d'évaluation de modèles. Permet la récupération sécurisée de métriques avec validation. Critique pour les systèmes d'analyse de performance et de comparaison de modèles.

75. set_result.txt (TrainOptimizeInterface.set_result - DÉFINITION RÉSULTAT MÉTRIQUE)
   - Lignes 1193-1206 dans ml_core.py (14 lignes)
   - FONCTION : Définit un résultat pour un modèle et une métrique spécifiques avec création automatique de la structure de données et logging
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * model_name (str) - Nom du modèle pour lequel définir le résultat
     * metric_name (str) - Nom de la métrique à définir (ex: 'accuracy', 'loss', 'f1_score')
     * value (float) - Valeur numérique du résultat à stocker
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CRÉATION MODÈLE** : Si model_name n'existe pas dans self.results, crée automatiquement self.results[model_name] = {}
     * **STOCKAGE VALEUR** : Enregistre la valeur avec self.results[model_name][metric_name] = value
     * **ÉCRASEMENT POSSIBLE** : Si la métrique existe déjà, la valeur est remplacée silencieusement
     * **LOGGING DEBUG** : Enregistre un message de debug avec métrique, valeur et modèle pour traçabilité
     * **STRUCTURE HIÉRARCHIQUE** : Maintient une structure modèle → métrique → valeur pour organisation
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour enregistrer les résultats d'évaluation de modèles. Permet le stockage organisé de métriques par modèle. Essentielle pour les systèmes de suivi de performance et d'analyse comparative de modèles.

76. get_best_model.txt (TrainOptimizeInterface.get_best_model - SÉLECTION MEILLEUR MODÈLE)
   - Lignes 1230-1263 dans ml_core.py (34 lignes)
   - FONCTION : Identifie et retourne le meilleur modèle selon une métrique spécifique avec support pour optimisation ascendante ou descendante
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * metric_name (str) - Nom de la métrique pour la comparaison (ex: 'accuracy', 'loss', 'f1_score')
     * higher_is_better (bool, défaut=True) - Si True, valeur plus élevée = meilleur; si False, valeur plus faible = meilleur
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION VARIABLES** : `best_model = None; best_value = float('-inf') if higher_is_better else float('inf')` initialisation comparaison
     * **PARCOURS RÉSULTATS** : `for model_name, metrics in self.results.items():` itération sur tous modèles
     * **VÉRIFICATION MÉTRIQUE** : `if metric_name in metrics:` contrôle existence métrique pour modèle
     * **EXTRACTION VALEUR** : `value = metrics[metric_name]` récupération valeur métrique
     * **COMPARAISON ASCENDANTE** : `if higher_is_better: if value > best_value:` logique optimisation maximisation
     * **COMPARAISON DESCENDANTE** : `else: if value < best_value:` logique optimisation minimisation
     * **MISE À JOUR MEILLEUR** : `best_value = value; best_model = model_name` assignation nouveau meilleur
     * **VALIDATION EXISTENCE** : `if best_model is None:` vérification qu'au moins un résultat existe
     * **EXCEPTION EXPLICITE** : `raise ValueError(f"Aucun résultat disponible pour la métrique '{metric_name}'")` erreur si aucun résultat
     * **RETOUR TUPLE** : `return best_model, best_value` retour nom modèle et valeur métrique
   - RETOUR : Tuple[str, float] - (nom du meilleur modèle, valeur de la métrique)
   - RAISES : ValueError - Si aucun résultat n'est disponible pour la métrique spécifiée
   - UTILITÉ : Méthode essentielle pour la sélection automatique du meilleur modèle selon des critères de performance. Critique pour les systèmes d'AutoML et la comparaison de modèles. Permet l'optimisation automatique de la sélection de modèles.

77. create_model_factory.txt (ModelProvider.create_model_factory - CRÉATION FACTORY MODÈLE)
   - Lignes 777-787 dans ml_core.py (11 lignes)
   - FONCTION : Crée une fonction factory pour instancier des modèles avec des paramètres par défaut prédéfinis, simplifiant la création répétée de modèles similaires
   - PARAMÈTRES :
     * model_class - Classe de modèle à instancier (ex: LGBMClassifier, LSTM, etc.)
     * **default_params - Paramètres par défaut à appliquer lors de chaque création de modèle
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉFINITION FACTORY** : `def factory(**kwargs): params = {**default_params, **kwargs}; return model_class(**params)` fonction interne
     * **FUSION PARAMÈTRES** : `{**default_params, **kwargs}` combine paramètres défaut avec kwargs fournis
     * **PRIORITÉ KWARGS** : Paramètres fournis dans kwargs écrasent paramètres par défaut
     * **INSTANCIATION** : `model_class(**params)` appel constructeur avec paramètres fusionnés
     * **RETOUR FACTORY** : `return factory` retourne fonction factory configurée pour réutilisation
     * **CLOSURE** : Utilise closure pour capturer model_class et default_params dans scope
     * **PATTERN FACTORY** : Implémente design pattern Factory Method avec paramètres par défaut
     * **TYPE HINTS** : `model_class: type`, `default_params: dict`, `-> function` validation statique
   - RETOUR : function - Fonction factory configurée pour créer des instances du modèle
   - UTILITÉ : Méthode avancée pour simplifier la création répétée de modèles avec configuration standard. Permet la standardisation des paramètres tout en autorisant la personnalisation. Essentielle pour les systèmes de modèles configurables et les pipelines ML.

78. predict_proba.txt (ModelInterface.predict_proba - PRÉDICTION PROBABILITÉS INTERFACE)
   - Lignes 1013-1024 dans ml_core.py (12 lignes)
   - FONCTION : Méthode d'interface abstraite pour la prédiction de probabilités à partir de features, définissant le contrat standard pour les prédictions probabilistes
   - PARAMÈTRES :
     * self - Instance de ModelInterface
     * X - Features/données d'entrée pour la prédiction de probabilités
     * **kwargs - Arguments supplémentaires variables pour personnalisation
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTHODE ABSTRAITE** : `pass` implémentation vide car interface à implémenter par sous-classes
     * **CONTRAT PROBABILISTE** : Définit signature standard pour prédictions probabilités
     * **FLEXIBILITÉ PARAMÈTRES** : `**kwargs` pour paramètres spécifiques à chaque type modèle
     * **POLYMORPHISME** : Permet utilisation uniforme différents modèles pour prédictions probabilistes
     * **DOCUMENTATION INTERFACE** : Fournit documentation standard pour toutes implémentations
     * **TYPE HINTS** : `X`, `**kwargs`, `-> probabilities` validation statique
     * **PATTERN TEMPLATE** : Définit template method pour implémentations concrètes
   - RETOUR : Probabilités (format dépend de l'implémentation concrète)
   - UTILITÉ : Interface fondamentale pour standardiser les prédictions probabilistes de modèles ML. Permet le polymorphisme pour les modèles nécessitant des probabilités. Essentielle pour les systèmes de confiance et d'incertitude.

79. get_calculate_uncertainty.txt (ModelProvider.get_calculate_uncertainty - RÉCUPÉRATION CALCUL INCERTITUDE)
   - Lignes 752-774 dans ml_core.py (23 lignes)
   - FONCTION : Importe et met en cache la méthode calculate_uncertainty depuis HybridBaccaratPredictor pour éviter les importations circulaires et optimiser les performances
   - PARAMÈTRES :
     * cls - Référence à la classe ModelProvider (méthode de classe)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION CACHE** : `if cls._calculate_uncertainty_func is not None: return cls._calculate_uncertainty_func` contrôle cache existant
     * **RETOUR CACHE** : Si fonction en cache, retourne immédiatement sans traitement supplémentaire
     * **RÉCUPÉRATION INSTANCE** : `temp_hbp = cls.get_hbp_instance()` obtient instance HybridBaccaratPredictor (singleton)
     * **MISE EN CACHE** : `cls._calculate_uncertainty_func = temp_hbp.calculate_uncertainty` stockage pour réutilisation
     * **RETOUR FONCTION** : `return cls._calculate_uncertainty_func` retourne méthode calculate_uncertainty
     * **ÉVITEMENT CIRCULAIRE** : Évite importations circulaires en important conditionnellement
     * **OPTIMISATION PERFORMANCE** : Cache pour éviter recréer instance HBP à chaque appel
     * **GARANTIE COHÉRENCE** : Utilise directement méthode instance HBP pour même logique production
     * **MÉTHODE CLASSE** : `@classmethod` pour accès variables classe
   - RETOUR : function - La méthode calculate_uncertainty de HybridBaccaratPredictor
   - UTILITÉ : Méthode avancée pour l'accès optimisé aux fonctions de calcul d'incertitude. Essentielle pour éviter les importations circulaires et optimiser les performances. Critique pour les systèmes nécessitant des calculs d'incertitude fréquents.

================================================================================
SECTION 9 : ANCIENNESCLASSES
================================================================================

80. class_ConfidenceCalculator.txt (ConfidenceCalculator - CLASSE CALCUL CONFIANCE)
   - Lignes 793-970 dans ml_core.py (178 lignes)
   - FONCTION : Classe complète dédiée au calcul de confiance et d'incertitude pour les prédictions de modèles ML, fournissant des méthodes avancées pour l'évaluation de la fiabilité des prédictions
   - PARAMÈTRES : Classe (pas de paramètres de méthode, mais contient des méthodes statiques)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CLASSE STATIQUE** : `class ConfidenceCalculator:` définition classe avec méthodes `@staticmethod`
     * **EXTRACTION CONFIANCE** : `@staticmethod def get_confidence_from_probabilities(probabilities, predicted_index):` méthode principale
     * **VALIDATION ENTRÉE** : `if predicted_index < 0 or predicted_index >= len(probabilities):` contrôle indices
     * **CONVERSION FORMATS** : `if torch.is_tensor(probabilities): probabilities = probabilities.detach().cpu().numpy()` support PyTorch
     * **CALCUL CONFIANCE** : `confidence = float(probabilities[predicted_index])` extraction probabilité prédite
     * **CALIBRATION PLACEHOLDER** : `@staticmethod def calculate_calibrated_confidence(probabilities, calibration_method='isotonic'):` méthode future
     * **LOGGING WARNINGS** : `logger.warning("Calibration de confiance non implémentée")` avertissements
     * **GESTION ERREURS** : `try-except` blocs pour capture exceptions avec fallbacks
     * **ARCHITECTURE MODULAIRE** : Méthodes statiques indépendantes pour réutilisation flexible
   - RETOUR : Classe (définition de classe, pas de retour direct)
   - UTILITÉ : Classe fondamentale pour l'évaluation de la confiance dans les systèmes ML. Essentielle pour les applications nécessitant une mesure de fiabilité des prédictions. Critique pour les systèmes de prise de décision basés sur la confiance et l'incertitude.

81. class_MemoryManager.txt (MemoryManager - CLASSE GESTION MÉMOIRE)
   - Lignes 361-524 dans ml_core.py (164 lignes)
   - FONCTION : Classe complète dédiée à la gestion optimisée de la mémoire PyTorch et LSTM, fournissant des méthodes statiques pour l'optimisation, le nettoyage et la surveillance de la mémoire
   - PARAMÈTRES : Classe (contient des méthodes statiques et de classe)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTHODES STATIQUES** : Principalement des méthodes statiques pour optimisation globale
     * **OPTIMISATION PYTORCH** : optimize_pytorch_memory pour configuration système complète
     * **OPTIMISATION LSTM** : optimize_lstm_memory pour modèles LSTM spécifiques
     * **NETTOYAGE MÉMOIRE** : cleanup_pytorch_memory pour libération ressources
     * **HOOKS SURVEILLANCE** : register_memory_hooks pour monitoring automatique
     * **CONFIGURATION AVANCÉE** : Threads, cache CUDA, garbage collector, variables environnement
     * **GESTION ERREURS** : Try/catch avec logging pour robustesse
     * **ARCHITECTURE MODULAIRE** : Méthodes indépendantes utilisables séparément ou ensemble
   - RETOUR : Classe (définition de classe, pas de retour direct)
   - UTILITÉ : Classe centrale pour la gestion efficace de la mémoire dans les applications ML intensives. Essentielle pour éviter les fuites mémoire et optimiser les performances. Critique pour les environnements à ressources limitées et les modèles volumineux.

82. class_LoggingManager.txt (LoggingManager - GESTIONNAIRE LOGGING CENTRALISÉ)
   - Lignes 130-212 dans ml_core.py (83 lignes)
   - FONCTION : Classe Singleton pour la gestion centralisée du logging avec configuration avancée des loggers, handlers, formatters et répertoires de logs
   - RESPONSABILITÉS :
     * **PATTERN SINGLETON** : `if cls._instance is None: cls._instance = super().__new__(cls)` instance unique garantie
     * **GESTION LOGGERS** : `self._loggers = {}` création et configuration loggers personnalisés par nom
     * **HANDLERS MULTIPLES** : `self._handlers = {}` support fichiers, console, rotation avec configurations spécifiques
     * **FORMATTERS** : `self._formatters = {}` formatage personnalisé messages avec timestamps et niveaux
     * **RÉPERTOIRES LOGS** : `os.makedirs(self._log_directory, exist_ok=True)` création automatique répertoires stockage
     * **INITIALISATION** : `cls._instance._initialize()` configuration structures internes lors création
     * **VARIABLE CLASSE** : `_instance = None` stockage instance au niveau classe
   - MÉTHODES PRINCIPALES :
     * __new__() - Implémentation Singleton avec vérification d'instance
     * _initialize() - Initialisation structures internes (loggers, handlers, formatters)
     * get_logger() - Récupération/création de loggers configurés
     * configure_root_logger() - Configuration du logger racine système
   - ARCHITECTURE :
     * **SINGLETON THREAD-SAFE** : Instance unique avec initialisation contrôlée
     * **CONFIGURATION FLEXIBLE** : Loggers configurables par domaine fonctionnel
     * **STOCKAGE ORGANISÉ** : Répertoire logs avec structure hiérarchique
   - UTILITÉ : Classe fondamentale pour le logging centralisé et cohérent dans toute l'application. Essentielle pour le debugging, monitoring et audit. Critique pour les systèmes de production nécessitant traçabilité complète.

83. class_ModuleInterface.txt (ModuleInterface - INTERFACE MODULAIRE INJECTION DÉPENDANCES)
   - Lignes 221-352 dans ml_core.py (132 lignes)
   - FONCTION : Classe Singleton pour l'injection de dépendances et la gestion modulaire avec registres de fonctions, classes, instances et chargement paresseux
   - RESPONSABILITÉS :
     * **PATTERN SINGLETON** : `if cls._instance is None: cls._instance = super().__new__(cls)` instance unique cohérence globale
     * **REGISTRE FONCTIONS** : `self._functions = {}` enregistrement et récupération fonctions par nom
     * **REGISTRE CLASSES** : `self._classes = {}` gestion centralisée classes avec accès par nom
     * **REGISTRE INSTANCES** : `self._instances = {}` stockage instances configurées pour réutilisation
     * **CHARGEMENT PARESSEUX** : `self._lazy_loaders = {}` lazy loading avec loaders pour optimisation mémoire
     * **FACTORIES** : `self._factories = {}` support pour factories création objets configurables
     * **INITIALISATION** : `cls._instance._initialize()` configuration structures internes lors création
   - MÉTHODES PRINCIPALES :
     * register_function/class/instance() - Enregistrement de dépendances
     * get_function/class/instance() - Récupération avec chargement paresseux
     * register_lazy_loader() - Configuration de chargeurs différés
   - ARCHITECTURE :
     * **INJECTION DÉPENDANCES** : Pattern DI pour découplage et testabilité
     * **LAZY LOADING** : Chargement à la demande pour optimisation ressources
     * **THREAD-SAFE** : Opérations atomiques sur registres partagés
     * **EXTENSIBLE** : Support pour nouveaux types de dépendances
   - UTILITÉ : Classe centrale pour l'architecture modulaire et l'injection de dépendances. Essentielle pour le découplage des composants et la testabilité. Critique pour les systèmes complexes nécessitant gestion flexible des dépendances.

84. class_PyTorchMemoryContext.txt (PyTorchMemoryContext - GESTIONNAIRE CONTEXTE MÉMOIRE PYTORCH)
   - Lignes 526-567 dans ml_core.py (42 lignes)
   - FONCTION : Classe gestionnaire de contexte pour l'optimisation automatique de la mémoire PyTorch avec support du protocole with statement
   - RESPONSABILITÉS :
     * **CONTEXT MANAGER** : Implémentation __enter__ et __exit__ pour protocole with
     * **OPTIMISATION ENTRÉE** : Nettoyage mémoire automatique à l'entrée si configuré
     * **OPTIMISATION SORTIE** : Nettoyage mémoire automatique à la sortie si configuré
     * **CONFIGURATION FLEXIBLE** : Flags optimize_on_enter et optimize_on_exit configurables
     * **GESTION ERREURS** : Nettoyage même en cas d'exception dans le bloc with
   - MÉTHODES PRINCIPALES :
     * __init__() - Configuration des flags d'optimisation entrée/sortie
     * __enter__() - Optimisation conditionnelle à l'entrée du contexte
     * __exit__() - Optimisation conditionnelle à la sortie avec gestion d'exceptions
   - ARCHITECTURE :
     * **PATTERN CONTEXT MANAGER** : Utilisation avec statement pour gestion automatique
     * **DÉLÉGATION** : Utilise MemoryManager.optimize_pytorch_memory() pour optimisations
     * **CONFIGURATION GRANULAIRE** : Contrôle précis des moments d'optimisation
     * **ROBUSTESSE** : Fonctionne même si exceptions dans le bloc utilisateur
   - UTILITÉ : Classe essentielle pour la gestion automatique de la mémoire PyTorch dans des blocs de code spécifiques. Permet l'optimisation transparente sans intervention manuelle. Critique pour les sections de code intensives en mémoire GPU.

85. class_LSTMMemoryContext.txt (LSTMMemoryContext - GESTIONNAIRE CONTEXTE MÉMOIRE LSTM)
   - Lignes 569-632 dans ml_core.py (64 lignes)
   - FONCTION : Classe gestionnaire de contexte spécialisée pour l'optimisation mémoire des modèles LSTM avec gestion des états cachés et configuration avancée
   - RESPONSABILITÉS :
     * **CONTEXT MANAGER LSTM** : Optimisation spécialisée pour modèles LSTM avec états
     * **GESTION ÉTATS CACHÉS** : Nettoyage automatique des hidden states et cell states
     * **CONFIGURATION AVANCÉE** : Paramètres spécifiques LSTM (batch_size, sequence_length, etc.)
     * **OPTIMISATION ENTRÉE/SORTIE** : Nettoyage conditionnel à l'entrée et sortie du contexte
     * **MONITORING MÉMOIRE** : Surveillance spécialisée pour patterns d'utilisation LSTM
   - MÉTHODES PRINCIPALES :
     * __init__() - Configuration paramètres LSTM et flags d'optimisation
     * __enter__() - Optimisation LSTM à l'entrée avec paramètres spécifiques
     * __exit__() - Nettoyage LSTM à la sortie avec gestion d'exceptions
   - ARCHITECTURE :
     * **SPÉCIALISATION LSTM** : Optimisations ciblées pour architecture LSTM
     * **DÉLÉGATION SPÉCIALISÉE** : Utilise MemoryManager.optimize_lstm_memory()
     * **PARAMÈTRES CONTEXTUELS** : Configuration basée sur caractéristiques du modèle
     * **ROBUSTESSE LSTM** : Gestion des cas spécifiques aux réseaux récurrents
   - UTILITÉ : Classe spécialisée pour l'optimisation mémoire des modèles LSTM et RNN. Essentielle pour les séquences longues et modèles récurrents complexes. Critique pour les applications nécessitant gestion fine de la mémoire LSTM.

86. class_ModelInterface.txt (ModelInterface - INTERFACE ABSTRAITE MODÈLES ML)
   - Lignes 976-1052 dans ml_core.py (77 lignes)
   - FONCTION : Classe abstraite définissant l'interface standard pour tous les modèles ML avec méthodes obligatoires et optionnelles pour polymorphisme
   - RESPONSABILITÉS :
     * **INTERFACE ABSTRAITE** : Définition du contrat standard pour tous les modèles ML
     * **MÉTHODES OBLIGATOIRES** : fit(), predict(), save(), load() doivent être implémentées
     * **MÉTHODES OPTIONNELLES** : predict_proba() avec implémentation par défaut
     * **POLYMORPHISME** : Permet l'utilisation uniforme de différents types de modèles
     * **STANDARDISATION** : Garantit cohérence d'interface entre tous les modèles
   - MÉTHODES PRINCIPALES :
     * fit() - Entraînement du modèle (méthode abstraite)
     * predict() - Prédiction sur nouvelles données (méthode abstraite)
     * predict_proba() - Prédiction probabiliste (implémentation par défaut)
     * save() - Sauvegarde du modèle (méthode abstraite)
     * load() - Chargement du modèle (méthode abstraite)
   - ARCHITECTURE :
     * **ABC (Abstract Base Class)** : Utilise abc.ABC pour définir interface abstraite
     * **CONTRAT STRICT** : Méthodes abstraites obligent implémentation dans sous-classes
     * **FLEXIBILITÉ** : Permet extensions spécifiques tout en gardant interface commune
     * **DOCUMENTATION INTÉGRÉE** : Docstrings détaillées pour chaque méthode
   - UTILITÉ : Interface fondamentale pour standardiser tous les modèles ML du système. Essentielle pour le polymorphisme et l'interchangeabilité des modèles. Critique pour maintenir cohérence architecturale dans systèmes ML complexes.

87. class_ModelProvider.txt (ModelProvider - FOURNISSEUR MODÈLES ET INSTANCES)
   - Lignes 638-787 dans ml_core.py (150 lignes)
   - FONCTION : Classe utilitaire pour la création, gestion et fourniture de modèles ML avec support Singleton pour HybridBaccaratPredictor et factories configurables
   - RESPONSABILITÉS :
     * **SINGLETON HBP** : Gestion instance unique HybridBaccaratPredictor avec configuration
     * **FACTORIES MODÈLES** : Création de factories pour instanciation répétée de modèles
     * **CACHE FONCTIONS** : Mise en cache de fonctions calculate_uncertainty pour performance
     * **CONFIGURATION AVANCÉE** : Support trial_id Optuna et configurations personnalisées
     * **ÉVITEMENT CYCLES** : Importations conditionnelles pour éviter dépendances circulaires
   - MÉTHODES PRINCIPALES :
     * get_hbp_instance() - Récupération/création instance HBP Singleton
     * create_model_factory() - Création factories avec paramètres par défaut
     * get_calculate_uncertainty() - Accès fonction calculate_uncertainty avec cache
   - ARCHITECTURE :
     * **PATTERN SINGLETON** : Instance unique HBP avec thread-safety
     * **PATTERN FACTORY** : Factories configurables pour création de modèles
     * **LAZY LOADING** : Chargement différé pour optimisation performance
     * **CACHE INTELLIGENT** : Mise en cache des fonctions coûteuses
   - UTILITÉ : Classe centrale pour la gestion et fourniture de modèles dans le système. Essentielle pour l'optimisation Optuna et la gestion des instances. Critique pour éviter conflits de ressources et garantir cohérence des modèles.

88. class_TrainOptimizeInterface.txt (TrainOptimizeInterface - INTERFACE ENTRAÎNEMENT ET OPTIMISATION)
   - Lignes 1054-1263 dans ml_core.py (210 lignes)
   - FONCTION : Classe Singleton pour la gestion centralisée de l'entraînement et optimisation de modèles ML avec registres de composants et évaluation de performance
   - RESPONSABILITÉS :
     * **PATTERN SINGLETON** : Instance unique pour cohérence globale des processus ML
     * **REGISTRE MODÈLES** : Gestion centralisée des modèles ML avec accès par nom
     * **REGISTRE MÉTRIQUES** : Stockage et récupération de fonctions de métrique personnalisées
     * **REGISTRE DONNÉES** : Gestion des sources de données avec accès uniforme
     * **HYPERPARAMÈTRES** : Configuration et stockage des hyperparamètres par modèle
     * **RÉSULTATS** : Stockage et analyse des résultats d'évaluation de modèles
   - MÉTHODES PRINCIPALES :
     * register_model/metric/data_source() - Enregistrement de composants
     * get_model/metric/data_source() - Récupération avec validation
     * set/get_hyperparameters() - Gestion configuration modèles
     * set/get_result() - Stockage et récupération résultats
     * get_best_model() - Sélection automatique du meilleur modèle
   - ARCHITECTURE :
     * **CENTRALISATION** : Point unique pour tous les composants ML
     * **VALIDATION** : Vérification existence avant récupération
     * **FLEXIBILITÉ** : Support pour types de composants variés
     * **ANALYSE** : Outils d'analyse comparative des modèles
   - UTILITÉ : Interface centrale pour tous les processus d'entraînement et d'optimisation ML. Essentielle pour la gestion cohérente des expériences ML. Critique pour les systèmes AutoML et la comparaison de modèles.

89. class_ThreadedTrainer.txt (ThreadedTrainer - ENTRAÎNEUR THREADÉ ASYNCHRONE)
   - Lignes 1361-1589 dans ml_core.py (229 lignes)
   - FONCTION : Classe pour l'entraînement asynchrone de modèles ML avec gestion thread-safe, callbacks de progression et contrôle d'arrêt gracieux
   - RESPONSABILITÉS :
     * **ENTRAÎNEMENT ASYNCHRONE** : Exécution d'entraînement dans thread séparé
     * **GESTION ÉTATS** : Suivi des états running, completed, error avec thread-safety
     * **CALLBACKS PROGRESSION** : Notifications temps réel du progrès d'entraînement
     * **ARRÊT GRACIEUX** : Mécanisme stop_event pour arrêt coopératif
     * **GESTION ERREURS** : Capture et stockage des exceptions pour diagnostic
     * **RÉSULTATS** : Stockage thread-safe des résultats d'entraînement
   - MÉTHODES PRINCIPALES :
     * __init__() - Configuration trainer avec fonction et callbacks
     * start() - Démarrage entraînement asynchrone avec thread
     * stop() - Arrêt gracieux avec événement de synchronisation
     * is_training_running() - Vérification état d'exécution
     * get_result/error() - Récupération résultats et erreurs
   - ARCHITECTURE :
     * **THREADING** : Utilise threading.Thread pour exécution asynchrone
     * **ÉVÉNEMENTS** : threading.Event pour synchronisation et arrêt
     * **THREAD-SAFETY** : Accès atomique aux variables partagées
     * **CALLBACKS** : Pattern Observer pour notifications de progression
   - UTILITÉ : Classe essentielle pour l'entraînement non-bloquant de modèles ML. Permet interfaces utilisateur responsives pendant entraînements longs. Critique pour systèmes interactifs et entraînements de modèles volumineux.

90. class_ThreadedOptimizer.txt (ThreadedOptimizer - OPTIMISEUR THREADÉ HYPERPARAMÈTRES)
   - Lignes 1591-1729 dans ml_core.py (139 lignes)
   - FONCTION : Classe pour l'optimisation asynchrone d'hyperparamètres avec support Optuna, GridSearch et callbacks de progression en temps réel
   - RESPONSABILITÉS :
     * **OPTIMISATION ASYNCHRONE** : Exécution d'optimisation dans thread séparé
     * **SUPPORT OPTUNA** : Intégration native avec framework Optuna pour optimisation bayésienne
     * **CALLBACKS PROGRESSION** : Notifications temps réel du progrès d'optimisation
     * **ARRÊT GRACIEUX** : Mécanisme stop_event pour arrêt coopératif des études
     * **GESTION ERREURS** : Capture et stockage des exceptions d'optimisation
     * **RÉSULTATS** : Stockage thread-safe des meilleurs hyperparamètres trouvés
   - MÉTHODES PRINCIPALES :
     * __init__() - Configuration optimiseur avec fonction objective et callbacks
     * start() - Démarrage optimisation asynchrone avec thread
     * stop() - Arrêt gracieux avec événement de synchronisation
     * is_optimization_running() - Vérification état d'exécution
     * get_result/error() - Récupération résultats et erreurs d'optimisation
   - ARCHITECTURE :
     * **THREADING** : Utilise threading.Thread pour exécution asynchrone
     * **ÉVÉNEMENTS** : threading.Event pour synchronisation et arrêt
     * **THREAD-SAFETY** : Accès atomique aux variables partagées
     * **CALLBACKS** : Pattern Observer pour notifications de progression
   - UTILITÉ : Classe essentielle pour l'optimisation non-bloquante d'hyperparamètres. Permet interfaces utilisateur responsives pendant optimisations longues. Critique pour systèmes AutoML et recherche d'hyperparamètres optimaux.

91. class_ConfigManager.txt (ConfigManager - GESTIONNAIRE CONFIGURATION CENTRALISÉ)
   - Lignes 35-121 dans ml_core.py (87 lignes)
   - FONCTION : Classe Singleton pour la gestion centralisée de la configuration avec support namespaces, validation, valeurs par défaut et chargement depuis fichiers Python
   - RESPONSABILITÉS :
     * **PATTERN SINGLETON** : Instance unique garantie avec __new__ et _initialize
     * **NAMESPACES** : Organisation de la configuration par espaces de noms logiques
     * **CHARGEMENT FICHIERS** : Import de configurations depuis fichiers Python
     * **VALIDATION** : Système de validateurs personnalisés pour valeurs de configuration
     * **VALEURS DÉFAUT** : Gestion des valeurs par défaut avec fallback automatique
     * **TRAÇABILITÉ** : Suivi des sources de configuration pour debugging
   - MÉTHODES PRINCIPALES :
     * __new__() - Implémentation Singleton avec vérification d'instance
     * _initialize() - Initialisation structures internes (config, sources, validateurs)
     * load_from_python_file() - Chargement configuration depuis fichier Python
     * get/set() - Récupération et définition de valeurs avec validation
   - ARCHITECTURE :
     * **SINGLETON THREAD-SAFE** : Instance unique avec initialisation contrôlée
     * **CONFIGURATION HIÉRARCHIQUE** : Support namespaces pour organisation
     * **VALIDATION FLEXIBLE** : Validateurs personnalisés par clé de configuration
     * **IMPORT DYNAMIQUE** : Chargement de modules Python pour configuration
   - UTILITÉ : Classe fondamentale pour la gestion centralisée et cohérente de la configuration. Essentielle pour maintenir la cohérence des paramètres dans toute l'application. Critique pour les systèmes complexes nécessitant configuration flexible et validation.
