# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\data_manager.py
# Lignes: 297 à 378
# Type: Méthode de la classe BaccaratSequenceManager

    def evaluate_performance(self, y_true: np.ndarray, y_pred_proba: np.ndarray) -> dict:
        """
        Évalue la performance (loss, accuracy) sur des données DÉJÀ FILTRÉES.
        MODIFIÉ: Utilise Scikit-learn au lieu de TensorFlow.
        Suppose que y_true et y_pred_proba correspondent
        exclusivement aux mains cibles >= min_target_hand_index.

        Args:
            y_true (np.ndarray): Les résultats réels (0 ou 1) des mains évaluées.
                                 Doit être 1D.
            y_pred_proba (np.ndarray): Les probabilités prédites pour la classe 1
                                       des mains évaluées. Doit être 1D.

        Returns:
            dict: Un dictionnaire contenant 'loss' et 'accuracy'.
                  Retourne des NaNs si les inputs sont vides ou invalides.
        """
        if not isinstance(y_true, np.ndarray) or not isinstance(y_pred_proba, np.ndarray):
             self.logger.warning("Attention evaluate_performance: y_true et y_pred_proba doivent être des np.ndarray.")
             return {'loss': np.nan, 'accuracy': np.nan}

        if y_true.ndim != 1 or y_pred_proba.ndim != 1:
             self.logger.warning(f"Attention evaluate_performance: y_true ({y_true.ndim}D) et y_pred_proba ({y_pred_proba.ndim}D) doivent être 1D.")
             # Essayer d'aplatir si possible (ex: sortie modèle (N, 1))
             if y_pred_proba.ndim == 2 and y_pred_proba.shape[1] == 1:
                 y_pred_proba = y_pred_proba.flatten()
                 if y_true.ndim != 1 or y_pred_proba.ndim != 1: # Re-vérifier après flatten
                     return {'loss': np.nan, 'accuracy': np.nan}
             elif y_true.ndim != 1: # Si y_true n'est pas 1D
                 return {'loss': np.nan, 'accuracy': np.nan}


        if y_true.shape[0] == 0 or y_pred_proba.shape[0] == 0:
            self.logger.warning("Attention evaluate_performance: Données d'évaluation vides. Retour de NaN.")
            return {'loss': np.nan, 'accuracy': np.nan}

        if y_true.shape != y_pred_proba.shape:
             self.logger.warning(f"Attention evaluate_performance: Shapes incompatibles - y_true: {y_true.shape}, y_pred_proba: {y_pred_proba.shape}. Retour de NaN.")
             return {'loss': np.nan, 'accuracy': np.nan}

        # Vérifier si y_true contient bien 0 et 1 (ou juste une classe)
        unique_labels = np.unique(y_true)
        if not np.all(np.isin(unique_labels, [0, 1])):
             self.logger.warning(f"Attention evaluate_performance: y_true contient des labels inattendus: {unique_labels}. Calcul métriques peut échouer.")
             # Ne retourne pas NaN ici, laisse sklearn gérer, mais logue l'avertissement.

        # Préparer les prédictions de classe pour accuracy_score
        try:
             y_pred_class = (y_pred_proba >= 0.5).astype(int)
        except Exception as e_astype:
             self.logger.error(f"Erreur conversion y_pred_proba en classes: {e_astype}")
             return {'loss': np.nan, 'accuracy': np.nan}

        loss = np.nan
        accuracy = np.nan

        try:
             # --- Calcul de la Perte (Log Loss avec Scikit-learn) ---
             # Gérer le cas où il n'y a qu'une seule classe dans y_true (log_loss peut échouer)
             if len(unique_labels) < 2:
                  self.logger.warning(f"Calcul LogLoss ignoré car une seule classe présente dans y_true ({unique_labels}).")
                  loss = np.nan # Ou 0.0 si on considère la prédiction parfaite? Nan est plus sûr.
             else:
                  # log_loss de sklearn gère le clipping interne pour éviter log(0)
                  loss = log_loss(y_true, y_pred_proba, eps=1e-15) # eps similaire à celui de Keras

             # --- Calcul de l'Accuracy (avec Scikit-learn) ---
             accuracy = accuracy_score(y_true, y_pred_class)

             # Vérifier si les résultats sont finis
             if not np.isfinite(loss): loss = np.nan
             if not np.isfinite(accuracy): accuracy = np.nan

             return {'loss': loss, 'accuracy': accuracy}

        except ValueError as ve:
             # Peut arriver si y_pred_proba sort de [0, 1] ou si labels inattendus
             self.logger.error(f"Erreur valeur pendant calcul métriques sklearn: {ve}")
             return {'loss': np.nan, 'accuracy': np.nan}
        except Exception as e:
            self.logger.error(f"Erreur inattendue durant calcul métriques sklearn: {e}", exc_info=True)
            return {'loss': np.nan, 'accuracy': np.nan}