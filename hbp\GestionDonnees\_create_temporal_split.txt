# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 4331 à 4349
# Type: Méthode de la classe HybridBaccaratPredictor

    def _create_temporal_split(self,  X_lgbm_all: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
      """Extrait la logique du split temporel dans une fonction séparée."""
      logger_instance = getattr(self, 'logger', logging.getLogger(__name__))
      n_splits = max(2, getattr(self.config, 'lgbm_cv_splits', 5))
      tscv = TimeSeriesSplit(n_splits=n_splits)
      train_indices_list = []
      val_indices_list = []

      for train_idx, val_idx in tscv.split(X_lgbm_all):
          train_indices_list.append(train_idx)
          val_indices_list.append(val_idx)

      if not train_indices_list or not val_indices_list:
          logger_instance.error("Impossible générer splits après préparation Manager.")
          return (None, None)

      train_indices = train_indices_list[-1]
      val_indices = val_indices_list[-1]
      return train_indices, val_indices