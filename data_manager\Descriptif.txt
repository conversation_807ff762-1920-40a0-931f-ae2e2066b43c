DESCRIPTIF DÉTAILLÉ DES MÉTHODES - DEUXIÈME VAGUE TECHNIQUE
================================================================================

Ce fichier contient la description technique détaillée de toutes les méthodes du système data_manager.py.

ÉTAT DOCUMENTATION : DEUXIÈME VAGUE COMPLÈTE
- **Couverture** : 100% des méthodes enrichies techniquement
- **Niveau** : Code source réel intégré, détails d'implémentation
- **Qualité** : 25-40 lignes par méthode avec précision technique

STRUCTURE DU SYSTÈME :
- **SECTION 1 : GESTIONDONNEES** (2 méthodes) - Génération et filtrage des données pour modèles ML
- **SECTION 2 : PREPARATIONMODELES** (1 méthode) - Interface avec les modèles LGBM et LSTM
- **SECTION 3 : EVALUATIONPERFORMANCE** (1 méthode) - Calcul des métriques de performance
- **SECTION 4 : ANCIENNESCLASSES** (1 classe) - Définitions de classes principales

TOTAL : 5 MÉTHODES/CLASSES ANALYSÉES

================================================================================

SECTION 1 : GESTIONDONNEES
================================================================================

1. __init__.txt (BaccaratSequenceManager.__init__ - INITIALISATION GESTIONNAIRE SÉQUENCES)
   - Lignes 30-85 dans data_manager.py (56 lignes)
   - FONCTION : Initialise le gestionnaire de séquences Baccarat modifié avec fenêtre adaptative pour modèles LGBM et LSTM
   - PARAMÈTRES :
     * self - Instance de BaccaratSequenceManager
     * sequence_length (int) - Taille maximale matrice sortie features LSTM
     * min_target_hand_index (int) - Index minimum main cible (0-based) à inclure
     * hybrid_feature_creator (Callable) - Fonction création features hybrides [LGBM], [LSTM]
     * lgbm_feature_count (int) - Nombre attendu features LGBM
     * lstm_seq_len (int) - Longueur attendue séquence features LSTM
     * lstm_feature_count (int) - Nombre attendu features par pas temps LSTM
     * parent_logger (optional) - Logger externe optionnel
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SEQUENCE_LENGTH** : `if not isinstance(sequence_length, int) or sequence_length <= 0: raise ValueError("sequence_length doit être un entier positif.")`
     * **VALIDATION MIN_TARGET** : `if not isinstance(min_target_hand_index, int) or min_target_hand_index < 0: raise ValueError("min_target_hand_index doit être un entier positif ou nul.")`
     * **VALIDATION CREATOR** : `if not callable(hybrid_feature_creator): raise TypeError("hybrid_feature_creator doit être une fonction callable.")`
     * **VALIDATION LGBM_COUNT** : `if not isinstance(lgbm_feature_count, int) or lgbm_feature_count <= 0: raise ValueError("lgbm_feature_count doit être un entier positif.")`
     * **VALIDATION LSTM_SEQ** : `if not isinstance(lstm_seq_len, int) or lstm_seq_len <= 0: raise ValueError("lstm_seq_len doit être un entier positif.")`
     * **VALIDATION LSTM_COUNT** : `if not isinstance(lstm_feature_count, int) or lstm_feature_count <= 0: raise ValueError("lstm_feature_count doit être un entier positif.")`
     * **ASSIGNATION ATTRIBUTS** : `self.sequence_length = sequence_length`, `self.min_target_hand_index = min_target_hand_index`, `self.hybrid_feature_creator = hybrid_feature_creator`
     * **STOCKAGE COUNTS** : `self.lgbm_feature_count = lgbm_feature_count`, `self.lstm_seq_len = lstm_seq_len`, `self.lstm_feature_count = lstm_feature_count`
     * **CALCUL SHAPE LSTM** : `self.lstm_expected_shape = (lstm_seq_len, lstm_feature_count)` commentaire "Mettre à jour la shape"
     * **CONFIGURATION LOGGER** : `self.logger = parent_logger if parent_logger else logging.getLogger(__name__)`
     * **LOGGING INITIALISATION** : `logger.info(f"BaccaratSequenceManager (Modifié) initialisé:")` avec détails configuration
     * **LOGGING DÉTAILS** : Messages info sur sequence_length, min_target_hand_index, hybrid_feature_creator.__name__, validation counts et shape
     * **FENÊTRE ADAPTATIVE** : Commentaire "= lstm_seq_len pour create_hybrid_features" sur sequence_length
   - RETOUR : None (constructeur)
   - UTILITÉ : Méthode fondamentale pour créer gestionnaire séquences Baccarat. Essentielle pour configuration modèles ML. Critique pour validation paramètres et fenêtre adaptative.

2. _generate_filtered_data_for_shoe.txt (BaccaratSequenceManager._generate_filtered_data_for_shoe - GÉNÉRATION DONNÉES FILTRÉES SABOT)
   - Lignes 87-190 dans data_manager.py (104 lignes)
   - FONCTION : Génère features LGBM/LSTM, labels, séquences préfixes et indices pour un seul sabot avec filtrage min_target_hand_index et garantie manches 31-60
   - PARAMÈTRES :
     * self - Instance de BaccaratSequenceManager
     * shoe_pb_sequence (List[str]) - Séquence P/B ('player'/'banker') du sabot
     * game_index_offset (int) - Index global du premier coup potentiel de ce sabot
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION LISTES** : `X_lgbm_list, y_list, X_lstm_list, prefix_list, origin_list = [], [], [], [], []`
     * **CALCUL LONGUEUR** : `game_len = len(shoe_pb_sequence)`
     * **VALIDATION LONGUEUR** : `if game_len != 60: self.logger.warning(f"Sabot de longueur {game_len} != 60 détecté (offset={game_index_offset})")` commentaire "sans log pour éviter de surcharger les logs"
     * **VÉRIFICATION MINIMUM** : `if game_len < 2: self.logger.warning(f"Sabot trop court ({game_len} < 2), ignoré."); return X_lgbm_list, y_list, X_lstm_list, prefix_list, origin_list`
     * **DÉFINITION CIBLES** : `target_round_min = 31`, `target_round_max = 60` commentaire "MODIFICATION: Définir les limites des manches cibles (31-60)"
     * **BOUCLE PRINCIPALE** : `for i in range(1, game_len):` commentaire "On commence à l'index 1 (pour avoir au moins 1 élément d'historique)"
     * **CALCUL POSITION** : `position_1_indexed = i + 1` commentaire "'i' est l'index (0-based) de la main à prédire (la CIBLE y)"
     * **CONDITION CIBLE** : `is_target_round = (position_1_indexed >= target_round_min and position_1_indexed <= target_round_max)`
     * **FILTRE PRINCIPAL** : `if i >= self.min_target_hand_index or is_target_round:` commentaire "MODIFICATION: Ajouter la condition is_target_round"
     * **SÉQUENCE ENTRÉE** : `input_sequence = shoe_pb_sequence[:i]` commentaire "Utilise toute la séquence disponible jusqu'à i-1 (fenêtre adaptative)"
     * **OUTCOME CIBLE** : `actual_outcome = shoe_pb_sequence[i]` commentaire "La cible à l'index i"
     * **GÉNÉRATION FEATURES** : `features_lgbm, features_lstm_np = self.hybrid_feature_creator(input_sequence)`
     * **VALIDATION LGBM** : `valid_lgbm = (features_lgbm is not None and len(features_lgbm) == self.lgbm_feature_count)`
     * **VALIDATION LSTM** : `valid_lstm = (features_lstm_np is not None and features_lstm_np.shape == self.lstm_expected_shape)`
     * **CONVERSION LABEL** : `label = 1 if actual_outcome == 'banker' else 0` commentaire système zero-based
     * **CALCUL INDEX GLOBAL** : `global_origin_index = game_index_offset + i`
     * **AJOUT DONNÉES** : `if valid_lgbm and valid_lstm:` puis append à toutes listes
     * **GESTION ERREURS** : Construction `reason = []` avec conditions détaillées pour logging échecs
     * **LOGGING CONDITIONNEL** : `if is_target_round: self.logger.warning(...)` vs `self.logger.debug(...)`
     * **VÉRIFICATION FINALE** : `if game_len >= target_round_max:` calcul `target_indices` et `expected_target_count` avec warning si manquantes
   - RETOUR : Tuple[List, List, List, List, List] - (X_lgbm, y, X_lstm, prefix_seq, origin_idx) pour ce sabot
   - UTILITÉ : Méthode interne critique pour génération données filtrées par sabot. Essentielle pour garantie manches cibles 31-60. Permet fenêtre adaptative et validation robuste.

================================================================================

SECTION 2 : PREPARATIONMODELES
================================================================================

3. prepare_data_for_model.txt (BaccaratSequenceManager.prepare_data_for_model - PRÉPARATION DONNÉES MODÈLES)
   - Lignes 192-291 dans data_manager.py (100 lignes)
   - FONCTION : Prépare données X_lgbm, y, X_lstm, préfixes et origines agrégées depuis liste séquences P/B avec filtrage min_target_hand_index
   - PARAMÈTRES :
     * self - Instance de BaccaratSequenceManager
     * list_of_pb_sequences (List[List[str]]) - Liste séquences P/B (chaque séquence est List[str])
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION GLOBALES** : `all_X_lgbm, all_y, all_X_lstm, all_prefixes, all_origins = [], [], [], [], []`
     * **OFFSET GLOBAL** : `current_global_offset = 0` pour tracking indices globaux
     * **CALCUL NOMBRE** : `num_games = len(list_of_pb_sequences)`
     * **LOGGING DÉBUT** : `self.logger.info(f"Manager: Préparation données filtrées depuis {num_games} séquences P/B...")`
     * **BOUCLE SÉQUENCES** : `for game_idx, shoe_seq in enumerate(list_of_pb_sequences):`
     * **VALIDATION TYPE** : `if not isinstance(shoe_seq, list): self.logger.warning(f"Manager: Élément {game_idx} n'est pas une liste, ignoré."); continue`
     * **GÉNÉRATION SABOT** : `try: X_lgbm_shoe, y_shoe, X_lstm_shoe, prefix_shoe, origin_shoe = self._generate_filtered_data_for_shoe(shoe_seq, current_global_offset)`
     * **AGRÉGATION RÉSULTATS** : `all_X_lgbm.extend(X_lgbm_shoe)`, `all_y.extend(y_shoe)`, `all_X_lstm.extend(X_lstm_shoe)`, `all_prefixes.extend(prefix_shoe)`, `all_origins.extend(origin_shoe)`
     * **GESTION ERREURS** : `except Exception as e_gen: self.logger.error(f"Manager: Erreur génération données sabot {game_idx}: {e_gen}", exc_info=True)`
     * **MISE À JOUR OFFSET** : `current_global_offset += len(shoe_seq)` commentaire "L'offset est le début du *prochain* sabot"
     * **VÉRIFICATION VIDE** : `if not all_y: self.logger.warning("Manager: Aucune donnée valide générée après filtrage et génération."); return None, None, None, None, None`
     * **CONVERSION NUMPY** : `try: X_lgbm_np = np.array(all_X_lgbm, dtype=np.float64)` commentaire "Utiliser float64 pour LGBM"
     * **CONVERSION Y** : `y_np = np.array(all_y, dtype=np.int64)`
     * **CONVERSION LSTM** : `X_lstm_np = np.stack(all_X_lstm, axis=0).astype(np.float32)` commentaire "LSTM float32 OK"
     * **ASSIGNATION LISTES** : `list_of_prefixes_final = all_prefixes`, `list_of_origins_final = all_origins`
     * **VALIDATION ÉTIQUETTES** : `unique_labels = np.unique(y_np); if not np.all(np.isin(unique_labels, [0, 1])): raise ValueError(...)`
     * **CALCUL ÉCHANTILLONS** : `num_samples_final = len(y_np)`
     * **LOGGING FINAL** : Messages info détaillés sur shapes X_lgbm, y, X_lstm et nombres préfixes/origines
     * **RETOUR SUCCÈS** : `return X_lgbm_np, y_np, X_lstm_np, list_of_prefixes_final, list_of_origins_final`
     * **GESTION EXCEPTIONS** : `except ValueError as e_val:` et `except Exception as e_gen:` avec logging et return None
   - RETOUR : Tuple[Optional[np.ndarray], Optional[np.ndarray], Optional[np.ndarray], Optional[List[List[str]]], Optional[List[int]]] - (X_lgbm, y, X_lstm, prefixes, origins) ou (None, None, None, None, None)
   - UTILITÉ : Méthode principale pour agrégation données multi-sabots. Essentielle pour interface modèles ML. Critique pour validation finale et conversion NumPy.

================================================================================

SECTION 3 : EVALUATIONPERFORMANCE
================================================================================

4. evaluate_performance.txt (BaccaratSequenceManager.evaluate_performance - ÉVALUATION PERFORMANCE MODÈLES)
   - Lignes 297-378 dans data_manager.py (82 lignes)
   - FONCTION : Évalue performance (loss, accuracy) sur données déjà filtrées en utilisant scikit-learn au lieu de TensorFlow
   - PARAMÈTRES :
     * self - Instance de BaccaratSequenceManager
     * y_true (np.ndarray) - Résultats réels (0 ou 1) des mains évaluées, doit être 1D
     * y_pred_proba (np.ndarray) - Probabilités prédites pour classe 1 des mains évaluées, doit être 1D
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION TYPES** : `if not isinstance(y_true, np.ndarray) or not isinstance(y_pred_proba, np.ndarray): return {'loss': np.nan, 'accuracy': np.nan}`
     * **VALIDATION DIMENSIONS** : `if y_true.ndim != 1 or y_pred_proba.ndim != 1:` avec warning détaillé
     * **GESTION FLATTEN** : `if y_pred_proba.ndim == 2 and y_pred_proba.shape[1] == 1: y_pred_proba = y_pred_proba.flatten()` commentaire "ex: sortie modèle (N, 1)"
     * **RE-VÉRIFICATION** : `if y_true.ndim != 1 or y_pred_proba.ndim != 1: return {'loss': np.nan, 'accuracy': np.nan}` après flatten
     * **VÉRIFICATION VIDE** : `if y_true.shape[0] == 0 or y_pred_proba.shape[0] == 0: self.logger.warning("Données d'évaluation vides"); return {'loss': np.nan, 'accuracy': np.nan}`
     * **VALIDATION SHAPES** : `if y_true.shape != y_pred_proba.shape: self.logger.warning(f"Shapes incompatibles - y_true: {y_true.shape}, y_pred_proba: {y_pred_proba.shape}"); return {'loss': np.nan, 'accuracy': np.nan}`
     * **CONTRÔLE ÉTIQUETTES** : `unique_labels = np.unique(y_true); if not np.all(np.isin(unique_labels, [0, 1])): self.logger.warning(...)`
     * **CONVERSION CLASSES** : `try: y_pred_class = (y_pred_proba >= 0.5).astype(int) except Exception as e_astype: return {'loss': np.nan, 'accuracy': np.nan}`
     * **INITIALISATION MÉTRIQUES** : `loss = np.nan`, `accuracy = np.nan`
     * **GESTION CLASSE UNIQUE** : `if len(unique_labels) < 2: self.logger.warning(...); loss = np.nan` commentaire "log_loss peut échouer"
     * **CALCUL LOG LOSS** : `else: loss = log_loss(y_true, y_pred_proba, eps=1e-15)` commentaire "eps similaire à celui de Keras"
     * **CALCUL ACCURACY** : `accuracy = accuracy_score(y_true, y_pred_class)`
     * **VALIDATION FINITUDE** : `if not np.isfinite(loss): loss = np.nan; if not np.isfinite(accuracy): accuracy = np.nan`
     * **RETOUR SUCCÈS** : `return {'loss': loss, 'accuracy': accuracy}`
     * **GESTION ERREURS** : `except ValueError as ve:` et `except Exception as e:` avec logging détaillé et return NaN
     * **ROBUSTESSE** : Commentaire "sklearn gère le clipping interne pour éviter log(0)"
   - RETOUR : dict - Dictionnaire contenant 'loss' et 'accuracy', retourne NaNs si inputs invalides
   - UTILITÉ : Méthode essentielle pour évaluation performance modèles ML. Critique pour métriques robustes. Remplace TensorFlow par scikit-learn pour compatibilité.

================================================================================

SECTION 4 : ANCIENNESCLASSES
================================================================================

5. class_BaccaratSequenceManager.txt (BaccaratSequenceManager - CLASSE GESTIONNAIRE SÉQUENCES BACCARAT)
   - Lignes 15-378 dans data_manager.py (364 lignes)
   - FONCTION : Classe gestionnaire de séquences Baccarat modifié avec fenêtre adaptative pour modèles LGBM et LSTM
   - RESPONSABILITÉS :
     * **GESTION SÉQUENCES** : Traitement séquences P/B avec fenêtre adaptative selon commentaire "Utilise toute la séquence disponible jusqu'à i-1"
     * **FILTRAGE DONNÉES** : Application `min_target_hand_index` et garantie manches 31-60 avec `target_round_min = 31`, `target_round_max = 60`
     * **GÉNÉRATION FEATURES** : Interface avec `self.hybrid_feature_creator(input_sequence)` retournant `(features_lgbm, features_lstm_np)`
     * **VALIDATION ROBUSTE** : Contrôles `isinstance()`, `len()`, `shape` et cohérence données avec logging détaillé
     * **AGRÉGATION MULTI-SABOTS** : Traitement `list_of_pb_sequences` avec `current_global_offset += len(shoe_seq)`
     * **ÉVALUATION PERFORMANCE** : Métriques `log_loss()` et `accuracy_score()` de scikit-learn avec gestion NaN
     * **LOGGING DÉTAILLÉ** : `self.logger.info()`, `self.logger.warning()`, `self.logger.error()` pour traçabilité
   - MÉTHODES PRINCIPALES :
     * __init__(sequence_length, min_target_hand_index, hybrid_feature_creator, ...) - Validation `isinstance()` et `ValueError` si invalide
     * _generate_filtered_data_for_shoe(shoe_pb_sequence, game_index_offset) - Boucle `for i in range(1, game_len):` avec filtrage
     * prepare_data_for_model(list_of_pb_sequences) - Conversion `np.array(dtype=np.float64)` et `np.stack().astype(np.float32)`
     * evaluate_performance(y_true, y_pred_proba) - Calculs `log_loss(eps=1e-15)` et `accuracy_score()` avec gestion erreurs
   - ARCHITECTURE :
     * **FENÊTRE ADAPTATIVE** : `input_sequence = shoe_pb_sequence[:i]` utilise toute séquence disponible
     * **SYSTÈME ZERO-BASED** : `label = 1 if actual_outcome == 'banker' else 0` selon standard PyTorch
     * **ROBUSTESSE** : `try/except` avec `exc_info=True` et retours `None` ou `np.nan` si erreurs
     * **PERFORMANCE** : `np.array(dtype=np.float64)` pour LGBM, `np.float32` pour LSTM, logging conditionnel
     * **FLEXIBILITÉ** : Support `Callable[[List[str]], Tuple[Optional[List[float]], Optional[np.ndarray]]]` pour générateurs
   - UTILITÉ : Classe centrale pour gestion séquences Baccarat ML. Essentielle pour préparation données modèles. Critique pour garantie qualité et robustesse données d'entraînement.

