# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 649 à 746
# Type: Méthode de la classe ModelProvider

    def get_hbp_instance(cls, trial_id=None, config=None):
        """
        Implémentation du pattern Singleton pour HybridBaccaratPredictor.
        Retourne l'instance existante ou en crée une nouvelle si nécessaire.
        Désactive l'auto-update pour garantir la stabilité pendant l'optimisation.
        Initialise également un collecteur de statistiques pour réduire les logs excessifs.

        Args:
            trial_id: Identifiant de l'essai Optuna (optionnel)
            config: Configuration à utiliser pour l'instance HBP (optionnel)

        Returns:
            HybridBaccaratPredictor: L'instance unique de HybridBaccaratPredictor
        """
        # Vérifier si l'instance existe déjà
        if cls._hbp_instance is not None:
            # Si une configuration est fournie et différente de celle actuelle, mettre à jour la configuration
            if config is not None and hasattr(cls._hbp_instance, 'config') and cls._hbp_instance.config != config:
                logger.info("Mise à jour de la configuration de l'instance HBP existante")
                # Mettre à jour uniquement les attributs qui existent dans les deux configurations
                for attr_name in dir(config):
                    if not attr_name.startswith('_') and hasattr(cls._hbp_instance.config, attr_name):
                        setattr(cls._hbp_instance.config, attr_name, getattr(config, attr_name))

            # Retourner l'instance existante
            return cls._hbp_instance

        # Utiliser un verrou pour éviter les problèmes de concurrence
        with cls._hbp_instance_lock:
            # Vérifier à nouveau si l'instance a été créée pendant l'acquisition du verrou
            if cls._hbp_instance is not None:
                return cls._hbp_instance

            # Importer HybridBaccaratPredictor uniquement lorsque cette fonction est appelée
            from hbp import HybridBaccaratPredictor

            # Créer une instance avec la configuration fournie ou une configuration minimale
            if config is None:
                from config import PredictorConfig
                config = PredictorConfig()

            # Créer l'instance HBP avec la configuration
            temp_hbp = HybridBaccaratPredictor(config)
            logger.info(f"Instance HBP créée avec configuration {'fournie' if config is not None else 'par défaut'}")

            # Désactiver l'auto-update pour garantir la stabilité pendant l'optimisation
            if hasattr(temp_hbp, 'auto_update_enabled') and temp_hbp.auto_update_enabled is not None:
                temp_hbp.auto_update_enabled.set(False)
                logger.info("Auto-update désactivé pour l'instance HBP utilisée pendant l'optimisation")

            # Initialiser le collecteur de statistiques si disponible
            try:
                from optuna_optimizer import OptimizationStatsCollector
                stats_collector = OptimizationStatsCollector(trial_id=trial_id)
                temp_hbp.optimization_stats_collector = stats_collector
                logger.info(f"Collecteur de statistiques initialisé pour l'essai {trial_id}")
            except ImportError:
                logger.warning("OptimizationStatsCollector non disponible, collecteur de statistiques non initialisé")

            # Définir le flag pour indiquer que nous sommes en phase d'optimisation Optuna
            temp_hbp.is_optuna_running = True
            logger.info("Flag is_optuna_running défini à True pour l'instance HBP")

            # Initialiser la séquence avec une liste vide pour éviter les erreurs
            if not hasattr(temp_hbp, 'sequence') or temp_hbp.sequence is None:
                temp_hbp.sequence = []
                logger.info("Séquence initialisée avec une liste vide pour éviter les erreurs")

            # Initialiser le modèle Markov si nécessaire avec les paramètres corrects
            if hasattr(temp_hbp, 'markov') and temp_hbp.markov is None:
                from models import PersistentMarkov
                # Récupérer les paramètres de configuration pour Markov
                markov_max_order = temp_hbp.config.max_markov_order

                # Garantir une valeur minimale de 1.0 et maximale de 8.0
                markov_max_order = max(1.0, min(8.0, markov_max_order))

                markov_smoothing = temp_hbp.config.markov_smoothing

                # Initialiser le modèle Markov avec les paramètres corrects
                try:
                    # Utiliser directement la valeur flottante de max_order
                    temp_hbp.markov = PersistentMarkov(max_order=markov_max_order, smoothing=markov_smoothing)
                    logger.info(f"Modèle Markov initialisé pour l'instance HBP avec max_order={markov_max_order}, smoothing={markov_smoothing}")
                except Exception as e:
                    logger.error(f"Erreur lors de l'initialisation du modèle Markov: {e}")
                    # Laisser le modèle Markov à None pour qu'il soit exclu des prédictions
                    temp_hbp.markov = None
                    # Désactiver l'utilisation du modèle Markov dans la configuration
                    if hasattr(temp_hbp.config, 'use_markov_model'):
                        temp_hbp.config.use_markov_model = False
                        logger.warning("Modèle Markov désactivé (use_markov_model=False) en raison d'une erreur d'initialisation")

            # Stocker l'instance dans la variable de classe
            cls._hbp_instance = temp_hbp
            logger.info("Instance HBP stockée comme singleton")

            return cls._hbp_instance