# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1388 à 1430
# Type: Méthode de la classe ThreadedTrainer

    def start(self, X_lgbm, y_lgbm, X_lstm, y_lstm, config_override=None):
        """
        Démarre l'entraînement dans un thread séparé.

        Args:
            X_lgbm: Features pour LGBM
            y_lgbm: Labels pour LGBM
            X_lstm: Features pour LSTM
            y_lstm: Labels pour LSTM
            config_override: Configuration à utiliser pour l'entraînement (optionnel)

        Returns:
            bool: True si l'entraînement a démarré, False sinon
        """
        if self.is_running:
            logger.warning("L'entraînement est déjà en cours d'exécution")
            return False

        # Réinitialiser les événements et les résultats
        self.stop_event.clear()
        self.result = None
        self.error = None
        self.start_time = time.time()

        # Vérifier que l'instance d'entraînement est valide
        if not hasattr(self.trainer_instance, '_train_models_async'):
            logger.error("L'instance d'entraînement ne possède pas la méthode '_train_models_async'")
            if self.error_callback:
                self.error_callback("L'instance d'entraînement ne possède pas la méthode '_train_models_async'")
            return False

        # Démarrer le thread d'entraînement
        self.thread = threading.Thread(
            target=self._run_training,
            args=(X_lgbm, y_lgbm, X_lstm, y_lstm, config_override),
            name="ThreadedTrainer",
            daemon=True
        )
        self.thread.start()
        self.is_running = True

        logger.info("Entraînement démarré dans un thread séparé")
        return True