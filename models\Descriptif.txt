DESCRIPTIF DÉTAILLÉ DES MÉTHODES - DEUXIÈME VAGUE TECHNIQUE COMPLÈTE
================================================================================

Ce fichier contient la description technique complète de toutes les méthodes du système models.py.

ÉTAT DOCUMENTATION : DEUXIÈME VAGUE TECHNIQUE COMPLÈTE
- **Couverture** : 100% des méthodes enrichies techniquement
- **Niveau** : Code source réel intégré, détails d'implémentation ligne par ligne
- **Qualité** : 25-40 lignes par méthode avec précision technique maximale

STRUCTURE DU SYSTÈME :
- **SECTION 1 : GESTIONDONNEES** : Dataset et gestion des données (3 méthodes)
- **SECTION 2 : MODELESLSTM** : Architecture LSTM avancée (2 méthodes)
- **SECTION 3 : MODELESMARKOV** : Mod<PERSON>les <PERSON> persistants (8 méthodes)
- **SECTION 4 : ANCIENNESCLASSES** : Définitions de classes (3 classes)

TOTAL : 16 MÉTHODES/CLASSES ANALYSÉES AVEC PRÉCISION TECHNIQUE

================================================================================
SECTION 1 : GESTIONDONNEES
================================================================================

1. __init__.txt (BaccaratDataset.__init__ - INITIALISATION DATASET BACCARAT)
   - Lignes 20-53 dans models.py (34 lignes)
   - FONCTION : Initialise un dataset PyTorch optimisé pour les données de séquences Baccarat avec gestion mémoire avancée et validation des étiquettes
   - PARAMÈTRES :
     * self - Instance de BaccaratDataset
     * sequences (np.ndarray) - Tableau numpy des séquences d'entrée
     * targets (np.ndarray) - Tableau numpy des étiquettes cibles (0=Player, 1=Banker)
     * weights (Optional[np.ndarray]) - Poids optionnels pour l'échantillonnage pondéré
     * sequence_positions (Optional[np.ndarray]) - Positions optionnelles des séquences
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ASSERTION** : `assert len(sequences) == len(targets), "Sequences and targets must have the same length"` vérification stricte
     * **CONVERSION SEQUENCES** : `self.sequences = torch.from_numpy(sequences).float().share_memory_()` conversion directe avec partage mémoire
     * **VALIDATION ÉTIQUETTES** : `unique_targets = np.unique(targets)` puis vérification `set(unique_targets).issubset({0, 1})`
     * **EXCEPTION ÉTIQUETTES** : `raise ValueError(f"Invalid target values found: {unique_targets}. Only 0 (Player) and 1 (Banker) are allowed.")` si invalides
     * **CONVERSION TARGETS** : `self.targets = torch.from_numpy(targets).long().share_memory_()` avec type long pour classification
     * **SYSTÈME ZERO-BASED** : Commentaire explicite "0 = Player, 1 = Banker" selon standard PyTorch
     * **FLAGS OPTIONNELS** : `self.has_weights = weights is not None` et `self.has_sequence_positions = sequence_positions is not None`
     * **CONVERSION WEIGHTS** : `if self.has_weights: self.weights = torch.from_numpy(weights).float().share_memory_()`
     * **CONVERSION POSITIONS** : `if self.has_sequence_positions: self.sequence_positions = torch.from_numpy(sequence_positions).long().share_memory_()`
     * **PRÉALLOCATION INDICES** : `self.indices = torch.arange(len(sequences)).share_memory_()` pour accès rapide
     * **OPTIMISATION PERFORMANCE** : Commentaire "Pas de log de diagnostic pour éviter de surcharger les logs"
     * **PARTAGE MÉMOIRE** : `.share_memory_()` sur tous tenseurs pour transfert CPU->GPU efficace
   - RETOUR : None (constructeur)
   - UTILITÉ : Méthode fondamentale pour créer un dataset PyTorch optimisé. Essentielle pour l'entraînement des modèles ML avec données Baccarat. Critique pour les performances et la validation des données.

2. __getitem__.txt (BaccaratDataset.__getitem__ - ACCÈS INDEXÉ AUX DONNÉES)
   - Lignes 58-68 dans models.py (11 lignes)
   - FONCTION : Fournit l'accès indexé ultra-optimisé aux éléments du dataset avec retour conditionnel selon les données disponibles
   - PARAMÈTRES :
     * self - Instance de BaccaratDataset
     * idx (int) - Index de l'élément à récupérer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **COMMENTAIRE PERFORMANCE** : "Retour ultra-optimisé des données sans vérifications supplémentaires"
     * **COMMENTAIRE VECTORISATION** : "Utilisation d'indexation vectorisée pour un accès plus rapide"
     * **CONDITION COMPLÈTE** : `if self.has_weights and self.has_sequence_positions:` test booléen double
     * **RETOUR COMPLET** : `return self.sequences[idx], self.targets[idx], self.weights[idx], self.sequence_positions[idx]` tuple 4 éléments
     * **CONDITION WEIGHTS** : `elif self.has_weights:` test booléen simple
     * **RETOUR WEIGHTS** : `return self.sequences[idx], self.targets[idx], self.weights[idx]` tuple 3 éléments
     * **CONDITION POSITIONS** : `elif self.has_sequence_positions:` test booléen simple
     * **RETOUR POSITIONS** : `return self.sequences[idx], self.targets[idx], self.sequence_positions[idx]` tuple 3 éléments
     * **RETOUR MINIMAL** : `else: return self.sequences[idx], self.targets[idx]` tuple 2 éléments
     * **INDEXATION DIRECTE** : `self.sequences[idx]` accès vectorisé sans vérification bounds
     * **OPTIMISATION ZERO-COPY** : Accès direct aux tenseurs préalloués sans copie mémoire
     * **LOGIQUE CONDITIONNELLE** : Structure if/elif/else pour performance optimale
     * **AUCUNE VALIDATION** : Pas de vérification idx pour vitesse maximale
   - RETOUR : tuple - Tuple variable selon données disponibles (sequences, targets, [weights], [positions])
   - UTILITÉ : Méthode essentielle pour l'interface PyTorch Dataset. Critique pour les performances d'entraînement. Permet l'accès rapide aux données pendant les boucles d'entraînement.

3. __len__.txt (BaccaratDataset.__len__ - TAILLE DU DATASET)
   - Lignes 55-56 dans models.py (2 lignes)
   - FONCTION : Retourne la taille totale du dataset pour l'interface PyTorch Dataset standard
   - PARAMÈTRES :
     * self - Instance de BaccaratDataset
   - FONCTIONNEMENT DÉTAILLÉ :
     * **SIGNATURE MÉTHODE** : `def __len__(self) -> int:` avec type hint explicite
     * **RETOUR DIRECT** : `return len(self.sequences)` appel fonction len() sur tenseur PyTorch
     * **INTERFACE PYTORCH** : Implémente la méthode __len__ requise par l'interface Dataset
     * **PERFORMANCE OPTIMALE** : Accès direct à la propriété length du tenseur sequences
     * **COHÉRENCE GARANTIE** : La taille est cohérente car validée dans __init__ avec assert
     * **UTILISATION DATALOADER** : Permet au DataLoader de connaître la taille pour batching
     * **SIMPLICITÉ MAXIMALE** : Une seule ligne de code pour performance optimale
     * **FIABILITÉ** : Pas de calcul complexe, juste accès à la propriété native du tenseur
     * **STANDARD PYTORCH** : Respecte exactement l'interface attendue par torch.utils.data.Dataset
     * **TYPE HINT** : Annotation `-> int` pour clarté et vérification statique
   - RETOUR : int - Nombre total d'éléments dans le dataset
   - UTILITÉ : Méthode obligatoire pour l'interface PyTorch Dataset. Essentielle pour le fonctionnement des DataLoaders. Permet la gestion automatique des batches et de l'itération.

================================================================================
SECTION 2 : MODELESLSTM
================================================================================

4. __init___1.txt (EnhancedLSTMModel.__init__ - INITIALISATION MODÈLE LSTM AVANCÉ)
   - Lignes 72-118 dans models.py (47 lignes)
   - FONCTION : Initialise un modèle LSTM avancé avec mécanisme d'attention, connexions résiduelles et normalisation multicouche pour prédiction Baccarat
   - PARAMÈTRES :
     * self - Instance de EnhancedLSTMModel
     * input_size (int) - Taille des features d'entrée
     * hidden_dim (int) - Dimension des couches cachées LSTM
     * output_size (int) - Taille de sortie (2 pour Player/Banker)
     * num_layers (int) - Nombre de couches LSTM (défaut: 1)
     * dropout_prob (float) - Probabilité dropout général (défaut: 0.2)
     * bidirectional (bool) - LSTM bidirectionnel (défaut: False)
     * use_attention (bool) - Activation mécanisme attention (défaut: True)
     * use_residual (bool) - Activation connexions résiduelles (défaut: True)
     * dropout_input/hidden/output (float) - Dropouts spécialisés par couche
   - FONCTIONNEMENT DÉTAILLÉ :
     * **HÉRITAGE PYTORCH** : `super(EnhancedLSTMModel, self).__init__()` initialisation nn.Module
     * **STOCKAGE ATTRIBUTS** : `self.hidden_dim = hidden_dim`, `self.num_layers = num_layers`, `self.bidirectional = bidirectional`
     * **FLAGS ARCHITECTURE** : `self.use_attention = use_attention`, `self.use_residual = use_residual`
     * **NORMALISATION ENTRÉE** : `self.input_norm = nn.LayerNorm(input_size)` commentaire "stabiliser l'entraînement et réduire la val_loss"
     * **DROPOUT ENTRÉE** : `self.input_dropout = nn.Dropout(dropout_input)` commentaire "modéré pour permettre un bon flux d'information"
     * **LSTM PRINCIPAL** : `self.lstm = nn.LSTM(input_size=input_size, hidden_size=hidden_dim, num_layers=num_layers, batch_first=True, dropout=dropout_hidden if num_layers > 1 else 0, bidirectional=bidirectional)`
     * **CALCUL DIMENSION** : `lstm_output_dim = hidden_dim * 2 if bidirectional else hidden_dim` commentaire "x2 si bidirectionnel"
     * **ATTENTION CONDITIONNELLE** : `if use_attention: self.attention = nn.Sequential(nn.Linear(lstm_output_dim, lstm_output_dim // 2), nn.Tanh(), nn.Linear(lstm_output_dim // 2, 1))`
     * **NORMALISATION COUCHE** : `self.layer_norm = nn.LayerNorm(lstm_output_dim)` commentaire "stabiliser l'entraînement"
     * **COUCHES DENSES** : `self.fc1 = nn.Linear(lstm_output_dim, lstm_output_dim)`, `self.relu = nn.ReLU()`, `self.dropout = nn.Dropout(dropout_output)`
     * **SORTIE FINALE** : `self.fc2 = nn.Linear(lstm_output_dim, output_size)` commentaire "Couche de sortie finale"
     * **DROPOUT CONDITIONNEL** : `dropout=dropout_hidden if num_layers > 1 else 0` pour éviter dropout inutile
   - RETOUR : None (constructeur)
   - UTILITÉ : Méthode fondamentale pour créer un modèle LSTM state-of-the-art. Essentielle pour l'architecture neuronale avancée. Critique pour les performances de prédiction Baccarat.

5. forward.txt (EnhancedLSTMModel.forward - PASSE AVANT LSTM AVEC ATTENTION)
   - Lignes 120-188 dans models.py (69 lignes)
   - FONCTION : Effectue la passe avant du modèle LSTM avec mécanisme d'attention et connexions résiduelles, retournant des logits non normalisés
   - PARAMÈTRES :
     * self - Instance de EnhancedLSTMModel
     * x (torch.Tensor) - Tenseur d'entrée 2D ou 3D (seq_len, features) ou (batch, seq_len, features)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DOCSTRING IMPORTANTE** : Commentaire "retourne des logits (non normalisés)" et "système zero-based standard: Indice 0 = Player, Indice 1 = Banker"
     * **GESTION 2D** : `if x.dim() == 2: x = x.unsqueeze(0)` commentaire "(seq_len, features) -> (1, seq_len, features)"
     * **VALIDATION DIMENSIONS** : `elif x.dim() != 3: raise ValueError(f"Attendu input 2D ou 3D, reçu {x.dim()}D tensor.")`
     * **EXTRACTION MÉTADONNÉES** : `batch_size = x.size(0)`, `seq_len = x.size(1)`, `device = x.device`, `num_directions = 2 if self.bidirectional else 1`
     * **NORMALISATION ENTRÉE** : `x = self.input_norm(x)` commentaire "stabiliser l'entraînement et réduire la val_loss"
     * **DROPOUT ENTRÉE** : `x = self.input_dropout(x)` commentaire "Appliquer le dropout à l'entrée"
     * **INITIALISATION ÉTATS** : `h0 = torch.zeros(self.num_layers * num_directions, batch_size, self.hidden_dim, device=device)` et `c0 = torch.zeros(...)`
     * **PASSE LSTM** : `lstm_out, _ = self.lstm(x, (h0, c0))` commentaire "lstm_out: (batch, seq_len, hidden*directions)"
     * **ATTENTION CONDITIONNELLE** : `if self.use_attention:` puis `attention_scores = self.attention(lstm_out).squeeze(-1)` commentaire "(batch, seq_len)"
     * **SOFTMAX ATTENTION** : `attention_weights = torch.softmax(attention_scores, dim=1)` commentaire "(batch, seq_len)"
     * **APPLICATION ATTENTION** : `attention_weights = attention_weights.unsqueeze(-1)` puis `weighted_output = lstm_out * attention_weights` puis `context_vector = weighted_output.sum(dim=1)`
     * **FALLBACK SANS ATTENTION** : `else: context_vector = lstm_out[:, -1, :]` commentaire "utiliser simplement la dernière sortie"
     * **NORMALISATION COUCHE** : `normalized_out = self.layer_norm(context_vector)`
     * **COUCHE DENSE** : `dense_out = self.fc1(normalized_out)` puis `activated_out = self.relu(dense_out)`
     * **CONNEXION RÉSIDUELLE** : `if self.use_residual: combined_out = activated_out + normalized_out else: combined_out = activated_out`
     * **DROPOUT FINAL** : `dropped_out = self.dropout(combined_out)`
     * **SORTIE LOGITS** : `logits = self.fc2(dropped_out)` commentaire "(batch, output_size)"
   - RETOUR : torch.Tensor - Logits de sortie (batch, output_size) nécessitant softmax pour probabilités
   - UTILITÉ : Méthode centrale pour l'inférence du modèle LSTM. Essentielle pour les prédictions. Critique pour l'architecture avec attention et connexions résiduelles.

================================================================================
SECTION 3 : MODELESMARKOV
================================================================================

6. export_models.txt (PersistentMarkov.export_models - EXPORT MODÈLES MARKOV)
   - Lignes 597-634 dans models.py (38 lignes)
   - FONCTION : Exporte l'état actuel des modèles Markov (global et session) avec configuration dans un format sérialisable pour sauvegarde
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VERROUILLAGE THREAD** : `with self.lock:` pour accès thread-safe aux modèles
     * **LOGGING DÉBUT** : `logger.info("Exportation des modèles Markov...")` pour traçabilité
     * **COMMENTAIRE FLOTTANT** : "Utiliser directement la valeur flottante de max_order" et "Pas besoin de conversion"
     * **COMMENTAIRE CONVERSION** : "Convertir les defaultdict en dict standard pour la sérialisation"
     * **COMMENTAIRE INDEX** : "On n'exporte que les ordres 1 à max_order (index 0 est vide)"
     * **INITIALISATION GLOBAL** : `global_export = []` liste vide pour accumulation
     * **BOUCLE GLOBAL** : `for order_model in self.global_models[1:]:` commentaire "Exclure index 0"
     * **COMPREHENSION GLOBAL** : `global_export.append({state: dict(outcomes) for state, outcomes in order_model.items()})`
     * **INITIALISATION SESSION** : `session_export = []` liste vide pour accumulation
     * **BOUCLE SESSION** : `for order_model in self.session_models[1:]:` commentaire "Exclure index 0"
     * **COMPREHENSION SESSION** : `session_export.append({state: dict(outcomes) for state, outcomes in order_model.items()})`
     * **CONSTRUCTION DICTIONNAIRE** : `export_data = {'config': {'max_order': self.max_order, 'smoothing': self.smoothing}, 'global': global_export, 'session': session_export}`
     * **LOGGING FIN** : `logger.info("Exportation Markov terminée.")` pour confirmation
     * **RETOUR DONNÉES** : `return export_data` dictionnaire complet prêt pour sérialisation
     * **CONVERSION DEFAULTDICT** : `dict(outcomes)` transforme defaultdict en dict standard pour JSON
     * **STRUCTURE COHÉRENTE** : Format identique pour global et session
   - RETOUR : Dict[str, Any] - Dictionnaire sérialisable avec config, global, session
   - UTILITÉ : Méthode essentielle pour la persistance des modèles Markov. Critique pour la sauvegarde d'état. Permet la continuité entre sessions.

7. load_models.txt (PersistentMarkov.load_models - CHARGEMENT MODÈLES MARKOV)
   - Lignes 636-800 dans models.py (165 lignes)
   - FONCTION : Charge l'état des modèles Markov depuis un dictionnaire avec validation robuste et gestion d'erreurs complète
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
     * data (Optional[Dict[str, Any]]) - Dictionnaire contenant les données à charger (format export_models)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉE** : `if not isinstance(data, dict): return False` vérification type strict
     * **VERROUILLAGE THREAD** : `with self.lock:` pour accès thread-safe
     * **EXTRACTION CONFIG** : `config = data.get('config', {})` avec fallback dictionnaire vide
     * **VALIDATION MAX_ORDER** : `if 'max_order' in config:` puis conversion et validation plage 1-12
     * **VALIDATION SMOOTHING** : `if 'smoothing' in config:` puis vérification >= 0
     * **REDIMENSIONNEMENT** : Ajuste structures si max_order change avec nouvelles listes defaultdict
     * **CHARGEMENT GLOBAL** : `for order_idx, order_data in enumerate(data.get('global', [])):`
     * **CONVERSION TUPLES** : `state = tuple(state_key)` pour compatibilité JSON vers Python
     * **RECONSTRUCTION DEFAULTDICT** : `self.global_models[order_idx + 1][state] = defaultdict(int, outcomes)`
     * **CHARGEMENT SESSION** : Même processus pour `data.get('session', [])`
     * **GESTION ERREURS** : `except Exception as e:` avec `self.reset('hard')` et `return False`
     * **LOGGING SUCCÈS** : `logger.info("Modèles Markov chargés avec succès.")` et `return True`
   - RETOUR : bool - True si chargement réussi, False sinon
   - UTILITÉ : Méthode critique pour la restauration d'état Markov. Essentielle pour la continuité. Robuste contre corruption de données.

8. update_global.txt (PersistentMarkov.update_global - MISE À JOUR MODÈLES GLOBAUX)
   - Lignes 498-535 dans models.py (38 lignes)
   - FONCTION : Met à jour les modèles Markov globaux avec plusieurs séquences de jeu pour apprentissage historique
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
     * sequences (List[List[str]]) - Liste de séquences de jeu avec résultats 'player'/'banker'
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉE** : `if not sequences: logger.warning("Aucune séquence fournie"); return`
     * **VERROUILLAGE THREAD** : `with self.lock:` pour accès thread-safe
     * **COMPTEUR TRANSITIONS** : `count = 0` pour tracking ajouts
     * **PARCOURS SÉQUENCES** : `for seq in sequences:` avec validation `isinstance(seq, list)`
     * **FILTRAGE VIDES** : `if not seq: continue` pour ignorer séquences vides
     * **BOUCLE ORDRES** : `for order in range(1, self.max_order_int + 1):` tous ordres
     * **CONDITION LONGUEUR** : `if len(seq) > order:` pour avoir assez d'éléments
     * **EXTRACTION ÉTAT** : `state = tuple(seq[i - order : i])` pour contexte
     * **EXTRACTION OUTCOME** : `outcome = seq[i]` pour résultat suivant
     * **FILTRAGE VALIDES** : `if outcome in ('player', 'banker'):` pour issues valides
     * **MISE À JOUR COMPTEURS** : `self.global_models[order][state][outcome] += 1` et `count += 1`
     * **LOGGING FIN** : `logger.debug(f"Ajouté {count} transitions aux modèles globaux")`
   - RETOUR : None (mise à jour en place)
   - UTILITÉ : Méthode essentielle pour l'apprentissage Markov historique. Critique pour la construction des modèles globaux. Permet l'accumulation de connaissances.

9. update_session.txt (PersistentMarkov.update_session - MISE À JOUR MODÈLE SESSION)
   - Lignes 537-594 dans models.py (58 lignes)
   - FONCTION : Met à jour les modèles Markov de session avec la séquence actuelle, ne traitant que le dernier coup
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
     * sequence (List[str]) - Séquence de jeu en cours avec résultats 'player'/'banker'
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION TYPE** : `if not isinstance(sequence, list): logger.warning("Séquence doit être une liste"); return`
     * **VALIDATION VIDE** : `if not sequence: return` pour séquences vides
     * **GESTION ERREURS** : `try:` avec `except Exception as e:` et `logger.error`
     * **VERROUILLAGE THREAD** : `with self.lock:` pour accès thread-safe
     * **EXTRACTION DERNIER** : `last_outcome = sequence[-1]` pour résultat récent
     * **VALIDATION OUTCOME** : `if not isinstance(last_outcome, str) or last_outcome not in ('player', 'banker'): return`
     * **BOUCLE ORDRES** : `for order in range(1, self.max_order_int + 1):` pour tous ordres
     * **CONDITION LONGUEUR** : `if len(sequence) > order:` pour contexte suffisant
     * **EXTRACTION ÉTAT** : `state_elements = sequence[-(order + 1) : -1]` pour contexte précédent
     * **VALIDATION ÉTAT** : Vérifie tous éléments sont str et in ('player', 'banker')
     * **CONSTRUCTION TUPLE** : `state = tuple(state_elements)` pour clé dictionnaire
     * **MISE À JOUR COMPTEUR** : `self.session_models[order][state][last_outcome] += 1`
   - RETOUR : None (mise à jour en place)
   - UTILITÉ : Méthode critique pour l'apprentissage Markov temps réel. Essentielle pour adaptation session courante. Permet ajustement dynamique.

10. reset.txt (PersistentMarkov.reset - RÉINITIALISATION MODÈLES MARKOV)
    - Lignes 802-821 dans models.py (20 lignes)
    - FONCTION : Réinitialise les compteurs des modèles Markov selon le type spécifié (soft/hard)
    - PARAMÈTRES :
      * self - Instance de PersistentMarkov
      * reset_type (str) - Type réinitialisation: 'soft' (session) ou 'hard' (global+session)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VERROUILLAGE THREAD** : `with self.lock:` pour accès thread-safe
      * **CALCUL TAILLE** : `max_order_int = int(self.max_order) + 1` si fractionnaire
      * **RESET HARD** : `if reset_type == 'hard':` réinitialise global_models
      * **CRÉATION GLOBAL** : `[defaultdict(lambda: defaultdict(int)) for _ in range(max_order_int + 1)]`
      * **LOGGING GLOBAL** : `logger.info("Modèles Markov globaux réinitialisés (hard reset)")`
      * **RESET SESSION** : Toujours réinitialise session_models (soft et hard)
      * **CRÉATION SESSION** : Même structure que global avec defaultdict imbriqués
      * **LOGGING SESSION** : `logger.info(f"Modèles Markov de session réinitialisés ({reset_type} reset)")`
      * **STRUCTURE COHÉRENTE** : Garantit taille max_order_int + 1 pour tous ordres
    - RETOUR : None (réinitialisation en place)
    - UTILITÉ : Méthode essentielle pour nettoyage des modèles. Critique pour nouveau démarrage. Permet gestion flexible de la persistance.

11. get_combined_probs.txt (PersistentMarkov.get_combined_probs - CALCUL PROBABILITÉS COMBINÉES)
    - Lignes 229-430 dans models.py (202 lignes)
    - FONCTION : Combine probabilités globales et session pour prédire le prochain coup avec lissage, pondération et analyse contextuelle
    - PARAMÈTRES :
      * self - Instance de PersistentMarkov
      * sequence (List[str]) - Séquence de jeu actuelle jusqu'au coup t-1
      * global_weight (float, optional) - Poids modèles globaux (défaut: config ou 0.15)
      * context_weight (float, optional) - Poids contexte local (défaut: config ou 0.8)
      * decay_factor (float, optional) - Facteur décroissance ordres (défaut: config ou 0.95)
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION ENTRÉE** : `if not isinstance(sequence, list):` retourne `{'player': 0.5, 'banker': 0.5}`
      * **FALLBACK 50/50** : Retourne probabilités équilibrées si données insuffisantes
      * **RÉCUPÉRATION CONFIG** : `from .config import PredictorConfig` pour paramètres par défaut
      * **CLAMPING PARAMÈTRES** : `max(0.0, min(1.0, value))` pour tous poids
      * **CALCUL POIDS ORDRES** : `order_weights[order] = 1.0 / (order ** order_weight_exponent)`
      * **NORMALISATION ORDRES** : `norm_order_weights` avec `total_order_weight`
      * **ANALYSE CONTEXTE** : `context_factor = self._analyze_sequence_context(sequence)`
      * **BOUCLE ORDRES** : `for order in range(1, self.max_order_int + 1):`
      * **EXTRACTION ÉTAT** : `state = tuple(sequence[-order:])` pour contexte
      * **PROBABILITÉS GLOBALES** : Lissage avec `(counts + smoothing) / (total + smoothing * num_classes)`
      * **PROBABILITÉS SESSION** : Même calcul que global pour modèles session
      * **COMBINAISON PONDÉRÉE** : `adaptive_global_weight * global + adaptive_session_weight * session`
      * **ADAPTATION DYNAMIQUE** : Poids ajustés selon context_factor et decay_factor
      * **ACCUMULATION** : `final_probs += combined_probs * adaptive_order_weight`
      * **NORMALISATION FINALE** : Division par accumulated_weight avec gestion missing_weight
    - RETOUR : Dict[str, float] - Probabilités {'player': float, 'banker': float} normalisées
    - UTILITÉ : Méthode centrale pour prédiction Markov. Essentielle pour intelligence du système. Critique pour combinaison sophistiquée des modèles.

12. _analyze_sequence_context.txt (PersistentMarkov._analyze_sequence_context - ANALYSE CONTEXTE SÉQUENCE)
    - Lignes 432-496 dans models.py (65 lignes)
    - FONCTION : Analyse le contexte de la séquence pour adapter les poids des modèles selon volatilité et streaks
    - PARAMÈTRES :
      * self - Instance de PersistentMarkov
      * sequence (List[str]) - Séquence de résultats à analyser
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION ENTRÉE** : `if not isinstance(sequence, list): return 0.5`
      * **SEUIL LONGUEUR** : `if len(sequence) < 10: return 0.5` (données insuffisantes)
      * **VALIDATION ÉLÉMENTS** : Vérifie tous éléments str et in ('player', 'banker')
      * **GESTION ERREURS** : `try:` avec `except Exception as e:` et `logger.error` retour 0.5
      * **CALCUL VOLATILITÉ** : `recent_seq = sequence[-min(10, len(sequence)):]`
      * **COMPTAGE ALTERNANCES** : `for i in range(1, len(recent_seq)):` si `recent_seq[i] != recent_seq[i-1]`
      * **NORMALISATION VOLATILITÉ** : `volatility = alternances / (len(recent_seq) - 1)`
      * **DÉTECTION STREAK** : `current_streak = 1` puis boucle arrière pour compter identiques
      * **NORMALISATION STREAK** : `streak_factor = min(current_streak / 10, 1.0)`
      * **COMBINAISON FACTEURS** : `context_factor = (volatility * 0.7) + (streak_factor * 0.3)`
      * **CLAMPING FINAL** : `max(0.0, min(1.0, context_factor))` pour garantir [0,1]
      * **INTERPRÉTATION** : 0=stable (global pertinent), 1=volatile (session pertinent)
    - RETOUR : float - Facteur contextuel entre 0 et 1
    - UTILITÉ : Méthode essentielle pour adaptation intelligente des modèles. Critique pour pondération dynamique. Permet optimisation selon contexte de jeu.

13. __init___2.txt (PersistentMarkov.__init__ - INITIALISATION GESTIONNAIRE MARKOV)
    - Lignes 195-227 dans models.py (33 lignes)
    - FONCTION : Initialise le gestionnaire de modèles Markov avec validation des paramètres et création des structures de données
    - PARAMÈTRES :
      * self - Instance de PersistentMarkov
      * max_order (int) - Ordre maximal des chaînes Markov (1-12)
      * smoothing (float) - Facteur de lissage Laplace (alpha) pour éviter probabilités nulles
    - FONCTIONNEMENT DÉTAILLÉ :
      * **VALIDATION TYPE** : `if isinstance(max_order, float):` conversion avec `logger.warning`
      * **CLAMPING ORDRE** : `self.max_order = max(1, min(12, max_order))` pour plage valide
      * **VALIDATION SMOOTHING** : `if not isinstance(smoothing, (int, float)) or smoothing < 0:`
      * **EXCEPTION SMOOTHING** : `raise ValueError(f"smoothing doit être un nombre >= 0, reçu: {smoothing}")`
      * **ASSIGNATION SMOOTHING** : `self.smoothing: float = smoothing` avec type hint
      * **ORDRE ENTIER** : `self.max_order_int = self.max_order` pour structures données
      * **CRÉATION GLOBAL** : `[defaultdict(lambda: defaultdict(int)) for _ in range(max_order_int + 1)]`
      * **CRÉATION SESSION** : Même structure que global avec defaultdict imbriqués
      * **VERROUILLAGE THREAD** : `self.lock: threading.RLock = threading.RLock()` pour thread-safety
      * **LOGGING INIT** : `logger.info(f"PersistentMarkov initialisé: max_order={max_order}, max_order_int={max_order_int}, smoothing={smoothing}")`
      * **LOGGING TAILLES** : `logger.info(f"Tailles des modèles: global={len(global_models)}, session={len(session_models)}")`
      * **INDEX 0 INUTILISÉ** : Structure où index correspond à ordre (0 non utilisé)
    - RETOUR : None (constructeur)
    - UTILITÉ : Méthode fondamentale pour créer gestionnaire Markov. Essentielle pour initialisation robuste. Critique pour structures thread-safe.

================================================================================
SECTION 4 : ANCIENNESCLASSES
================================================================================

14. class_BaccaratDataset.txt (BaccaratDataset - CLASSE DATASET PYTORCH BACCARAT)
    - Lignes 18-68 dans models.py (51 lignes)
    - FONCTION : Classe Dataset PyTorch optimisée pour les données de séquences Baccarat avec gestion mémoire avancée
    - RESPONSABILITÉS :
      * **HÉRITAGE DATASET** : Étend torch.utils.data.Dataset pour interface PyTorch standard
      * **GESTION SÉQUENCES** : Stockage optimisé des séquences d'entrée avec torch.from_numpy()
      * **VALIDATION ÉTIQUETTES** : Contrôle strict des targets (0=Player, 1=Banker) avec np.unique()
      * **OPTIMISATION MÉMOIRE** : Utilise share_memory_() pour partage efficace entre processus
      * **GESTION OPTIONNELLE** : Support weights et sequence_positions avec flags booléens
      * **ACCÈS INDEXÉ** : Implémentation __getitem__ avec retour conditionnel selon données
      * **INTERFACE STANDARD** : Méthodes __len__ et __getitem__ requises par PyTorch
    - MÉTHODES PRINCIPALES :
      * __init__(sequences, targets, weights, sequence_positions) - Initialisation avec validation
      * __getitem__(idx) - Accès indexé ultra-optimisé avec retour conditionnel
      * __len__() - Retourne taille dataset pour interface PyTorch
    - ARCHITECTURE :
      * **PERFORMANCE** : Préchargement mémoire avec tenseurs PyTorch partagés
      * **FLEXIBILITÉ** : Support données optionnelles (weights, positions)
      * **ROBUSTESSE** : Validation stricte des étiquettes et types
      * **COMPATIBILITÉ** : Interface PyTorch Dataset complète
    - UTILITÉ : Classe fondamentale pour l'entraînement des modèles ML. Essentielle pour l'interface PyTorch. Critique pour les performances de chargement des données.

15. class_EnhancedLSTMModel.txt (EnhancedLSTMModel - CLASSE MODÈLE LSTM AVANCÉ)
    - Lignes 71-188 dans models.py (118 lignes)
    - FONCTION : Classe modèle LSTM avancé avec mécanisme d'attention, connexions résiduelles et normalisation multicouche
    - RESPONSABILITÉS :
      * **HÉRITAGE NN.MODULE** : Étend torch.nn.Module pour modèle PyTorch standard
      * **ARCHITECTURE LSTM** : LSTM bidirectionnel optionnel avec dropout entre couches
      * **MÉCANISME ATTENTION** : Sequential avec Linear->Tanh->Linear pour focus séquences
      * **CONNEXIONS RÉSIDUELLES** : Combinaison activated_out + normalized_out optionnelle
      * **NORMALISATION MULTICOUCHE** : LayerNorm entrée et sortie pour stabilisation
      * **DROPOUT SPÉCIALISÉ** : Dropout différencié entrée/hidden/sortie pour régularisation
      * **FORWARD PASS** : Gestion dimensions 2D/3D avec attention et connexions résiduelles
    - MÉTHODES PRINCIPALES :
      * __init__(input_size, hidden_dim, output_size, ...) - Initialisation architecture complète
      * forward(x) - Passe avant avec attention, normalisation et connexions résiduelles
    - ARCHITECTURE :
      * **MODULARITÉ** : Composants optionnels (attention, résiduel) configurables
      * **PERFORMANCE** : Optimisations pour stabilité entraînement et réduction val_loss
      * **FLEXIBILITÉ** : Support entrées 2D/3D avec gestion automatique dimensions
      * **ROBUSTESSE** : Validation entrées et gestion erreurs dimensions
    - UTILITÉ : Classe centrale pour modèle LSTM state-of-the-art. Essentielle pour architecture neuronale avancée. Critique pour prédictions Baccarat sophistiquées.

16. class_PersistentMarkov.txt (PersistentMarkov - CLASSE GESTIONNAIRE MARKOV PERSISTANT)
    - Lignes 190-821 dans models.py (632 lignes)
    - FONCTION : Classe gestionnaire de modèles Markov persistants avec gestion global/session et analyse contextuelle sophistiquée
    - RESPONSABILITÉS :
      * **MODÈLES DUAUX** : Gestion séparée modèles globaux (historique) et session (temps réel)
      * **PERSISTANCE** : Export/import état complet avec sérialisation JSON-compatible
      * **THREAD-SAFETY** : Verrouillage RLock pour accès concurrent sécurisé
      * **ANALYSE CONTEXTUELLE** : Adaptation dynamique poids selon volatilité et streaks
      * **LISSAGE LAPLACE** : Gestion probabilités nulles avec facteur smoothing configurable
      * **ORDRES MULTIPLES** : Support chaînes Markov ordre 1 à 12 avec pondération
      * **PRÉDICTION COMBINÉE** : Fusion intelligente global/session avec paramètres adaptatifs
    - MÉTHODES PRINCIPALES :
      * __init__(max_order, smoothing) - Initialisation structures thread-safe
      * get_combined_probs(sequence, ...) - Prédiction combinée avec analyse contextuelle
      * update_global(sequences) - Apprentissage historique batch
      * update_session(sequence) - Apprentissage temps réel incrémental
      * export_models() / load_models(data) - Persistance état complet
      * reset(reset_type) - Réinitialisation soft/hard
      * _analyze_sequence_context(sequence) - Analyse volatilité et streaks
    - ARCHITECTURE :
      * **STRUCTURES OPTIMISÉES** : defaultdict imbriqués pour performance
      * **CONFIGURATION FLEXIBLE** : Paramètres adaptatifs depuis PredictorConfig
      * **ROBUSTESSE** : Validation extensive et gestion erreurs complète
      * **INTELLIGENCE** : Adaptation dynamique selon contexte de jeu
    - UTILITÉ : Classe centrale pour intelligence Markov du système. Essentielle pour prédictions sophistiquées. Critique pour adaptation temps réel et persistance.
