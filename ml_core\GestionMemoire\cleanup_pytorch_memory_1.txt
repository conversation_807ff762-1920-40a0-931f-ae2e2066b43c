# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1285 à 1293
# Type: Méthode
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

def cleanup_pytorch_memory():
    """
    Nettoie la mémoire PyTorch après utilisation.
    Cette fonction doit être appelée à la fin de chaque époque d'entraînement.

    Returns:
        bool: True si le nettoyage a réussi, False sinon
    """
    return MemoryManager.cleanup_pytorch_memory()