# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 7682 à 7914
# Type: Méthode de la classe HybridBaccaratPredictor

    def _show_lstm_learning_curves(self):
        """Affiche les courbes d'apprentissage LSTM dans l'onglet des graphiques."""
        # Effacer le contenu précédent
        for widget in self.plot_display_frame.winfo_children():
            widget.destroy()

        if not hasattr(self, 'lstm_metrics') or not self.lstm_metrics:
            ttk.Label(self.plot_display_frame, text="Aucune donnée de courbe d'apprentissage LSTM disponible").pack(pady=50)
            return

        # Vérifier si les données des courbes sont disponibles
        has_train_loss = 'train_losses' in self.lstm_metrics and self.lstm_metrics['train_losses']
        has_val_loss = 'val_losses' in self.lstm_metrics and self.lstm_metrics['val_losses']
        has_train_acc = 'train_accuracies' in self.lstm_metrics and self.lstm_metrics['train_accuracies']
        has_val_acc = 'val_accuracies' in self.lstm_metrics and self.lstm_metrics['val_accuracies']

        if not (has_train_loss or has_val_loss or has_train_acc or has_val_acc):
            ttk.Label(self.plot_display_frame, text="Aucune donnée de courbe d'apprentissage LSTM disponible").pack(pady=50)
            return

        # Créer un canvas pour afficher les courbes
        canvas = tk.Canvas(self.plot_display_frame, bg='white')
        canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Obtenir les dimensions du canvas
        canvas_width = canvas.winfo_width()
        canvas_height = canvas.winfo_height()

        # Si le canvas n'est pas encore rendu, utiliser des dimensions par défaut
        if canvas_width <= 1 or canvas_height <= 1:
            canvas_width = 600
            canvas_height = 400

        # Définir les marges
        margin_left = 50
        margin_right = 20
        margin_top = 20
        margin_bottom = 50

        # Calculer les dimensions du graphique
        plot_width = canvas_width - margin_left - margin_right
        plot_height = (canvas_height - margin_top - margin_bottom) // 2

        # Dessiner les axes pour les pertes
        canvas.create_line(
            margin_left, margin_top,
            margin_left, margin_top + plot_height,
            fill="black", width=2
        )
        canvas.create_line(
            margin_left, margin_top + plot_height,
            canvas_width - margin_right, margin_top + plot_height,
            fill="black", width=2
        )

        # Dessiner les étiquettes des axes pour les pertes
        canvas.create_text(
            canvas_width // 2,
            margin_top + plot_height + 20,
            text="Époque",
            fill="black"
        )
        canvas.create_text(
            margin_left // 2,
            margin_top + plot_height // 2,
            text="Perte",
            fill="black",
            angle=90
        )

        # Dessiner les axes pour les exactitudes
        canvas.create_line(
            margin_left, margin_top + plot_height + 40,
            margin_left, canvas_height - margin_bottom,
            fill="black", width=2
        )
        canvas.create_line(
            margin_left, canvas_height - margin_bottom,
            canvas_width - margin_right, canvas_height - margin_bottom,
            fill="black", width=2
        )

        # Dessiner les étiquettes des axes pour les exactitudes
        canvas.create_text(
            canvas_width // 2,
            canvas_height - margin_bottom // 2,
            text="Époque",
            fill="black"
        )
        canvas.create_text(
            margin_left // 2,
            margin_top + plot_height + 40 + plot_height // 2,
            text="Exactitude",
            fill="black",
            angle=90
        )

        # Dessiner les courbes de perte
        if has_train_loss or has_val_loss:
            # Déterminer les valeurs min et max pour l'échelle
            all_losses = []
            if has_train_loss:
                all_losses.extend(self.lstm_metrics['train_losses'])
            if has_val_loss:
                all_losses.extend(self.lstm_metrics['val_losses'])

            min_loss = min(all_losses) if all_losses else 0
            max_loss = max(all_losses) if all_losses else 1

            # Ajouter une marge pour l'échelle
            loss_range = max_loss - min_loss
            min_loss = max(0, min_loss - loss_range * 0.1)
            max_loss = max_loss + loss_range * 0.1

            # Dessiner la courbe de perte d'entraînement
            if has_train_loss:
                train_losses = self.lstm_metrics['train_losses']
                self._draw_curve(
                    canvas,
                    train_losses,
                    min_loss, max_loss,
                    margin_left, margin_top,
                    plot_width, plot_height,
                    color="blue"
                )

            # Dessiner la courbe de perte de validation
            if has_val_loss:
                val_losses = self.lstm_metrics['val_losses']
                self._draw_curve(
                    canvas,
                    val_losses,
                    min_loss, max_loss,
                    margin_left, margin_top,
                    plot_width, plot_height,
                    color="red"
                )

        # Dessiner les courbes d'exactitude
        if has_train_acc or has_val_acc:
            # Déterminer les valeurs min et max pour l'échelle
            all_accs = []
            if has_train_acc:
                all_accs.extend(self.lstm_metrics['train_accuracies'])
            if has_val_acc:
                all_accs.extend(self.lstm_metrics['val_accuracies'])

            min_acc = min(all_accs) if all_accs else 0
            max_acc = max(all_accs) if all_accs else 1

            # Ajouter une marge pour l'échelle
            acc_range = max_acc - min_acc
            min_acc = max(0, min_acc - acc_range * 0.1)
            max_acc = min(1, max_acc + acc_range * 0.1)

            # Dessiner la courbe d'exactitude d'entraînement
            if has_train_acc:
                train_accs = self.lstm_metrics['train_accuracies']
                self._draw_curve(
                    canvas,
                    train_accs,
                    min_acc, max_acc,
                    margin_left, margin_top + plot_height + 40,
                    plot_width, plot_height,
                    color="green"
                )

            # Dessiner la courbe d'exactitude de validation
            if has_val_acc:
                val_accs = self.lstm_metrics['val_accuracies']
                self._draw_curve(
                    canvas,
                    val_accs,
                    min_acc, max_acc,
                    margin_left, margin_top + plot_height + 40,
                    plot_width, plot_height,
                    color="purple"
                )

        # Dessiner la légende
        legend_y = margin_top + 10
        legend_x = margin_left + plot_width - 150

        if has_train_loss:
            canvas.create_line(
                legend_x, legend_y, legend_x + 20, legend_y,
                fill="blue", width=2
            )
            canvas.create_text(
                legend_x + 70, legend_y,
                text="Perte d'entraînement",
                fill="blue",
                anchor="w"
            )
            legend_y += 20

        if has_val_loss:
            canvas.create_line(
                legend_x, legend_y, legend_x + 20, legend_y,
                fill="red", width=2
            )
            canvas.create_text(
                legend_x + 70, legend_y,
                text="Perte de validation",
                fill="red",
                anchor="w"
            )
            legend_y += 20

        if has_train_acc:
            canvas.create_line(
                legend_x, legend_y, legend_x + 20, legend_y,
                fill="green", width=2
            )
            canvas.create_text(
                legend_x + 70, legend_y,
                text="Exactitude d'entraînement",
                fill="green",
                anchor="w"
            )
            legend_y += 20

        if has_val_acc:
            canvas.create_line(
                legend_x, legend_y, legend_x + 20, legend_y,
                fill="purple", width=2
            )
            canvas.create_text(
                legend_x + 70, legend_y,
                text="Exactitude de validation",
                fill="purple",
                anchor="w"
            )