# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 1401 à 1422
# Type: Méthode de la classe ConsecutiveConfidenceCalculator
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def get_current_wait_ratio(self, config=None) -> float:
        """
        Calcule le ratio actuel de recommandations WAIT par rapport au total.

        Args:
            config: Configuration du prédicteur (optionnel, pour mise à jour des paramètres)

        Returns:
            float: Ratio de recommandations WAIT (entre 0 et 1)
        """
        # Récupérer le ratio optimal depuis la configuration ou utiliser la valeur par défaut
        optimal_ratio = getattr(config, 'optimal_wait_ratio', self.optimal_wait_ratio) if config else self.optimal_wait_ratio

        if not hasattr(self, 'recent_recommendations') or not self.recent_recommendations:
            return optimal_ratio  # Valeur par défaut si aucune donnée

        # Compter les recommandations WAIT (en tenant compte des différentes casses possibles)
        wait_count = sum(1 for rec in self.recent_recommendations if isinstance(rec, str) and rec.lower() == 'wait')
        total_count = len(self.recent_recommendations)

        # Calculer et retourner le ratio
        return wait_count / total_count if total_count > 0 else optimal_ratio