# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\models.py
# Lignes: 802 à 821
# Type: Méthode de la classe PersistentMarkov

    def reset(self, reset_type: str = 'soft') -> None:
        """
        Réinitialise les compteurs des modèles Markov.

        Args:
            reset_type (str): Type de réinitialisation :
                              - 'soft': Réinitialise uniquement les modèles de session.
                              - 'hard': Réinitialise les modèles globaux ET de session.
        """
        with self.lock:
            # Utiliser max_order_int pour les structures de données
            max_order_int = int(self.max_order) + 1 if self.max_order > int(self.max_order) else int(self.max_order)

            if reset_type == 'hard':
                self.global_models = [defaultdict(lambda: defaultdict(int)) for _ in range(max_order_int + 1)]
                logger.info("Modèles Markov globaux réinitialisés (hard reset).")

            # Dans tous les cas (soft ou hard), réinitialiser la session
            self.session_models = [defaultdict(lambda: defaultdict(int)) for _ in range(max_order_int + 1)]
            logger.info(f"Modèles Markov de session réinitialisés (reset type: {reset_type}).")