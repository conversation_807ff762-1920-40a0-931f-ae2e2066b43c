# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 138 à 142
# Type: Méthode de la classe LoggingManager
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(LoggingManager, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance