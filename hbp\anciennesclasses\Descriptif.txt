DESCRIPTIF DÉTAILLÉ DES MÉTHODES - ANCIENNES CLASSES
================================================================================

Ce fichier contient la description détaillée des définitions de classes
du système ML de prédiction Baccarat (hbp.py).

DOMAINE FONCTIONNEL : ANCIENNES CLASSES
Définitions complètes des classes du système.

TOTAL : 2 CLASSES ANALYSÉES

Dernière mise à jour: 25/05/2025 - Création plateforme maintenance

================================================================================
DÉFINITIONS DE CLASSES
================================================================================

1. class_HybridBaccaratPredictor.txt (Classe HybridBaccaratPredictor - Classe principale du système)
   - Lignes 82-14024 dans hbp.py (13943 lignes)
   - FONCTION : Classe principale du système de prédiction Baccarat hybride intégrant ML avancé, interface utilisateur et optimisation
   - ARCHITECTURE GÉNÉRALE :
     * **MODÈLES ML :** Intègre Markov, LGBM (LightGBM), LSTM avec pondération bayésienne adaptative
     * **INTERFACE UTILISATEUR :** Interface Tkinter complète avec graphiques matplotlib temps réel
     * **OPTIMISATION :** Système Optuna pour hyperparamètres avec métriques personnalisées
     * **GESTION DONNÉES :** Pipeline complet préparation, validation et mise en cache
     * **THREAD-SAFETY :** Protection complète avec verrous pour accès concurrent
   - ATTRIBUTS PRINCIPAUX :
     * **self.config** (PredictorConfig) - Configuration système avec tous paramètres
     * **self.sequence** (List[str]) - Séquence des résultats ('player'/'banker')
     * **self.weights** (Dict[str, float]) - Poids adaptatifs des méthodes (markov, lgbm, lstm)
     * **self.lgbm_base** (BaggingClassifier) - Modèle LGBM principal avec ensemble
     * **self.lstm** (torch.nn.Module) - Réseau LSTM bidirectionnel
     * **self.markov** (MarkovModel) - Modèle Markov multi-ordres
     * **self.prediction_history** (List[Dict]) - Historique complet des prédictions
     * **self.consecutive_confidence_calculator** - Calculateur confiance manches 31-60
   - VERROUS THREAD-SAFETY :
     * **self.model_lock** (threading.RLock) - Protection modèles ML
     * **self.weights_lock** (threading.RLock) - Protection poids adaptatifs
     * **self.sequence_lock** (threading.RLock) - Protection séquence et historique
     * **self.training_lock** (threading.RLock) - Protection processus entraînement
     * **self.markov_lock** (threading.RLock) - Protection spécifique modèle Markov
   - MÉTHODES PRINCIPALES :
     * **__init__()** - Constructeur avec double mode (UI/Optuna)
     * **hybrid_prediction()** - Cœur prédiction avec pondération bayésienne
     * **train_models()** - Entraînement complet avec validation croisée
     * **record_outcome()** - Enregistrement résultats avec mise à jour adaptative
     * **run_hyperparameter_optimization()** - Optimisation Optuna multi-objectifs
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION :** Configure modèles, interface, verrous et cache selon mode (UI/Optuna)
     * **PRÉDICTION HYBRIDE :** Combine prédictions avec incertitude épistémique/aléatoire
     * **ADAPTATION POIDS :** Ajuste poids selon performance récente avec lissage
     * **OPTIMISATION CONTINUE :** Mise à jour rapide modèles et hyperparamètres
     * **INTERFACE TEMPS RÉEL :** Affichage graphiques, métriques et contrôles
   - UTILITÉ : Architecture centrale complète pour prédiction Baccarat avec IA avancée, interface professionnelle et optimisation automatique

2. class_ConsecutiveConfidenceCalculator.txt (Classe ConsecutiveConfidenceCalculator - Calculateur confiance consécutive)
   - Lignes 1422-1579 dans hbp.py (158 lignes)
   - FONCTION : Classe spécialisée pour calcul de confiance sur recommandations NON-WAIT consécutives avec analyse de patterns
   - ARCHITECTURE SPÉCIALISÉE :
     * **ANALYSE PATTERNS :** Système d'extraction et classification de patterns basé sur features
     * **STATISTIQUES ADAPTATIVES :** Suivi performance par pattern avec historique complet
     * **CONFIANCE CONTEXTUELLE :** Calcul confiance spécifique aux manches 31-60
     * **MÉTRIQUES CONSÉCUTIVES :** Optimisation pour séquences de recommandations valides
   - ATTRIBUTS PRINCIPAUX :
     * **self.pattern_stats** (defaultdict) - Statistiques par pattern {total, success, consecutive_lengths, max_consecutive}
     * **self.recent_recommendations** (List[str]) - Historique récent des recommandations
     * **self.recent_outcomes** (List[str]) - Historique récent des résultats réels
     * **self.max_recent_history** (int) - Taille maximale historique (défaut: 50)
   - MÉTHODES PRINCIPALES :
     * **__init__()** - Initialise structures de données et paramètres
     * **calculate_confidence()** - Calcule confiance basée sur patterns et contexte
     * **_extract_pattern_key()** - Extrait clé pattern depuis vecteur features
     * **update_recent_data()** - Met à jour historique avec nouvelles données
     * **get_current_wait_ratio()** - Calcule ratio WAIT/NON-WAIT actuel
     * **get_recent_nonwait_success_rate()** - Taux succès recommandations NON-WAIT récentes
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION PATTERNS :** Utilise 5 premières features avec arrondi intelligent (ratios: 1 décimale, autres: 0 décimale)
     * **CALCUL CONFIANCE :** Combine taux succès (70%) et longueur moyenne séquences consécutives (30%)
     * **VALIDATION PLAGE :** Vérifie si manche dans target_round_min à target_round_max
     * **AJUSTEMENT ADAPTATIF :** Modifie confiance selon performance récente et patterns similaires
     * **GESTION HISTORIQUE :** Maintient historique limité avec rotation automatique
   - UTILITÉ : Gestion sophistiquée de la confiance pour recommandations consécutives avec optimisation spécifique manches 31-60
