# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 3528 à 3699
# Type: Méthode de la classe OptunaOptimizer

    def validate_progressive_adaptation(self, adapted_params, validation_steps=3):
        """
        Valide et affine progressivement les hyperparamètres adaptés sur des sous-ensembles croissants.

        Cette méthode utilise une approche de validation progressive pour affiner les hyperparamètres
        en les testant sur des sous-ensembles de taille croissante (25%, 50%, 75%, 100%).

        Args:
            adapted_params: Dictionnaire des paramètres adaptés initialement
            validation_steps: Nombre d'étapes de validation progressive

        Returns:
            Dict: Paramètres affinés après validation progressive
        """
        import os
        import numpy as np
        import time

        logger.warning("=" * 80)
        logger.warning("VALIDATION PROGRESSIVE DES HYPERPARAMÈTRES ADAPTÉS")
        logger.warning("=" * 80)

        # Vérifier si le fichier historical_data.txt existe
        historical_data_path = os.path.join(os.getcwd(), "historical_data.txt")
        if not os.path.exists(historical_data_path):
            logger.warning(f"Fichier {historical_data_path} non trouvé. Validation progressive impossible.")
            return adapted_params

        try:
            # Compter le nombre total de lignes dans le fichier
            with open(historical_data_path, 'r') as f:
                total_lines = sum(1 for _ in f)

            # Définir les tailles des sous-ensembles pour la validation progressive
            subset_sizes = []
            for i in range(1, validation_steps + 1):
                subset_size = int((i / validation_steps) * total_lines)
                subset_sizes.append(subset_size)

            logger.warning(f"Validation progressive sur {validation_steps} sous-ensembles: {subset_sizes} lignes")

            # Paramètres à ajuster progressivement
            progressive_params = {
                'lgbm_subsample': [],
                'lgbm_min_child_samples': [],
                'lgbm_learning_rate': [],
                'lstm_batch_size': [],
                'lstm_epochs': [],
                'lstm_learning_rate': [],
                'markov_depth': [],
                'markov_smoothing': []
            }

            # Créer une configuration de base
            base_config = self.config.clone()

            # Appliquer les paramètres adaptés à la configuration de base
            for param_name, param_value in adapted_params.items():
                if not param_name.startswith('_') and hasattr(base_config, param_name):
                    setattr(base_config, param_name, param_value)

            # Validation progressive sur des sous-ensembles croissants
            for step, subset_size in enumerate(subset_sizes):
                logger.warning(f"Étape {step+1}/{validation_steps}: Validation sur {subset_size} lignes ({subset_size/total_lines*100:.1f}% du total)")

                # Charger un sous-ensemble des données
                subset_indices = np.random.choice(total_lines, size=subset_size, replace=False)

                # Créer une configuration pour cette étape
                step_config = base_config.clone()

                # Ajuster certains paramètres en fonction de la taille du sous-ensemble
                scale_factor = subset_size / (total_lines * 0.1)  # Par rapport à 10% des données

                # Ajuster les paramètres LGBM
                if hasattr(step_config, 'lgbm_subsample'):
                    step_config.lgbm_subsample = self._apply_nonlinear_formula('lgbm_subsample',
                                                                              adapted_params.get('lgbm_subsample', 0.8),
                                                                              scale_factor)
                    progressive_params['lgbm_subsample'].append(step_config.lgbm_subsample)

                if hasattr(step_config, 'lgbm_min_child_samples'):
                    step_config.lgbm_min_child_samples = self._apply_nonlinear_formula('lgbm_min_child_samples',
                                                                                      adapted_params.get('lgbm_min_child_samples', 20),
                                                                                      scale_factor)
                    progressive_params['lgbm_min_child_samples'].append(step_config.lgbm_min_child_samples)

                if hasattr(step_config, 'lgbm_learning_rate'):
                    step_config.lgbm_learning_rate = self._apply_nonlinear_formula('lgbm_learning_rate',
                                                                                  adapted_params.get('lgbm_learning_rate', 0.1),
                                                                                  scale_factor)
                    progressive_params['lgbm_learning_rate'].append(step_config.lgbm_learning_rate)

                # Ajuster les paramètres LSTM
                if hasattr(step_config, 'lstm_batch_size'):
                    step_config.lstm_batch_size = self._apply_nonlinear_formula('lstm_batch_size',
                                                                               adapted_params.get('lstm_batch_size', 32),
                                                                               scale_factor)
                    progressive_params['lstm_batch_size'].append(step_config.lstm_batch_size)

                if hasattr(step_config, 'lstm_epochs'):
                    step_config.lstm_epochs = self._apply_nonlinear_formula('lstm_epochs',
                                                                           adapted_params.get('lstm_epochs', 5),
                                                                           scale_factor)
                    progressive_params['lstm_epochs'].append(step_config.lstm_epochs)

                if hasattr(step_config, 'lstm_learning_rate'):
                    step_config.lstm_learning_rate = self._apply_nonlinear_formula('lstm_learning_rate',
                                                                                  adapted_params.get('lstm_learning_rate', 0.001),
                                                                                  scale_factor)
                    progressive_params['lstm_learning_rate'].append(step_config.lstm_learning_rate)

                # Ajuster les paramètres Markov
                if hasattr(step_config, 'markov_depth'):
                    step_config.markov_depth = self._apply_nonlinear_formula('markov_depth',
                                                                            adapted_params.get('markov_depth', 4),
                                                                            scale_factor)
                    progressive_params['markov_depth'].append(step_config.markov_depth)

                if hasattr(step_config, 'markov_smoothing'):
                    step_config.markov_smoothing = self._apply_nonlinear_formula('markov_smoothing',
                                                                                adapted_params.get('markov_smoothing', 0.1),
                                                                                scale_factor)
                    progressive_params['markov_smoothing'].append(step_config.markov_smoothing)

                # Simuler une évaluation rapide (dans un vrai cas, on exécuterait une validation)
                logger.warning(f"Configuration pour l'étape {step+1}: {step_config.__dict__}")
                time.sleep(0.5)  # Simuler un temps de traitement

            # Analyser les tendances et extrapoler pour l'ensemble complet
            refined_params = adapted_params.copy()

            for param_name, values in progressive_params.items():
                if values:
                    # Analyser la tendance (croissante, décroissante, stable)
                    if len(values) >= 2:
                        trend = sum(values[i] - values[i-1] for i in range(1, len(values)))

                        # Extrapoler en fonction de la tendance
                        if abs(trend) < 0.001 * values[0]:  # Tendance stable
                            # Utiliser la dernière valeur
                            refined_value = values[-1]
                        elif trend > 0:  # Tendance croissante
                            # Extrapoler avec un facteur de prudence
                            refined_value = values[-1] * (1 + 0.1 * (trend / values[0]))
                        else:  # Tendance décroissante
                            # Extrapoler avec un facteur de prudence
                            refined_value = values[-1] * (1 + 0.1 * (trend / values[0]))

                        # Mettre à jour le paramètre raffiné
                        if param_name in refined_params:
                            original_value = refined_params[param_name]
                            refined_params[param_name] = refined_value
                            logger.warning(f"Paramètre {param_name} raffiné: {original_value} -> {refined_value}")

            # Ajouter des méta-informations sur la validation progressive
            if '_meta_info' not in refined_params:
                refined_params['_meta_info'] = {}

            refined_params['_meta_info']['progressive_validation'] = {
                'steps': validation_steps,
                'subset_sizes': subset_sizes,
                'trends': {param: values for param, values in progressive_params.items() if values}
            }

            logger.warning("Validation progressive terminée. Paramètres raffinés.")
            return refined_params

        except Exception as e:
            logger.error(f"Erreur lors de la validation progressive: {e}")
            logger.warning("Utilisation des paramètres adaptés sans validation progressive.")
            return adapted_params