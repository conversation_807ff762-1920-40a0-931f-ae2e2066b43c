# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\hbp.py
# Lignes: 4888 à 4903
# Type: Méthode de la classe HybridBaccaratPredictor

    def _select_and_save_optimized_models(self) -> None:
        """
        Ouvre une boîte de dialogue pour sélectionner un fichier de paramètres optimisés,
        puis appelle save_optimized_models avec le fichier sélectionné.
        """
        params_file_path = filedialog.askopenfilename(
            title="Sélectionnez le fichier de paramètres optimisés",
            filetypes=(("Fichiers JSON", "*.json"), ("Tous les fichiers", "*.*")),
            initialdir="viable_trials"
        )

        if not params_file_path:
            logger.info("Sauvegarde des modèles optimisés annulée par l'utilisateur.")
            return

        self.save_optimized_models(params_file_path)