# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 4683 à 4694
# Type: Méthode de la classe HybridBaccaratPredictor

        def update_ui():
             # Valider la valeur
             value_clamped = max(0, min(100, int(value)))
             if hasattr(self, 'progress_var') and self.progress_var:
                 self.progress_var.set(value_clamped)
             if hasattr(self, 'progress_label_var') and self.progress_label_var:
                 # Tronquer les messages trop longs
                 message_display = message[:100] + '...' if len(message) > 100 else message
                 self.progress_label_var.set(message_display)
             # Mettre à jour l'UI si la fenêtre existe toujours
             if self.is_ui_available():
                 self.root.update_idletasks()