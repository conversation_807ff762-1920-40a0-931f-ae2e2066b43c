# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\config.py
# Lignes: 995 à 1031
# Type: Méthode de la classe PredictorConfig

    def get_min_sequence_length(self):
        """
        Détermine la longueur minimale de séquence requise pour tous les modèles.
        Utilise uniquement des paramètres existants dans la configuration.

        Returns:
            int: La longueur minimale de séquence requise.
        """
        # Longueur minimale pour Markov
        markov_max_order = getattr(self, 'markov_max_order', 5)
        # Utiliser markov_context_length si défini, sinon utiliser une valeur par défaut basée sur markov_max_order
        markov_context_length = getattr(self, 'markov_context_length', markov_max_order * 2)
        markov_min_length = max(markov_max_order, markov_context_length)

        # Longueur minimale pour LSTM
        lstm_sequence_length = getattr(self, 'lstm_sequence_length', 20)
        # Utiliser lstm_context_length si défini, sinon utiliser lstm_sequence_length
        lstm_context_length = getattr(self, 'lstm_context_length', lstm_sequence_length)
        lstm_min_length = lstm_context_length

        # Longueur minimale pour LGBM
        # Utiliser lgbm_min_features si défini, sinon utiliser une valeur par défaut
        lgbm_feature_window = getattr(self, 'lgbm_feature_window', 10)
        # Utiliser lgbm_context_length si défini, sinon utiliser une valeur basée sur lgbm_feature_window
        lgbm_context_length = getattr(self, 'lgbm_context_length', lgbm_feature_window * 2)
        lgbm_min_length = lgbm_context_length

        # Prendre le maximum des trois
        min_sequence_length = max(markov_min_length, lstm_min_length, lgbm_min_length)

        # Journaliser les longueurs minimales uniquement lors du premier appel ou si elles changent
        # Utiliser un attribut statique pour stocker les valeurs précédentes
        if not hasattr(self.__class__, '_last_min_lengths') or self.__class__._last_min_lengths != (markov_min_length, lstm_min_length, lgbm_min_length, min_sequence_length):
            logger.debug(f"Longueurs minimales de séquence: Markov={markov_min_length}, LSTM={lstm_min_length}, LGBM={lgbm_min_length}, Global={min_sequence_length}")
            self.__class__._last_min_lengths = (markov_min_length, lstm_min_length, lgbm_min_length, min_sequence_length)

        return min_sequence_length