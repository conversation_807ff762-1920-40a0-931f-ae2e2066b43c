# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1677 à 1702
# Type: Méthode de la classe ThreadedOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def stop(self):
        """
        Arrête l'optimisation en cours.

        Returns:
            bool: True si l'optimisation a été arrêtée, False sinon
        """
        if not self.is_running:
            logger.warning("Aucune optimisation en cours d'exécution")
            return False

        # Définir l'événement d'arrêt
        self.stop_event.set()

        # Attendre que le thread se termine (avec un timeout)
        if self.thread:
            self.thread.join(timeout=5.0)

            # Si le thread ne s'est pas terminé, le forcer à s'arrêter
            if self.thread.is_alive():
                logger.warning("Le thread d'optimisation ne s'est pas arrêté proprement, forçage de l'arrêt")
                # Nous ne pouvons pas forcer l'arrêt d'un thread en Python, mais nous pouvons le marquer comme terminé
                self.is_running = False

        logger.info("Optimisation arrêtée")
        return True