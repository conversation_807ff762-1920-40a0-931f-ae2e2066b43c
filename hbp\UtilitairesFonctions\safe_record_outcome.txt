# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 12344 à 12486
# Type: Méthode de la classe HybridBaccaratPredictor

    def safe_record_outcome(self, outcome: str) -> None:
        """
        Enregistre le résultat d'une manche et déclenche la prochaine prédiction/mise à jour. Thread-safe.
        AJOUT: Empêche l'enregistrement si la limite de 60 manches est atteinte.
        AJOUT: Appelle l'auto-update aux manches cibles.
        MODIFIÉ: Ne vide PLUS le cache LGBM à chaque coup.
        """
        # Mesurer le temps total de traitement
        import time
        start_time = time.time()

        logger.debug(f"safe_record: ENTRÉE pour {outcome}")
        ui_available = self.is_ui_available()

        try:
            # Utiliser tous les verrous nécessaires pour lecture/écriture état cohérent
            # Note: auto_fast_update_if_needed prendra ses propres verrous si nécessaire
            lock_start = time.time()
            with self.sequence_lock, self.markov_lock, self.model_lock, self.weights_lock:
                lock_time = time.time() - lock_start
                logger.debug(f"safe_record: Verrous acquis en {lock_time*1000:.1f}ms.")

                # ***** Vérifier la limite de 60 manches *****
                if len(self.sequence) >= 60:
                    logger.warning(f"Limite de 60 manches atteinte (actuellement {len(self.sequence)}). Enregistrement de '{outcome}' ignoré.")
                    if ui_available:
                        messagebox.showwarning("Limite Atteinte", "Vous avez atteint la limite de 60 manches pour cette session.\nLe coup n'a pas été enregistré.")
                    return # Quitter la fonction AVANT de faire quoi que ce soit d'autre
                # ***** FIN Vérification Limite *****

                try:
                    # 1. Mettre à jour séquence (SEULEMENT si limite non atteinte)
                    self.sequence.append(outcome)
                    current_round_num = len(self.sequence) # Obtenir numéro manche ACTUELLE
                    logger.info(f"Manche {current_round_num}/60 enregistrée: {outcome.upper()}")

                    # <<< === AJOUT DE L'APPEL À L'AUTO-UPDATE === >>>
                    # Appelée après mise à jour séquence, avant le reste
                    try:
                        # Cette méthode gère elle-même les conditions et les verrous nécessaires
                        # Elle ne bloquera que brièvement si une tâche est en cours
                        self.auto_fast_update_if_needed(current_round_num)
                    except Exception as e_auto_update:
                        # Logguer une erreur mais NE PAS arrêter le reste du traitement du coup
                        logger.error(f"Erreur pendant l'appel automatique à auto_fast_update_if_needed: {e_auto_update}", exc_info=True)
                    # <<< ======================================== >>>


                    # Stocker la prédiction faite *avant* que ce résultat ne soit connu (pour update_weights)
                    prediction_made_for_this_round = self.prediction_history[-1] if self.prediction_history else {}

                    # 2. Mettre à jour Markov session
                    if self.markov:
                        try:
                            # update_session est rapide et n'a pas besoin de thread séparé ici
                            self.markov.update_session(self.sequence)
                        except Exception as e_markov: logger.error("Erreur Markov session update", exc_info=False) # exc_info=False pour éviter trop de logs

                    # 3. Mettre à jour compteurs patterns
                    try:
                        # _update_pattern_counts est rapide
                        self._update_pattern_counts(outcome)
                    except Exception as e_pattern: logger.error(f"ERREUR DANS _update_pattern_counts: {e_pattern}", exc_info=True) # Garder exc_info ici si bug suspect

                    # 4. Vider cache LGBM (rapide)
                    # >>>>>>>>>>>>> MODIFICATION: LIGNE SUPPRIMÉE <<<<<<<<<<<<<<
                    # self.lgbm_cache = deque(maxlen=100) # Recréer sous le verrou
                    # >>>>>>>>>>>>> FIN MODIFICATION <<<<<<<<<<<<<<<

                    # 5. Créer features pour la *prochaine* prédiction (basée sur nouvelle séquence)
                    # create_hybrid_features est relativement rapide
                    lgbm_feat, lstm_feat = self.create_hybrid_features(self.sequence)

                    # 6. Effectuer la prédiction hybride pour le coup SUIVANT
                    # hybrid_prediction prend model_lock en interne
                    next_prediction = {}
                    try:
                        # Récupérer la phase d'optimisation depuis la configuration
                        optimization_phase = getattr(self.config, 'optimization_phase', None)

                        # Appeler hybrid_prediction avec le paramètre optimization_phase
                        next_prediction = self.hybrid_prediction(lgbm_feat, lstm_feat, optimization_phase=optimization_phase)
                    except NotFittedError as e_notfitted:
                        logger.warning(f"NotFittedError dans hybrid_prediction: {e_notfitted}. Retourne 50/50.")
                        next_prediction = {'player': 0.5, 'banker': 0.5, 'uncertainty': 0.5,'recommendation': 'wait', 'methods': {}}
                    except Exception as e_hybrid:
                        logger.error("ERREUR pendant hybrid_prediction", exc_info=True)
                        next_prediction = {'player': 0.5, 'banker': 0.5, 'uncertainty': 0.5,'recommendation': 'wait', 'methods': {}}

                    # 7. Ajouter la NOUVELLE prédiction à l'historique (rapide, sous verrou sequence_lock)
                    self.prediction_history.append(next_prediction)

                    # 8. Mettre à jour les poids basés sur la prédiction PRÉCÉDENTE et le résultat ACTUEL
                    # update_weights prend les verrous nécessaires en interne
                    if prediction_made_for_this_round: # S'assurer qu'une prédiction avait été faite
                         try:
                              self.update_weights(prediction_made_for_this_round, outcome)
                         except Exception as e_weights: logger.error(f"Erreur pendant update_weights: {e_weights}", exc_info=True)

                    # 8bis. Vérifier si nous sommes dans la plage de manches cibles (31-60)
                    target_round_min = getattr(self.config, 'target_round_min', 31)
                    target_round_max = getattr(self.config, 'target_round_max', 60)
                    is_target_round = target_round_min <= current_round_num <= target_round_max

                    # Si nous sommes dans la plage cible, s'assurer que le calculateur de confiance consécutive est initialisé
                    if is_target_round and not hasattr(self, 'consecutive_confidence_calculator'):
                        logger.info(f"Manche {current_round_num}: Initialisation du calculateur de confiance consécutive manquant (safe_record)")
                        self.init_consecutive_confidence_calculator()

                    # Mettre à jour le calculateur de confiance consécutive
                    try:
                        recommendation = prediction_made_for_this_round.get('recommendation', 'wait')
                        self.update_consecutive_confidence_calculator(current_round_num, recommendation, outcome)
                    except Exception as e_consecutive:
                        logger.error(f"Erreur pendant update_consecutive_confidence_calculator: {e_consecutive}", exc_info=True)

                    # 9. Planifier les MàJ UI via root.after (rapide à planifier)
                    if ui_available:
                         pred_copy_ui = next_prediction.copy() # Copie pour lambda
                         try:
                             # Planifier l'exécution dans le thread UI principal
                             self.root.after(0, lambda p=pred_copy_ui: self.lightweight_update_display(p))
                             self.root.after(10, self.update_display) # Appelle draw_trend_chart si visible
                             self.root.after(30, self._update_weights_display) # Met à jour affichage poids
                         except Exception as e_after:
                             # Erreur si la fenêtre est fermée pendant les after?
                             logger.error(f"Erreur pendant planification root.after: {e_after}", exc_info=False)

                except Exception as e_inner:
                     # Erreurs inattendues dans la logique principale après l'appel auto-update
                     logger.error(f"Erreur critique DANS safe_record_outcome (après appel auto-update): {e_inner}", exc_info=True)
                finally:
                     # Ce logger marque la fin du bloc principal, pas forcément la libération du verrou externe
                     logger.debug("safe_record: Fin bloc interne logique principale.")
            # Le verrou WITH externe est libéré ici

        except Exception as e_lock: # Rare, erreur liée à l'acquisition/relâchement du verrou externe
             logger.error(f"Erreur critique liée aux verrous EXTERNES dans safe_record_outcome: {e_lock}", exc_info=True)

        # Mesurer le temps total d'exécution
        total_time = time.time() - start_time
        logger.debug(f"Temps total de traitement: {total_time*1000:.1f}ms pour {outcome}")
        logger.debug(f"safe_record: SORTIE pour {outcome}")