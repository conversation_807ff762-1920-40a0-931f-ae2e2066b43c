DESCRIPTIF DÉTAILLÉ DES MÉTHODES - GESTION DONNÉES
================================================================================

Ce fichier contient la description détaillée des méthodes de gestion des données du système data_manager.py.

ÉTAT DOCUMENTATION : PREMIÈRE VAGUE FONCTIONNELLE
- **Couverture** : Méthodes de gestion des données documentées
- **Niveau** : Documentation fonctionnelle standardisée
- **Qualité** : 15-25 lignes par méthode minimum

DOMAINE FONCTIONNEL : GESTION DONNÉES
- Génération et filtrage des données pour modèles ML
- Préparation des séquences Baccarat
- Validation et transformation des données
- Interface avec les générateurs de features

================================================================================

1. __init__.txt (BaccaratSequenceManager.__init__ - INITIALISATION GESTIONNAIRE SÉQUENCES)
   - Lignes 30-85 dans data_manager.py (56 lignes)
   - FONCTION : Initialise le gestionnaire de séquences Baccarat modifié avec fenêtre adaptative pour modèles LGBM et LSTM
   - PARAMÈTRES :
     * self - Instance de BaccaratSequenceManager
     * sequence_length (int) - Taille maximale matrice sortie features LSTM
     * min_target_hand_index (int) - Index minimum main cible (0-based) à inclure
     * hybrid_feature_creator (Callable) - Fonction création features hybrides [LGBM], [LSTM]
     * lgbm_feature_count (int) - Nombre attendu features LGBM
     * lstm_seq_len (int) - Longueur attendue séquence features LSTM
     * lstm_feature_count (int) - Nombre attendu features par pas temps LSTM
     * parent_logger (optional) - Logger externe optionnel
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION SEQUENCE_LENGTH** : `if not isinstance(sequence_length, int) or sequence_length <= 0: raise ValueError("sequence_length doit être un entier positif.")`
     * **VALIDATION MIN_TARGET** : `if not isinstance(min_target_hand_index, int) or min_target_hand_index < 0: raise ValueError("min_target_hand_index doit être un entier positif ou nul.")`
     * **VALIDATION CREATOR** : `if not callable(hybrid_feature_creator): raise TypeError("hybrid_feature_creator doit être une fonction callable.")`
     * **VALIDATION LGBM_COUNT** : `if not isinstance(lgbm_feature_count, int) or lgbm_feature_count <= 0: raise ValueError("lgbm_feature_count doit être un entier positif.")`
     * **VALIDATION LSTM_SEQ** : `if not isinstance(lstm_seq_len, int) or lstm_seq_len <= 0: raise ValueError("lstm_seq_len doit être un entier positif.")`
     * **VALIDATION LSTM_COUNT** : `if not isinstance(lstm_feature_count, int) or lstm_feature_count <= 0: raise ValueError("lstm_feature_count doit être un entier positif.")`
     * **ASSIGNATION ATTRIBUTS** : `self.sequence_length = sequence_length`, `self.min_target_hand_index = min_target_hand_index`, `self.hybrid_feature_creator = hybrid_feature_creator`
     * **STOCKAGE COUNTS** : `self.lgbm_feature_count = lgbm_feature_count`, `self.lstm_seq_len = lstm_seq_len`, `self.lstm_feature_count = lstm_feature_count`
     * **CALCUL SHAPE LSTM** : `self.lstm_expected_shape = (lstm_seq_len, lstm_feature_count)` commentaire "Mettre à jour la shape"
     * **CONFIGURATION LOGGER** : `self.logger = parent_logger if parent_logger else logging.getLogger(__name__)`
     * **LOGGING INITIALISATION** : `logger.info(f"BaccaratSequenceManager (Modifié) initialisé:")` avec détails configuration
     * **LOGGING DÉTAILS** : Messages info sur sequence_length, min_target_hand_index, hybrid_feature_creator.__name__, validation counts et shape
     * **FENÊTRE ADAPTATIVE** : Commentaire "= lstm_seq_len pour create_hybrid_features" sur sequence_length
   - RETOUR : None (constructeur)
   - UTILITÉ : Méthode fondamentale pour créer gestionnaire séquences Baccarat. Essentielle pour configuration modèles ML. Critique pour validation paramètres et fenêtre adaptative.

2. _generate_filtered_data_for_shoe.txt (BaccaratSequenceManager._generate_filtered_data_for_shoe - GÉNÉRATION DONNÉES FILTRÉES SABOT)
   - Lignes 87-190 dans data_manager.py (104 lignes)
   - FONCTION : Génère features LGBM/LSTM, labels, séquences préfixes et indices pour un seul sabot avec filtrage min_target_hand_index et garantie manches 31-60
   - PARAMÈTRES :
     * self - Instance de BaccaratSequenceManager
     * shoe_pb_sequence (List[str]) - Séquence P/B ('player'/'banker') du sabot
     * game_index_offset (int) - Index global du premier coup potentiel de ce sabot
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION LISTES** : `X_lgbm_list, y_list, X_lstm_list, prefix_list, origin_list = [], [], [], [], []`
     * **CALCUL LONGUEUR** : `game_len = len(shoe_pb_sequence)`
     * **VALIDATION LONGUEUR** : `if game_len != 60: self.logger.warning(f"Sabot de longueur {game_len} != 60 détecté (offset={game_index_offset})")` commentaire "sans log pour éviter de surcharger les logs"
     * **VÉRIFICATION MINIMUM** : `if game_len < 2: self.logger.warning(f"Sabot trop court ({game_len} < 2), ignoré."); return X_lgbm_list, y_list, X_lstm_list, prefix_list, origin_list`
     * **DÉFINITION CIBLES** : `target_round_min = 31`, `target_round_max = 60` commentaire "MODIFICATION: Définir les limites des manches cibles (31-60)"
     * **BOUCLE PRINCIPALE** : `for i in range(1, game_len):` commentaire "On commence à l'index 1 (pour avoir au moins 1 élément d'historique)"
     * **CALCUL POSITION** : `position_1_indexed = i + 1` commentaire "'i' est l'index (0-based) de la main à prédire (la CIBLE y)"
     * **CONDITION CIBLE** : `is_target_round = (position_1_indexed >= target_round_min and position_1_indexed <= target_round_max)`
     * **FILTRE PRINCIPAL** : `if i >= self.min_target_hand_index or is_target_round:` commentaire "MODIFICATION: Ajouter la condition is_target_round"
     * **SÉQUENCE ENTRÉE** : `input_sequence = shoe_pb_sequence[:i]` commentaire "Utilise toute la séquence disponible jusqu'à i-1 (fenêtre adaptative)"
     * **OUTCOME CIBLE** : `actual_outcome = shoe_pb_sequence[i]` commentaire "La cible à l'index i"
     * **GÉNÉRATION FEATURES** : `features_lgbm, features_lstm_np = self.hybrid_feature_creator(input_sequence)`
     * **VALIDATION LGBM** : `valid_lgbm = (features_lgbm is not None and len(features_lgbm) == self.lgbm_feature_count)`
     * **VALIDATION LSTM** : `valid_lstm = (features_lstm_np is not None and features_lstm_np.shape == self.lstm_expected_shape)`
     * **CONVERSION LABEL** : `label = 1 if actual_outcome == 'banker' else 0` commentaire système zero-based
     * **CALCUL INDEX GLOBAL** : `global_origin_index = game_index_offset + i`
     * **AJOUT DONNÉES** : `if valid_lgbm and valid_lstm:` puis append à toutes listes
     * **GESTION ERREURS** : Construction `reason = []` avec conditions détaillées pour logging échecs
     * **LOGGING CONDITIONNEL** : `if is_target_round: self.logger.warning(...)` vs `self.logger.debug(...)`
     * **VÉRIFICATION FINALE** : `if game_len >= target_round_max:` calcul `target_indices` et `expected_target_count` avec warning si manquantes
   - RETOUR : Tuple[List, List, List, List, List] - (X_lgbm, y, X_lstm, prefix_seq, origin_idx) pour ce sabot
   - UTILITÉ : Méthode interne critique pour génération données filtrées par sabot. Essentielle pour garantie manches cibles 31-60. Permet fenêtre adaptative et validation robuste.