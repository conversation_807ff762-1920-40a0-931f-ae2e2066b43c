# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 2790 à 2856
# Type: Méthode de la classe OptunaOptimizer

    def _export_sklearn_to_onnx(self, model, output_path, input_shape=None, input_names=None, output_names=None, opset_version=12):
        """
        Exporte un modèle scikit-learn au format ONNX.

        Args:
            model: Modèle scikit-learn à exporter
            output_path: Chemin du fichier ONNX de sortie
            input_shape: Forme des données d'entrée
            input_names: Noms des entrées du modèle
            output_names: Noms des sorties du modèle
            opset_version: Version de l'ensemble d'opérations ONNX

        Returns:
            str: Chemin du fichier ONNX généré
        """
        try:
            import skl2onnx
            from skl2onnx.common.data_types import FloatTensorType, Int64TensorType, StringTensorType
            import onnxruntime as rt
            import numpy as np

            # Déterminer la forme des données d'entrée
            if input_shape is None:
                # Forme par défaut pour un vecteur de caractéristiques
                input_shape = [None, 10]  # [batch_size, n_features]

            # Déterminer le type de données d'entrée
            input_type = FloatTensorType(input_shape)

            # Convertir le modèle en ONNX
            onx = skl2onnx.convert_sklearn(
                model,
                initial_types=[('input', input_type)],
                options={id(model): {'zipmap': False}},  # Désactiver zipmap pour les classificateurs
                target_opset=opset_version
            )

            # Sauvegarder le modèle ONNX
            with open(output_path, "wb") as f:
                f.write(onx.SerializeToString())

            logger.warning(f"Modèle scikit-learn exporté au format ONNX: {output_path}")

            # Vérifier que le modèle ONNX est valide
            try:
                sess = rt.InferenceSession(output_path)
                input_name = sess.get_inputs()[0].name

                # Créer des données d'entrée factices pour tester le modèle
                X_test = np.random.rand(5, input_shape[1]).astype(np.float32)

                # Exécuter une inférence de test
                _ = sess.run(None, {input_name: X_test})

                logger.warning("Validation du modèle ONNX réussie")
            except Exception as e:
                logger.warning(f"Erreur lors de la validation du modèle ONNX: {e}")

            return output_path

        except ImportError as e:
            logger.warning(f"Impossible d'exporter le modèle scikit-learn en ONNX: {e}")
            logger.warning("Installez les packages requis: pip install skl2onnx onnxruntime")
            return None
        except Exception as e:
            logger.warning(f"Erreur lors de l'exportation du modèle scikit-learn en ONNX: {e}")
            return None