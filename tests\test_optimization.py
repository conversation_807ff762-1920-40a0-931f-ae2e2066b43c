#!/usr/bin/env python3
"""
Tests complets pour l'optimisation Optuna
"""

import sys
import os
import unittest
import tempfile
import time
import numpy as np
from pathlib import Path

# Ajouter le dossier du programme au path
PROG_DIR = r"C:\Users\<USER>\Desktop\travail\plateforme\notes_travail\prog"
sys.path.insert(0, PROG_DIR)

try:
    from hbp import HybridBaccaratPredictor
    from config import PredictorConfig
    import optuna
except ImportError as e:
    print(f"ERREUR: Impossible d'importer modules: {e}")
    sys.exit(1)


class TestOptimizationBasic(unittest.TestCase):
    """Tests de base pour l'optimisation"""
    
    def setUp(self):
        """Initialisation avant chaque test"""
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        
        # Charger vraies données si disponibles
        historical_file = os.path.join(PROG_DIR, "historical_data.txt")
        if os.path.exists(historical_file):
            try:
                with open(historical_file, 'r') as f:
                    content = f.read().strip()
                    self.test_data = [int(x) for x in content.split(',')][:1000]  # Limiter pour tests
            except:
                self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 100
        else:
            self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 100
            
    def test_optuna_study_creation(self):
        """Test création étude Optuna"""
        try:
            study = optuna.create_study(direction='maximize')
            self.assertIsNotNone(study)
            self.assertIsInstance(study, optuna.Study)
            
        except Exception as e:
            self.fail(f"Échec création étude Optuna: {e}")
            
    def test_optimization_objective_function(self):
        """Test fonction objectif d'optimisation"""
        try:
            # Créer fonction objectif simple
            def objective(trial):
                # Paramètres à optimiser
                weight_lgbm = trial.suggest_float('weight_lgbm', 0.1, 0.8)
                weight_lstm = trial.suggest_float('weight_lstm', 0.1, 0.8)
                weight_markov = 1.0 - weight_lgbm - weight_lstm
                
                if weight_markov < 0.1:
                    return 0.0
                    
                # Simuler score
                return np.random.random()
                
            # Tester fonction
            study = optuna.create_study(direction='maximize')
            study.optimize(objective, n_trials=3, timeout=30)
            
            self.assertGreater(len(study.trials), 0)
            
        except Exception as e:
            self.fail(f"Échec fonction objectif: {e}")
            
    def test_parameter_space_definition(self):
        """Test définition espace paramètres"""
        try:
            # Tester définition paramètres
            result = self.predictor.define_parameter_space()
            
            if result is not None:
                self.assertIsInstance(result, dict)
                
        except Exception as e:
            self.fail(f"Échec définition espace paramètres: {e}")


class TestOptimizationParameters(unittest.TestCase):
    """Tests des paramètres d'optimisation"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 50
        
    def test_weight_parameters(self):
        """Test optimisation poids des modèles"""
        try:
            # Créer trial factice
            study = optuna.create_study()
            trial = study.ask()
            
            # Tester suggestion poids
            weight_lgbm = trial.suggest_float('weight_lgbm', 0.1, 0.8)
            weight_lstm = trial.suggest_float('weight_lstm', 0.1, 0.8)
            weight_markov = 1.0 - weight_lgbm - weight_lstm
            
            # Vérifier contraintes
            self.assertGreaterEqual(weight_lgbm, 0.1)
            self.assertLessEqual(weight_lgbm, 0.8)
            self.assertGreaterEqual(weight_lstm, 0.1)
            self.assertLessEqual(weight_lstm, 0.8)
            
            # Vérifier somme
            total = weight_lgbm + weight_lstm + weight_markov
            self.assertAlmostEqual(total, 1.0, places=6)
            
        except Exception as e:
            self.fail(f"Échec test poids: {e}")
            
    def test_lstm_parameters(self):
        """Test optimisation paramètres LSTM"""
        try:
            study = optuna.create_study()
            trial = study.ask()
            
            # Paramètres LSTM
            sequence_length = trial.suggest_int('lstm_sequence_length', 5, 30)
            hidden_size = trial.suggest_int('lstm_hidden_size', 16, 128)
            learning_rate = trial.suggest_float('lstm_learning_rate', 0.001, 0.1)
            
            # Vérifier plages
            self.assertGreaterEqual(sequence_length, 5)
            self.assertLessEqual(sequence_length, 30)
            self.assertGreaterEqual(hidden_size, 16)
            self.assertLessEqual(hidden_size, 128)
            self.assertGreaterEqual(learning_rate, 0.001)
            self.assertLessEqual(learning_rate, 0.1)
            
        except Exception as e:
            self.fail(f"Échec test paramètres LSTM: {e}")
            
    def test_lgbm_parameters(self):
        """Test optimisation paramètres LGBM"""
        try:
            study = optuna.create_study()
            trial = study.ask()
            
            # Paramètres LGBM
            n_estimators = trial.suggest_int('lgbm_n_estimators', 50, 500)
            max_depth = trial.suggest_int('lgbm_max_depth', 3, 15)
            learning_rate = trial.suggest_float('lgbm_learning_rate', 0.01, 0.3)
            
            # Vérifier plages
            self.assertGreaterEqual(n_estimators, 50)
            self.assertLessEqual(n_estimators, 500)
            self.assertGreaterEqual(max_depth, 3)
            self.assertLessEqual(max_depth, 15)
            self.assertGreaterEqual(learning_rate, 0.01)
            self.assertLessEqual(learning_rate, 0.3)
            
        except Exception as e:
            self.fail(f"Échec test paramètres LGBM: {e}")


class TestOptimizationExecution(unittest.TestCase):
    """Tests d'exécution de l'optimisation"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        
        # Données réduites pour tests rapides
        self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 20
        
    def test_single_trial_execution(self):
        """Test exécution d'un seul trial"""
        try:
            # Créer fonction objectif simplifiée
            def simple_objective(trial):
                # Paramètres minimaux
                weight_lgbm = trial.suggest_float('weight_lgbm', 0.2, 0.6)
                weight_lstm = trial.suggest_float('weight_lstm', 0.2, 0.6)
                weight_markov = 1.0 - weight_lgbm - weight_lstm
                
                if weight_markov < 0.1:
                    return 0.0
                    
                # Simuler évaluation rapide
                return 0.5 + np.random.random() * 0.1
                
            study = optuna.create_study(direction='maximize')
            study.optimize(simple_objective, n_trials=1, timeout=60)
            
            self.assertEqual(len(study.trials), 1)
            self.assertIsNotNone(study.best_trial)
            
        except Exception as e:
            self.fail(f"Échec exécution trial: {e}")
            
    def test_optimization_timeout(self):
        """Test timeout d'optimisation"""
        start_time = time.time()
        
        try:
            def slow_objective(trial):
                time.sleep(0.1)  # Simuler calcul lent
                return np.random.random()
                
            study = optuna.create_study(direction='maximize')
            study.optimize(slow_objective, timeout=2)  # 2 secondes max
            
            end_time = time.time()
            elapsed = end_time - start_time
            
            # Devrait s'arrêter proche de 2 secondes
            self.assertLess(elapsed, 5)  # Marge pour overhead
            
        except Exception as e:
            self.fail(f"Échec test timeout: {e}")
            
    def test_optimization_with_real_data(self):
        """Test optimisation avec vraies données"""
        try:
            # Charger données réelles si disponibles
            historical_file = os.path.join(PROG_DIR, "historical_data.txt")
            if os.path.exists(historical_file):
                with open(historical_file, 'r') as f:
                    content = f.read().strip()
                    real_data = [int(x) for x in content.split(',')][:500]  # Limiter
                    
                # Tester optimisation rapide
                result = self.predictor.optimize_parameters(real_data, n_trials=2, timeout=120)
                
                if result is not None:
                    self.assertIsInstance(result, dict)
                    
        except Exception as e:
            self.fail(f"Échec optimisation données réelles: {e}")


class TestOptimizationResults(unittest.TestCase):
    """Tests des résultats d'optimisation"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        
    def test_best_parameters_extraction(self):
        """Test extraction meilleurs paramètres"""
        try:
            # Créer étude avec résultats factices
            study = optuna.create_study(direction='maximize')
            
            def objective(trial):
                weight_lgbm = trial.suggest_float('weight_lgbm', 0.2, 0.6)
                weight_lstm = trial.suggest_float('weight_lstm', 0.2, 0.6)
                return weight_lgbm + weight_lstm  # Score simple
                
            study.optimize(objective, n_trials=5)
            
            # Extraire meilleurs paramètres
            best_params = study.best_params
            
            self.assertIsInstance(best_params, dict)
            self.assertIn('weight_lgbm', best_params)
            self.assertIn('weight_lstm', best_params)
            
        except Exception as e:
            self.fail(f"Échec extraction paramètres: {e}")
            
    def test_optimization_history(self):
        """Test historique d'optimisation"""
        try:
            study = optuna.create_study(direction='maximize')
            
            def objective(trial):
                return np.random.random()
                
            study.optimize(objective, n_trials=3)
            
            # Vérifier historique
            trials = study.trials
            
            self.assertEqual(len(trials), 3)
            
            for trial in trials:
                self.assertIsNotNone(trial.value)
                self.assertIsInstance(trial.params, dict)
                
        except Exception as e:
            self.fail(f"Échec test historique: {e}")
            
    def test_optimization_convergence(self):
        """Test convergence d'optimisation"""
        try:
            study = optuna.create_study(direction='maximize')
            
            # Fonction avec optimum connu
            def quadratic_objective(trial):
                x = trial.suggest_float('x', -10, 10)
                return -(x - 5) ** 2  # Maximum en x=5
                
            study.optimize(quadratic_objective, n_trials=20)
            
            # Vérifier convergence vers optimum
            best_x = study.best_params['x']
            self.assertAlmostEqual(best_x, 5.0, delta=2.0)  # Proche de 5
            
        except Exception as e:
            self.fail(f"Échec test convergence: {e}")


if __name__ == '__main__':
    print("=== TESTS OPTIMISATION ===")
    print(f"Répertoire programme: {PROG_DIR}")
    
    # Créer suite de tests
    suite = unittest.TestSuite()
    
    # Ajouter tests
    suite.addTest(unittest.makeSuite(TestOptimizationBasic))
    suite.addTest(unittest.makeSuite(TestOptimizationParameters))
    suite.addTest(unittest.makeSuite(TestOptimizationExecution))
    suite.addTest(unittest.makeSuite(TestOptimizationResults))
    
    # Exécuter tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Afficher résultats
    print(f"\n=== RÉSULTATS OPTIMISATION ===")
    print(f"Tests exécutés: {result.testsRun}")
    print(f"Échecs: {len(result.failures)}")
    print(f"Erreurs: {len(result.errors)}")
    
    if result.failures:
        print("\nÉCHECS:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
            
    if result.errors:
        print("\nERREURS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
            
    sys.exit(0 if result.wasSuccessful() else 1)
