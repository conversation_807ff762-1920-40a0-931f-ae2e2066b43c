# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 3797 à 3816
# Type: Méthode de la classe WaitPlacementOptimizer

    def get_stats(self):
        """
        Retourne les statistiques de l'optimiseur.

        Returns:
            Dict: Statistiques de l'optimiseur
        """
        return {
            "total_waits": self.total_waits,
            "effective_waits": self.effective_waits,
            "missed_opportunities": self.missed_opportunities,
            "wait_efficiency": self.effective_waits / self.total_waits if self.total_waits > 0 else 0,
            "current_consecutive_valid": self.current_consecutive_valid,
            "max_consecutive_valid": self.max_consecutive_valid,
            "current_wait_ratio": self.current_wait_ratio,
            "error_pattern_threshold": self.error_pattern_threshold,
            "transition_uncertainty_threshold": self.transition_uncertainty_threshold,
            "error_patterns_count": len(self.error_patterns),
            "transition_patterns_count": len(self.transition_patterns)
        }