DESCRIPTIF DÉTAILLÉ DES MÉTHODES - CALCUL DE CONFIANCE
================================================================================

Ce fichier contient la description détaillée des méthodes liées au calcul de confiance.

ÉTAT DOCUMENTATION : PREMIÈRE VAGUE EN COURS
- **Couverture** : En cours de documentation
- **Niveau** : Documentation fonctionnelle standardisée
- **Qualité** : 15-25 lignes par méthode minimum

DOMAINE FONCTIONNEL : CALCUL DE CONFIANCE
- Calcul d'incertitude et de confiance
- Calibration des probabilités
- Métriques de confiance
- Évaluation de la fiabilité

MÉTHODES DOCUMENTÉES :
================================================================================

1. calculate_calibrated_confidence.txt (ConfidenceCalculator.calculate_calibrated_confidence - CALIBRATION CONFIANCE)
   - Lignes 955-970 dans ml_core.py (16 lignes)
   - FONCTION : Calcule une confiance calibrée à partir des probabilités brutes pour améliorer la fiabilité des scores de confiance, avec support pour différentes méthodes de calibration (isotonic, platt)
   - PARAMÈTRES :
     * probabilities - Probabilités brutes après softmax à calibrer
     * calibration_method (str, défaut='isotonic') - Méthode de calibration ('isotonic' pour régression isotonique ou 'platt' pour scaling de Platt)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉTHODE PLACEHOLDER** : Implémentation actuelle est un placeholder en attente de développement complet
     * **CALIBRATION ISOTONIC** : Méthode par défaut utilisant la régression isotonique pour calibration monotone
     * **CALIBRATION PLATT** : Alternative utilisant le Platt scaling (régression logistique) pour calibration
     * **DÉPENDANCE SKLEARN** : Nécessiterait sklearn.calibration.CalibratedClassifierCV pour implémentation complète
     * **WARNING LOGGING** : `logger.warning("Calibration de confiance non implémentée, retour des probabilités brutes")` message explicite
     * **RETOUR DIRECT** : `return probabilities` retourne probabilités sans modification
     * **TYPE HINTS COMPLETS** : `probabilities`, `calibration_method='isotonic'` paramètres avec défaut
     * **DOCSTRING COMPLÈTE** : Args et Returns avec descriptions détaillées
     * **MÉTHODE STATIQUE** : `@staticmethod` pour utilisation sans instance classe
     * **PATTERN PLACEHOLDER** : Implémentation temporaire en attente développement sklearn
     * **FONCTIONNALITÉ FUTURE** : Prévue pour améliorer significativement la fiabilité des scores de confiance
   - RETOUR : Probabilités (actuellement non calibrées, identiques à l'entrée)
   - UTILITÉ : Fonctionnalité avancée pour améliorer la fiabilité des prédictions de confiance. Essentielle pour les applications critiques nécessitant des probabilités bien calibrées. Actuellement en développement, retourne les probabilités brutes.

2. get_confidence_from_probabilities.txt (ConfidenceCalculator.get_confidence_from_probabilities - EXTRACTION CONFIANCE)
   - Lignes 800-832 dans ml_core.py (33 lignes)
   - FONCTION : Extrait la confiance pour une classe prédite spécifique à partir des probabilités, avec gestion robuste des différents formats de données et validation des indices
   - PARAMÈTRES :
     * probabilities - Tenseur PyTorch, liste ou array numpy contenant les probabilités après softmax
     * predicted_class - Indice 0-based de la classe prédite (0=Player, 1=Banker)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION INDICE** : Vérifie que predicted_class est dans la plage valide [0, 1]
     * **CORRECTION AUTOMATIQUE** : Si l'indice est invalide, le corrige avec max(0, min(1, predicted_class))
     * **WARNING LOGGING** : Enregistre un avertissement si un indice invalide est détecté
     * **DÉTECTION FORMAT** : Identifie le type de données (tenseur PyTorch 2D, 1D, liste, array numpy)
     * **TENSEUR 2D** : Pour probabilities.dim() > 1, accède avec probabilities[0][predicted_class].item()
     * **TENSEUR 1D** : Pour tenseur 1D, accède avec probabilities[predicted_class].item()
     * **LISTE/ARRAY** : Pour autres formats, utilise float(probabilities[predicted_class])
     * **EXTRACTION SÉCURISÉE** : Utilise .item() pour les tenseurs PyTorch pour conversion en float Python
     * **GESTION UNIVERSELLE** : Compatible avec tous les formats de données ML courants
   - RETOUR : float - Valeur de confiance pour la classe prédite (probabilité entre 0 et 1)
   - UTILITÉ : Méthode centrale pour extraire la confiance des prédictions ML. Essentielle pour l'évaluation de la fiabilité des modèles. Robuste aux différents formats de données et erreurs d'indices. Critique pour les systèmes de prise de décision basés sur la confiance.

3. get_confidence_from_probabilities_1.txt (get_confidence_from_probabilities - WRAPPER EXTRACTION CONFIANCE - DOUBLON)
   - Lignes 1330-1341 dans ml_core.py (12 lignes)
   - FONCTION : Fonction wrapper qui délègue l'extraction de confiance à la méthode statique ConfidenceCalculator.get_confidence_from_probabilities, fournissant une interface simplifiée
   - PARAMÈTRES :
     * probabilities - Tenseur de probabilités après softmax
     * predicted_class - Indice 0-based de la classe prédite (0 ou 1)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉLÉGATION DIRECTE** : Appelle immédiatement ConfidenceCalculator.get_confidence_from_probabilities avec les mêmes paramètres
     * **INTERFACE SIMPLIFIÉE** : Permet d'accéder à l'extraction de confiance sans référencer explicitement ConfidenceCalculator
     * **TRANSPARENCE TOTALE** : Transmet tous les paramètres sans modification ni traitement supplémentaire
     * **MÊME COMPORTEMENT** : Produit exactement le même résultat que la méthode originale de ConfidenceCalculator
   - RETOUR : float - Valeur de confiance pour la classe prédite (retour direct de ConfidenceCalculator.get_confidence_from_probabilities)
   - UTILITÉ : Fonction de commodité pour l'extraction de confiance sans nécessiter de connaître l'architecture interne. Particulièrement utile pour les utilisateurs qui veulent extraire rapidement la confiance d'une prédiction sans se soucier de l'implémentation sous-jacente.

4. train_consecutive_confidence_calculator.txt (ConfidenceCalculator.train_consecutive_confidence_calculator - CALCUL CONFIANCE RECOMMANDATIONS CONSÉCUTIVES)
   - Lignes 835-952 dans ml_core.py (118 lignes)
   - FONCTION : Calcule la confiance pour les recommandations consécutives basée sur l'analyse de motifs dans l'historique des séquences avec gestion des manches cibles
   - PARAMÈTRES :
     * sequence_history (List[str]) - Historique des résultats ('banker' ou 'player')
     * current_position (int) - Position actuelle dans la séquence
     * config (optionnel) - Configuration avec target_round_min/max et wait_threshold
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION HISTORIQUE** : `if not sequence_history or current_position < 10:` vérification suffisance données
     * **RETOUR DÉFAUT** : `return {"confidence": 0.5, "expected_consecutive": 0, ...}` si historique insuffisant
     * **DÉTECTION MANCHES CIBLES** : `is_target_round = (current_position % 10) in [0, 1, 2, 3, 4]` calcul modulo pour manches cibles
     * **RETOUR HORS CIBLES** : `if not is_target_round: return {"confidence": 0.5, ...}` confiance neutre hors manches
     * **ANALYSE PATTERNS** : Recherche patterns similaires dans historique avec fenêtre glissante et seuil similarité
     * **COMPTAGE PATTERNS** : `banker_count = sum(1 for result in similar_patterns if result == 'banker')` comptage occurrences banker
     * **CALCUL CONFIANCE** : `confidence = max(banker_count, player_count) / similar_patterns_count` proportion majoritaire
     * **EXPECTED CONSECUTIVE** : `expected_consecutive = min(5, int(confidence * 5))` calcul recommandations consécutives
     * **SEUIL WAIT** : `wait_threshold = getattr(config, 'wait_threshold', 0.65) if config else 0.65` paramètre configurable
     * **RECOMMANDATION WAIT** : `wait_recommendation = confidence < wait_threshold` logique décision binaire
     * **FORCES RECOMMANDATION** : `wait_recommendation_strength = max(0.0, wait_threshold - confidence) / wait_threshold` calcul intensité wait
     * **RETOUR COMPLET** : Dictionnaire structuré avec confidence, expected_consecutive, similar_patterns_count, success_rate, etc.
   - RETOUR : Dict[str, float] - Métriques complètes (confidence, expected_consecutive, similar_patterns_count, success_rate, is_target_round, wait_recommendation_strength, non_wait_recommendation_strength, wait_recommendation, wait_reason)
   - UTILITÉ : Méthode sophistiquée pour analyser les motifs de séquences et calculer la confiance des recommandations. Essentielle pour les systèmes de prédiction basés sur l'analyse de motifs historiques. Critique pour optimiser les stratégies de recommandation avec gestion des risques.

5. train_consecutive_confidence_calculator_1.txt (train_consecutive_confidence_calculator - WRAPPER CALCUL CONFIANCE CONSÉCUTIVE - DOUBLON)
   - Lignes 1343-1355 dans ml_core.py (13 lignes)
   - FONCTION : Fonction wrapper qui délègue le calcul de confiance consécutive à ConfidenceCalculator.train_consecutive_confidence_calculator, fournissant une interface simplifiée
   - PARAMÈTRES :
     * sequence_history - Historique des résultats (liste de 'banker' ou 'player')
     * current_position - Position actuelle dans la séquence
     * config (optionnel) - Configuration optionnelle
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉLÉGATION DIRECTE** : Appelle immédiatement ConfidenceCalculator.train_consecutive_confidence_calculator avec les mêmes paramètres
     * **INTERFACE SIMPLIFIÉE** : Permet d'accéder au calcul de confiance consécutive sans référencer explicitement ConfidenceCalculator
     * **TRANSPARENCE TOTALE** : Transmet tous les paramètres sans modification ni traitement supplémentaire
     * **MÊME COMPORTEMENT** : Produit exactement le même résultat que la méthode originale de ConfidenceCalculator
   - RETOUR : Dict[str, float] - Dictionnaire contenant les métriques de confiance (retour direct de ConfidenceCalculator.train_consecutive_confidence_calculator)
   - UTILITÉ : Fonction de commodité pour le calcul de confiance consécutive sans nécessiter de connaître l'architecture interne. Particulièrement utile pour les utilisateurs qui veulent calculer rapidement la confiance des recommandations consécutives sans se soucier de l'implémentation sous-jacente.

6. get_confidence_from_probabilities.txt (ConfidenceCalculator.get_confidence_from_probabilities - EXTRACTION CONFIANCE DEPUIS PROBABILITÉS)
   - Lignes 800-832 dans ml_core.py (33 lignes)
   - FONCTION : Extrait la confiance pour une classe prédite à partir des probabilités avec gestion robuste des formats de données et validation des indices
   - PARAMÈTRES :
     * probabilities - Tenseur de probabilités (après softmax) ou liste/array de probabilités
     * predicted_class (int) - Classe prédite (indice 0-based, 0=Player ou 1=Banker)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION INDICE** : `if predicted_class < 0 or predicted_class > 1:` contrôle bornes [0, 1]
     * **LOGGING WARNING** : `logger.warning(f"ATTENTION: Indice invalide détecté dans predicted_class: {predicted_class}")` avertissement formaté
     * **CORRECTION AUTOMATIQUE** : `predicted_class = max(0, min(1, predicted_class))` clamp entre 0 et 1
     * **GESTION TENSEUR 2D** : `if hasattr(probabilities, 'dim') and probabilities.dim() > 1: confidence = probabilities[0][predicted_class].item()` PyTorch 2D
     * **GESTION TENSEUR 1D** : `elif hasattr(probabilities, 'item'): confidence = probabilities[predicted_class].item()` PyTorch 1D
     * **GESTION ARRAY/LISTE** : `else: confidence = float(probabilities[predicted_class])` numpy/liste Python
     * **CONVERSION FLOAT** : `float()` assure type Python standard pour compatibilité
     * **DOCSTRING COMPLÈTE** : Args, Returns avec descriptions détaillées types et indices
     * **ROBUSTESSE FORMATS** : Support tenseurs PyTorch, arrays numpy, listes Python
     * **GESTION ERREURS** : Validation et correction automatique indices invalides
   - RETOUR : float - Confiance pour la classe prédite (valeur entre 0 et 1)
   - UTILITÉ : Méthode essentielle pour extraire la confiance depuis différents formats de probabilités. Permet la robustesse face aux variations de format de données. Critique pour les systèmes ML utilisant différents frameworks (PyTorch, NumPy, listes).
