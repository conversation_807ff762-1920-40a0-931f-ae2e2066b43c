# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 3672 à 3764
# Type: Méthode de la classe WaitPlacementOptimizer

    def update_with_result(self, features, recommendation, outcome, prediction):
        """
        Met à jour les statistiques après avoir observé le résultat d'une recommandation.

        Args:
            features: Features utilisées pour la recommandation
            recommendation: Recommandation faite ('player', 'banker', 'WAIT')
            outcome: R<PERSON><PERSON>at réel ('player' ou 'banker')
            prediction: Prédiction du modèle ('player' ou 'banker')
        """
        # Normaliser les entrées
        recommendation = recommendation.lower() if isinstance(recommendation, str) else recommendation
        outcome = outcome.lower() if isinstance(outcome, str) else outcome
        prediction = prediction.lower() if isinstance(prediction, str) else prediction

        # Créer une clé de pattern
        pattern_key = self._create_pattern_key(features)

        # Ajouter à l'historique
        self.pattern_history.append(pattern_key)
        self.outcome_history.append(outcome)
        self.recommendation_history.append(recommendation)

        # Limiter la taille des historiques
        if len(self.pattern_history) > self.max_pattern_history:
            self.pattern_history = self.pattern_history[-self.max_pattern_history:]
            self.outcome_history = self.outcome_history[-self.max_pattern_history:]
            self.recommendation_history = self.recommendation_history[-self.max_pattern_history:]

        # Mettre à jour les compteurs
        self.total_decisions += 1

        # Mettre à jour les statistiques de séquences consécutives
        if recommendation != 'wait':
            # C'est une recommandation NON-WAIT
            is_correct = (recommendation == outcome)

            if is_correct:
                # Recommandation NON-WAIT correcte
                self.current_consecutive_valid += 1
                self.correct_non_wait_decisions += 1

                # Mettre à jour le maximum de recommandations consécutives valides
                if self.current_consecutive_valid > self.max_consecutive_valid:
                    self.max_consecutive_valid = self.current_consecutive_valid
            else:
                # Recommandation NON-WAIT incorrecte - réinitialiser le compteur
                self.current_consecutive_valid = 0
        else:
            # C'est une recommandation WAIT
            self.total_waits += 1

            # Un WAIT est efficace si la prédiction aurait été incorrecte
            if prediction != outcome:
                self.effective_waits += 1
                self.correct_wait_decisions += 1
            else:
                self.missed_opportunities += 1

        # Calculer l'efficacité des WAIT
        wait_efficiency = self.effective_waits / self.total_waits if self.total_waits > 0 else 0
        self.wait_efficiency_history.append(wait_efficiency)

        # Calculer le ratio WAIT actuel
        wait_count = sum(1 for rec in self.recommendation_history if rec == 'wait')
        total_count = len(self.recommendation_history)
        self.current_wait_ratio = wait_count / total_count if total_count > 0 else 0

        # Mettre à jour les patterns d'erreur
        if pattern_key not in self.error_patterns:
            self.error_patterns[pattern_key] = {'total': 0, 'errors': 0}

        self.error_patterns[pattern_key]['total'] += 1
        if prediction != outcome:
            self.error_patterns[pattern_key]['errors'] += 1

        # Mettre à jour les patterns de transition
        if len(self.outcome_history) >= 2:
            last_outcomes = self.outcome_history[-2:]
            if last_outcomes[0] != last_outcomes[1]:
                # C'est une transition
                transition_key = f"{last_outcomes[0]}_{last_outcomes[1]}"

                if transition_key not in self.transition_patterns:
                    self.transition_patterns[transition_key] = {'total': 0, 'errors': 0}

                self.transition_patterns[transition_key]['total'] += 1
                if prediction != outcome:
                    self.transition_patterns[transition_key]['errors'] += 1

        # Adapter les seuils si nécessaire
        if self.adaptive_thresholds and self.total_decisions % 50 == 0:
            self._adapt_thresholds()