# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\data_manager.py
# Lignes: 192 à 291
# Type: Méthode de la classe BaccaratSequenceManager

    def prepare_data_for_model(self,
                                list_of_pb_sequences: List[List[str]]
                                ) -> Tuple[Optional[np.ndarray], Optional[np.ndarray], Optional[np.ndarray],
                                           Optional[List[List[str]]], Optional[List[int]]]:
        """
        Prépare les données X_lgbm, y, X_lstm, préfixes et origines agrégées
        à partir d'une liste de séquences P/B, en appliquant le filtrage
        par 'min_target_hand_index' via la génération de features.

        Args:
            list_of_pb_sequences (list): Liste de séquences P/B (chaque séquence est une List[str]).

        Returns:
            tuple: Un tuple contenant 5 éléments:
                   - X_lgbm (np.ndarray): Shape (nb_samples, nb_features_lgbm) ou None.
                   - y (np.ndarray): Shape (nb_samples,) ou None.
                   - X_lstm (np.ndarray): Shape (nb_samples, lstm_seq_len, lstm_features) ou None.
                   - list_of_prefixes (List[List[str]]): Liste des séquences préfixes correspondantes ou None.
                   - list_of_origins (List[int]): Liste des indices d'origine globaux correspondants ou None.
                   Retourne (None, None, None, None, None) si aucune donnée valide n'est générée.
        """
        all_X_lgbm, all_y, all_X_lstm, all_prefixes, all_origins = [], [], [], [], []
        current_global_offset = 0
        num_games = len(list_of_pb_sequences)

        self.logger.info(f"Manager: Préparation données filtrées depuis {num_games} séquences P/B...")

        for game_idx, shoe_seq in enumerate(list_of_pb_sequences):
            if not isinstance(shoe_seq, list):
                self.logger.warning(f"Manager: Élément {game_idx} n'est pas une liste, ignoré.")
                continue

            # Générer les données filtrées pour ce sabot
            try:
                X_lgbm_shoe, y_shoe, X_lstm_shoe, prefix_shoe, origin_shoe = self._generate_filtered_data_for_shoe(
                    shoe_seq,
                    current_global_offset
                )
                # Ajouter les résultats aux listes globales
                all_X_lgbm.extend(X_lgbm_shoe)
                all_y.extend(y_shoe)
                all_X_lstm.extend(X_lstm_shoe)
                all_prefixes.extend(prefix_shoe)
                all_origins.extend(origin_shoe)

            except Exception as e_gen:
                self.logger.error(f"Manager: Erreur génération données sabot {game_idx}: {e_gen}", exc_info=True)
                # Continuer avec le sabot suivant

            # Mettre à jour l'offset global pour le prochain sabot
            # L'offset est le début du *prochain* sabot, donc nombre de coups total du sabot actuel
            current_global_offset += len(shoe_seq)

        # --- Conversion finale en tableaux NumPy ---
        if not all_y: # Si aucune donnée n'a été générée/conservée après filtrage
            self.logger.warning("Manager: Aucune donnée valide générée après filtrage et génération.")
            return None, None, None, None, None

        try:
            X_lgbm_np = np.array(all_X_lgbm, dtype=np.float64) # Utiliser float64 pour LGBM
            y_np = np.array(all_y, dtype=np.int64)
            X_lstm_np = np.stack(all_X_lstm, axis=0).astype(np.float32) # LSTM float32 OK

            # list_of_prefixes et list_of_origins sont déjà des listes
            list_of_prefixes_final = all_prefixes
            list_of_origins_final = all_origins

            # --- Vérifications finales des tailles ---
            num_samples_final = len(y_np)
            if not (X_lgbm_np.shape[0] == num_samples_final and
                    X_lstm_np.shape[0] == num_samples_final and
                    len(list_of_prefixes_final) == num_samples_final and
                    len(list_of_origins_final) == num_samples_final):
                 raise ValueError(f"Incohérence taille finale! y:{num_samples_final}, "
                                  f"X_lgbm:{X_lgbm_np.shape[0]}, X_lstm:{X_lstm_np.shape[0]}, "
                                  f"prefixes:{len(list_of_prefixes_final)}, origins:{len(list_of_origins_final)}")

            # Vérifier que les étiquettes sont bien des indices 0-based (0 = Player, 1 = Banker)
            unique_labels = np.unique(y_np)
            if not np.all(np.isin(unique_labels, [0, 1])):
                self.logger.error(f"ERREUR CRITIQUE: Les étiquettes contiennent des valeurs invalides: {unique_labels}. "
                                 f"Doivent être uniquement 0 (Player) ou 1 (Banker).")
                raise ValueError(f"Étiquettes invalides détectées: {unique_labels}. Doivent être 0 ou 1.")

            self.logger.info(f"Manager: Préparation données filtrées terminée.")
            self.logger.info(f"  - Nombre total d'échantillons générés/filtrés : {num_samples_final}")
            self.logger.info(f"  - Shape X_lgbm: {X_lgbm_np.shape}")
            self.logger.info(f"  - Shape y: {y_np.shape}")
            self.logger.info(f"  - Shape X_lstm: {X_lstm_np.shape}")
            self.logger.info(f"  - Nombre Préfixes: {len(list_of_prefixes_final)}")
            self.logger.info(f"  - Nombre Origines: {len(list_of_origins_final)}")

            return X_lgbm_np, y_np, X_lstm_np, list_of_prefixes_final, list_of_origins_final

        except ValueError as ve:
            self.logger.error(f"Manager: Erreur valeur pendant conversion/vérif NumPy finale: {ve}", exc_info=True)
            return None, None, None, None, None
        except Exception as e_np:
            self.logger.error(f"Manager: Erreur inattendue pendant conversion NumPy finale: {e_np}", exc_info=True)
            return None, None, None, None, None