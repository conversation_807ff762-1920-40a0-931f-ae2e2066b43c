# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 321 à 341
# Type: Méthode de la classe ModuleInterface

    def get_instance(self, name: str) -> Any:
        """
        Récupère une instance enregistrée dans l'interface.

        Args:
            name: Nom de l'instance à récupérer

        Returns:
            L'instance enregistrée

        Raises:
            KeyError: Si l'instance n'est pas enregistrée
        """
        if name not in self._instances:
            if name in self._lazy_loaders:
                self._lazy_loaders[name]()  # Exécuter le chargeur paresseux

            if name not in self._instances:
                raise KeyError(f"Instance '{name}' non enregistrée dans l'interface")

        return self._instances[name]