# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 7362 à 7407
# Type: Méthode de la classe OptunaOptimizer

        def prepare_data_for_next_phase():
            logger.warning(f"Préparation des données pour la phase {phase_to} avec parallélisation optimisée")

            # Sous-tâches pour la préparation des données
            def prepare_lgbm_features():
                return self.X_lgbm_full[subset_indices]

            def prepare_lstm_features():
                return self.X_lstm_full[subset_indices]

            def prepare_targets():
                return self.y_full[subset_indices]

            # Utiliser un pool de threads interne pour paralléliser la préparation des données
            with concurrent.futures.ThreadPoolExecutor(max_workers=3) as data_executor:
                future_lgbm = data_executor.submit(prepare_lgbm_features)
                future_lstm = data_executor.submit(prepare_lstm_features)
                future_y = data_executor.submit(prepare_targets)

                # Attendre que toutes les sous-tâches soient terminées
                concurrent.futures.wait([future_lgbm, future_lstm, future_y])

                # Récupérer les résultats
                X_lgbm_subset = future_lgbm.result()
                X_lstm_subset = future_lstm.result()
                y_subset = future_y.result()

            logger.warning(f"Préparation des données terminée: {len(subset_indices)} exemples traités")

            # Mettre en cache les features
            self._cache_features(subset_indices, X_lgbm_subset, X_lstm_subset, y_subset)

            # Journaliser des informations sur l'apprentissage par curriculum
            if phase_to == 1 or phase_to == 2 or phase_to == 3 or phase_to == 'markov':
                # Calculer la difficulté moyenne des exemples (si applicable)
                if hasattr(self, 'difficulty_scores') and self.difficulty_scores is not None:
                    subset_difficulties = self.difficulty_scores[subset_indices]
                    avg_difficulty = np.mean(subset_difficulties)
                    logger.info(f"Apprentissage par curriculum activé: {len(subset_indices)} exemples avec difficulté moyenne {avg_difficulty:.4f}")

                # Si nous passons à la phase Markov, préparer des données spécifiques pour Markov
                if phase_to == 'markov':
                    logger.warning(f"Préparation des données spécifiques pour la phase Markov")
                    # Ici, nous pourrions ajouter une préparation spécifique pour Markov si nécessaire

            return (X_lgbm_subset.shape, X_lstm_subset.shape, y_subset.shape)