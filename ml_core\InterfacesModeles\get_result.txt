# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1208 à 1228
# Type: Méthode de la classe TrainOptimizeInterface

    def get_result(self, model_name: str, metric_name: str) -> float:
        """
        Récupère un résultat pour un modèle et une métrique.

        Args:
            model_name: Nom du modèle
            metric_name: Nom de la métrique

        Returns:
            Valeur du résultat

        Raises:
            KeyError: Si le résultat n'est pas défini pour ce modèle et cette métrique
        """
        if model_name not in self.results:
            raise KeyError(f"Résultats non définis pour le modèle '{model_name}'")

        if metric_name not in self.results[model_name]:
            raise KeyError(f"Résultat non défini pour la métrique '{metric_name}' du modèle '{model_name}'")

        return self.results[model_name][metric_name]