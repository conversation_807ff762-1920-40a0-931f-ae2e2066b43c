# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 288 à 308
# Type: Méthode de la classe ModuleInterface

    def get_class(self, name: str) -> Type:
        """
        Récupère une classe enregistrée dans l'interface.

        Args:
            name: Nom de la classe à récupérer

        Returns:
            La classe enregistrée

        Raises:
            KeyError: Si la classe n'est pas enregistrée
        """
        if name not in self._classes:
            if name in self._lazy_loaders:
                self._lazy_loaders[name]()  # Exécuter le chargeur paresseux

            if name not in self._classes:
                raise KeyError(f"Classe '{name}' non enregistrée dans l'interface")

        return self._classes[name]