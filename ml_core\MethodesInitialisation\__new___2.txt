# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 230 à 234
# Type: Méthode de la classe ModuleInterface
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ModuleInterface, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance