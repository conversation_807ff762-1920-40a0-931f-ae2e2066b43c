DESCRIPTIF DÉTAILLÉ DES MÉTHODES - UTILITAIRES INTERNES
================================================================================

Ce fichier contient la description détaillée des méthodes utilitaires internes.

ÉTAT DOCUMENTATION : PREMIÈRE VAGUE EN COURS
- **Couverture** : En cours de documentation
- **Niveau** : Documentation fonctionnelle standardisée
- **Qualité** : 15-25 lignes par méthode minimum

DOMAINE FONCTIONNEL : UTILITAIRES INTERNES
- Fonctions d'aide et outils internes
- Utilitaires de support
- Fonctions auxiliaires
- Helpers et assistants

MÉTHODES DOCUMENTÉES :
================================================================================

1. get_instance.txt (ModuleInterface.get_instance - RÉCUPÉRATION INSTANCE ENREGISTRÉE)
   - Lignes 321-341 dans ml_core.py (21 lignes)
   - FONCTION : Récupère une instance enregistrée dans l'interface avec support pour le chargement paresseux et gestion d'erreurs appropriée
   - PARAMÈTRES :
     * self - Instance de ModuleInterface
     * name (str) - Nom de l'instance à récupérer depuis le registre
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION PRIMAIRE** : `if name not in self._instances:` contrôle existence directe dans dictionnaire
     * **CHARGEMENT PARESSEUX CONDITIONNEL** : `if name in self._lazy_loaders:` vérifie disponibilité loader différé
     * **EXÉCUTION LOADER** : `self._lazy_loaders[name]()` appelle fonction de chargement sans paramètres
     * **DOUBLE VÉRIFICATION POST-LOADER** : `if name not in self._instances:` contrôle à nouveau après tentative de chargement
     * **EXCEPTION EXPLICITE** : `raise KeyError(f"Instance '{name}' non enregistrée dans l'interface")` avec message formaté
     * **RETOUR SÉCURISÉ** : `return self._instances[name]` accès direct au dictionnaire après validation
     * **PATTERN LAZY LOADING** : Séquence if-check → lazy-load → if-check → error/return pour optimisation mémoire
     * **THREAD-SAFETY** : Accès atomique aux dictionnaires self._instances et self._lazy_loaders
     * **TYPE HINTS** : `name: str` et `-> Any` pour validation statique et documentation
     * **DOCSTRING COMPLÈTE** : Args, Returns, Raises avec descriptions détaillées
   - RETOUR : Any - L'instance enregistrée (type dépend de l'instance stockée)
   - RAISES : KeyError - Si l'instance n'est pas enregistrée et aucun loader paresseux disponible
   - UTILITÉ : Méthode essentielle pour accéder aux instances enregistrées avec chargement paresseux. Permet l'optimisation mémoire et la gestion flexible des dépendances. Critique pour les systèmes modulaires avec chargement à la demande.

2. register_instance.txt (ModuleInterface.register_instance - ENREGISTREMENT INSTANCE)
   - Lignes 310-319 dans ml_core.py (10 lignes)
   - FONCTION : Enregistre une instance dans l'interface avec un nom unique pour permettre la récupération ultérieure et la gestion centralisée des instances
   - PARAMÈTRES :
     * self - Instance de ModuleInterface
     * name (str) - Nom unique de l'instance pour identification et récupération
     * instance (Any) - Instance à enregistrer (peut être n'importe quel type d'objet)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE ATOMIQUE** : `self._instances[name] = instance` assignation directe dans dictionnaire
     * **ÉCRASEMENT SILENCIEUX** : Si name existe déjà, remplace l'instance précédente sans avertissement
     * **LOGGING DEBUG** : `logger.debug(f"Instance '{name}' enregistrée dans l'interface")` avec f-string formatée
     * **TYPE HINTS COMPLETS** : `name: str`, `instance: Any`, `-> None` pour validation statique
     * **GESTION UNIVERSELLE** : Any accepte classes, fonctions, objets, primitives, collections
     * **RÉFÉRENCE DIRECTE** : Stocke pointeur vers instance originale, pas de copie/clone
     * **THREAD-SAFETY** : Opération atomique sur dictionnaire Python (GIL protection)
     * **DOCSTRING STANDARD** : Args avec descriptions pour name et instance
     * **PATTERN REGISTRY** : Implémente registre centralisé avec clé-valeur simple
     * **PERFORMANCE** : O(1) pour insertion dans dictionnaire hash
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour construire un registre d'instances centralisé. Permet la gestion de multiples instances avec accès par nom. Essentielle pour les systèmes modulaires utilisant l'injection de dépendances et le pattern Registry.

3. get_class.txt (ModuleInterface.get_class - RÉCUPÉRATION CLASSE ENREGISTRÉE)
   - Lignes 288-308 dans ml_core.py (21 lignes)
   - FONCTION : Récupère une classe enregistrée dans l'interface avec support pour le chargement paresseux et gestion d'erreurs appropriée
   - PARAMÈTRES :
     * self - Instance de ModuleInterface
     * name (str) - Nom de la classe à récupérer depuis le registre
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION PRIMAIRE** : `if name not in self._classes:` contrôle existence directe dans dictionnaire
     * **CHARGEMENT PARESSEUX CONDITIONNEL** : `if name in self._lazy_loaders:` vérifie disponibilité loader différé
     * **EXÉCUTION LOADER** : `self._lazy_loaders[name]()` appelle fonction de chargement sans paramètres
     * **DOUBLE VÉRIFICATION POST-LOADER** : `if name not in self._classes:` contrôle à nouveau après tentative de chargement
     * **EXCEPTION EXPLICITE** : `raise KeyError(f"Classe '{name}' non enregistrée dans l'interface")` avec message formaté
     * **RETOUR SÉCURISÉ** : `return self._classes[name]` accès direct au dictionnaire après validation
     * **PATTERN LAZY LOADING** : Séquence if-check → lazy-load → if-check → error/return identique à get_instance
     * **TYPE HINTS COMPLETS** : `name: str` et `-> Type` pour validation statique et documentation
     * **THREAD-SAFETY** : Accès atomique aux dictionnaires self._classes et self._lazy_loaders
     * **DOCSTRING COMPLÈTE** : Args, Returns, Raises avec descriptions détaillées
   - RETOUR : Type - La classe enregistrée (type Python)
   - RAISES : KeyError - Si la classe n'est pas enregistrée et aucun loader paresseux disponible
   - UTILITÉ : Méthode essentielle pour accéder aux classes enregistrées avec chargement paresseux. Permet l'optimisation mémoire et la gestion flexible des dépendances de classes. Critique pour les systèmes modulaires avec chargement dynamique de classes.

4. register_class.txt (ModuleInterface.register_class - ENREGISTREMENT CLASSE)
   - Lignes 277-286 dans ml_core.py (10 lignes)
   - FONCTION : Enregistre une classe dans l'interface avec un nom unique pour permettre la récupération ultérieure et la gestion centralisée des classes
   - PARAMÈTRES :
     * self - Instance de ModuleInterface
     * name (str) - Nom unique de la classe pour identification et récupération
     * cls (Type) - Classe Python à enregistrer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE ATOMIQUE** : `self._classes[name] = cls` assignation directe dans dictionnaire
     * **ÉCRASEMENT SILENCIEUX** : Si name existe déjà, remplace la classe précédente sans avertissement
     * **LOGGING DEBUG** : `logger.debug(f"Classe '{name}' enregistrée dans l'interface")` avec f-string formatée
     * **TYPE HINTS COMPLETS** : `name: str`, `cls: Type`, `-> None` pour validation statique
     * **GESTION TYPES** : Type accepte classes, métaclasses, types génériques, ABC
     * **RÉFÉRENCE DIRECTE** : Stocke pointeur vers classe originale, pas de copie/clone
     * **THREAD-SAFETY** : Opération atomique sur dictionnaire Python (GIL protection)
     * **DOCSTRING STANDARD** : Args avec descriptions pour name et cls
     * **PATTERN REGISTRY** : Implémente registre centralisé avec clé-valeur simple
     * **PERFORMANCE** : O(1) pour insertion dans dictionnaire hash
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour construire un registre de classes centralisé. Permet la gestion de multiples classes avec accès par nom. Essentielle pour les systèmes modulaires utilisant l'injection de dépendances et le pattern Registry pour les classes.

5. get_function.txt (ModuleInterface.get_function - RÉCUPÉRATION FONCTION ENREGISTRÉE)
   - Lignes 255-275 dans ml_core.py (21 lignes)
   - FONCTION : Récupère une fonction enregistrée dans l'interface avec support pour le chargement paresseux et gestion d'erreurs appropriée
   - PARAMÈTRES :
     * self - Instance de ModuleInterface
     * name (str) - Nom de la fonction à récupérer depuis le registre
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION PRIMAIRE** : `if name not in self._functions:` contrôle existence directe dans dictionnaire
     * **CHARGEMENT PARESSEUX CONDITIONNEL** : `if name in self._lazy_loaders:` vérifie disponibilité loader différé
     * **EXÉCUTION LOADER** : `self._lazy_loaders[name]()` appelle fonction de chargement sans paramètres
     * **DOUBLE VÉRIFICATION POST-LOADER** : `if name not in self._functions:` contrôle à nouveau après tentative de chargement
     * **EXCEPTION EXPLICITE** : `raise KeyError(f"Fonction '{name}' non enregistrée dans l'interface")` avec message formaté
     * **RETOUR SÉCURISÉ** : `return self._functions[name]` accès direct au dictionnaire après validation
     * **PATTERN LAZY LOADING** : Séquence if-check → lazy-load → if-check → error/return identique aux autres get_*
     * **TYPE HINTS COMPLETS** : `name: str` et `-> Callable` pour validation statique et documentation
     * **THREAD-SAFETY** : Accès atomique aux dictionnaires self._functions et self._lazy_loaders
     * **DOCSTRING COMPLÈTE** : Args, Returns, Raises avec descriptions détaillées
   - RETOUR : Callable - La fonction enregistrée (objet callable)
   - RAISES : KeyError - Si la fonction n'est pas enregistrée et aucun loader paresseux disponible
   - UTILITÉ : Méthode essentielle pour accéder aux fonctions enregistrées avec chargement paresseux. Permet l'optimisation mémoire et la gestion flexible des dépendances de fonctions. Critique pour les systèmes modulaires avec chargement dynamique de fonctions.

6. register_function.txt (ModuleInterface.register_function - ENREGISTREMENT FONCTION)
   - Lignes 244-253 dans ml_core.py (10 lignes)
   - FONCTION : Enregistre une fonction dans l'interface avec un nom unique pour permettre la récupération ultérieure et la gestion centralisée des fonctions
   - PARAMÈTRES :
     * self - Instance de ModuleInterface
     * name (str) - Nom unique de la fonction pour identification et récupération
     * function (Callable) - Fonction Python à enregistrer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE ATOMIQUE** : `self._functions[name] = function` assignation directe dans dictionnaire
     * **ÉCRASEMENT SILENCIEUX** : Si name existe déjà, remplace la fonction précédente sans avertissement
     * **LOGGING DEBUG** : `logger.debug(f"Fonction '{name}' enregistrée dans l'interface")` avec f-string formatée
     * **TYPE HINTS COMPLETS** : `name: str`, `function: Callable`, `-> None` pour validation statique
     * **GESTION CALLABLES** : Callable accepte fonctions, méthodes, lambdas, classes avec __call__
     * **RÉFÉRENCE DIRECTE** : Stocke pointeur vers fonction originale, pas de copie/clone
     * **THREAD-SAFETY** : Opération atomique sur dictionnaire Python (GIL protection)
     * **DOCSTRING STANDARD** : Args avec descriptions pour name et function
     * **PATTERN REGISTRY** : Implémente registre centralisé avec clé-valeur simple
     * **PERFORMANCE** : O(1) pour insertion dans dictionnaire hash
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour construire un registre de fonctions centralisé. Permet la gestion de multiples fonctions avec accès par nom. Essentielle pour les systèmes modulaires utilisant l'injection de dépendances et le pattern Registry pour les fonctions.

7. get_hbp_instance.txt (ModelProvider.get_hbp_instance - RÉCUPÉRATION INSTANCE HBP SINGLETON)
   - Lignes 649-746 dans ml_core.py (98 lignes)
   - FONCTION : Implémente le pattern Singleton pour HybridBaccaratPredictor avec configuration avancée, initialisation complète et gestion thread-safe
   - PARAMÈTRES :
     * cls - Référence à la classe ModelProvider (méthode de classe)
     * trial_id (optionnel) - Identifiant de l'essai Optuna pour collecteur de statistiques
     * config (optionnel) - Configuration à utiliser pour l'instance HBP
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION INSTANCE** : Contrôle si cls._hbp_instance existe déjà pour éviter la recréation
     * **MISE À JOUR CONFIG** : Si config fournie et différente, met à jour les attributs de configuration existants
     * **THREAD-SAFETY** : Utilise cls._hbp_instance_lock pour éviter les problèmes de concurrence
     * **DOUBLE-CHECK LOCKING** : Vérifie à nouveau l'instance après acquisition du verrou
     * **IMPORTATION CONDITIONNELLE** : Importe HybridBaccaratPredictor seulement quand nécessaire pour éviter les cycles
     * **CONFIGURATION DÉFAUT** : Crée PredictorConfig() si aucune configuration fournie
     * **DÉSACTIVATION AUTO-UPDATE** : Désactive auto_update_enabled pour stabilité pendant optimisation
     * **COLLECTEUR STATS** : Initialise OptimizationStatsCollector si disponible pour trial_id
     * **FLAG OPTUNA** : Définit is_optuna_running=True pour indiquer phase d'optimisation
     * **INITIALISATION SÉQUENCE** : Crée sequence=[] vide pour éviter les erreurs
     * **MODÈLE MARKOV** : Initialise PersistentMarkov avec paramètres de configuration validés
     * **GESTION ERREURS** : Capture exceptions Markov et désactive use_markov_model si échec
   - RETOUR : HybridBaccaratPredictor - L'instance unique configurée et initialisée
   - UTILITÉ : Méthode centrale pour accès singleton HBP avec configuration complète. Essentielle pour optimisation Optuna et gestion centralisée des instances. Critique pour éviter les conflits de ressources et garantir la cohérence.

8. register_lazy_loader.txt (ModuleInterface.register_lazy_loader - ENREGISTREMENT CHARGEUR PARESSEUX)
   - Lignes 343-352 dans ml_core.py (10 lignes)
   - FONCTION : Enregistre un chargeur paresseux qui sera exécuté uniquement lorsque la dépendance sera demandée, implémentant le pattern Lazy Loading pour optimisation mémoire
   - PARAMÈTRES :
     * self - Instance de ModuleInterface
     * name (str) - Nom de la dépendance à charger paresseusement
     * loader (Callable) - Fonction qui charge la dépendance à la demande
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE LOADER** : Enregistre le chargeur dans self._lazy_loaders[name] avec le nom comme clé
     * **EXÉCUTION DIFFÉRÉE** : Le loader ne sera appelé que lors de la première demande de la dépendance
     * **ÉCRASEMENT POSSIBLE** : Si un loader avec le même nom existe, il sera remplacé silencieusement
     * **LOGGING DEBUG** : Enregistre un message de debug confirmant l'enregistrement du chargeur paresseux
     * **PATTERN LAZY LOADING** : Implémente le chargement paresseux pour optimisation mémoire et performance
     * **FONCTION CALLABLE** : Accepte toute fonction callable qui peut charger la dépendance
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour implémenter le chargement paresseux de dépendances. Permet l'optimisation mémoire en chargeant les ressources seulement quand nécessaire. Essentielle pour les systèmes modulaires avec dépendances lourdes ou optionnelles.

9. load_from_python_file.txt (ConfigManager.load_from_python_file - CHARGEMENT CONFIGURATION FICHIER PYTHON)
   - Lignes 56-88 dans ml_core.py (33 lignes)
   - FONCTION : Charge la configuration depuis un fichier Python en extrayant les attributs publics et en les stockant dans un namespace spécifique
   - PARAMÈTRES :
     * self - Instance de ConfigManager
     * file_path (str) - Chemin vers le fichier Python contenant la configuration
     * namespace (str, défaut='default') - Espace de noms pour organiser la configuration
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CRÉATION SPEC** : `spec = importlib.util.spec_from_file_location("config_module", file_path)` création spécification module
     * **INSTANCIATION MODULE** : `config_module = importlib.util.module_from_spec(spec)` création objet module
     * **EXÉCUTION MODULE** : `spec.loader.exec_module(config_module)` exécution code Python du fichier
     * **EXTRACTION ATTRIBUTS** : `config_dict = {key: value for key, value in vars(config_module).items() if not key.startswith('_') and not callable(value)}` filtrage attributs publics
     * **STOCKAGE CONFIG** : `self._config[namespace] = config_dict` assignation dans dictionnaire configuration
     * **TRAÇABILITÉ SOURCE** : `self._config_sources[namespace] = file_path` stockage chemin source pour debugging
     * **LOGGING SUCCÈS** : `logger.info(f"Configuration chargée depuis {file_path} dans l'espace de noms '{namespace}'")` confirmation
     * **GESTION ERREURS** : `try-except` capture toutes exceptions avec logging détaillé
     * **TYPE HINTS** : `file_path: str`, `namespace: str = 'default'`, `-> bool` validation statique
     * **RETOUR SUCCÈS** : `return True` en cas de succès, `return False` en cas d'erreur
   - RETOUR : bool - True si le chargement a réussi, False en cas d'erreur
   - UTILITÉ : Méthode avancée pour charger la configuration depuis des fichiers Python. Permet la configuration dynamique et flexible. Essentielle pour les systèmes nécessitant des configurations complexes avec logique Python.

10. get_metric.txt (TrainOptimizeInterface.get_metric - RÉCUPÉRATION MÉTRIQUE ENREGISTRÉE)
   - Lignes 1146-1162 dans ml_core.py (17 lignes)
   - FONCTION : Récupère une métrique enregistrée dans l'interface avec validation d'existence et gestion d'erreurs appropriée
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * name (str) - Nom de la métrique à récupérer depuis le registre
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION EXISTENCE** : `if name not in self.metrics:` contrôle existence directe dans dictionnaire
     * **EXCEPTION EXPLICITE** : `raise KeyError(f"Métrique '{name}' non enregistrée dans l'interface")` avec message formaté
     * **RETOUR SÉCURISÉ** : `return self.metrics[name]` accès direct au dictionnaire après validation
     * **TYPE HINTS COMPLETS** : `name: str` et `-> Any` pour validation statique et documentation
     * **DOCSTRING COMPLÈTE** : Args, Returns, Raises avec descriptions détaillées
     * **THREAD-SAFETY** : Accès atomique au dictionnaire self.metrics (GIL protection)
     * **PERFORMANCE** : O(1) pour vérification et récupération dans dictionnaire hash
     * **GESTION ERREURS** : Message d'erreur explicite incluant nom métrique pour debugging
   - RETOUR : Any - La métrique enregistrée (type dépend de la métrique stockée)
   - RAISES : KeyError - Si la métrique demandée n'est pas enregistrée dans l'interface
   - UTILITÉ : Méthode essentielle pour accéder aux métriques enregistrées dans le système. Permet la récupération sécurisée avec validation d'existence. Critique pour les systèmes d'évaluation utilisant des métriques personnalisées.

11. register_metric.txt (TrainOptimizeInterface.register_metric - ENREGISTREMENT MÉTRIQUE)
   - Lignes 1135-1144 dans ml_core.py (10 lignes)
   - FONCTION : Enregistre une métrique dans l'interface avec un nom unique pour permettre la récupération ultérieure et la gestion centralisée des métriques
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * name (str) - Nom unique de la métrique pour identification et récupération
     * metric (Any) - Métrique à enregistrer (peut être une fonction, classe ou objet métrique)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE ATOMIQUE** : `self.metrics[name] = metric` assignation directe dans dictionnaire
     * **ÉCRASEMENT SILENCIEUX** : Si name existe déjà, remplace la métrique précédente sans avertissement
     * **LOGGING DEBUG** : `logger.debug(f"Métrique '{name}' enregistrée dans l'interface")` avec f-string formatée
     * **TYPE HINTS COMPLETS** : `name: str`, `metric: Any`, `-> None` pour validation statique
     * **GESTION UNIVERSELLE** : Any accepte fonctions, classes, objets métriques, callables
     * **RÉFÉRENCE DIRECTE** : Stocke pointeur vers métrique originale, pas de copie/clone
     * **THREAD-SAFETY** : Opération atomique sur dictionnaire Python (GIL protection)
     * **DOCSTRING STANDARD** : Args avec descriptions pour name et metric
     * **PATTERN REGISTRY** : Implémente registre centralisé avec clé-valeur simple
     * **PERFORMANCE** : O(1) pour insertion dans dictionnaire hash
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour construire un registre de métriques centralisé. Permet la gestion de multiples métriques avec accès par nom. Essentielle pour les systèmes d'évaluation utilisant des métriques personnalisées et configurables.

12. get_result_1.txt (ThreadedTrainer.get_result - RÉCUPÉRATION RÉSULTAT ENTRAÎNEMENT - HOMONYME)
   - Lignes 1564-1571 dans ml_core.py (8 lignes)
   - FONCTION : Récupère le résultat de l'entraînement threadé pour inspection et analyse des performances
   - PARAMÈTRES :
     * self - Instance de ThreadedTrainer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RETOUR DIRECT** : Retourne self.result sans traitement supplémentaire
     * **ÉTAT RÉSULTAT** : Contient le résultat de l'entraînement ou None si non terminé/échoué
     * **INSPECTION** : Permet l'inspection du résultat pour analyse et debugging
     * **GETTER SIMPLE** : Méthode getter pure sans effets de bord
     * **THREAD-SAFE** : Lecture atomique d'une référence d'objet
   - RETOUR : Any ou None - Le résultat de l'entraînement ou None si l'entraînement n'est pas terminé ou a échoué
   - UTILITÉ : Méthode essentielle pour récupérer les résultats d'entraînement threadé. Permet l'inspection des performances et l'analyse des résultats. Critique pour les interfaces utilisateur affichant les résultats et les systèmes d'analyse de performance.

13. get_result_2.txt (ThreadedOptimizer.get_result - RÉCUPÉRATION RÉSULTAT OPTIMISATION - HOMONYME)
   - Lignes 1704-1711 dans ml_core.py (8 lignes)
   - FONCTION : Récupère le résultat de l'optimisation threadée pour inspection et analyse des hyperparamètres optimaux
   - PARAMÈTRES :
     * self - Instance de ThreadedOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **RETOUR DIRECT** : Retourne self.result sans traitement supplémentaire
     * **ÉTAT RÉSULTAT** : Contient le résultat de l'optimisation ou None si non terminée/échouée
     * **INSPECTION** : Permet l'inspection du résultat pour analyse et debugging
     * **GETTER SIMPLE** : Méthode getter pure sans effets de bord
     * **THREAD-SAFE** : Lecture atomique d'une référence d'objet
   - RETOUR : Any ou None - Le résultat de l'optimisation ou None si l'optimisation n'est pas terminée ou a échoué
   - UTILITÉ : Méthode essentielle pour récupérer les résultats d'optimisation threadée. Permet l'inspection des hyperparamètres optimaux et l'analyse des résultats. Critique pour les interfaces utilisateur affichant les résultats d'optimisation et les systèmes d'analyse de performance.

14. get_hyperparameters.txt (TrainOptimizeInterface.get_hyperparameters - RÉCUPÉRATION HYPERPARAMÈTRES MODÈLE)
   - Lignes 1175-1191 dans ml_core.py (17 lignes)
   - FONCTION : Récupère les hyperparamètres pour un modèle spécifique avec validation d'existence et gestion d'erreurs appropriée
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * model_name (str) - Nom du modèle pour lequel récupérer les hyperparamètres
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION EXISTENCE** : Contrôle si model_name existe dans self.hyperparameters
     * **GESTION ERREUR** : Lève KeyError avec message explicite si les hyperparamètres ne sont pas définis
     * **MESSAGE EXPLICITE** : Inclut le nom du modèle dans le message d'erreur pour debugging
     * **RÉCUPÉRATION DIRECTE** : Retourne self.hyperparameters[model_name] après validation
     * **VALIDATION PRÉALABLE** : Assure que seuls les hyperparamètres valides et définis sont retournés
   - RETOUR : Dict[str, Any] - Dictionnaire des hyperparamètres pour le modèle spécifié
   - RAISES : KeyError - Si les hyperparamètres ne sont pas définis pour le modèle demandé
   - UTILITÉ : Méthode essentielle pour accéder aux hyperparamètres de modèles dans le système. Permet la récupération sécurisée avec validation. Critique pour les systèmes d'optimisation et de configuration de modèles ML.

15. set_hyperparameters.txt (TrainOptimizeInterface.set_hyperparameters - DÉFINITION HYPERPARAMÈTRES MODÈLE)
   - Lignes 1164-1173 dans ml_core.py (10 lignes)
   - FONCTION : Définit les hyperparamètres pour un modèle spécifique avec stockage centralisé et logging des modifications
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * model_name (str) - Nom du modèle pour lequel définir les hyperparamètres
     * hyperparameters (Dict[str, Any]) - Dictionnaire des hyperparamètres à stocker
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE ATOMIQUE** : `self.hyperparameters[model_name] = hyperparameters` assignation directe dans dictionnaire
     * **ÉCRASEMENT SILENCIEUX** : Si hyperparamètres existent déjà pour ce modèle, ils seront remplacés sans avertissement
     * **LOGGING DEBUG** : `logger.debug(f"Hyperparamètres définis pour le modèle '{model_name}'")` avec f-string formatée
     * **TYPE HINTS COMPLETS** : `model_name: str`, `hyperparameters: Dict[str, Any]`, `-> None` validation statique
     * **GESTION UNIVERSELLE** : Dict[str, Any] accepte tous types d'hyperparamètres (int, float, str, bool, listes)
     * **RÉFÉRENCE DIRECTE** : Stocke pointeur vers dictionnaire original, pas de copie/clone
     * **THREAD-SAFETY** : Opération atomique sur dictionnaire Python (GIL protection)
     * **DOCSTRING STANDARD** : Args avec descriptions pour model_name et hyperparameters
     * **PATTERN REGISTRY** : Implémente registre centralisé avec clé-valeur simple
     * **PERFORMANCE** : O(1) pour insertion dans dictionnaire hash
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour configurer les hyperparamètres de modèles dans le système. Permet la gestion centralisée des configurations de modèles. Essentielle pour les systèmes d'optimisation et de configuration dynamique de modèles ML.

16. get_data_source.txt (TrainOptimizeInterface.get_data_source - RÉCUPÉRATION SOURCE DONNÉES)
   - Lignes 1117-1133 dans ml_core.py (17 lignes)
   - FONCTION : Récupère une source de données enregistrée dans l'interface avec validation d'existence et gestion d'erreurs appropriée
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * name (str) - Nom de la source de données à récupérer depuis le registre
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION EXISTENCE** : `if name not in self.data_sources:` contrôle existence directe dans dictionnaire
     * **EXCEPTION EXPLICITE** : `raise KeyError(f"Source de données '{name}' non enregistrée dans l'interface")` avec message formaté
     * **RETOUR SÉCURISÉ** : `return self.data_sources[name]` accès direct au dictionnaire après validation
     * **TYPE HINTS COMPLETS** : `name: str` et `-> Any` pour validation statique et documentation
     * **DOCSTRING COMPLÈTE** : Args, Returns, Raises avec descriptions détaillées
     * **THREAD-SAFETY** : Accès atomique au dictionnaire self.data_sources (GIL protection)
     * **PERFORMANCE** : O(1) pour vérification et récupération dans dictionnaire hash
     * **GESTION ERREURS** : Message d'erreur explicite incluant nom source pour debugging
     * **PATTERN REGISTRY** : Accès sécurisé aux sources de données enregistrées par nom
   - RETOUR : Any - La source de données enregistrée (type dépend de la source stockée)
   - RAISES : KeyError - Si la source de données demandée n'est pas enregistrée dans l'interface
   - UTILITÉ : Méthode essentielle pour accéder aux sources de données enregistrées dans le système. Permet la récupération sécurisée avec validation d'existence. Critique pour les systèmes utilisant multiples sources de données avec gestion centralisée.

17. register_data_source.txt (TrainOptimizeInterface.register_data_source - ENREGISTREMENT SOURCE DONNÉES)
   - Lignes 1106-1115 dans ml_core.py (10 lignes)
   - FONCTION : Enregistre une source de données dans l'interface avec un nom unique pour permettre la récupération ultérieure et la gestion centralisée des sources
   - PARAMÈTRES :
     * self - Instance de TrainOptimizeInterface
     * name (str) - Nom unique de la source de données pour identification et récupération
     * data_source (Any) - Source de données à enregistrer (peut être DataFrame, fichier, API, etc.)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STOCKAGE ATOMIQUE** : `self.data_sources[name] = data_source` assignation directe dans dictionnaire
     * **ÉCRASEMENT SILENCIEUX** : Si source avec même nom existe, elle sera remplacée sans avertissement
     * **LOGGING DEBUG** : `logger.debug(f"Source de données '{name}' enregistrée dans l'interface")` avec f-string formatée
     * **TYPE HINTS COMPLETS** : `name: str`, `data_source: Any`, `-> None` pour validation statique
     * **GESTION UNIVERSELLE** : Any accepte DataFrames, fichiers, connexions DB, APIs, streams
     * **RÉFÉRENCE DIRECTE** : Stocke pointeur vers source originale, pas de copie/clone
     * **THREAD-SAFETY** : Opération atomique sur dictionnaire Python (GIL protection)
     * **DOCSTRING STANDARD** : Args avec descriptions pour name et data_source
     * **PATTERN REGISTRY** : Implémente registre centralisé avec clé-valeur simple
     * **PERFORMANCE** : O(1) pour insertion dans dictionnaire hash
   - RETOUR : None (opération de stockage, pas de retour)
   - UTILITÉ : Méthode fondamentale pour construire un registre de sources de données centralisé. Permet la gestion de multiples sources avec accès par nom. Essentielle pour les systèmes ML utilisant diverses sources de données avec gestion centralisée.

18. get_hbp_instance_1.txt (get_hbp_instance - WRAPPER INSTANCE HBP - DOUBLON)
   - Lignes 1308-1319 dans ml_core.py (12 lignes)
   - FONCTION : Fonction wrapper qui délègue la création d'instance HBP à ModelProvider.get_hbp_instance, fournissant une interface simplifiée pour l'accès singleton
   - PARAMÈTRES :
     * trial_id (optionnel) - Identifiant de l'essai Optuna pour collecteur de statistiques
     * config (optionnel) - Configuration à utiliser pour l'instance HBP
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉLÉGATION DIRECTE** : `return ModelProvider.get_hbp_instance(trial_id, config)` appel méthode statique avec paramètres
     * **INTERFACE SIMPLIFIÉE** : Permet accès instance HBP sans référencer explicitement ModelProvider
     * **TRANSPARENCE TOTALE** : Transmet tous paramètres sans modification ni traitement supplémentaire
     * **MÊME COMPORTEMENT** : Produit exactement même résultat que méthode originale ModelProvider
     * **IMPORTATION CONDITIONNELLE** : Évite importations circulaires en déléguant à ModelProvider
     * **DOCSTRING COMPLÈTE** : Args, Returns avec descriptions détaillées
     * **PATTERN FACADE** : Simplifie accès à fonctionnalité complexe ModelProvider
     * **TYPE HINTS** : `trial_id=None`, `config=None`, `-> HybridBaccaratPredictor` validation statique
   - RETOUR : HybridBaccaratPredictor - Instance singleton configurée (retour direct de ModelProvider.get_hbp_instance)
   - UTILITÉ : Fonction de commodité pour l'accès à l'instance HBP sans nécessiter de connaître l'architecture interne. Particulièrement utile pour les utilisateurs qui veulent accéder rapidement à l'instance HBP sans se soucier du pattern Singleton sous-jacent.

19. get_calculate_uncertainty_1.txt (get_calculate_uncertainty - WRAPPER CALCUL INCERTITUDE - DOUBLON)
   - Lignes 1321-1328 dans ml_core.py (8 lignes)
   - FONCTION : Fonction wrapper qui délègue l'accès à la méthode calculate_uncertainty à ModelProvider.get_calculate_uncertainty, fournissant une interface simplifiée
   - PARAMÈTRES : Aucun paramètre
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉLÉGATION DIRECTE** : `return ModelProvider.get_calculate_uncertainty()` appel méthode statique sans paramètres
     * **INTERFACE SIMPLIFIÉE** : Permet accès fonction calculate_uncertainty sans référencer explicitement ModelProvider
     * **TRANSPARENCE TOTALE** : Retourne directement résultat méthode déléguée sans traitement supplémentaire
     * **MÊME COMPORTEMENT** : Produit exactement même résultat que méthode originale ModelProvider
     * **IMPORTATION CONDITIONNELLE** : Évite importations circulaires en déléguant à ModelProvider
     * **DOCSTRING COMPLÈTE** : Returns avec description détaillée
     * **PATTERN FACADE** : Simplifie accès à fonctionnalité complexe ModelProvider
     * **TYPE HINTS** : `-> function` validation statique pour retour fonction
   - RETOUR : function - La méthode calculate_uncertainty de HybridBaccaratPredictor (retour direct de ModelProvider.get_calculate_uncertainty)
   - UTILITÉ : Fonction de commodité pour l'accès à la fonction de calcul d'incertitude sans nécessiter de connaître l'architecture interne. Particulièrement utile pour les utilisateurs qui veulent calculer l'incertitude rapidement sans se soucier de l'implémentation sous-jacente.
