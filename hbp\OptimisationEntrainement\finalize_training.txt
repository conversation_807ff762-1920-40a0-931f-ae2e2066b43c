# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 12057 à 12126
# Type: Méthode de la classe HybridBaccaratPredictor

    def finalize_training(self, success: bool, start_time: float, train_summary: List[str]): # <--- Signature modifiée
        ui_available = self.is_ui_available()
        total_time = time.time() - start_time
        logger.info(f"Fin du processus d'entraînement COMPLET. Durée: {total_time:.2f}s. Succès: {success}")
        final_message = ""
        save_successful = False

        # Exécuter les opérations non-UI d'abord
        if success:
            # Construire message succès avec résumé
            summary_str = ', '.join(train_summary) if train_summary else "Étapes non spécifiées"
            success_details = f"Entraînement principal réussi ({summary_str}).\nDurée: {total_time:.1f}s."
            logger.info(f"Détails succès: {success_details}")

            # Tentative sauvegarde
            logger.info("Tentative de sauvegarde auto post-entraînement complet...")
            save_successful = self._save_state_to_models_dir()

            if save_successful:
                 logger.info("Sauvegarde auto post-entraînement complet réussie.")
                 final_message = f"Entr. & Sauvegarde OK ({total_time:.1f}s)."
            else:
                 logger.error("Échec sauvegarde auto post-entraînement complet.")
                 final_message = f"Entr. OK, SAUVEGARDE ÉCHOUÉE ({total_time:.1f}s)."
        else:
            # Échec entraînement
            final_message = f"Échec Entr. Complet ({total_time:.1f}s)."
            logger.error("Finalisation entraînement COMPLET: ÉCHEC.")

        # Nettoyage mémoire
        gc.collect()
        if torch.cuda.is_available():
             try: torch.cuda.empty_cache()
             except Exception: pass
        logger.debug("Nettoyage GC/CUDA effectué dans finalize_training.")

        # Planifier toutes les opérations UI dans le thread principal via root.after
        if ui_available:
            def ui_operations():
                # Activer les contrôles d'entraînement
                self.toggle_training_controls(enabled=True)

                # Mettre à jour la progression
                self._update_progress(100, "Entraînement terminé" if success else "Échec entraînement")

                if success:
                    # Afficher le message de succès
                    messagebox.showinfo("Entraînement Terminé", success_details)

                    # Mettre à jour la progression pour la sauvegarde
                    self._update_progress(95, "Sauvegarde état...")

                    # Afficher le message d'échec de sauvegarde si nécessaire
                    if not save_successful:
                        messagebox.showwarning("Sauvegarde Auto Échouée", "Entraînement réussi, mais sauvegarde auto échouée. Vérifiez logs.")

                    # Mettre à jour la progression finale
                    self._update_progress(100, final_message)

                    # Réinitialiser la session
                    logger.info("Réinitialisation session interne après entraînement COMPLET réussi...")
                    self.reset_data('soft', confirm=False) # Reset soft (efface session, pas modèles)
                    logger.info("Reset 'soft' post-entraînement complet terminé.")
                else:
                    # Afficher le message d'erreur
                    self._update_progress(0, final_message)
                    messagebox.showerror("Erreur Entraînement", "L'entraînement principal a échoué.\nConsultez les logs pour les détails.")

            # Planifier l'exécution des opérations UI dans le thread principal
            self.root.after(0, ui_operations)