# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1704 à 1711
# Type: Méthode de la classe ThreadedOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def get_result(self):
        """
        Récupère le résultat de l'optimisation.

        Returns:
            Le résultat de l'optimisation ou None si l'optimisation n'est pas terminée ou a échoué
        """
        return self.result