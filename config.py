"""
Module de configuration pour le prédicteur de Baccarat.
Contient la classe PredictorConfig et la configuration du logging.
"""

import os
import sys
import logging
import logging.config
from datetime import datetime
from typing import Dict, List, Optional, Any
import warnings
import torch
import threading

# --- Constants ---
MODEL_SAVE_DIR = "models"
LOG_DIRECTORY = "logs"

# Configuration du logger
class SilentOptunaFilter(logging.Filter):
    """Filtre qui supprime les logs pendant l'optimisation Optuna, sauf les logs importants."""

    def __init__(self):
        super().__init__()
        # Liste des messages répétitifs à filtrer même s'ils sont de niveau WARNING ou ERROR
        self.repetitive_patterns = [
            # Messages d'initialisation
            "Auto-update désactivé par défaut",
            "PersistentMarkov initialisé",
            "Initialisation de l'optimiseur de placement des WAIT",
            "WaitPlacementOptimizer initialisé",
            "Optimiseur de placement des WAIT initialisé",
            "Modèle LSTM optimisé pour la mémoire",
            "Optimisation de la mémoire PyTorch",
            "Impossible de définir le nombre de threads",
            "Configuration PyTorch",
            "Optimisation de la mémoire PyTorch terminée",
            "RÉINITIALISATION (SOFT)",
            "Modèles Markov de session réinitialisés",

            # Messages de diagnostic d'entraînement
            "DIAGNOSTIC TRAIN:",
            "DIAGNOSTIC EVAL",
            "DIAGNOSTIC CONVERSION:",
            "DIAGNOSTIC DATASET:",
            "Gradient norm élevé:",
            "Nettoyage GC/CUDA effectué",
            "Incertitude détaillée:",
            "Poids: confidence_weight=",
            "Mise à jour des métriques cibles",
            "Métriques consécutives:",
            "Facteur de difficulté calculé",
            "Poids LSTM calculés:",
            "Poids d'échantillons calculés:",
            "_auto_update_callback: Cette méthode n'est plus utilisée",
            "Cache LGBM vidé après annulation",
            "Incohérence shape features LSTM",
            "Modèle 'calibrated_lgbm' non initialisé. Retour 50/50",
            "Modèle LSTM non initialisé. Retour 50/50",
            "Échec car modèle 'calibrated_lgbm' ou scaler non 'fit'",
            "Modèle LGBM non entraîné dans"
        ]

        # Compteur pour les messages répétitifs
        self.repetitive_counts = {}

    def filter(self, record):
        # Vérifier si le thread actuel est un thread Optuna
        is_optuna_thread = "OptunaOptimization" in threading.current_thread().name

        # Vérifier si le message est répétitif
        message = record.getMessage()
        is_repetitive = any(pattern in message for pattern in self.repetitive_patterns)

        # Corriger le niveau de log pour les messages DIAGNOSTIC qui utilisent CRITICAL incorrectement
        if "DIAGNOSTIC" in message and record.levelno == logging.CRITICAL:
            # Rétrograder au niveau INFO pour éviter de déclencher des alertes
            record.levelno = logging.INFO
            record.levelname = "INFO"

        # Si c'est un message répétitif, le compter et ne l'afficher que la première fois
        if is_repetitive:
            if message not in self.repetitive_counts:
                self.repetitive_counts[message] = 1
                # Afficher le message la première fois avec une note
                if record.levelno >= logging.WARNING:
                    record.msg = f"{record.msg} (messages similaires seront filtrés)"
                # Ne pas afficher les messages de diagnostic même la première fois s'ils sont trop verbeux
                if any(verbose_pattern in message for verbose_pattern in [
                    "DIAGNOSTIC TRAIN:", "DIAGNOSTIC EVAL", "Gradient norm élevé:",
                    "Incertitude détaillée:", "Poids: confidence_weight="
                ]):
                    return False
                return True
            else:
                self.repetitive_counts[message] += 1
                # Ne pas afficher les messages répétitifs
                return False

        # Toujours afficher les logs de niveau WARNING et ERROR s'ils ne sont pas répétitifs
        if record.levelno >= logging.WARNING:
            # Filtrer certains messages WARNING qui sont normaux et attendus
            if "Erreur lors de l'optimisation de la mémoire PyTorch" in message:
                return False
            return True

        # Toujours afficher les logs contenant des mots-clés importants
        important_keywords = [
            "VIABLE", "ESSAI", "OPTIMISATION", "WAIT", "NON-WAIT", "VAGUE", "Progression",
            "Epoch", "Val Loss", "Val Accuracy", "Train Loss", "Train Accuracy",
            "Objectif 1", "Objectif 2", "Score composite", "Early stopping"
        ]
        if any(keyword in message for keyword in important_keywords):
            return True

        # Supprimer les autres logs des threads Optuna
        return not is_optuna_thread

# Créer le répertoire de logs s'il n'existe pas
if not os.path.exists(LOG_DIRECTORY):
    os.makedirs(LOG_DIRECTORY)

# Configurer le logger
log_filename = os.path.join(LOG_DIRECTORY, f"predictor_{datetime.now():%Y%m%d_%H%M%S}.log")
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
    handlers=[
        logging.FileHandler(log_filename, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ],
)

# Obtenir le logger pour ce module
logger = logging.getLogger(__name__)

# Ajouter le filtre pour supprimer les logs pendant l'optimisation Optuna
optuna_filter = SilentOptunaFilter()

# Appliquer le filtre au logger racine pour affecter tous les loggers
root_logger = logging.getLogger()
root_logger.addFilter(optuna_filter)

# Appliquer également au logger de ce module
logger.addFilter(optuna_filter)

# Filtrage des avertissements
warnings.filterwarnings('ignore', category=UserWarning, module='lightgbm')
warnings.filterwarnings('ignore', category=UserWarning, module='joblib')
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning, module='optuna._experimental')
# Supprimer les avertissements concernant les paramètres fixes hors plage
warnings.filterwarnings('ignore', category=UserWarning, message='Fixed parameter .* is out of range for distribution .*')


class PredictorConfig:
    """
    Configuration du prédicteur de Baccarat.
    Contient tous les paramètres configurables du prédicteur et les noms standardisés des métriques.
    """

    # Noms standardisés des métriques pour assurer la cohérence entre les différents modules
    # Métriques de précision
    METRIC_PRECISION_NON_WAIT = 'precision_non_wait_late_game'  # Précision des recommandations NON-WAIT pour les manches 31-60
    METRIC_PRECISION_WAIT = 'precision_wait_late_game'  # Précision des recommandations WAIT pour les manches 31-60

    # Métriques de recommandation
    METRIC_RECOMMENDATION_RATE = 'recommendation_rate_late_game'  # Taux de recommandation NON-WAIT pour les manches 31-60
    METRIC_WAIT_RATIO = 'wait_ratio'  # Ratio de recommandations WAIT (1 - recommendation_rate)

    # Métriques de séquences consécutives
    METRIC_CONSECUTIVE_SCORE = 'consecutive_valid_non_wait'  # Score composite basé sur les séquences consécutives
    METRIC_MAX_CONSECUTIVE = 'max_consecutive'  # Nombre maximum de recommandations NON-WAIT valides consécutives

    # Métriques de viabilité
    METRIC_VIABLE = 'viable'  # Indique si l'essai est viable (a au moins un WAIT et un NON-WAIT)
    METRIC_HAS_WAIT = 'has_wait_in_target'  # Indique si l'essai a au moins une recommandation WAIT
    METRIC_HAS_NON_WAIT = 'has_non_wait_in_target'  # Indique si l'essai a au moins une recommandation NON-WAIT

    # Autres métriques
    METRIC_WAIT_DECISION_ACCURACY = 'wait_decision_accuracy'  # Précision des décisions WAIT
    METRIC_MISSED_OPPORTUNITIES = 'missed_opportunities'  # Nombre d'opportunités manquées
    METRIC_WAIT_EFFICIENCY = 'wait_efficiency'  # Efficacité des recommandations WAIT
    METRIC_RECOVERY_RATE = 'recovery_rate_after_wait'  # Taux de récupération après un WAIT
    METRIC_PREDICTION_STABILITY = 'prediction_stability'  # Stabilité des prédictions

    # Paramètres de scoring
    SCORING_PRECISION_NON_WAIT_WEIGHT = 1.0  # 100% du score basé sur la précision NON-WAIT
    OPTIMAL_WAIT_RATIO = 0.4  # Ratio d'attente optimal pour l'équilibre entre WAIT et NON-WAIT

    # Constantes pour le calcul de l'incertitude
    UNCERTAINTY_VARIANCE_NORMALIZATION_FACTOR = 2.0  # Réduit de 4.0 à 2.0 pour obtenir des valeurs d'incertitude plus réalistes
    UNCERTAINTY_MIN_CLIP = 0.0  # Valeur minimale pour le clipping de l'incertitude
    UNCERTAINTY_MAX_CLIP = 1.0  # Valeur maximale pour le clipping de l'incertitude
    UNCERTAINTY_DEFAULT_VALUE = 0.5  # Valeur par défaut en cas d'erreur

    # Constantes pour l'évaluation des recommandations WAIT
    WAIT_PRECISION_DEFAULT = 1.0  # Valeur par défaut pour la précision des recommandations WAIT

    # Constantes pour le calcul des séquences consécutives
    CONSECUTIVE_SCORE_MAX_WEIGHT = 0.8  # Poids pour le maximum de recommandations consécutives
    CONSECUTIVE_SCORE_WEIGHTED_MEAN_WEIGHT = 0.15  # Poids pour la moyenne pondérée des séquences consécutives
    CONSECUTIVE_SCORE_PRECISION_WEIGHT = 0.05  # Poids pour la précision dans le score consécutif

    def __init__(self, config_overrides=None):
        self.initial_weights: Dict[str, float] = {'lgbm': 0.20, 'lstm': 0.60, 'markov': 0.20}  # Ajusté pour donner plus d'importance au modèle LSTM
        self.min_rounds_per_game: int = 15
        self.min_training_samples: int = 500
        self.min_target_hand_index_training: int = 30
        self.bagging_n_estimators: int = 141
        self.lgbm_cv_splits: int = 5
        self.scheduler_patience: int = 3  # Augmenté de 2 à 3 pour permettre plus d'exploration avant de réduire le LR
        self.scheduler_factor: float = 0.3  # Réduit de 0.5 à 0.3 pour une réduction plus agressive du taux d'apprentissage
        self.batch_sizes: Dict[str, int] = {'small': 512, 'medium': 1024, 'large': 2048}

        # Paramètres pour la création des features LSTM
        self.lstm_streak_normalization_factor: float = 8.0# Facteur pour normaliser la longueur des streaks
        self.lstm_recent_window_size: int = 4# Taille de la fenêtre pour les statistiques récentes
        self.lstm_avg_game_length: int = 80# Longueur moyenne d'une partie pour la normalisation
        self.lstm_use_adaptive_window: bool = True# Utiliser une fenêtre adaptative pour LSTM

        # Paramètre pour le calcul des features avec decay
        self.decay_factor: float = 0.95# Augmenté pour donner un poids plus équilibré aux résultats récents

        self.random_seed: int = 42
        self.early_stopping_patience: int = 8
        self.decision_diff_threshold: float = 0.01695948581607369

        # Paramètres de seuil de confiance et de décision - optimisables par Optuna
        self.min_confidence_for_recommendation = 0.30  # Réduit pour permettre plus de recommandations NON-WAIT
        self.adaptive_confidence_threshold: bool = True  # Optimisable: True/False
        self.confidence_adjustment_step: float = 0.05  # Plage optimisable: 0.01-0.08

        # Paramètres de seuil de décision - optimisables par Optuna
        # Ces seuils sont utilisés pour déterminer quand faire une recommandation NON-WAIT vs WAIT
        self.decision_threshold_min: float = 0.3  # Plage optimisable: 0.15-0.6
        self.decision_threshold_max: float = 0.6  # Plage optimisable: 0.15-0.6

        # Paramètre pour le calcul de l'incertitude - optimisable par Optuna
        self.transition_uncertainty_threshold: float = 0.6  # Plage optimisable: 0.3-0.9
        self.fallback_penalty_factor: float = 0.14039566669804696

        # Facteur d'équilibrage pour éviter un biais vers WAIT
        self.wait_bias_factor: float = 0.9  # Valeur < 1.0 pour favoriser les NON-WAIT

        # Paramètres Markov - Assurer la cohérence entre l'optimisation et l'entraînement
        # Ces paramètres doivent être synchronisés lors de l'optimisation
        self.max_markov_order = 1  # Valeur ajustée automatiquement - plage: [1, 14]  # Ordre maximal des chaînes de Markov (entier entre 1 et 12)
        self.markov_smoothing = 0.15
        self.markov_global_weight = 0.15  # Poids à accorder aux modèles globaux (entre 0 et 1)
        self.markov_context_weight = 0.8  # Poids à accorder au contexte local dans le modèle de Markov
        self.markov_decay_factor = 0.95  # Facteur de décroissance pour l'influence des états passés

        # Paramètres LGBM - Assurer la cohérence entre l'optimisation et l'entraînement
        # Ces paramètres doivent être synchronisés lors de l'optimisation
        self.lgbm_learning_rate = 0.008  # Valeur ajustée automatiquement - plage: [0.008, 0.2088545395184536]  # Corrigé pour être dans la plage recommandée [0.008, 0.0192584178564537]
        self.lgbm_max_depth = 3  # Valeur ajustée automatiquement - plage: [3, 16]  # Valeur ajustée automatiquement - plage: [7, 30]  # Valeur ajustée automatiquement - plage: [7, 14]
        self.lgbm_num_leaves = 26  # Valeur ajustée automatiquement - plage: [26, 105]  # Corrigé pour être dans la plage recommandée [35, 66]
        self.lgbm_n_estimators = 109  # Valeur ajustée automatiquement - plage: [109, 204]  # Valeur ajustée automatiquement - plage: [109, 190]  # Valeur ajustée automatiquement - plage: [109, 180]
        self.lgbm_min_child_samples: int = 25
        self.lgbm_subsample: float = 0.7
        self.lgbm_colsample_bytree: float = 0.9
        self.lgbm_reg_alpha: float = 1.5
        self.lgbm_reg_lambda: float = 0.0002

        # Assurer la cohérence entre lstm_hidden_size et lstm_hidden_dim
        # Ces deux paramètres doivent toujours avoir la même valeur
        # Modifié pour correspondre à la taille du modèle cible (1024/2 = 512 pour un modèle bidirectionnel)
        self.lstm_hidden_size: int = 512
        self.lstm_hidden_dim = self.lstm_hidden_size  # Utiliser la même valeur que lstm_hidden_size
        self.lstm_num_layers = 1  # Valeur ajustée automatiquement - plage: [1, 6]  # Maintenu à 2 pour un bon équilibre complexité/performance
        self.lstm_dropout = 0.2747397139282454  # Valeur ajustée automatiquement - plage: [0.2747397139282454, 0.55]  # Réduit de 0.35 à 0.3 pour une régularisation plus équilibrée
        self.lstm_learning_rate = 8.574561224318143e-05  # Valeur ajustée automatiquement - plage: [8.574561224318143e-05, 0.0005]e-05  # Valeur ajustée automatiquement - plage: [9.025853920334888e-05, 0.0005]  # Réduit de 0.0003 à 0.0002 pour une convergence plus stable
        self.lstm_weight_decay = 0.00045197574943495926  # Valeur ajustée automatiquement - plage: [0.00045197574943495926, 0.003]  # Valeur ajustée automatiquement - plage: [0.0004543124351463129, 0.003]  # Réduit de 0.0008 à 0.0005 pour une régularisation L2 plus légère
        self.lstm_batch_size: int = 512
        self.lstm_sequence_length: int = 12
        self.lstm_epochs: int = 10  # Augmenté de 8 à 10 pour permettre un apprentissage plus approfondi
        self.lstm_input_size: int = 12  # Modifié de 15 à 12 pour supprimer les 3 features d'optimisation
        self.lgbm_input_size: int = 25  # Nombre de features LGBM (25 au lieu de 28 après suppression des 3 features d'optimisation)
        self.lstm_bidirectional: bool = True
        self.gradient_clip_norm: float = 1.5  # Réduit de 2.0 à 1.5 pour éviter l'explosion des gradients
        self.max_epochs: int = 12  # Augmenté de 10 à 12 pour permettre plus d'époques
        self.lstm_early_stopping_patience: int = 10  # Augmenté pour permettre plus d'exploration

        self.use_advanced_features: bool = True
        self.use_advanced_lstm: bool = True
        self.scheduler_type: str = 'cosine_warm'  # Maintenu pour éviter les minima locaux
        self.label_smoothing: float = 0.05  # Réduit de 0.1 à 0.05 pour réduire la régularisation et améliorer la val_loss
        self.use_mixup: bool = True
        self.mixup_alpha: float = 0.1  # Réduit de 0.12 à 0.1 pour une augmentation de données plus douce
        self.use_focal_loss: bool = False  # Désactivé pour simplifier la fonction de perte et réduire la val_loss
        self.focal_loss_gamma: float = 1.5  # Réduit de 2.0 à 1.5 pour une pénalisation plus équilibrée des exemples difficiles
        self.focal_loss_alpha: float = 0.5  # Ajusté de 0.52 à 0.5 pour un équilibrage plus fin des classes
        self.lstm_dropout_input: float = 0.1  # Réduit de 0.15 à 0.1 pour permettre un meilleur flux d'information
        self.lstm_dropout_hidden: float = 0.2  # Réduit de 0.25 à 0.2 pour une régularisation plus équilibrée
        self.lstm_dropout_output: float = 0.15  # Réduit de 0.2 à 0.15 pour améliorer la qualité des prédictions
        self.lstm_use_attention: bool = True
        self.lstm_use_residual: bool = True

        # Paramètres supplémentaires pour LSTM
        self.lstm_banker_weight: float = 1.0
        self.lstm_player_weight: float = 1.0
        self.lstm_streak_weight: float = 1.0
        self.lstm_alternance_weight: float = 1.0
        self.lstm_imbalance_scale: float = 2.0
        self.lstm_base_features_count: int = 9
        self.lstm_cache_max_size: int = 1000

        # Paramètres pour la normalisation des probabilités
        self.probability_sum_tolerance: float = 1e-6

        # Paramètres pour le seuil de décision dans hybrid_prediction
        self.base_reduction_factor: float = 0.6  # Augmenté pour un seuil de décision plus élevé et plus sélectif
        self.phase2_reduction_factor: float = 0.5  # Augmenté pour un seuil de décision plus élevé en phase 2
        self.phase2_uncertainty_reduction: float = 0.3  # Augmenté pour donner plus d'importance à l'incertitude
        self.phase2_threshold_factor: float = 0.75  # Ajouté et ajusté à 0.75 (au lieu de 0.65) pour un meilleur équilibre en phase 2
        self.wait_ratio_factor_high: float = 0.15  # Maintenu à 0.15
        self.wait_ratio_factor_low: float = 0.07  # Augmenté de 0.05 à 0.07 pour favoriser les NON-WAIT

        # Paramètres supplémentaires pour hybrid_prediction (extraits des valeurs codées en dur)
        # Note: min_confidence_factor, range_confidence_factor, confidence_weight, uncertainty_weight et consecutive_weight
        # sont définis plus bas avec leurs valeurs optimisées
        self.non_trained_threshold_multiplier: float = 1.5  # Multiplicateur pour les modèles non entraînés
        self.prob_diff_weight: float = 0.3  # Poids pour la différence de probabilité
        self.default_markov_prob: float = 0.5  # Probabilité par défaut pour Markov
        self.default_lgbm_prob: float = 0.5  # Probabilité par défaut pour LGBM
        self.default_lstm_prob: float = 0.5  # Probabilité par défaut pour LSTM

        # Paramètres supplémentaires pour le calcul d'incertitude
        self.w_epistemic: float = 0.3  # Augmenté pour donner plus d'importance à l'incertitude épistémique
        self.w_aleatoric: float = 0.3  # Augmenté pour donner plus d'importance à l'incertitude aléatoire
        self.w_sensitivity: float = 0.3  # Augmenté pour donner plus d'importance à la sensibilité contextuelle
        self.w_consecutive: float = 0.1  # Augmenté pour donner plus d'importance à la confiance consécutive
        self.w_consecutive_target: float = 0.3  # Augmenté pour donner plus d'importance à la confiance consécutive dans les manches cibles
        self.global_uncertainty_factor: float = 0.8  # Augmenté pour donner plus d'importance à l'incertitude globale
        self.base_uncertainty_non_trained: float = 0.5  # Augmenté pour une incertitude de base plus réaliste

        # Paramètres pour l'ajustement du score de décision
        self.bell_curve_bonus_factor: float = 0.2  # Facteur pour le bonus en forme de cloche
        self.confidence_threshold_adjustment: float = 0.03  # Facteur d'ajustement du seuil de confiance
        self.phase1_threshold_factor: float = 1.2  # Facteur pour le seuil de confiance en phase 1
        self.wait_ratio_tolerance: float = 0.1  # Tolérance pour le ratio WAIT/NON-WAIT

        # Paramètres pour calculate_model_confidence
        self.markov_streak_factor: float = 1.2  # Facteur pour Markov lors de la détection de streaks
        self.lstm_streak_factor: float = 1.15  # Facteur pour LSTM lors de la détection de streaks
        self.lstm_long_sequence_factor: float = 1.3  # Facteur max pour LSTM avec longues séquences
        self.lstm_long_sequence_threshold: int = 30  # Seuil pour considérer une séquence comme longue
        self.markov_short_sequence_factor: float = 0.7  # Facteur min pour Markov avec courtes séquences
        self.markov_short_sequence_threshold: int = 20  # Seuil pour considérer une séquence comme courte
        self.historical_factor_min: float = 0.8  # Facteur historique minimum
        self.historical_factor_max: float = 2.0  # Facteur historique maximum
        # Paramètres de normalisation et de clipping
        self.confidence_normalization_factor: float = 2.0  # Facteur pour normaliser la confiance (distance à 0.5)
        self.confidence_min_clip: float = 0.0  # Valeur minimale pour le clipping de la confiance
        self.confidence_max_clip: float = 1.0  # Valeur maximale pour le clipping de la confiance
        self.uncertainty_normalization_factor: float = 2.0  # Réduit de 4.0 à 2.0 pour obtenir des valeurs d'incertitude plus réalistes
        self.uncertainty_min_clip: float = 0.0  # Valeur minimale pour le clipping de l'incertitude
        self.uncertainty_max_clip: float = 1.0  # Valeur maximale pour le clipping de l'incertitude

        # Paramètres pour les features et la normalisation
        self.sensitivity_normalization_factor: float = 5.0  # Facteur pour normaliser la sensibilité contextuelle
        self.consecutive_normalization_factor: float = 10.0  # Facteur pour normaliser les séquences consécutives
        self.round_normalization_factor: float = 100.0  # Facteur pour normaliser les numéros de manches
        self.temporal_factor_min: float = 0.5  # Valeur minimale pour le facteur temporel
        self.temporal_factor_max: float = 1.0  # Valeur maximale pour le facteur temporel
        self.default_feature_value: float = 0.5  # Valeur par défaut pour les features

        self.phase2_wait_ratio_high_factor: float = 1.2  # Ajusté à 1.2 (au lieu de 1.0) pour permettre des WAIT plus stratégiques
        self.phase2_wait_ratio_low_factor: float = 0.5  # Réduit de 0.7 à 0.5 pour diminuer davantage l'effet du ratio WAIT bas
        self.consecutive_adjustment_factor: float = 0.01  # Maintenu à 0.01
        self.phase2_consecutive_factor: float = 0.7  # Maintenu à 0.7
        # Les paramètres decision_threshold_min et decision_threshold_max sont déjà définis plus haut
        self.phase2_decision_threshold_min: float = 0.3  # Augmenté pour un seuil minimum plus sélectif en phase 2
        self.phase2_decision_threshold_max: float = 0.6  # Augmenté pour permettre des seuils plus élevés en phase 2
        self.diagnostic_min_threshold: float = 0.20  # Réduit de 0.25 à 0.20 pour abaisser le seuil de diagnostic


        self.normalization: str = 'robust_scaler'
        self.feature_engineering_methods: List[str] = [
            'temporal_features',
            'statistical_moments',
            'frequency_domain_features'
        ]

        self.jitter_sigma: float = 0.05
        self.time_warp_sigma: float = 0.15
        self.window_slice_ratio: float = 0.8598795328856113

        self.use_ensemble: bool = True
        self.ensemble_num_variants: int = 6
        self.temporal_weight: float = 0.48188624315221656
        self.mc_dropout_samples: int = 12
        self.optimizer_type: str = 'adamw'

        self.lgbm_early_stopping_rounds: int = 11
        self.optuna_max_epochs: int = 3
        self.optuna_max_patience: int = 999
        self.optuna_num_simulations: int = 3
        self.optuna_num_trials: int = 3

        self.stability_window_size: int = 12
        self.stability_threshold: float = 0.*****************
        self.distribution_window_size: int = 20
        self.excellence_confidence_threshold: float = 0.****************
        self.display_metrics_interval: int = 1
        self.display_batch_interval: int = 10
        self.real_time_metrics: bool = True

        self.simulation_banker_prob: float = 0.****************
        self.simulation_prob_variation: float = 0.*****************
        self.simulation_min_prob: float = 0.****************
        self.simulation_max_prob: float = 0.****************

        self.batch_size_factor: float = 0.*****************
        self.feature_selection_threshold: float = 0.*****************
        self.temporal_decay_factor: float = 0.****************
        self.uncertainty_threshold: float = 0.90  # Augmenté drastiquement pour maximiser la tolérance à l'incertitude

        self.decay_factor: float = 0.95  # Déjà modifié plus haut, mais répété ici pour cohérence
        self.sample_weight_decay_factor: float = 0.999
        self.uncertainty_max_samples: float = 0.****************
        self.uncertainty_max_features: float = 0.****************
        self.calibration_method: str = 'isotonic'  # Maintenu à 'isotonic' qui est généralement plus performant que 'sigmoid' pour la calibration
        self.w_disagree: float = 0.5998579884436314
        self.w_entropy: float = 0.20944491868184306

        self.non_trained_threshold_multiplier: float = 1.8430817163354527

        self.min_confidence_factor: float = 0.30  # Réduit de 0.3998 à 0.30 pour abaisser le seuil de confiance minimal
        self.range_confidence_factor: float = 1.30  # Augmenté de 1.15 à 1.30 pour élargir la plage de confiance
        self.w_epistemic: float = 0.3  # Réduit de 0.4 à 0.3 pour diminuer l'impact de l'incertitude épistémique
        self.w_aleatoric: float = 0.2  # Réduit de 0.3 à 0.2 pour diminuer l'impact de l'incertitude aléatoire
        self.w_sensitivity: float = 0.2  # Réduit de 0.3 à 0.2 pour diminuer l'impact de la sensibilité
        self.w_consecutive: float = 0.15  # Maintenu à 0.15
        self.w_consecutive_target: float = 0.2  # Maintenu à 0.2
        self.global_uncertainty_factor: float = 0.3  # Réduit fortement pour diminuer l'impact de l'incertitude globale
        self.base_uncertainty_non_trained: float = 0.3  # Réduit fortement pour diminuer l'incertitude de base

        self.precision_non_wait_weight: float = 0.9383601464973889
        self.rate_component_weight: float = 0.14543194831960382
        self.precision_wait_weight: float = 0.12398638198034741

        self.objective1_weight = 0.95  # Augmenté pour donner encore plus d'importance à l'objectif 1
        self.consecutive_focus_factor = 5.0  # Valeur ajustée automatiquement - plage: [5.0, 8.80589982927813]  # Réduit de 8.0 à 5.0 pour un équilibre plus approprié
        self.late_game_weight_factor = 7.472137331851989  # Valeur ajustée automatiquement - plage: [7.472137331851989, 12.781795143701256]  # Valeur ajustée automatiquement - plage: [7.472137331851989, 12.0]  # Réduit de 12.0 à 8.0 pour un équilibre plus approprié
        # Valeurs fixes pour les manches cibles (non optimisables)
        self.target_round_min: int = 31
        self.target_round_max: int = 60
        self.consecutive_penalty_factor: float = 2.0  # Réduit de 2.5 à 2.0 pour une pénalisation plus équilibrée

        self.optimization_phase: Optional[int] = None



        self.max_sequence_weight: float = 0.8943312694728984
        self.avg_sequence_weight: float = 0.1416936975820895
        self.error_rate_weight: float = 0.026201150652689267
        self.sequence_bonus_threshold: int = 4
        self.sequence_bonus_factor: float = 0.15670566036536204
        self.broken_sequence_penalty: float = 1.3869764815854904

        self.ratio_bonus_threshold: float = 0.06850631750862846
        self.ratio_bonus_max: float = 0.24973726003484886
        self.precision_bonus_threshold: float = 0.6986015524059871
        self.precision_bonus_factor: float = 0.46425831768680265
        self.wait_efficiency_bonus_threshold: float = 0.7473438998081844
        self.wait_efficiency_bonus_factor: float = 0.3498718536308886
        self.recovery_bonus_threshold: float = 0.6945213100132244
        self.recovery_bonus_factor: float = 0.15447456954418273
        self.precision_penalty_threshold: float = 0.8947061123497413


        self.wait_ratio_tolerance: float = 0.20  # Augmenté de 0.06 à 0.20 pour plus de flexibilité dans le ratio WAIT/NON-WAIT
        self.wait_decision_weight: float = 0.350407937273205
        self.missed_opportunity_penalty: float = 0.2073248629792097
        self.recovery_rate_weight: float = 0.3383108487516634
        self.transition_uncertainty_threshold: float = 0.90  # Augmenté drastiquement pour maximiser la tolérance à l'incertitude

        self.base_confidence_threshold: float = 0.35  # Augmenté pour un seuil de confiance plus sélectif
        self.confidence_threshold_adjustment: float = 0.02  # Augmenté pour donner plus d'impact à l'ajustement du seuil
        self.confidence_adjustment_rate: float = 0.02  # Réduit pour un ajustement plus progressif
        self.confidence_recovery_rate: float = 0.02  # Réduit pour une récupération plus progressive



        # Paramètres fixes pour le calculateur de confiance consécutive
        self.max_recent_history: int = 50  # Nombre maximum de recommandations récentes à conserver
        self.default_confidence_value: float = 0.5  # Valeur par défaut pour calculate_confidence
        self.default_wait_ratio: float = self.OPTIMAL_WAIT_RATIO  # Valeur par défaut pour get_current_wait_ratio
        self.confidence_adjustment_min: float = 0.5  # Valeur minimale pour get_confidence_adjustment
        self.confidence_adjustment_max: float = 2.0  # Valeur maximale pour get_confidence_adjustment

        self.recent_performance_min_samples: int = 5
        self.position_range_lower: float = 0.3
        self.position_range_upper: float = 0.7
        self.bell_curve_factor: float = 0.23182749931079627



        self.kpi_window_size: int = 12
        self.stability_threshold: float = 0.*****************
        self.high_confidence_threshold: float = 0.6979107590428456



        self.sequence_efficiency_max_weight: float = 0.7681128255773682
        self.sequence_efficiency_precision_weight: float = 0.10603635443515537
        self.sequence_efficiency_wait_weight: float = 0.17262116737607752

        self.use_markov_model: bool = True

        self.min_sample_weight: float = 0.3
        self.max_sample_weight: float = 3.0

        # Ces valeurs sont déjà définies plus haut (lignes 384-385)
        # et sont fixes (non optimisables)
        self.late_game_factor: float = 1.0584140830713586
        self.occurrence_factor_divisor: float = 137.2860405020855
        self.consecutive_factor_divisor: float = 13.584395088552016
        self.max_occurrence_factor: float = 1.9045595232654486
        self.max_consecutive_factor: float = 1.6629703772501234
        self.consecutive_confidence_adjustment_factor: float = 0.25963269801768657
        self.min_occurrences: int = 7
        self.max_pattern_length: int = 10
        self.consecutive_error_penalty: float = 0.09144806394695709
        self.consecutive_recovery_rate: float = 0.059709899897986314
        self.max_consecutive_target: int = 14

        self.pattern_similarity_threshold: float = 0.7067833009177356
        self.max_similar_patterns: int = 8
        self.sequence_bonus_threshold: int = 4
        self.sequence_bonus_factor: float = 0.15670566036536204

        self.success_rate_weight: float = 0.373035926434813
        self.consecutive_length_weight: float = 0.493638781213985
        self.pattern_frequency_weight: float = 0.12723021350940258

        self.confidence_weight: float = 0.4  # Réduit pour un meilleur équilibre
        self.uncertainty_weight: float = 0.2  # Augmenté pour donner plus d'importance à l'incertitude
        self.consecutive_weight: float = 0.2  # Augmenté pour donner plus d'importance aux séquences consécutives
        self.prob_diff_weight: float = 0.2  # Réduit pour un meilleur équilibre

        # Paramètres pour le calcul de validity_probability (auparavant codés en dur)
        self.validity_confidence_weight: float = 0.4  # Réduit pour un meilleur équilibre
        self.validity_prob_diff_weight: float = 0.4   # Augmenté pour donner plus d'importance à la différence de probabilité
        self.validity_certainty_weight: float = 0.2   # Augmenté pour donner plus d'importance à la certitude

        # Seuil de validité (toujours égal à decision_threshold)
        # validity_threshold n'a pas de valeur fixe car il est toujours égal à decision_threshold
        # Il est défini dans la méthode hybrid_prediction

        self.consecutive_threshold: int = 8

        # Paramètres pour le traitement des séquences consécutives
        self.min_pattern_occurrences: int = 7
        self.max_pattern_history: int = 1000
        self.recent_history_window: int = 10
        # Note: consecutive_weight est défini plus haut

        self.selected_features: List[str] = [
            'banker_count', 'player_count', 'ratio_banker', 'ratio_player',
            'banker_streaks', 'player_streaks', 'banker_decay', 'player_decay',
            'banker_streak_2', 'banker_streak_3', 'banker_streak_4', 'banker_streak_5',
            'banker_streak_6', 'banker_streak_7', 'player_streak_2', 'player_streak_3',
            'player_streak_4', 'player_streak_5', 'player_streak_6', 'player_streak_7',
            'alternate_count_2', 'alternate_count_3', 'alternate_ratio',
            'max_banker_streak', 'max_player_streak',
            # Features spécifiques à l'optimisation Optuna
            'confidence', 'error_pattern_threshold', 'transition_uncertainty_threshold'
        ]
        self.ui_update_interval: int = 1000
        self.default_cpu_cores: int = 8
        self.default_max_memory_gb: int = 28
        self.n_jobs: int = -1

        # Paramètres pour l'optimisation de la mémoire et des performances
        self.use_advanced_cache: bool = True  # Activer le cache avancé pour les données prétraitées
        self.max_advanced_cache_size_gb: float = 8.0  # Taille maximale du cache avancé en Go
        self.enable_profiling: bool = False  # Activer le profilage des performances
        self.enable_memory_profiling: bool = False  # Activer le profilage de la mémoire
        self.historical_data_sample_percentage: float = 0.10  # Pourcentage des données à échantillonner (10%)
        self.use_stratified_sampling: bool = True  # Utiliser l'échantillonnage stratifié

        # Paramètres pour le débogage Optuna
        self.optuna_debug_mode: bool = False# Activer/désactiver le mode de débogage Optuna
        self.optuna_debug_log_dir: str = 'logs/optuna_debug'# Répertoire pour les journaux de débogage Optuna

        # Paramètres pour l'arrêt automatique d'Optuna basé sur la viabilité des essais
        self.optuna_viable_trials_required: int = 2# Nombre d'essais viables requis pour arrêter
        self.optuna_max_trials: int = 3# Nombre maximum d'essais à exécuter

        # Seuils de viabilité
        self.viability_min_accuracy: float = 0.55# Précision minimale requise
        self.viability_min_wait_ratio: float = 0.2# Ratio minimum de recommandations WAIT
        self.viability_max_wait_ratio: float = 0.5# Ratio maximum de recommandations WAIT
        self.viability_max_stability_variance: float = 0.15# Variance maximale des prédictions
        self.viability_max_consecutive_errors: int = 4# Nombre maximum d'erreurs consécutives

        # Seuils d'arrêt précoce pour l'évaluation des configurations
        self.min_non_wait_accuracy: float = 0.2  # Augmenté pour exiger une précision minimale des NON-WAIT
        self.min_wait_accuracy: float = 0.2      # Augmenté pour exiger une précision minimale des WAIT
        self.min_recommendation_rate: float = 0.05 # Augmenté pour exiger un taux de recommandation minimal
        self.max_wait_ratio: float = 0.8         # Réduit pour éviter trop de recommandations WAIT
        self.min_wait_ratio: float = 0.2         # Augmenté pour exiger un minimum de recommandations WAIT

        # Poids pour le calcul du score final de viabilité
        self.viability_accuracy_weight: float = 0.4
        self.viability_wait_ratio_weight: float = 0.3
        self.viability_stability_weight: float = 0.2
        self.viability_consecutive_errors_weight: float = 0.1

        # Paramètres pour la simulation des vagues d'optimisation
        self.optuna_simulate_waves: bool = True# Activer la simulation des vagues dans la fonction objectif
        self.optuna_n_waves: int = 3# Nombre de vagues d'optimisation à simuler
        self.optuna_ideal_wait_ratio: float = self.OPTIMAL_WAIT_RATIO  # Ratio idéal de recommandations WAIT

        self.lgbm_params: Dict[str, Any] = {
            'n_jobs': -1,
            'verbose': -1,
            'boosting_type': 'gbdt',
            'objective': 'binary',
            'metric': 'binary_logloss'
        }

        self.optuna_search_space: Dict[str, tuple] = {
            # Paramètres de poids des modèles - Ajustés pour donner plus de poids au LSTM
            'weight_lstm': ('float', 0.15, 0.60),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'weight_markov': ('float', 0.02, 0.15),  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.237
            'weight_lgbm': ('float', 0.15, 0.25),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.243

            # PRIORITÉ 1: Paramètres critiques pour la précision des recommandations NON-WAIT
            'base_reduction_factor': ('float', 0.4, 0.8),  # Plage augmentée pour des seuils de décision plus sélectifs
            'decision_threshold_min': ('float', 0.15, 0.6),  # Plage élargie (0.15-0.6) pour permettre plus de variation
            'decision_threshold_max': ('float', 0.15, 0.6),  # Plage élargie (0.15-0.6) pour permettre plus de variation
            'transition_uncertainty_threshold': ('float', 0.3047507313528937, 0.7),  # Plage ajustée automatiquement,  # Plage élargie (0.3-0.9) pour permettre plus de variation
            'phase2_reduction_factor': ('float', 0.4, 0.8),  # Plage augmentée pour des seuils de décision plus sélectifs
            'phase2_uncertainty_reduction': ('float', 0.2, 0.5),  # Plage augmentée pour donner plus d'importance à l'incertitude
            'prob_diff_weight': ('float', 0.1, 1),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Poids pour la différence de probabilité dans le score de décision
            'diagnostic_min_threshold': ('float', 0.1, 1),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur fixe 0.300
            'lstm_banker_weight': ('float', 0.1, 1.35),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur fixe 1.342
            'lstm_player_weight': ('float', 0.1, 1.****************),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 1.399
            'lstm_streak_weight': ('float', 0.1, 1.6),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 1.538
            'lstm_alternance_weight': ('float', 0.1, 1.4),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'lstm_imbalance_scale': ('float', 0.1, 2.8),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie vers le haut

            # PRIORITÉ 2: Paramètres pour les séquences consécutives correctes
            'consecutive_adjustment_factor': ('float', 0.007528531541734057, 0.018),  # Plage ajustée automatiquement,  # Plage élargie vers le haut
            'phase2_consecutive_factor': ('float', 0.57587194615569, 1.0240633703073543),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur fixe 0.624


            # PRIORITÉ 3: Paramètres pour l'équilibre WAIT/NON-WAIT
            'wait_ratio_factor_high': ('float', 0.1, 0.6),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur fixe 0.223
            'wait_ratio_factor_low': ('float', 0.02, 0.2),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'phase2_wait_ratio_high_factor': ('float', 0.5, 1),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage étendue pour inclure 1.2
            'phase2_wait_ratio_low_factor': ('float', 0.1, 1),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.842

            # PRIORITÉ 4: Paramètres pour la stabilité des prédictions

            # PRIORITÉ 5: Autres paramètres
            'phase2_decision_threshold_min': ('float', 0.3, 0.6),  # Plage augmentée pour des seuils plus sélectifs en phase 2
            'phase2_decision_threshold_max': ('float', 0.3, 0.6),  # Plage augmentée pour des seuils plus sélectifs en phase 2

            # Paramètres LGBM - Optimisés pour plus de précision
            'lgbm_n_estimators': ('int', 109, 204),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'lgbm_learning_rate': ('float', 0.008, 0.2088545395184536, {'log': True}),  # Plage ajustée automatiquement,  # Plage minimale ajustée à 0.008 pour éviter les valeurs trop faibles
            'lgbm_max_depth': ('int', 3, 16),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur fixe 12
            'lgbm_num_leaves': ('int', 26, 105),  # Plage ajustée automatiquement,  # Plage ajustée pour être cohérente avec les recommandations
            'lgbm_min_child_samples': ('int', 18, 28),
            'lgbm_subsample': ('float', 0.5631124290831144, 0.857615992590337),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'lgbm_colsample_bytree': ('float', 0.48864341929130145, 1.0198648077946026),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.787
            'lgbm_reg_alpha': ('float', 1.5, 4.376087305831109, {'log': True}),  # Plage ajustée automatiquement,
            'lgbm_reg_lambda': ('float', 0.00005, 0.0003, {'log': True}),
            'lgbm_early_stopping_rounds': ('int', 9, 22),  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 17

            # Paramètres LSTM - Plage continue pour lstm_hidden_dim, lstm_hidden_size sera synchronisé
            'lstm_hidden_dim': ('int', 256, 1081),  # Plage ajustée automatiquement,  # Plage continue d'entiers pour plus de flexibilité
            # lstm_hidden_size est supprimé de l'espace de recherche et sera synchronisé avec lstm_hidden_dim
            'lstm_num_layers': ('int', 1, 6),  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 1
            'lstm_dropout': ('float', 0.2747397139282454, 0.55),  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.512
            'lstm_learning_rate': ('float', 0.0001, 0.002, {'log': True}),  # Plage réduite davantage pour favoriser des taux d'apprentissage très faibles
            'lstm_weight_decay': ('float', 0.00045197574943495926, 0.003, {'log': True}),  # Plage ajustée automatiquement,  # Plage augmentée davantage pour favoriser une régularisation encore plus forte
            'lstm_bidirectional': ('categorical', [True, False]),  # Remis dans l'espace de recherche pour permettre à Optuna de suggérer sa valeur

            'lstm_sequence_length': ('int', 4, 20),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 16


            # Paramètres Markov - Ajustés pour plus de précision et de flexibilité
            'max_markov_order': ('int', 1, 14),  # Plage ajustée automatiquement,  # Changé en type 'int' avec plage 1-12 car les ordres Markov sont des entiers
            'markov_smoothing': ('float', 0.08, 0.22),
            'markov_global_weight': ('float', 0.15, 0.75),  # Poids à accorder aux modèles globaux (entre 0 et 1)
            'markov_context_weight': ('float', 0.6, 0.9),  # Poids à accorder au contexte local dans le modèle de Markov
            'markov_decay_factor': ('float', 0.85, 0.98),  # Facteur de décroissance pour l'influence des états passés


            'gradient_clip_norm': ('float', 0.6711639422017494, 2.5),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'batch_size_factor': ('float', 0.11611287699226094, 0.3),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,

            'scheduler_type': ('categorical', ['cosine_warm', 'reduce_on_plateau', 'step']),  # Ajout d'options pour plus de flexibilité
            'scheduler_patience': ('int', 1, 9),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur fixe 5
            'scheduler_factor': ('float', 0.09303999370310236, 0.35),  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.301
            'optimizer_type': ('categorical', ['adam', 'adamw']),
            'label_smoothing': ('float', 0.05, 0.20),

            'mixup_alpha': ('float', 0.1, 0.4),

            'focal_loss_gamma': ('float', 1.5, 4.423636132390798),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 3.303
            'focal_loss_alpha': ('float', 0.42857591147696095, 0.7029722944508692),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.498
            'lstm_dropout_input': ('float', 0.09107147388423224, 0.25),  # Plage ajustée automatiquement,  # Plage étendue pour inclure la valeur actuelle
            'lstm_dropout_hidden': ('float', 0.2, 0.4241553499653589),  # Plage ajustée automatiquement,  # Plage étendue pour inclure la valeur actuelle
            'lstm_dropout_output': ('float', 0.14, 0.40420159862530225),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur fixe 0.147
            # Supprimé car déjà défini plus haut avec les deux options

            'normalization': ('categorical', ['robust_scaler', 'min_max_scaler']),

            'jitter_sigma': ('float', 0.05, 0.18),  # Plage élargie pour inclure la valeur 0.153
            'time_warp_sigma': ('float', 0.08643928926215229, 0.25),  # Plage ajustée automatiquement,
            'window_slice_ratio': ('float', 0.7503209708047155, 1.2015209293682643),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur fixe 1.002


            'ensemble_num_variants': ('int', 1, 11),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 1
            'temporal_weight': ('float', 0.17307624317159426, 0.6434173314959302),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'mc_dropout_samples': ('int', 5, 21),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur fixe 17

            'stability_window_size': ('int', 0, 17),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 4

            # Paramètres d'arrêt précoce pour l'évaluation des configurations
            'min_non_wait_accuracy': ('float', 0.2, 0.4),   # Augmenté pour exiger une précision minimale des NON-WAIT
            'min_wait_accuracy': ('float', 0.2, 0.4),       # Augmenté pour exiger une précision minimale des WAIT
            'min_recommendation_rate': ('float', 0.05, 0.2), # Augmenté pour exiger un taux de recommandation minimal
            'max_wait_ratio': ('float', 0.6, 0.8),          # Réduit pour éviter trop de recommandations WAIT
            'min_wait_ratio': ('float', 0.2, 0.4),          # Augmenté pour exiger un minimum de recommandations WAIT
            'stability_threshold': ('float', 0.03, 0.07),
            'distribution_window_size': ('int', 13, 28),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'excellence_confidence_threshold': ('float', 0.****************, 0.****************),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.757

            'simulation_banker_prob': ('float', 0.*****************, 0.68),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.459
            'simulation_prob_variation': ('float', 0.007411537106481294, 0.05),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour plus de flexibilité
            'simulation_min_prob': ('float', 0.*****************, 0.5),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur fixe 0.366
            'simulation_max_prob': ('float', 0.*****************, 0.****************),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour plus de flexibilité

            # PRIORITÉ 1 - Paramètres critiques pour obtenir au moins une recommandation NON-WAIT
            # Plages optimisées pour favoriser les recommandations NON-WAIT précises
            'min_confidence_for_recommendation': ('float', 0.15, 0.7131058528178182),  # Plage ajustée automatiquement,  # Plage encore plus élargie (0.15-0.65) pour permettre plus de variation

            'confidence_adjustment_step': ('float', 0.00989295390196255, 0.08),  # Plage ajustée automatiquement - Augmenté pour des ajustements plus rapides
            'fallback_penalty_factor': ('float', 0.07347666946445043, 0.2),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'uncertainty_threshold': ('float', 0.27591278233979805, 0.6),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur fixe 0.362
            'optimal_wait_ratio_target': ('float', self.OPTIMAL_WAIT_RATIO - 0.1, self.OPTIMAL_WAIT_RATIO + 0.05),  # Centré autour de OPTIMAL_WAIT_RATIO

            'mo_weight_markov': ('float', 0.14196022884115211, 0.25),  # Plage ajustée automatiquement,
            'mo_weight_lgbm': ('float', 0.15971462210089563, 0.35),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'mo_weight_lstm': ('float', 0.53, 0.7802557768858728),  # Plage ajustée pour avoir un minimum de 0.53

            'feature_selection_threshold': ('float', 0.048801492141951903, 0.10989113405956895),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'temporal_decay_factor': ('float', 0.7930142715581187, 1.3785914074044665),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.874

            'decay_factor': ('float', 0.5597917855094144, 1.1),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 1.047
            'sample_weight_decay_factor': ('float', 0.79845310731583, 1.15),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.965
            'uncertainty_max_samples': ('float', 0.46935569459171345, 0.8854157629001462),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.542
            'uncertainty_max_features': ('float', 0.6224062016610621, 1.05),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.841
            'calibration_method': ('categorical', ['isotonic', 'sigmoid']),
            'w_disagree': ('float', 0.4465809444371203, 0.68),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'w_entropy': ('float', 0.13507210156798505, 0.25),  # Plage ajustée automatiquement,

            'non_trained_threshold_multiplier': ('float', 1.4692556256336113, 3.1897042573986534),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 1.648

            'min_confidence_factor': ('float', 0.2096871530662189, 0.4),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.284
            'range_confidence_factor': ('float', 0.8900621831220307, 1.7343511374615952),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur fixe 1.044
            'w_epistemic': ('float', 0.27140309984654487, 0.5775260393835319),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage étendue pour inclure 0.30
            'w_aleatoric': ('float', 0.20, 0.35),  # Plage étendue pour inclure 0.20
            'w_sensitivity': ('float', 0.19, 0.45),  # Plage élargie pour inclure la valeur fixe 0.198
            'w_consecutive': ('float', 0.15, 0.26896960271338705),  # Plage ajustée automatiquement,
            'w_consecutive_target': ('float', 0.2, 0.4422937240356507),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'global_uncertainty_factor': ('float', 0.5143995806091778, 1.0),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage étendue pour inclure 0.586
            'base_uncertainty_non_trained': ('float', 0.3516287022215024, 0.75),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.730

            'precision_non_wait_weight': ('float', 0.7318539450150278, 1.0944213232176767),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur fixe 0.795
            'rate_component_weight': ('float', 0.0453432343402155, 0.15883165669603114),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'precision_wait_weight': ('float', 0.04617666768624173, 0.15),  # Plage ajustée automatiquement,

            # objective1_weight est retiré de l'espace de recherche Optuna
            # car il doit être fixe et non optimisable
            # Sa valeur fixe est définie dans __init__ : objective1_weight = 0.95
            'consecutive_focus_factor': ('float', 5.0, 8.80589982927813),  # Plage ajustée automatiquement,
            'late_game_weight_factor': ('float', 7.472137331851989, 12.781795143701256),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            # target_round_min et target_round_max sont retirés de l'espace de recherche Optuna
            # car ils doivent être fixes et non optimisables
            # Leurs valeurs fixes sont définies dans __init__ : target_round_min = 31 et target_round_max = 60
            'consecutive_penalty_factor': ('float', 1.6327202918674693, 3.0247506835277798),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 2.409

            'max_sequence_weight': ('float', 0.6163400983597542, 1.1315556890164375),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur fixe 0.780
            'avg_sequence_weight': ('float', 0.07319584911965452, 0.18),  # Plage ajustée automatiquement,
            'error_rate_weight': ('float', 0.02, 0.10506061427329155),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'sequence_bonus_threshold': ('int', 0, 6),  # Plage ajustée automatiquement,
            'sequence_bonus_factor': ('float', 0.12, 0.30),  # Plage élargie pour inclure la valeur 0.145
            'broken_sequence_penalty': ('float', 0.9788602178430579, 1.8),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,

            # Paramètres critiques pour l'équilibre WAIT/NON-WAIT
            'wait_ratio_tolerance': ('float', 0.04847654065650752, 0.25),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement - Augmenté pour plus de flexibilité
            'wait_decision_weight': ('float', 0.1843069104488761, 0.5130620465445431),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'missed_opportunity_penalty': ('float', 0.1, 0.2970789054650849),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'recovery_rate_weight': ('float', 0.15, 0.40991915169517873),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,

            # Paramètres pour le placement des WAIT
            'wait_ratio_min_threshold': ('float', 0.15, 0.49832206269888063),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.306
            'error_pattern_threshold': ('float', 0.28156953777207405, 0.65),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.430
            'transition_uncertainty_threshold': ('float', 0.3047507313528937, 0.7),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Augmenté pour tolérer plus d'incertitude
            'wait_optimizer_confidence_threshold': ('float', 0.3863667407194147, 0.7),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Réduit pour favoriser NON-WAIT

            # Paramètres pour l'optimisation des séquences consécutives
            'consecutive_threshold': ('int', 1, 12),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 3
            'max_consecutive_target': ('int', 5, 14),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'consecutive_error_penalty': ('float', 0.04742105230145422, 0.1),  # Plage ajustée automatiquement,
            'consecutive_recovery_rate': ('float', 0.04, 0.09660431712747179),  # Plage ajustée automatiquement,

            # Paramètres pour le traitement des séquences consécutives
            'min_pattern_occurrences': ('int', 0, 12),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'consecutive_weight': ('float', 0.4729958802258975, 0.9),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.846

            # Paramètres pour l'ajustement du seuil de confiance
            'base_confidence_threshold': ('float', 0.2, 0.35),  # Plage ajustée pour inclure la nouvelle valeur 0.30
            'confidence_threshold_adjustment': ('float', 0.01, 0.05),  # Plage ajustée pour inclure la nouvelle valeur 0.02
            'confidence_adjustment_rate': ('float', 0.008, 0.018954231605604378),  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.015
            'confidence_recovery_rate': ('float', 0.010, 0.025),  # Plage élargie pour inclure la valeur 0.020

            # Paramètres pour l'efficacité des séquences
            'sequence_efficiency_max_weight': ('float', 0.75, 1.400986256830102),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur fixe 0.950
            'sequence_efficiency_precision_weight': ('float', 0.08574479665827517, 0.2),  # Plage ajustée automatiquement,
            'sequence_efficiency_wait_weight': ('float', 0.1, 0.2167692542807381),  # Plage ajustée automatiquement,

            # Supprimé car déjà défini plus haut avec les deux options

            'min_sample_weight': ('float', 0.20, 0.40),
            'max_sample_weight': ('float', 3.0, 6.478188340044383),  # Plage ajustée automatiquement,

            'late_game_factor': ('float', 0.6584860764389717, 1.21),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur fixe 1.208
            'occurrence_factor_divisor': ('float', 88.78707090397126, 162.82278446514164),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour plus de flexibilité
            'consecutive_factor_divisor': ('float', 10.0, 28.37050062308653),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour plus de flexibilité
            'max_occurrence_factor': ('float', 1.3034750234722012, 2.4751987819941252),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 1.531
            'max_consecutive_factor': ('float', 1.42, 2.7219629698489136),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 1.425
            'consecutive_confidence_adjustment_factor': ('float', 0.15795437947284532, 0.3202056214954613),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,

            'pattern_similarity_threshold': ('float', 0.4698404372126059, 0.85),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.826
            'max_similar_patterns': ('int', 0, 17),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,

            'success_rate_weight': ('float', 0.19960780395579084, 0.45),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'consecutive_length_weight': ('float', 0.27545453877182113, 0.6757929013315027),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.331
            'pattern_frequency_weight': ('float', 0.093624255124316, 0.27049483911494526),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,

            'confidence_weight': ('float', 0.3, 0.5954705839828378),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'uncertainty_weight': ('float', 0.10, 0.25),

            # Paramètres pour le calcul de validity_probability
            'validity_confidence_weight': ('float', 0.4, 0.6),  # Poids de la confiance dans le calcul de validity_probability
            'validity_prob_diff_weight': ('float', 0.2, 0.4),   # Poids de la différence de probabilité dans le calcul de validity_probability
            'validity_certainty_weight': ('float', 0.1, 0.3),   # Poids de la certitude (1 - incertitude) dans le calcul de validity_probability

            # Paramètres pour le calcul de confiance et d'incertitude
            'markov_streak_factor': ('float', 1.0, 1.7912734732147344),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'lstm_streak_factor': ('float', 0.8490383029821825, 1.4267312534182484),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'sensitivity_normalization_factor': ('float', 2.2832214812073697, 7.0),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'consecutive_normalization_factor': ('float', 4.13205872222386, 15.0),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,





            # Paramètres pour l'ajustement de la courbe de décision
            'bell_curve_factor': ('float', 0.2, 0.42244114077588485),  # Plage ajustée automatiquement,



            # Paramètres pour les métriques et KPIs
            'kpi_window_size': ('int', 2, 12),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,
            'stability_threshold': ('float', 0.04, 0.10),
            'high_confidence_threshold': ('float', 0.49973014650771824, 0.9527861693452214),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.594

            # Paramètres pour les bonus et pénalités
            'ratio_bonus_threshold': ('float', 0.03, 0.07),
            'ratio_bonus_max': ('float', 0.12, 0.2926440314462572),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.136
            'precision_bonus_threshold': ('float', 0.48453091924346026, 0.8114193625869572),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.579
            'precision_bonus_factor': ('float', 0.4, 0.828602084650647),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.623
            'wait_efficiency_bonus_threshold': ('float', 0.4938006727935838, 0.9080037314159997),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.638
            'wait_efficiency_bonus_factor': ('float', 0.25, 0.4938390533189508),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur fixe 0.374
            'recovery_bonus_threshold': ('float', 0.6167151802021367, 1.0515870962783525),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.783
            'recovery_bonus_factor': ('float', 0.1355324243594333, 0.25),  # Plage ajustée automatiquement,
            'precision_penalty_threshold': ('float', 0.6755269644128541, 1.1348408335100828),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,  # Plage élargie pour inclure la valeur 0.772

            # Paramètres pour les occurrences et patterns
            'min_occurrences': ('int', 1, 10),  # Plage ajustée automatiquement,  # Plage ajustée automatiquement,

            # Paramètres pour les fonctionnalités avancées


            # Paramètre pour le calcul des features avec decay - Supprimé car déjà défini plus haut
        }

        if self.initial_weights:
            total = sum(self.initial_weights.values())
            if total > 1e-9:
                self.initial_weights = {k: v/total for k, v in self.initial_weights.items()}
            else:
                num_methods = len(self.initial_weights)
                self.initial_weights = {k: 1.0/num_methods for k in self.initial_weights.keys()} if num_methods > 0 else {}

                self.weight_lgbm = 0.21  # Valeur ajustée automatiquement - plage: [0.21, 0.4838427862493963]  # Valeur ajustée automatiquement - plage: [0.21, 0.4500546061019481]  # Valeur ajustée automatiquement - plage: [0.21, 0.4262112439134851]  # Valeur ajustée automatiquement - plage: [0.21, 0.3896448156676251]  # Valeur ajustée automatiquement - plage: [0.21, 0.3694275890925648]
                self.weight_lstm = 0.27318973526913776  # Valeur ajustée automatiquement - plage: [0.27318973526913776, 0.55]  # Valeur ajustée automatiquement - plage: [0.2897976112004819, 0.55]  # Valeur ajustée automatiquement - plage: [0.31490584409702227, 0.55]  # Valeur ajustée automatiquement - plage: [0.34853461351086185, 0.55]
                self.weight_markov = 0.19528563540727728  # Valeur ajustée automatiquement - plage: [0.19528563540727728, 0.35]

        resources_forced = False
        if isinstance(config_overrides, dict):
            try:
                forced_cores = int(config_overrides.get('cpu_cores'))
                forced_mem = int(config_overrides.get('max_memory_gb'))
                if forced_cores >= 1 and forced_mem >= 1:
                    self.default_cpu_cores = forced_cores
                    self.default_max_memory_gb = forced_mem
                    logger.info(f"RESSOURCES FORCÉES: CPU={self.default_cpu_cores}, RAM={self.default_max_memory_gb}GB")
                    resources_forced = True
            except (ValueError, TypeError, KeyError):
                logger.warning("Override invalide CPU/Mem.")

        if not resources_forced:
            # Suppression du log de débogage pour éviter les messages répétitifs
            # pendant l'optimisation Optuna
            env_cores = os.getenv('FORCE_CPU_CORES')
            env_mem = os.getenv('FORCE_MAX_MEM_GB')
            if env_cores and env_mem:
                try:
                    self.default_cpu_cores = max(1, int(env_cores))
                    self.default_max_memory_gb = max(1, int(env_mem))
                    logger.info(f"Config env: CPU={self.default_cpu_cores}, RAM={self.default_max_memory_gb}GB")
                except ValueError as e:
                    logger.warning(f"Erreur vars env: {e}. Fallback.")
                    self._detect_resources_with_psutil()
            else:
                self._detect_resources_with_psutil()

    def _detect_resources_with_psutil(self):
        """
        Détecte les ressources système disponibles (CPU, mémoire) avec psutil.
        """
        try:
            # Tentative d'import de psutil (peut être absent)
            try:
                import psutil
            except ImportError:
                psutil = None

            if psutil:
                detected = False
                try:
                    cores = psutil.cpu_count(logical=True)
                    mem_bytes = psutil.virtual_memory().total
                    if cores:
                        self.default_cpu_cores = max(1, cores)
                        calculated_mem_gb = max(2, int(mem_bytes / (1024**3) * 0.8))
                        self.default_max_memory_gb = max(28, calculated_mem_gb)
                        detected = True
                    else:
                        pass
                except Exception as e_psutil:
                    pass
                if not detected:
                    self.default_cpu_cores = 4
                    self.default_max_memory_gb = 28
            else:
                self.default_cpu_cores = 4
                self.default_max_memory_gb = 28
        except Exception as e_overall:
            self.default_cpu_cores = 4
            self.default_max_memory_gb = 28

    def clone(self):
        """
        Crée une copie profonde de la configuration.

        Returns:
            PredictorConfig: Une nouvelle instance avec les mêmes attributs
        """
        import copy
        new_config = PredictorConfig()

        # Copier tous les attributs de l'instance actuelle vers la nouvelle instance
        for attr_name, attr_value in vars(self).items():
            setattr(new_config, attr_name, copy.deepcopy(attr_value))

        # Journaliser les attributs copiés pour le débogage
        logger.debug(f"Configuration clonée avec {len(vars(new_config))} attributs")

        return new_config

    def get_min_sequence_length(self):
        """
        Détermine la longueur minimale de séquence requise pour tous les modèles.
        Utilise uniquement des paramètres existants dans la configuration.

        Returns:
            int: La longueur minimale de séquence requise.
        """
        # Longueur minimale pour Markov
        markov_max_order = getattr(self, 'markov_max_order', 5)
        # Utiliser markov_context_length si défini, sinon utiliser une valeur par défaut basée sur markov_max_order
        markov_context_length = getattr(self, 'markov_context_length', markov_max_order * 2)
        markov_min_length = max(markov_max_order, markov_context_length)

        # Longueur minimale pour LSTM
        lstm_sequence_length = getattr(self, 'lstm_sequence_length', 20)
        # Utiliser lstm_context_length si défini, sinon utiliser lstm_sequence_length
        lstm_context_length = getattr(self, 'lstm_context_length', lstm_sequence_length)
        lstm_min_length = lstm_context_length

        # Longueur minimale pour LGBM
        # Utiliser lgbm_min_features si défini, sinon utiliser une valeur par défaut
        lgbm_feature_window = getattr(self, 'lgbm_feature_window', 10)
        # Utiliser lgbm_context_length si défini, sinon utiliser une valeur basée sur lgbm_feature_window
        lgbm_context_length = getattr(self, 'lgbm_context_length', lgbm_feature_window * 2)
        lgbm_min_length = lgbm_context_length

        # Prendre le maximum des trois
        min_sequence_length = max(markov_min_length, lstm_min_length, lgbm_min_length)

        # Journaliser les longueurs minimales uniquement lors du premier appel ou si elles changent
        # Utiliser un attribut statique pour stocker les valeurs précédentes
        if not hasattr(self.__class__, '_last_min_lengths') or self.__class__._last_min_lengths != (markov_min_length, lstm_min_length, lgbm_min_length, min_sequence_length):
            logger.debug(f"Longueurs minimales de séquence: Markov={markov_min_length}, LSTM={lstm_min_length}, LGBM={lgbm_min_length}, Global={min_sequence_length}")
            self.__class__._last_min_lengths = (markov_min_length, lstm_min_length, lgbm_min_length, min_sequence_length)

        return min_sequence_length
