# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 1486 à 1579
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

                def calculate_confidence(self, features_vector, current_round, config):
                    """
                    Calcule la confiance dans les recommandations NON-WAIT pour la manche actuelle.

                    Args:
                        features_vector (List[float]): Vecteur de features pour la manche actuelle
                        current_round (int): Numéro de la manche actuelle
                        config: Configuration du prédicteur

                    Returns:
                        Dict: Dictionnaire contenant les métriques de confiance
                    """
                    # Extraire les paramètres de configuration
                    target_round_min = config.target_round_min
                    target_round_max = config.target_round_max

                    # Vérifier si nous sommes dans la plage de manches cibles
                    is_target_round = target_round_min <= current_round <= target_round_max

                    # Si nous ne sommes pas dans la plage cible, retourner une confiance faible
                    if not is_target_round:
                        return {
                            "confidence": getattr(config, 'default_confidence_value', 0.5),
                            "expected_consecutive": 0,
                            "similar_patterns_count": 0,
                            "success_rate": getattr(config, 'default_confidence_value', 0.5),
                            "wait_recommendation_strength": 0.0,
                            "non_wait_recommendation_strength": 0.0
                        }

                    # Extraire la clé de pattern à partir du vecteur de features
                    pattern_key = self._extract_pattern_key(features_vector)

                    # Récupérer les statistiques pour ce pattern
                    pattern_stats = self.pattern_stats[pattern_key]

                    # Calculer le taux de succès pour ce pattern
                    success_rate = pattern_stats["success"] / max(1, pattern_stats["total"])

                    # Calculer la longueur moyenne des séquences consécutives pour ce pattern
                    avg_consecutive = np.mean(pattern_stats["consecutive_lengths"]) if pattern_stats["consecutive_lengths"] else 0

                    # Calculer la confiance basée sur le taux de succès et la longueur moyenne des séquences consécutives
                    confidence = success_rate * 0.7 + min(1.0, avg_consecutive / 10.0) * 0.3

                    # Calculer la force de recommandation WAIT
                    wait_recommendation_strength = 0.0

                    # Calculer la force de recommandation NON-WAIT
                    non_wait_recommendation_strength = confidence

                    # Calculer le bonus en forme de cloche pour les manches au milieu de la plage cible
                    relative_pos = (current_round - target_round_min) / (target_round_max - target_round_min)
                    bell_curve_bonus = 1.0 + 0.2 * (1.0 - 4.0 * (relative_pos - 0.5) ** 2)

                    # Calculer le bonus de séquence pour les patterns avec des séquences consécutives longues
                    sequence_bonus = 1.0
                    if pattern_stats["max_consecutive"] >= getattr(config, 'sequence_bonus_threshold', 4):
                        sequence_bonus = 1.0 + getattr(config, 'sequence_bonus_factor', 0.1) * (pattern_stats["max_consecutive"] - getattr(config, 'sequence_bonus_threshold', 4))

                    # Calculer le facteur de jeu tardif pour les manches proches de la fin de la plage cible
                    late_game_factor = 1.0
                    if relative_pos > 0.7:
                        late_game_factor = 1.0 + getattr(config, 'late_game_factor', 1.0) * (relative_pos - 0.7) / 0.3

                    # Calculer le facteur d'occurrence pour les patterns fréquents
                    occurrence_factor = min(1.0 + pattern_stats["total"] / getattr(config, 'occurrence_factor_divisor', 10.0), getattr(config, 'max_occurrence_factor', 2.0))

                    # Calculer le facteur consécutif pour les patterns avec des séquences consécutives longues
                    consecutive_factor = min(1.0 + pattern_stats["max_consecutive"] / getattr(config, 'consecutive_factor_divisor', 10.0), getattr(config, 'max_consecutive_factor', 2.0))

                    # Calculer le ratio WAIT actuel
                    current_wait_ratio = self.get_current_wait_ratio()

                    # Ajuster la force de recommandation NON-WAIT en fonction des facteurs calculés
                    non_wait_recommendation_strength = non_wait_recommendation_strength * bell_curve_bonus * sequence_bonus * late_game_factor * occurrence_factor * consecutive_factor

                    # Limiter la force de recommandation NON-WAIT entre 0 et 1
                    non_wait_recommendation_strength = min(1.0, max(0.0, non_wait_recommendation_strength))

                    return {
                        "confidence": confidence,
                        "expected_consecutive": avg_consecutive,
                        "similar_patterns_count": pattern_stats["total"],
                        "success_rate": success_rate,
                        "wait_recommendation_strength": wait_recommendation_strength,
                        "non_wait_recommendation_strength": non_wait_recommendation_strength,
                        "bell_curve_bonus": bell_curve_bonus,
                        "sequence_bonus": sequence_bonus,
                        "late_game_factor": late_game_factor,
                        "occurrence_factor": occurrence_factor,
                        "consecutive_factor": consecutive_factor,
                        "current_wait_ratio": current_wait_ratio
                    }