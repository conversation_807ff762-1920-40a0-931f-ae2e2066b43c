# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 777 à 787
# Type: Méthode de la classe ModelProvider

    def create_model_factory(model_class, **default_params):
        """
        Crée une factory pour instancier des modèles avec des paramètres par défaut.
        Nouvelle fonctionnalité pour simplifier la création de modèles.
        """
        def factory(**kwargs):
            # Combiner les paramètres par défaut avec ceux fournis
            params = {**default_params, **kwargs}
            return model_class(**params)

        return factory