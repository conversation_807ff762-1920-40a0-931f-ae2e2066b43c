# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 4078 à 4135
# Type: Méthode de la classe HybridBaccaratPredictor

    def _get_historical_data_for_refit(self) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """Tente de récupérer et préparer TOUTES les données historiques pour le refit des wrappers LGBM."""
        # Utiliser un verrou car accède à self.loaded_historical et self.historical_data
        with self.sequence_lock:
            if not self.loaded_historical or not self.historical_data:
                logger.warning("_get_historical_data_for_refit: Pas de données historiques chargées.")
                return None, None

            data_to_process = self.historical_data[:] # Créer une copie pour itération

        logger.info("Préparation des données historiques complètes pour le refit des wrappers LGBM...")
        all_features_lgbm = []
        all_labels_lgbm = []
        min_seq_len_for_features = self.config.lstm_sequence_length
        total_rounds_extracted = 0

        # Utiliser les noms de features actuellement définis dans l'instance
        current_feature_names = self.feature_names[:]
        n_expected_features = len(current_feature_names)
        if n_expected_features == 0:
            logger.error("_get_historical_data_for_refit: self.feature_names est vide. Impossible de générer/valider.")
            return None, None

        lstm_input_feat_size = getattr(self.lstm, 'input_size', 6) if self.lstm else 6
        expected_lstm_shape = (self.config.lstm_sequence_length, lstm_input_feat_size)

        for game_idx, game_sequence in enumerate(data_to_process):
            if len(game_sequence) <= min_seq_len_for_features:
                # logger.debug(f"  Partie {game_idx+1} ignorée pour refit (trop courte)")
                continue

            for i in range(min_seq_len_for_features, len(game_sequence)):
                input_sequence = game_sequence[:i]
                actual_outcome = game_sequence[i]
                # Appeler create_hybrid_features avec les feature_names actuels
                features_lgbm, _ = self.create_hybrid_features(input_sequence) # Ignore LSTM part

                if features_lgbm is not None and len(features_lgbm) == n_expected_features:
                    label = 1 if actual_outcome == 'banker' else 0
                    all_features_lgbm.append(features_lgbm)
                    all_labels_lgbm.append(label)
                    total_rounds_extracted += 1
                elif features_lgbm is not None:
                     logger.warning(f"Incohérence nbre features LGBM pour refit à partie {game_idx+1}, coup {i}. Attendu {n_expected_features}, obtenu {len(features_lgbm)}. Ignoré.")
                # else: create_hybrid_features a déjà loggé si None

        if not all_features_lgbm:
            logger.error("Échec de la génération de features LGBM à partir des données historiques pour refit.")
            return None, None

        try:
            X_lgbm = np.array(all_features_lgbm, dtype=np.float32)
            y_lgbm = np.array(all_labels_lgbm, dtype=np.int64)
            logger.info(f"Données historiques préparées pour refit LGBM: {X_lgbm.shape}. Total rounds extraits: {total_rounds_extracted}")
            return X_lgbm, y_lgbm
        except Exception as e_np:
            logger.error(f"Erreur de conversion NumPy pour données refit: {e_np}", exc_info=True)
            return None, None