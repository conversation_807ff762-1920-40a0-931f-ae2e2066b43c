# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 7106 à 7267
# Type: Méthode de la classe OptunaOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def _optimize_memory_for_full_dataset(self):
        """
        Optimise la mémoire spécifiquement pour l'entraînement sur le jeu de données complet.
        Cette méthode implémente des stratégies avancées pour réduire l'empreinte mémoire
        lors de l'utilisation de 100% des données.

        Returns:
            bool: True si l'optimisation a réussi
        """
        import gc
        import os
        import sys
        import numpy as np
        import torch

        logger.warning("=" * 80)
        logger.warning("OPTIMISATION AVANCÉE DE LA MÉMOIRE POUR L'ENTRAÎNEMENT COMPLET")
        logger.warning("=" * 80)

        # 1. Détection de la mémoire disponible
        try:
            import psutil
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            memory_usage_process = memory_info.rss / (1024 * 1024 * 1024)  # En GB

            # Mémoire système
            system_memory = psutil.virtual_memory()
            total_memory = system_memory.total / (1024 * 1024 * 1024)  # En GB
            available_memory = system_memory.available / (1024 * 1024 * 1024)  # En GB

            logger.warning(f"Mémoire totale: {total_memory:.2f} GB")
            logger.warning(f"Mémoire disponible: {available_memory:.2f} GB")
            logger.warning(f"Mémoire utilisée par le processus: {memory_usage_process:.2f} GB")

            # Estimer la mémoire nécessaire pour l'entraînement complet
            if hasattr(self, 'X_lgbm_full') and self.X_lgbm_full is not None:
                lgbm_size = self.X_lgbm_full.nbytes / (1024 * 1024 * 1024)  # En GB
                logger.warning(f"Taille estimée des données LGBM: {lgbm_size:.2f} GB")

                # Estimer la mémoire nécessaire pour LSTM (si activé)
                if hasattr(self.config, 'lstm_epochs') and self.config.lstm_epochs > 0:
                    # LSTM utilise généralement 2-3x plus de mémoire que les données brutes
                    lstm_memory_estimate = lgbm_size * 3
                    logger.warning(f"Estimation mémoire LSTM: {lstm_memory_estimate:.2f} GB")

                    # Vérifier si nous avons assez de mémoire
                    if lstm_memory_estimate > available_memory:
                        logger.warning("ALERTE: Mémoire insuffisante pour LSTM avec toutes les données!")
                        logger.warning("Application de stratégies d'optimisation mémoire agressives...")

                        # Réduire le nombre d'époques LSTM si nécessaire
                        if self.config.lstm_epochs > 2:
                            original_epochs = self.config.lstm_epochs
                            self.config.lstm_epochs = 2
                            logger.warning(f"Nombre d'époques LSTM réduit de {original_epochs} à {self.config.lstm_epochs}")

                        # Réduire la taille du batch si nécessaire
                        if hasattr(self.config, 'batch_size') and self.config.batch_size > 256:
                            original_batch = self.config.batch_size
                            self.config.batch_size = 256
                            logger.warning(f"Taille de batch réduite de {original_batch} à {self.config.batch_size}")
        except ImportError:
            logger.warning("Module psutil non disponible, optimisation mémoire limitée")

        # 2. Libération des caches et ressources non essentielles
        # Supprimer les données intermédiaires
        intermediate_data_attrs = [
            '_intermediate_features',
            '_feature_cache',
            '_prediction_cache',
            '_validation_results',
            '_temp_models'
        ]

        for attr in intermediate_data_attrs:
            if hasattr(self, attr):
                delattr(self, attr)
                logger.warning(f"Données intermédiaires {attr} libérées")

        # 3. Optimisation des structures de données
        # Convertir les tableaux NumPy en types plus compacts
        for attr_name in dir(self):
            if attr_name.startswith('_'):
                continue

            try:
                attr = getattr(self, attr_name)
                if isinstance(attr, np.ndarray):
                    # Tableaux de grande taille
                    if attr.size > 1000000:
                        # Conversion agressive des types
                        if attr.dtype == np.float64:
                            setattr(self, attr_name, attr.astype(np.float32))
                            logger.warning(f"Tableau {attr_name} converti de float64 à float32")
                        elif attr.dtype == np.float32 and np.max(np.abs(attr)) < 65504:
                            setattr(self, attr_name, attr.astype(np.float16))
                            logger.warning(f"Tableau {attr_name} converti de float32 à float16")
                        elif attr.dtype == np.int64:
                            setattr(self, attr_name, attr.astype(np.int32))
                            logger.warning(f"Tableau {attr_name} converti de int64 à int32")
                        elif attr.dtype == np.int32 and np.max(np.abs(attr)) < 32767:
                            setattr(self, attr_name, attr.astype(np.int16))
                            logger.warning(f"Tableau {attr_name} converti de int32 à int16")
            except:
                pass

        # 4. Optimisation des tenseurs PyTorch
        if torch.cuda.is_available():
            # Libérer le cache CUDA
            torch.cuda.empty_cache()
            logger.warning("Cache CUDA vidé")

            # Définir une stratégie d'allocation mémoire plus conservatrice
            if hasattr(torch.cuda, 'set_per_process_memory_fraction'):
                torch.cuda.set_per_process_memory_fraction(0.8)  # Utiliser 80% de la mémoire GPU max
                logger.warning("Limite d'utilisation mémoire GPU définie à 80%")

        # 5. Optimisation du garbage collector
        gc.collect()

        # Configurer le GC pour être plus agressif
        try:
            gc.set_threshold(100, 5, 5)  # Valeurs plus basses = GC plus fréquent
            logger.warning("Garbage collector configuré pour être plus agressif")
        except:
            logger.warning("Impossible de configurer le garbage collector")

        # 6. Compactage de la mémoire système
        try:
            import ctypes
            ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1)
            logger.warning("Mémoire système compactée")
        except:
            logger.warning("Impossible de compacter la mémoire système")

        # 7. Vérification finale de la mémoire
        try:
            import psutil
            system_memory = psutil.virtual_memory()
            available_memory = system_memory.available / (1024 * 1024 * 1024)  # En GB

            logger.warning(f"Mémoire disponible après optimisation: {available_memory:.2f} GB")

            # Alerte si la mémoire est toujours critique
            if available_memory < 2:
                logger.warning("ALERTE: Mémoire système toujours critique après optimisation!")
                logger.warning("Risque élevé d'erreur Out Of Memory pendant l'entraînement complet")

                # Stratégies d'urgence
                if hasattr(self.config, 'lstm_epochs') and self.config.lstm_epochs > 0:
                    self.config.lstm_epochs = 1
                    logger.warning(f"URGENCE: Nombre d'époques LSTM réduit à {self.config.lstm_epochs}")

                if hasattr(self.config, 'batch_size') and self.config.batch_size > 128:
                    self.config.batch_size = 128
                    logger.warning(f"URGENCE: Taille de batch réduite à {self.config.batch_size}")
        except:
            pass

        logger.warning("Optimisation mémoire pour l'entraînement complet terminée")
        return True