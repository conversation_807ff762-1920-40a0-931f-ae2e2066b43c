# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 2359 à 2461
# Type: Méthode de la classe HybridBaccaratPredictor

    def _save_model_metadata(self, model_filepath: str, package: Dict[str, Any]) -> bool:
        """
        Sauvegarde les métadonnées du modèle dans un fichier JSON associé.

        Args:
            model_filepath (str): Chemin du fichier du modèle
            package (Dict[str, Any]): Package contenant les données du modèle

        Returns:
            bool: True si la sauvegarde a réussi, False sinon
        """
        try:
            # Extraire les informations pertinentes du package
            config_details = package.get('config_details', {})

            # Calculer des métriques de performance
            performance_metrics = {}
            if hasattr(self, 'best_accuracy'):
                performance_metrics['best_accuracy'] = self.best_accuracy

            # Extraire les performances par méthode
            method_performance = {}
            if hasattr(self, 'method_performance') and self.method_performance:
                for method, perf in self.method_performance.items():
                    if 'accuracy' in perf:
                        method_performance[method] = perf['accuracy']

            # Créer un dictionnaire avec toutes les métadonnées
            metadata = {
                'timestamp': datetime.now().isoformat(),
                'model_file': os.path.basename(model_filepath),
                'model_type': 'HybridBaccaratPredictor',
                'version': package.get('predictor_version', 'unknown'),
                'performance_metrics': performance_metrics,
                'method_performance': method_performance,
                'sequence_length': len(package.get('sequence', [])),
                'hyperparameters': {
                    # Paramètres généraux
                    'min_confidence_for_recommendation': getattr(self.config, 'min_confidence_for_recommendation', 0.65),
                    # Suppression de max_confidence_for_recommendation qui n'existe pas dans PredictorConfig
                    'error_pattern_threshold': getattr(self.config, 'error_pattern_threshold', 0.5),
                    'transition_uncertainty_threshold': getattr(self.config, 'transition_uncertainty_threshold', 0.6),
                    'wait_optimizer_confidence_threshold': getattr(self.config, 'wait_optimizer_confidence_threshold', 0.5),
                    'wait_ratio_min_threshold': getattr(self.config, 'wait_ratio_min_threshold', 0.3),
                    'wait_ratio_max_threshold': getattr(self.config, 'wait_ratio_max_threshold', 0.7),

                    # Paramètres LSTM
                    'lstm_hidden_dim': getattr(self.config, 'lstm_hidden_dim', None),
                    'lstm_num_layers': getattr(self.config, 'lstm_num_layers', None),
                    'lstm_dropout': getattr(self.config, 'lstm_dropout', None),
                    'lstm_bidirectional': getattr(self.config, 'lstm_bidirectional', None),
                    'lstm_learning_rate': getattr(self.config, 'lstm_learning_rate', None),
                    'lstm_weight_decay': getattr(self.config, 'lstm_weight_decay', None),
                    'lstm_batch_size': getattr(self.config, 'lstm_batch_size', None),
                    'lstm_epochs': getattr(self.config, 'lstm_epochs', None),
                    'lstm_sequence_length': getattr(self.config, 'lstm_sequence_length', None),

                    # Paramètres LGBM
                    'lgbm_n_estimators': getattr(self.config, 'lgbm_n_estimators', None),
                    'lgbm_learning_rate': getattr(self.config, 'lgbm_learning_rate', None),
                    'lgbm_max_depth': getattr(self.config, 'lgbm_max_depth', None),
                    'lgbm_num_leaves': getattr(self.config, 'lgbm_num_leaves', None),
                    'lgbm_min_child_samples': getattr(self.config, 'lgbm_min_child_samples', None),
                    'lgbm_subsample': getattr(self.config, 'lgbm_subsample', None),
                    'lgbm_colsample_bytree': getattr(self.config, 'lgbm_colsample_bytree', None),
                    'lgbm_reg_alpha': getattr(self.config, 'lgbm_reg_alpha', None),
                    'lgbm_reg_lambda': getattr(self.config, 'lgbm_reg_lambda', None),

                    # Paramètres Markov
                    'max_markov_order': getattr(self.config, 'max_markov_order', None),
                    'markov_smoothing': getattr(self.config, 'markov_smoothing', None),

                    # Poids des modèles
                    'initial_weights': getattr(self.config, 'initial_weights', None),
                    'current_weights': self.weights.copy() if hasattr(self, 'weights') else None,
                    'best_weights': self.best_weights.copy() if hasattr(self, 'best_weights') else None,
                },
                'optimization_phase': config_details.get('optimization_phase', None),
                'training_info': {
                    'last_train_time': package.get('last_train_time', None),
                    'last_save_time': package.get('last_save_time', None),
                    'save_timestamp': package.get('save_timestamp', None),
                }
            }

            # Filtrer les valeurs None
            def filter_none_values(d):
                if isinstance(d, dict):
                    return {k: filter_none_values(v) for k, v in d.items() if v is not None}
                return d

            metadata = filter_none_values(metadata)

            # Sauvegarder dans un fichier JSON avec le même nom de base
            json_filepath = os.path.splitext(model_filepath)[0] + '.json'
            with open(json_filepath, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)

            logger.info(f"Métadonnées du modèle sauvegardées dans {json_filepath}")
            return True
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde des métadonnées: {e}", exc_info=True)
            return False