# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1135 à 1144
# Type: Méthode de la classe TrainOptimizeInterface

    def register_metric(self, name: str, metric: Any) -> None:
        """
        Enregistre une métrique dans l'interface.

        Args:
            name: Nom unique de la métrique
            metric: Métrique à enregistrer
        """
        self.metrics[name] = metric
        logger.debug(f"Métrique '{name}' enregistrée dans l'interface")