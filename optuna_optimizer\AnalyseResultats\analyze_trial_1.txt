# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 14208 à 14324
# Type: Méthode de la classe MetaOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def analyze_trial(self, trial: optuna.trial.FrozenTrial) -> None:
        """
        Analyse un essai Optuna pour déterminer s'il est problématique ou réussi.
        Utilise des métriques avancées pour une meilleure classification des essais.

        Args:
            trial: Essai Optuna à analyser
        """
        self.analyzed_trials_count += 1

        # Extraire le ratio WAIT et le score de l'essai
        wait_ratio = trial.user_attrs.get('wait_ratio', None)
        score = trial.user_attrs.get('score', None)
        recommendation_rate = trial.user_attrs.get('recommendation_rate', None)

        if wait_ratio is None:
            logger.warning(f"Essai {trial.number}: Ratio WAIT non trouvé dans les attributs utilisateur")
            return

        # Enregistrer les métriques dans l'historique des performances
        if score is not None:
            self.performance_history['scores'].append(score)
        self.performance_history['wait_ratios'].append(wait_ratio)
        if recommendation_rate is not None:
            self.performance_history['recommendation_rates'].append(recommendation_rate)

        # Vérifier si l'essai est problématique
        is_problematic = (wait_ratio <= self.wait_ratio_min_threshold or
                          wait_ratio >= self.wait_ratio_max_threshold)

        # Vérifier si l'essai est réussi (bon équilibre et bon score)
        is_successful = False
        if score is not None:
            is_successful = (
                score >= self.success_criteria['min_score'] and
                self.success_criteria['optimal_wait_ratio_min'] <= wait_ratio <= self.success_criteria['optimal_wait_ratio_max']
            )

            # Si le taux de recommandation est fourni, l'inclure dans les critères de succès
            if recommendation_rate is not None:
                is_successful = is_successful and (recommendation_rate >= self.success_criteria['min_recommendation_rate'])

        if is_problematic:
            logger.warning(f"Essai {trial.number} identifié comme problématique: ratio WAIT = {wait_ratio:.2f}")

            # Ajouter l'essai à la liste des essais problématiques
            self.problematic_trials.append({
                'trial_number': trial.number,
                'params': trial.params,
                'wait_ratio': wait_ratio,
                'score': score,
                'recommendation_rate': recommendation_rate,
                'too_few_wait': wait_ratio <= self.wait_ratio_min_threshold,
                'too_many_wait': wait_ratio >= self.wait_ratio_max_threshold
            })

            # Analyser les paramètres de l'essai
            self._identify_problematic_params()

            logger.warning(f"Nombre total d'essais problématiques: {len(self.problematic_trials)}")
            logger.warning(f"Paramètres problématiques identifiés: {list(self.problematic_params.keys())}")
        elif is_successful:
            # Enregistrer l'essai réussi
            self.successful_trials.append({
                'trial_number': trial.number,
                'params': trial.params,
                'wait_ratio': wait_ratio,
                'score': score,
                'recommendation_rate': recommendation_rate
            })

            # Limiter le nombre d'essais réussis stockés pour éviter une consommation excessive de mémoire
            if len(self.successful_trials) > 20:
                # Garder les essais avec les meilleurs scores
                self.successful_trials.sort(key=lambda x: x.get('score', 0), reverse=True)
                self.successful_trials = self.successful_trials[:20]

            logger.info(f"Essai {trial.number} identifié comme RÉUSSI: wait_ratio={wait_ratio:.4f}, score={score:.4f if score is not None else 'N/A'}")
            logger.info(f"Paramètres: {trial.params}")
            logger.info(f"Nombre total d'essais réussis stockés: {len(self.successful_trials)}")

            # Adapter les facteurs d'exploration/exploitation si l'échantillonnage adaptatif est activé
            if hasattr(self, 'use_adaptive_sampling') and self.use_adaptive_sampling and self.analyzed_trials_count > 10:
                # Réduire progressivement l'exploration et augmenter l'exploitation
                self.adaptive_sampling['exploration_factor'] = max(
                    self.adaptive_sampling['min_exploration'],
                    self.adaptive_sampling['exploration_factor'] - self.adaptive_sampling['adaptation_rate']
                )
                self.adaptive_sampling['exploitation_factor'] = 1.0 - self.adaptive_sampling['exploration_factor']

                logger.info(f"Facteurs d'échantillonnage adaptatif ajustés: "
                           f"exploration={self.adaptive_sampling['exploration_factor']:.2f}, "
                           f"exploitation={self.adaptive_sampling['exploitation_factor']:.2f}")

        # Adapter dynamiquement les critères de succès en fonction de l'historique des performances
        if hasattr(self, 'performance_history') and self.analyzed_trials_count % 10 == 0 and len(self.performance_history.get('scores', [])) >= 10:
            # Calculer les statistiques des performances
            mean_score = np.mean(self.performance_history['scores'])
            std_score = np.std(self.performance_history['scores'])

            # Ajuster le seuil de score minimum en fonction des performances observées
            # Plus conservateur au début, plus exigeant à mesure que l'optimisation progresse
            if self.analyzed_trials_count < 30:
                # Phase initiale: seuil plus bas pour encourager l'exploration
                new_min_score = max(0.5, mean_score - 0.5 * std_score)
            elif self.analyzed_trials_count < 60:
                # Phase intermédiaire: seuil modéré
                new_min_score = max(0.6, mean_score - 0.3 * std_score)
            else:
                # Phase avancée: seuil plus élevé pour se concentrer sur les meilleurs essais
                new_min_score = max(0.65, mean_score - 0.1 * std_score)

            # Mettre à jour le critère de score minimum
            self.success_criteria['min_score'] = new_min_score

            logger.info(f"Critère de score minimum ajusté: {new_min_score:.4f} "
                       f"(moyenne={mean_score:.4f}, écart-type={std_score:.4f})")