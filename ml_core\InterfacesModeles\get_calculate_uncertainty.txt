# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 752 à 774
# Type: Méthode de la classe ModelProvider

    def get_calculate_uncertainty(cls):
        """
        Importe la méthode calculate_uncertainty depuis hbp.py de manière conditionnelle
        pour éviter les importations circulaires.
        Utilise un cache pour éviter de recréer l'instance HBP à chaque appel.

        Returns:
            function: La méthode calculate_uncertainty
        """
        # Vérifier si la fonction est déjà en cache
        if cls._calculate_uncertainty_func is not None:
            return cls._calculate_uncertainty_func

        # Obtenir une instance de HybridBaccaratPredictor (utilise le singleton)
        temp_hbp = cls.get_hbp_instance()

        # Stocker la méthode calculate_uncertainty en cache
        cls._calculate_uncertainty_func = temp_hbp.calculate_uncertainty

        # Retourner la méthode calculate_uncertainty
        # Nous utilisons directement la méthode de l'instance HBP pour garantir
        # que nous utilisons exactement la même logique que dans la production
        return cls._calculate_uncertainty_func