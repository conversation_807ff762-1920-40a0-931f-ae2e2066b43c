# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 144 à 152
# Type: Méthode de la classe LoggingManager
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def _initialize(self):
        """Initialise le gestionnaire de journalisation."""
        self._loggers = {}
        self._handlers = {}
        self._formatters = {}
        self._log_directory = 'logs'

        # Créer le répertoire de logs s'il n'existe pas
        os.makedirs(self._log_directory, exist_ok=True)