# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 3338 à 3410
# Type: Méthode de la classe WaitPlacementOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, config=None):
        """
        Initialise l'optimiseur de placement des recommandations WAIT.

        Args:
            config: Configuration du prédicteur (optionnel)
        """
        # Paramètres configurables
        self.config = config

        # Seuils et paramètres
        if config is None:
            # Valeurs par défaut si aucune configuration n'est fournie
            self.error_pattern_threshold = 0.6
            self.transition_uncertainty_threshold = 0.7
            self.wait_efficiency_threshold = 0.7
            self.consecutive_priority_factor = 2.0
            self.min_pattern_occurrences = 3
            self.max_pattern_history = 1000
            self.recent_history_window = 10
            self.error_weight = 0.6
            self.transition_weight = 0.4
            self.wait_efficiency_weight = 0.5
            self.consecutive_weight = 0.5
            self.learning_rate = 0.1
            self.target_round_min = 31
            self.target_round_max = 60
            self.wait_ratio_min = 0.2
            self.wait_ratio_max = 0.5
            self.adaptive_thresholds = True
        else:
            # Récupérer les paramètres depuis la configuration
            self.error_pattern_threshold = getattr(config, 'error_pattern_threshold', 0.6)
            self.transition_uncertainty_threshold = getattr(config, 'transition_uncertainty_threshold', 0.7)
            self.wait_efficiency_threshold = getattr(config, 'wait_efficiency_threshold', 0.7)
            self.consecutive_priority_factor = getattr(config, 'consecutive_priority_factor', 2.0)
            self.min_pattern_occurrences = getattr(config, 'min_pattern_occurrences', 3)
            self.max_pattern_history = getattr(config, 'max_pattern_history', 1000)
            self.recent_history_window = getattr(config, 'recent_history_window', 10)
            self.error_weight = getattr(config, 'error_weight', 0.6)
            self.transition_weight = getattr(config, 'transition_weight', 0.4)
            self.wait_efficiency_weight = getattr(config, 'wait_efficiency_weight', 0.5)
            self.consecutive_weight = getattr(config, 'consecutive_weight', 0.5)
            self.learning_rate = getattr(config, 'learning_rate', 0.1)
            self.target_round_min = getattr(config, 'target_round_min', 31)
            self.target_round_max = getattr(config, 'target_round_max', 60)
            self.wait_ratio_min = getattr(config, 'wait_ratio_min', 0.2)
            self.wait_ratio_max = getattr(config, 'wait_ratio_max', 0.5)
            self.adaptive_thresholds = getattr(config, 'adaptive_thresholds', True)

        # Structures de données pour l'apprentissage
        self.error_patterns = {}  # Patterns qui précèdent souvent des erreurs
        self.transition_patterns = {}  # Patterns de transition entre séquences
        self.wait_efficiency_history = []  # Historique de l'efficacité des WAIT
        self.pattern_history = []  # Historique des patterns observés
        self.outcome_history = []  # Historique des résultats
        self.recommendation_history = []  # Historique des recommandations

        # Métriques de performance
        self.total_waits = 0
        self.effective_waits = 0  # WAIT qui ont évité une erreur
        self.missed_opportunities = 0  # WAIT qui ont manqué une bonne recommandation
        self.current_consecutive_valid = 0
        self.max_consecutive_valid = 0
        self.current_wait_ratio = 0.0

        # Compteurs pour l'adaptation des seuils
        self.total_decisions = 0
        self.correct_wait_decisions = 0
        self.correct_non_wait_decisions = 0

        # Logger
        self.logger = logging.getLogger(__name__)