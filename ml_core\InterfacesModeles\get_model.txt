# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1088 à 1104
# Type: Méthode de la classe TrainOptimizeInterface

    def get_model(self, name: str) -> Any:
        """
        Récupère un modèle enregistré dans l'interface.

        Args:
            name: Nom du modèle à récupérer

        Returns:
            Le modèle enregistré

        Raises:
            KeyError: Si le modèle n'est pas enregistré
        """
        if name not in self.models:
            raise KeyError(f"Modèle '{name}' non enregistré dans l'interface")

        return self.models[name]