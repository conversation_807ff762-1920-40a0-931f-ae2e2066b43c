DESCRIPTIF DÉTAILLÉ DES MÉTHODES - GESTIONRESSOURCES
=========================================================

Ce fichier contient la description détaillée de toutes les méthodes
présentes dans le sous-dossier GestionRessources.

1. _cache_features.txt (OptunaOptimizer._cache_features - MÉTHODE CACHE FEATURES)
   - Lignes 6211-6241 dans optuna_optimizer.py (31 lignes)
   - FONCTION : Met en cache features pour ensemble indices donné avec gestion cache avancé
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * indices - Indices des échantillons
     * X_lgbm - Features LGBM
     * X_lstm - Features LSTM
     * y (optionnel) - Labels
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION CACHE :** Appelle _initialize_advanced_data_cache() si cache inexistant
     * **VÉRIFICATION ACTIVATION :** Vérifie config.use_advanced_cache avant mise en cache
     * **GÉNÉRATION CLÉ :** Utilise _get_cache_key(indices) pour clé unique
     * **STOCKAGE FEATURES :** Met (X_lgbm, X_lstm, y) dans _advanced_data_cache['feature_cache']
     * **LOGGING CACHE :** Journalise nombre échantillons et début clé cache
     * **NETTOYAGE AUTOMATIQUE :** Appelle _cleanup_cache_if_needed() pour gestion mémoire
   - RETOUR : str - Clé de cache générée ou None si cache désactivé
   - UTILITÉ : Optimisation performance avec cache features pour réutilisation rapide


2. _cache_preprocessed_data.txt (OptunaOptimizer._cache_preprocessed_data - MÉTHODE CACHE DONNÉES PRÉTRAITÉES)
   - Lignes 6156-6181 dans optuna_optimizer.py (26 lignes)
   - FONCTION : Met en cache données prétraitées pour taille sous-ensemble donnée avec nettoyage automatique
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * subset_size - Taille du sous-ensemble
     * data - Données prétraitées à mettre en cache
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION CACHE :** Vérifie _advanced_data_cache, initialise si absent
     * **VÉRIFICATION ACTIVATION :** Contrôle config.use_advanced_cache avant mise en cache
     * **STOCKAGE DONNÉES :** Stocke dans _advanced_data_cache['preprocessed_data'][subset_size]
     * **LOGGING :** Journalise mise en cache avec subset_size pour traçabilité
     * **NETTOYAGE AUTOMATIQUE :** Appelle _cleanup_cache_if_needed() pour gestion mémoire
   - RETOUR : bool - True si mise en cache réussie, False si cache désactivé
   - UTILITÉ : Optimisation performance avec cache intelligent données prétraitées


3. _cleanup_cache_if_needed.txt (OptunaOptimizer._cleanup_cache_if_needed - MÉTHODE NETTOYAGE CACHE INTELLIGENT)
   - Lignes 6274-6441 dans optuna_optimizer.py (168 lignes)
   - FONCTION : Nettoie cache intelligemment pour éviter consommation excessive mémoire avec stratégies avancées
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * force (bool, défaut=False) - Force nettoyage même si intervalle non atteint
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION INTERVALLE :** Respecte cleanup_interval (5min) sauf si force=True
     * **CALCUL TAILLE RÉCURSIF :** Fonction get_size() avec protection cycles pour mesure précise
     * **ANALYSE COMPOSANTS :** Mesure taille preprocessed_data, feature_cache, validation_cache, etc.
     * **STATISTIQUES DÉTAILLÉES :** Affiche cache_hits/misses et répartition mémoire par composant
     * **SURVEILLANCE SYSTÈME :** Utilise psutil pour mémoire disponible et ajustement dynamique
     * **ADAPTATION AUTOMATIQUE :** Réduit max_cache_size si mémoire système <1GB disponible
     * **NETTOYAGE EXPIRÉ :** Supprime entrées avec timestamp > ttl (1h par défaut)
     * **LIMITATION ENTRÉES :** Garde max_items_per_category (100) plus récentes par composant
     * **PHASES PRIORITAIRES :** Préserve phase3/markov selon priority_phases configuration
     * **NETTOYAGE AGRESSIF :** Vide phases non prioritaires si cache >90% capacité
     * **COLLECTE GARBAGE :** Force gc.collect() après nettoyage pour libération mémoire
     * **MESURE POST-NETTOYAGE :** Recalcule et affiche nouvelle taille cache
   - RETOUR : None (nettoie cache en place)
   - UTILITÉ : Gestion mémoire intelligente avec nettoyage adaptatif et préservation données critiques


4. _configure_optimization_for_resources.txt (OptunaOptimizer._configure_optimization_for_resources - MÉTHODE CONFIGURATION OPTIMISATION RESSOURCES)
   - Lignes 5655-5718 dans optuna_optimizer.py (64 lignes)
   - FONCTION : Configure paramètres optimisation selon ressources disponibles avec adaptation workers, GPU, batch et cache
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * resources - Dictionnaire ressources disponibles (cpu, memory, gpu)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **WORKERS CPU :** 75% threads disponibles avec limitation mémoire (2GB/worker)
     * **LIMITATION MÉMOIRE :** memory_limited_workers = available_gb / 2 pour éviter OOM
     * **AJUSTEMENT N_JOBS :** Réduit config.n_jobs si > recommended_workers avec logging
     * **CONFIGURATION GPU :** Active/désactive config.use_gpu selon resources['gpu']['available']
     * **BATCH SIZE ADAPTATIF :** <4GB RAM → batch_size ≤ 256 pour mémoire limitée
     * **CACHE ADAPTATIF :** >16GB→4096MB, >8GB→2048MB, >4GB→1024MB, ≤4GB→512MB
     * **LOGGING DÉTAILLÉ :** Journalise tous ajustements workers, GPU, batch, cache
   - RETOUR : None (configure en place)
   - UTILITÉ : Optimisation automatique configuration selon ressources système pour performance maximale


5. _decompress_entry.txt (OptunaOptimizer._decompress_entry - MÉTHODE DÉCOMPRESSION ENTRÉE CACHE)
   - Lignes 10666-10699 dans optuna_optimizer.py (34 lignes)
   - FONCTION : Décompresse entrée cache compressée avec validation format et gestion erreurs
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * cache_dict - Dictionnaire cache contenant entrée
     * key - Clé entrée à décompresser
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION EXISTENCE :** Vérifie key in cache_dict avant traitement
     * **VALIDATION FORMAT :** Vérifie tuple longueur 2 avec marqueur '__compressed__'
     * **DÉCOMPRESSION ZLIB :** zlib.decompress() pour données compressées
     * **DÉSÉRIALISATION PICKLE :** pickle.loads() pour reconstruction objet
     * **REMPLACEMENT CACHE :** Remplace entrée compressée par version décompressée
     * **MISE À JOUR STATS :** Supprime key de cache_stats['compressed']
     * **GESTION ERREURS :** try/except avec logging warning si échec
   - RETOUR : bool - True si décompression réussie, False sinon
   - UTILITÉ : Décompression transparente cache avec validation robuste et gestion erreurs


6. _detect_available_resources.txt (OptunaOptimizer._detect_available_resources - MÉTHODE DÉTECTION RESSOURCES SYSTÈME)
   - Lignes 5554-5653 dans optuna_optimizer.py (100 lignes)
   - FONCTION : Détection complète des ressources système disponibles pour optimiser l'utilisation lors de l'optimisation
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STRUCTURE RESSOURCES :** Initialise dictionnaire complet avec CPU, mémoire, GPU, stockage, système
     * **DÉTECTION CPU :** Utilise multiprocessing pour détecter cœurs physiques et threads logiques
     * **ESTIMATION CŒURS :** Estime cœurs physiques = threads // 2 pour hyperthreading
     * **ARCHITECTURE CPU :** Récupère architecture avec platform.machine() (x86_64, ARM, etc.)
     * **DÉTECTION MÉMOIRE :** Utilise psutil.virtual_memory() pour total, disponible, pourcentage utilisé
     * **CONVERSION UNITÉS :** Convertit bytes en GB avec division par (1024**3)
     * **UTILISATION CPU :** Mesure utilisation CPU en temps réel avec psutil.cpu_percent(interval=0.5)
     * **ESPACE DISQUE :** Analyse stockage avec psutil.disk_usage() sur répertoire courant
     * **DÉTECTION GPU :** Utilise PyTorch pour détecter disponibilité CUDA et propriétés GPU
     * **PROPRIÉTÉS GPU :** Récupère nom, mémoire totale, version CUDA si GPU disponible
     * **INFORMATIONS SYSTÈME :** Collecte OS, version, version Python avec platform
     * **TIMESTAMP :** Ajoute horodatage pour traçabilité des mesures
     * **GESTION ERREURS :** Gestion robuste avec try/except pour chaque type de ressource
     * **LOGGING DÉTAILLÉ :** Journalise toutes les ressources détectées avec unités appropriées
     * **FALLBACK GRACIEUX :** Continue même si certains modules (psutil, torch) indisponibles
     * **CONFIGURATION AUTOMATIQUE :** Appelle _configure_optimization_for_resources() pour adaptation
     * **OPTIMISATION ADAPTATIVE :** Utilise ressources détectées pour configurer parallélisme et batch sizes
   - RETOUR : dict - Dictionnaire complet des ressources système avec toutes les métriques
   - UTILITÉ : Détection intelligente des ressources système pour optimisation adaptative des performances


7. _detect_available_resources_1.txt (OptunaOptimizer._detect_available_resources - MÉTHODE DÉTECTION RESSOURCES SYSTÈME COMPLÈTE)
   - Lignes 5897-6019 dans optuna_optimizer.py (123 lignes)
   - FONCTION : Détecte ressources système (CPU, RAM, GPU) et recommande paramètres optimaux utilisation ressources
   - PARAMÈTRES : self - Instance de la classe OptunaOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉTECTION CPU :** multiprocessing.cpu_count() avec 80% threads recommandés pour optimal_workers
     * **DÉTECTION MÉMOIRE :** psutil.virtual_memory() pour total/available GB et pourcentage utilisé
     * **BATCH SIZE ADAPTATIF :** <4GB→256, <8GB→512, <16GB→1024, ≥16GB→2048 selon mémoire disponible
     * **DÉTECTION GPU :** torch.cuda.is_available() avec device_count() et propriétés chaque GPU
     * **INFORMATIONS GPU :** torch.cuda.get_device_name() et total_memory pour chaque device
     * **AJUSTEMENT GPU :** Double optimal_batch_size (max 2048) si GPU disponible
     * **LIMITATION WORKERS :** 2GB/worker estimation avec min(cpu_threads, memory_limit) pour optimal_workers
     * **GESTION ERREURS :** try/except pour chaque détection avec fallback valeurs défaut
     * **LOGGING DÉTAILLÉ :** Journalise CPU, mémoire, GPU détectés avec recommandations
   - RETOUR : dict - Ressources détectées avec optimal_workers et optimal_batch_size recommandés
   - UTILITÉ : Détection complète ressources système pour optimisation automatique configuration


8. _enable_memory_profiling.txt (OptunaOptimizer._enable_memory_profiling - MÉTHODE ACTIVATION PROFILAGE MÉMOIRE)
   - Lignes 6679-6697 dans optuna_optimizer.py (19 lignes)
   - FONCTION : Active profilage mémoire avec tracemalloc pour identifier fuites mémoire
   - PARAMÈTRES : self - Instance de la classe OptunaOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **IMPORT TRACEMALLOC :** Importe module tracemalloc avec gestion ImportError
     * **DÉMARRAGE PROFILAGE :** tracemalloc.start() pour activation surveillance
     * **TIMESTAMP DÉBUT :** Stocke _memory_profiling_start_time pour mesures durée
     * **LOGGING ACTIVATION :** Confirme activation profilage mémoire
     * **FALLBACK GRACIEUX :** Retourne False si tracemalloc indisponible
   - RETOUR : bool - True si activé, False si module indisponible
   - UTILITÉ : Activation surveillance mémoire pour détection fuites et optimisation


9. _estimate_resource_constraints.txt (OptunaOptimizer._estimate_resource_constraints - MÉTHODE ESTIMATION CONTRAINTES RESSOURCES)
   - Lignes 12446-12486 dans optuna_optimizer.py (41 lignes)
   - FONCTION : Estime contraintes ressources selon mémoire, CPU, GPU disponibles avec calcul indicateur combiné
   - PARAMÈTRES : self - Instance de la classe OptunaOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **MÉMOIRE DISPONIBLE :** psutil.virtual_memory().available en GB pour évaluation contraintes
     * **CPU PHYSIQUES :** psutil.cpu_count(logical=False) pour cœurs réels disponibles
     * **GPU DISPONIBLE :** torch.cuda.is_available() pour détection accélération
     * **CONTRAINTE MÉMOIRE :** <4GB→2.0 (élevée), >16GB→0.5 (faible), sinon 1.0 (moyenne)
     * **CONTRAINTE CPU :** <4 cœurs→2.0 (élevée), >8 cœurs→0.5 (faible), sinon 1.0 (moyenne)
     * **CONTRAINTE GPU :** GPU disponible→1.0, pas GPU→2.0 (contrainte élevée)
     * **COMBINAISON :** (memory + cpu + gpu) / 3 pour indicateur global contraintes
     * **GESTION ERREURS :** try/except avec fallback 1.0 et logging erreur
     * **LOGGING INFORMATIF :** Journalise contraintes estimées pour traçabilité
   - RETOUR : float - Indicateur contraintes ressources (1.0 = contraintes moyennes)
   - UTILITÉ : Évaluation contraintes système pour adaptation intelligente paramètres optimisation


10. _get_cache_key.txt (OptunaOptimizer._get_cache_key - MÉTHODE GÉNÉRATION CLÉ CACHE)
   - Lignes 6122-6154 dans optuna_optimizer.py (33 lignes)
   - FONCTION : Génère clé cache unique basée sur indices, configuration et phase
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * indices (optionnel) - Indices échantillons, None pour tous indices
     * config (optionnel) - Configuration à utiliser, None pour configuration actuelle
     * phase (optionnel) - Phase d'optimisation
   - FONCTIONNEMENT DÉTAILLÉ :
     * **OPTIMISATION INDICES :** Si len(indices) > 100, utilise 50 premiers + 50 derniers triés
     * **ÉCHANTILLONNAGE INTELLIGENT :** Évite clés trop longues avec échantillonnage représentatif
     * **HASH INDICES :** Génère MD5 hash de sample_indices triés pour unicité
     * **HASH CONFIG :** Utilise MD5 de sorted(config.__dict__.items()) pour configuration
     * **DICTIONNAIRE CLÉ :** Crée key_dict avec indices_hash, indices_len, config_hash, phase
     * **CLÉ FINALE :** Génère MD5 de JSON.dumps(key_dict, sort_keys=True) pour unicité
   - RETOUR : str - Clé cache unique MD5 hexdigest
   - UTILITÉ : Génération clés cache uniques pour éviter collisions et optimiser stockage


11. _get_cached_features.txt (OptunaOptimizer._get_cached_features - MÉTHODE RÉCUPÉRATION FEATURES CACHE)
   - Lignes 6243-6272 dans optuna_optimizer.py (30 lignes)
   - FONCTION : Récupère features en cache pour ensemble indices donné avec compteurs hits/misses
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * indices - Indices des échantillons
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION CACHE :** Appelle _initialize_advanced_data_cache() si inexistant
     * **VÉRIFICATION ACTIVATION :** Vérifie config.use_advanced_cache, incrémente cache_misses si désactivé
     * **GÉNÉRATION CLÉ :** Utilise _get_cache_key(indices) pour clé unique
     * **RECHERCHE CACHE :** Vérifie présence cache_key dans feature_cache
     * **COMPTEUR HITS :** Incrémente cache_hits et log utilisation si trouvé
     * **COMPTEUR MISSES :** Incrémente cache_misses si non trouvé
   - RETOUR : tuple (X_lgbm, X_lstm, y) ou None si non trouvées
   - UTILITÉ : Récupération rapide features prétraitées avec suivi performance cache


12. _get_preprocessed_data.txt (OptunaOptimizer._get_preprocessed_data - MÉTHODE RÉCUPÉRATION DONNÉES PRÉTRAITÉES)
   - Lignes 6183-6209 dans optuna_optimizer.py (27 lignes)
   - FONCTION : Récupère données prétraitées depuis cache avancé pour taille sous-ensemble donnée
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * subset_size - Taille du sous-ensemble
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION CACHE :** _initialize_advanced_data_cache() si pas hasattr(self, '_advanced_data_cache')
     * **VÉRIFICATION ACTIVATION :** getattr(self.config, 'use_advanced_cache', True)
     * **CACHE DÉSACTIVÉ :** Incrémente cache_misses et retourne None
     * **RECHERCHE CACHE :** subset_size in _advanced_data_cache['preprocessed_data']
     * **CACHE HIT :** Incrémente cache_hits avec logging warning et retourne données
     * **CACHE MISS :** Incrémente cache_misses et retourne None
   - RETOUR : tuple - Données prétraitées ou None si non trouvées
   - UTILITÉ : Accès efficace données prétraitées avec gestion cache et statistiques hits/misses


13. _initialize_advanced_data_cache.txt (OptunaOptimizer._initialize_advanced_data_cache - MÉTHODE INITIALISATION CACHE AVANCÉ)
   - Lignes 6021-6092 dans optuna_optimizer.py (72 lignes)
   - FONCTION : Initialise cache avancé données prétraitées avec gestion mémoire optimisée
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **NETTOYAGE MÉMOIRE :** Force gc.collect() avant initialisation cache
     * **STRUCTURE CACHE :** Crée dictionnaire avec preprocessed_data, feature_cache, validation_cache
     * **CACHE MODÈLES :** Inclut model_cache, prediction_cache, importance_cache
     * **DONNÉES PHASES :** Structure phase_data pour phase0/1/2/3/markov séparées
     * **MÉTRIQUES CACHE :** Suit cache_hits, cache_misses, last_cleanup, memory_usage
     * **CONFIGURATION ADAPTATIVE :** max_size_mb, cleanup_interval, max_items_per_category, ttl
     * **DÉTECTION MÉMOIRE :** Utilise psutil pour ajuster taille cache selon RAM disponible
     * **ADAPTATION INTELLIGENTE :** >16GB→4GB cache, >8GB→2GB, >4GB→1GB, <4GB→512MB
     * **PHASES PRIORITAIRES :** Configure priority_phases=['phase3', 'markov'] pour conservation
     * **COMPRESSION :** Paramètre compression_level configurable (0-9)
     * **PLANIFICATION NETTOYAGE :** Appelle _schedule_cache_cleanup() pour maintenance automatique
   - RETOUR : None (initialise attribut _advanced_data_cache)
   - UTILITÉ : Cache haute performance avec adaptation automatique ressources système


14. _optimize_memory_for_full_dataset.txt (OptunaOptimizer._optimize_memory_for_full_dataset - MÉTHODE OPTIMISATION MÉMOIRE DATASET COMPLET)
   - Lignes 6881-6998 dans optuna_optimizer.py (118 lignes)
   - FONCTION : Optimise mémoire spécifiquement pour utilisation ensemble données complet avec stratégies avancées
   - PARAMÈTRES : self - Instance de la classe OptunaOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **COLLECTE GARBAGE MULTIPLE :** Double gc.collect() pour nettoyage complet
     * **DÉTECTION RESSOURCES :** psutil pour mémoire système (total, disponible, pourcentage)
     * **ALERTES CRITIQUES :** <2GB→optimisations agressives, <4GB→optimisations standards
     * **CACHE ADAPTATIF :** Réduit max_size_mb (256MB critique, 512MB limité)
     * **NETTOYAGE CACHES :** Vide feature_cache, prediction_cache, importance_cache si critique
     * **OPTIMISATION NUMPY :** Conversion float64→float32→float16 pour tableaux >1M éléments
     * **OPTIMISATION PYTORCH :** torch.cuda.empty_cache() et limitation GPU 80%
     * **CONVERSION STRUCTURES :** Listes >1000 éléments converties en tableaux NumPy
     * **COMPACTAGE SYSTÈME :** SetProcessWorkingSetSize Windows pour compactage mémoire
   - RETOUR : None (optimise mémoire en place)
   - UTILITÉ : Préparation mémoire optimale pour traitement dataset complet sans erreurs OOM


15. _optimize_memory_for_full_dataset_1.txt (OptunaOptimizer._optimize_memory_for_full_dataset - MÉTHODE OPTIMISATION MÉMOIRE DATASET COMPLET - DOUBLON)
   - Lignes 7106-7267 dans optuna_optimizer.py (163 lignes)
   - FONCTION : Optimise mémoire pour entraînement dataset complet avec stratégies avancées réduction empreinte mémoire
   - PARAMÈTRES : self - Instance de la classe OptunaOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉTECTION MÉMOIRE :** psutil pour memory_info, total/available memory en GB
     * **ESTIMATION BESOINS :** X_lgbm_full.nbytes pour taille données, LSTM 3x plus mémoire
     * **STRATÉGIES AGRESSIVES :** Réduit lstm_epochs à 2, batch_size à 256 si mémoire insuffisante
     * **LIBÉRATION CACHES :** Supprime _intermediate_features, _feature_cache, _prediction_cache, _validation_results, _temp_models
     * **OPTIMISATION TYPES :** Conversion float64→float32→float16, int64→int32→int16 pour tableaux >1M éléments
     * **OPTIMISATION CUDA :** torch.cuda.empty_cache(), set_per_process_memory_fraction(0.8)
     * **GARBAGE COLLECTOR :** gc.set_threshold(100, 5, 5) pour GC plus agressif
     * **COMPACTAGE SYSTÈME :** ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1)
     * **STRATÉGIES URGENCE :** lstm_epochs=1, batch_size=128 si <2GB disponible après optimisation
     * **VÉRIFICATION FINALE :** Contrôle mémoire disponible avec alertes critiques
   - RETOUR : bool - True si optimisation réussie
   - UTILITÉ : Optimisation mémoire avancée pour entraînement complet avec stratégies urgence et monitoring


16. _optimize_memory_for_full_dataset_2.txt (OptunaOptimizer._optimize_memory_for_full_dataset - MÉTHODE OPTIMISATION MÉMOIRE DATASET COMPLET - DOUBLON DE DOUBLON)
   - Lignes 11333-11476 dans optuna_optimizer.py (145 lignes)
   - FONCTION : Optimise mémoire pour grands ensembles données avec conversion types et générateur adaptatif
   - PARAMÈTRES : self - Instance de la classe OptunaOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **CONVERSION TYPES :** X_lgbm_full/X_lstm_full float64→float16 si data_range<65504, sinon float32
     * **ÉCONOMIE MÉMOIRE :** Calcule réduction pourcentage avec original_size vs new_size en MB
     * **COMPRESSION CACHE :** Supprime phase0/1/2_sequences de _preprocessed_data_cache pour économie
     * **GARBAGE COLLECTION :** Double gc.collect() pour libération mémoire complète
     * **GÉNÉRATEUR ADAPTATIF :** adaptive_data_generator avec batch_size selon mémoire disponible
     * **BATCH ADAPTATIF :** <2GB→100, <4GB→500, <8GB→1000, ≥8GB→2000 selon psutil.virtual_memory()
     * **OPTIMISATION CUDA :** torch.cuda.empty_cache() et set_per_process_memory_fraction(0.8)
     * **MONITORING MÉMOIRE :** Rapport détaillé process.memory_info() et system memory
     * **ALERTES CRITIQUES :** <2GB→OOM risk, <4GB→performances réduites
     * **STOCKAGE GÉNÉRATEUR :** self.data_generator pour utilisation ultérieure
   - RETOUR : None (optimise en place)
   - UTILITÉ : Optimisation mémoire complète avec générateur adaptatif et monitoring système


17. _optimize_memory_usage.txt (OptunaOptimizer._optimize_memory_usage - MÉTHODE OPTIMISATION UTILISATION MÉMOIRE)
   - Lignes 7000-7104 dans optuna_optimizer.py (105 lignes)
   - FONCTION : Optimise utilisation mémoire en libérant ressources inutilisées avec stratégies avancées entre phases
   - PARAMÈTRES : self - Instance de la classe OptunaOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **LIBÉRATION CACHES :** Supprime _cached_historical_data, _temp_model_cache, _temp_predictions, _temp_evaluation_results, _temp_feature_importances
     * **OPTIMISATION MODÈLES :** LightGBM save_model/reload pour réduction empreinte mémoire
     * **OPTIMISATION CUDA :** torch.cuda.empty_cache() pour libération cache GPU
     * **CONVERSION NUMPY :** Tableaux >1M éléments float64→float32→float16 selon plage valeurs
     * **GARBAGE COLLECTION :** Double gc.collect() pour cycles complets nettoyage
     * **COMPACTAGE SYSTÈME :** ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1)
     * **MONITORING DÉTAILLÉ :** psutil.Process().memory_info() et virtual_memory() pour rapport
     * **ALERTES CRITIQUES :** <2GB disponible → alerte OOM risk
     * **FALLBACK MESURE :** sys.getsizeof(self) si psutil indisponible
   - RETOUR : bool - True après optimisation
   - UTILITÉ : Optimisation mémoire complète entre phases avec monitoring et alertes critiques


18. _optimize_training_batch_sizes.txt (OptunaOptimizer._optimize_training_batch_sizes - MÉTHODE OPTIMISATION BATCH SIZES COMPLEXE)
   - Lignes 11963-12244 dans optuna_optimizer.py (282 lignes)
   - FONCTION : Optimisation automatique des tailles de batch et paramètres pour tous les modèles selon ressources et données
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **DÉTECTION RESSOURCES :** Utilise psutil pour RAM disponible, CPU cores, et torch pour GPU
     * **MÉTRIQUES ADAPTATIVES :** Calcule samples_per_core et memory_per_sample pour adaptation
     * **OPTIMISATION LGBM :** Calcule subsample, min_child_samples, num_iterations, learning_rate adaptatifs
     * **SUBSAMPLE ADAPTATIF :** Ajuste selon ratio ressources (0.85-0.98) pour éviter overfitting
     * **MIN_CHILD_SAMPLES :** Calcule selon taille données (0.0002-0.0005 * total_samples)
     * **ITERATIONS OPTIMALES :** Adapte num_iterations (50-500) selon complexité données
     * **LEARNING_RATE LGBM :** Calcule inversement proportionnel aux itérations (0.01-0.2)
     * **OPTIMISATION LSTM :** Calcule batch_size, epochs, learning_rate selon complexité modèle
     * **COMPLEXITÉ MODÈLE :** Estime mémoire par échantillon selon hidden_size, layers, input_size
     * **FACTEURS AJUSTEMENT :** Utilise complexity_factor et resource_factor pour adaptation
     * **BATCH SIZE GPU :** Optimise pour GPU (8-256) avec memory_factor basé sur VRAM
     * **BATCH SIZE CPU :** Optimise pour CPU (4-128) avec facteur mémoire RAM
     * **EPOCHS ADAPTATIFS :** Calcule inversement proportionnel à batch_size (2-15)
     * **LEARNING_RATE LSTM :** Adapte selon batch_size (0.0005-0.005)
     * **OPTIMISATION MARKOV :** Calcule depth, smoothing, batch_size selon taille données
     * **MARKOV DEPTH :** Adapte profondeur historique (3-6) selon total_samples
     * **MARKOV SMOOTHING :** Calcule lissage inversement proportionnel à taille données
     * **MARKOV BATCH_SIZE :** Adapte selon ressources (10-500) pour mises à jour
     * **SEGMENTS ÉVALUATION :** Configure evaluation_segments adaptatifs selon ressources
     * **PARALLÉLISATION OPTIMALE :** Calcule optimal_jobs par phase selon CPU cores
     * **PARAMÈTRES PAR PHASE :** Crée configurations spécialisées phase0-3 avec ajustements
     * **PHASE 0 EXPLORATION :** Paramètres plus élevés pour exploration rapide
     * **PHASE 1-2 ÉQUILIBRE :** Paramètres standards pour optimisation progressive
     * **PHASE 3 FINE-TUNING :** Paramètres réduits pour optimisation très fine
     * **STOCKAGE COMPLET :** Sauvegarde tous paramètres dans optimal_batch_params
     * **LOGGING DÉTAILLÉ :** Journalise tous paramètres optimisés par modèle
   - RETOUR : dict - Dictionnaire complet optimal_batch_params avec tous paramètres optimisés
   - UTILITÉ : Optimisation automatique complète des paramètres de batch pour tous modèles selon ressources système


19. _schedule_cache_cleanup.txt (OptunaOptimizer._schedule_cache_cleanup - MÉTHODE PLANIFICATION NETTOYAGE CACHE)
   - Lignes 6094-6120 dans optuna_optimizer.py (27 lignes)
   - FONCTION : Planifie nettoyage cache automatique selon intervalle configuré
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VÉRIFICATION CACHE :** Vérifie existence _advanced_data_cache avant traitement
     * **RÉCUPÉRATION CONFIG :** Lit cleanup_interval depuis cache_config (5min par défaut)
     * **CALCUL TEMPS :** Compare current_time avec last_cleanup pour décision nettoyage
     * **NETTOYAGE FORCÉ :** Appelle _cleanup_cache_if_needed(force=True) si intervalle dépassé
     * **MISE À JOUR TIMESTAMP :** Met à jour last_cleanup avec current_time après nettoyage
     * **LOGGING DEBUG :** Journalise temps écoulé depuis dernier nettoyage
   - RETOUR : None (planification automatique)
   - UTILITÉ : Maintenance automatique cache pour éviter accumulation excessive données


20. _take_memory_snapshot.txt (OptunaOptimizer._take_memory_snapshot - MÉTHODE INSTANTANÉ MÉMOIRE DÉTAILLÉ - 4ème MÉTHODE LA PLUS LONGUE)
   - Lignes 6699-6879 dans optuna_optimizer.py (181 lignes)
   - FONCTION : Prend instantané détaillé mémoire avec tracemalloc, psutil et analyse objets Python pour surveillance avancée
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * label - Étiquette pour l'instantané
   - FONCTIONNEMENT DÉTAILLÉ :
     * **TRACEMALLOC :** tracemalloc.start() et take_snapshot() avec statistics('lineno') pour top 20 blocs
     * **COMPARAISON :** snapshot.compare_to() avec instantané précédent pour différences mémoire
     * **PSUTIL PROCESSUS :** process.memory_info() pour RSS/VMS en MiB du processus actuel
     * **PSUTIL SYSTÈME :** virtual_memory() pour total/available GB et pourcentage utilisé
     * **ALERTES CRITIQUES :** <2GB→OOM risk, <4GB→performances réduites avec stockage alert
     * **OBJETS PYTHON :** gc.get_objects() avec comptage par type et tri par nombre instances
     * **HISTORIQUE :** _memory_snapshots_history limité à 10 instantanés pour éviter consommation
     * **TEMPS ÉCOULÉ :** Calcul depuis _memory_profiling_start_time pour monitoring durée
     * **STOCKAGE STRUCTURÉ :** memory_info avec timestamp, tracemalloc, psutil, objects, comparison
   - RETOUR : dict - Informations détaillées utilisation mémoire avec historique et comparaisons
   - UTILITÉ : Surveillance mémoire avancée avec traçage détaillé, comparaisons et alertes pour optimisation performance


21. _update_cache_stats.txt (OptunaOptimizer._update_cache_stats - MÉTHODE MISE À JOUR STATISTIQUES CACHE)
   - Lignes 10598-10664 dans optuna_optimizer.py (67 lignes)
   - FONCTION : Met à jour statistiques utilisation cache avec tracking accès et optimisation décompression
   - PARAMÈTRES :
     * self - Instance de la classe OptunaOptimizer
     * cache_type - Type cache ('preprocessed', 'feature', 'validation')
     * key - Clé entrée cache
     * hit (bool, défaut=False) - True si hit cache, False si miss
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION STATS :** cache_stats avec access_counts, last_access, creation_time, hit_value, compressed
     * **COMPTEUR ACCÈS :** access_counts[key] += 1 pour tracking fréquence utilisation
     * **TIMESTAMP ACCÈS :** last_access[key] = current_time pour LRU
     * **TEMPS ÉCONOMISÉ :** hit_value selon type (preprocessed=0.5s, feature=2.0s, validation=5.0s)
     * **DÉCOMPRESSION ADAPTATIVE :** Si key in compressed ET access_counts[key] >= 3
     * **CACHE SPÉCIALISÉ :** Gestion preprocessed_data, feature_cache, validation_cache
     * **OPTIMISATION ACCÈS :** _decompress_entry() pour entrées fréquemment utilisées
   - RETOUR : None (mise à jour statistiques internes)
   - UTILITÉ : Optimisation cache avec statistiques détaillées et décompression adaptative pour performance


TOTAL : 21 méthodes analysées et documentées
- Méthodes trouvées dans descriptif principal: 21
- Méthodes manquantes: 0