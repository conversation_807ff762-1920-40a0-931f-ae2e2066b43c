# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 4271 à 4303
# Type: Méthode de la classe HybridBaccaratPredictor

    def _apply_data_sampling(self, original_historical_data: List[List[str]], max_games: Optional[int], sampling_fraction: Optional[float]) -> Tuple[List[List[str]], str]:
        """
        Extrait la logique d'échantillonnage des données dans une fonction séparée.
        MODIFIÉ: Désactive l'échantillonnage pour garantir que toutes les manches 31-60 soient utilisées.
        """
        logger_instance = getattr(self, 'logger', logging.getLogger(__name__))
        num_original_games = len(original_historical_data)
        data_to_process = original_historical_data

        # MODIFICATION: Forcer l'utilisation de toutes les données, ignorer max_games et sampling_fraction
        sampling_applied_info = f"Utilisation historique COMPLET ({num_original_games} jeux) pour garantir toutes les manches 31-60."
        logger_instance.info("Échantillonnage désactivé pour garantir que toutes les manches 31-60 soient utilisées.")

        # Code original commenté pour référence
        """
        if max_games is not None and max_games > 0:
            if max_games < num_original_games:
              data_to_process = original_historical_data[-max_games:]
              sampling_applied_info = f"Échantillonnage appliqué : {len(data_to_process)} jeux récents (max={max_games})."
            else:
              sampling_applied_info = f"max_games ({max_games}) >= total jeux ({num_original_games}). Utilisation de tous."
        elif sampling_fraction is not None and 0 < sampling_fraction <= 1.0:
            num_sampled_games = max(1, int(num_original_games * sampling_fraction))
            if num_sampled_games < num_original_games:
              sampled_indices = np.random.choice(num_original_games, num_sampled_games, replace=False)
              sampled_indices.sort()
              data_to_process = [original_historical_data[i] for i in sampled_indices]
              sampling_applied_info = f"Échantillonnage appliqué : {len(data_to_process)} jeux aléatoires (frac={sampling_fraction:.2f})."
            else:
              sampling_applied_info = f"sampling_fraction ({sampling_fraction:.2f}) couvre tous les jeux ({num_original_games}). Utilisation de tous."
        """

        return data_to_process, sampling_applied_info