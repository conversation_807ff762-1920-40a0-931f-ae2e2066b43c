# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 6630 à 6677
# Type: Méthode de la classe OptunaOptimizer

    def _profile_function(self, func_name, func, *args, **kwargs):
        """
        Profile une fonction pour identifier les goulots d'étranglement.

        Args:
            func_name: Nom de la fonction à profiler
            func: Fonction à profiler
            *args: Arguments positionnels à passer à la fonction
            **kwargs: Arguments nommés à passer à la fonction

        Returns:
            Any: Résultat de la fonction
        """
        import time
        import cProfile
        import pstats
        import io

        # Vérifier si le profilage est activé
        if not getattr(self.config, 'enable_profiling', False):
            return func(*args, **kwargs)

        # Mesurer le temps d'exécution
        start_time = time.time()

        # Créer un profiler
        profiler = cProfile.Profile()
        profiler.enable()

        # Exécuter la fonction
        result = func(*args, **kwargs)

        # Arrêter le profiler
        profiler.disable()

        # Calculer le temps d'exécution
        elapsed_time = time.time() - start_time

        # Créer un rapport de profilage
        s = io.StringIO()
        ps = pstats.Stats(profiler, stream=s).sort_stats('cumulative')
        ps.print_stats(20)  # Afficher les 20 fonctions les plus coûteuses

        # Afficher le rapport
        logger.warning(f"Profilage de {func_name}: {elapsed_time:.2f} secondes")
        logger.warning(s.getvalue())

        return result