# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 654 à 657
# Type: Méthode de la classe DynamicRangeAdjuster

    def _backup_original_ranges(self) -> None:
        """Sauvegarde les plages originales."""
        self.original_ranges = self._get_current_ranges()
        logger.info(f"Plages originales sauvegardées: {self.original_ranges}")