#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de vérification complète des fichiers Descriptif.txt
Vérifie la numérotation, la complétude et la cohérence
"""

import os
import re
from pathlib import Path

def analyser_fichier_descriptif(filepath):
    """Analyse un fichier Descriptif.txt et retourne les statistiques"""
    print(f"\n🔍 Analyse de {filepath}")
    print("=" * 60)
    
    if not os.path.exists(filepath):
        print(f"❌ Fichier non trouvé: {filepath}")
        return None
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        total_lines = len(lines)
        
        # Détecter les sections
        sections = []
        section_pattern = r'^SECTION\s+\d+\s*:\s*([A-Z]+)'
        for i, line in enumerate(lines):
            match = re.match(section_pattern, line)
            if match:
                sections.append({
                    'line': i + 1,
                    'name': match.group(1),
                    'text': line.strip()
                })
        
        # Détecter les méthodes/fichiers
        methodes = []
        methode_pattern = r'^(\d+)\.\s+([a-zA-Z_][a-zA-Z0-9_]*\.txt)\s*\((.*?)\)'
        
        for i, line in enumerate(lines):
            match = re.match(methode_pattern, line)
            if match:
                numero = int(match.group(1))
                fichier = match.group(2)
                titre = match.group(3)
                
                # Analyser le contenu de la méthode
                contenu_methode = []
                j = i + 1
                while j < len(lines) and not re.match(methode_pattern, lines[j]) and not re.match(section_pattern, lines[j]):
                    contenu_methode.append(lines[j])
                    j += 1
                
                contenu_text = '\n'.join(contenu_methode).strip()
                
                methodes.append({
                    'numero': numero,
                    'fichier': fichier,
                    'titre': titre,
                    'line': i + 1,
                    'contenu_lines': len([l for l in contenu_methode if l.strip()]),
                    'has_fonction': 'FONCTION :' in contenu_text,
                    'has_parametres': 'PARAMÈTRES :' in contenu_text,
                    'has_fonctionnement': 'FONCTIONNEMENT DÉTAILLÉ :' in contenu_text,
                    'has_retour': 'RETOUR :' in contenu_text,
                    'has_utilite': 'UTILITÉ :' in contenu_text
                })
        
        # Vérifier la numérotation
        numeros_attendus = list(range(1, len(methodes) + 1))
        numeros_actuels = [m['numero'] for m in methodes]
        numerotation_correcte = numeros_actuels == numeros_attendus
        
        # Statistiques
        stats = {
            'filepath': filepath,
            'total_lines': total_lines,
            'sections': sections,
            'methodes': methodes,
            'nb_methodes': len(methodes),
            'numerotation_correcte': numerotation_correcte,
            'numeros_attendus': numeros_attendus,
            'numeros_actuels': numeros_actuels
        }
        
        # Affichage des résultats
        print(f"📄 Lignes totales: {total_lines}")
        print(f"📁 Sections détectées: {len(sections)}")
        for section in sections:
            print(f"   - Ligne {section['line']}: {section['text']}")
        
        print(f"📝 Méthodes détectées: {len(methodes)}")
        print(f"🔢 Numérotation: {'✅ CORRECTE' if numerotation_correcte else '❌ INCORRECTE'}")
        
        if not numerotation_correcte:
            print(f"   Attendu: {numeros_attendus}")
            print(f"   Actuel:  {numeros_actuels}")
        
        # Vérifier la complétude des descriptions
        methodes_incompletes = []
        for methode in methodes:
            if not all([methode['has_fonction'], methode['has_utilite']]):
                methodes_incompletes.append(methode)
        
        print(f"📋 Descriptions complètes: {len(methodes) - len(methodes_incompletes)}/{len(methodes)}")
        
        if methodes_incompletes:
            print("⚠️  Méthodes incomplètes:")
            for m in methodes_incompletes:
                missing = []
                if not m['has_fonction']: missing.append('FONCTION')
                if not m['has_parametres']: missing.append('PARAMÈTRES')
                if not m['has_fonctionnement']: missing.append('FONCTIONNEMENT')
                if not m['has_retour']: missing.append('RETOUR')
                if not m['has_utilite']: missing.append('UTILITÉ')
                print(f"   {m['numero']}. {m['fichier']} - Manque: {', '.join(missing)}")
        
        # Détail des méthodes
        print(f"\n📋 LISTE DES MÉTHODES:")
        for methode in methodes:
            status = "✅" if all([methode['has_fonction'], methode['has_utilite']]) else "⚠️"
            print(f"   {status} {methode['numero']}. {methode['fichier']} ({methode['contenu_lines']} lignes)")
        
        return stats
        
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse: {e}")
        return None

def verifier_coherence_avec_dossier(dossier, stats_descriptif):
    """Vérifie la cohérence entre le fichier Descriptif.txt et les fichiers du dossier"""
    print(f"\n🔗 Vérification cohérence avec dossier {dossier}")
    
    if not os.path.exists(dossier):
        print(f"❌ Dossier non trouvé: {dossier}")
        return False
    
    # Lister les fichiers .txt du dossier (sauf Descriptif.txt)
    fichiers_dossier = []
    for f in os.listdir(dossier):
        if f.endswith('.txt') and f != 'Descriptif.txt':
            fichiers_dossier.append(f)
    
    fichiers_dossier.sort()
    
    # Fichiers mentionnés dans Descriptif.txt
    fichiers_descriptif = [m['fichier'] for m in stats_descriptif['methodes']]
    fichiers_descriptif.sort()
    
    print(f"📁 Fichiers dans dossier: {len(fichiers_dossier)}")
    print(f"📝 Fichiers dans Descriptif.txt: {len(fichiers_descriptif)}")
    
    # Vérifier correspondance
    fichiers_manquants_descriptif = set(fichiers_dossier) - set(fichiers_descriptif)
    fichiers_manquants_dossier = set(fichiers_descriptif) - set(fichiers_dossier)
    
    coherence_parfaite = len(fichiers_manquants_descriptif) == 0 and len(fichiers_manquants_dossier) == 0
    
    print(f"🔗 Cohérence: {'✅ PARFAITE' if coherence_parfaite else '❌ PROBLÈMES'}")
    
    if fichiers_manquants_descriptif:
        print(f"⚠️  Fichiers dans dossier mais pas dans Descriptif.txt:")
        for f in sorted(fichiers_manquants_descriptif):
            print(f"   - {f}")
    
    if fichiers_manquants_dossier:
        print(f"⚠️  Fichiers dans Descriptif.txt mais pas dans dossier:")
        for f in sorted(fichiers_manquants_dossier):
            print(f"   - {f}")
    
    return coherence_parfaite

def main():
    """Fonction principale de vérification"""
    print("🔧 VÉRIFICATION COMPLÈTE DES FICHIERS DESCRIPTIF.TXT")
    print("=" * 80)
    
    # Liste des fichiers à vérifier avec leurs dossiers correspondants
    fichiers_a_verifier = [
        ("Descriptif.txt", None),  # Fichier principal
        ("ReseauxNeuronaux/Descriptif.txt", "ReseauxNeuronaux"),
        ("CalculConfiance/Descriptif.txt", "CalculConfiance"),
        ("OptimisationEntrainement/Descriptif.txt", "OptimisationEntrainement"),
        ("GestionDonnees/Descriptif.txt", "GestionDonnees"),
        ("EvaluationMetriques/Descriptif.txt", "EvaluationMetriques"),
        ("UtilitairesFonctions/Descriptif.txt", "UtilitairesFonctions"),
        ("anciennesclasses/Descriptif.txt", "anciennesclasses")
    ]
    
    resultats = []
    
    for fichier, dossier in fichiers_a_verifier:
        stats = analyser_fichier_descriptif(fichier)
        if stats:
            resultats.append(stats)
            
            # Vérifier cohérence avec dossier si applicable
            if dossier:
                coherence = verifier_coherence_avec_dossier(dossier, stats)
                stats['coherence_dossier'] = coherence
    
    # Résumé final
    print("\n" + "=" * 80)
    print("📊 RÉSUMÉ FINAL DE VÉRIFICATION")
    print("=" * 80)
    
    total_methodes = 0
    total_lignes = 0
    fichiers_ok = 0
    
    for stats in resultats:
        nom_fichier = os.path.basename(stats['filepath'])
        status_num = "✅" if stats['numerotation_correcte'] else "❌"
        status_coh = "✅" if stats.get('coherence_dossier', True) else "❌"
        
        print(f"{status_num}{status_coh} {nom_fichier}")
        print(f"   📄 {stats['total_lines']} lignes | 📝 {stats['nb_methodes']} méthodes")
        
        if not stats['numerotation_correcte']:
            print(f"   ⚠️  Numérotation incorrecte")
        
        if 'coherence_dossier' in stats and not stats['coherence_dossier']:
            print(f"   ⚠️  Incohérence avec dossier")
        
        total_methodes += stats['nb_methodes']
        total_lignes += stats['total_lines']
        
        if stats['numerotation_correcte'] and stats.get('coherence_dossier', True):
            fichiers_ok += 1
    
    print(f"\n🎯 STATISTIQUES GLOBALES:")
    print(f"   📁 Fichiers vérifiés: {len(resultats)}")
    print(f"   ✅ Fichiers corrects: {fichiers_ok}/{len(resultats)}")
    print(f"   📝 Total méthodes: {total_methodes}")
    print(f"   📄 Total lignes: {total_lignes}")
    
    if fichiers_ok == len(resultats):
        print(f"\n🎉 TOUS LES FICHIERS SONT CORRECTS ET COMPLETS!")
    else:
        print(f"\n⚠️  {len(resultats) - fichiers_ok} fichier(s) nécessitent des corrections")

if __name__ == "__main__":
    main()
