# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 277 à 286
# Type: Méthode de la classe ModuleInterface

    def register_class(self, name: str, cls: Type) -> None:
        """
        Enregistre une classe dans l'interface.

        Args:
            name: Nom unique de la classe
            cls: Classe à enregistrer
        """
        self._classes[name] = cls
        logger.debug(f"Classe '{name}' enregistrée dans l'interface")