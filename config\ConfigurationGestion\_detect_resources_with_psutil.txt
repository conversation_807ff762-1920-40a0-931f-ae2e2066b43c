# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\config.py
# Lignes: 941 à 974
# Type: Méthode de la classe PredictorConfig

    def _detect_resources_with_psutil(self):
        """
        Détecte les ressources système disponibles (CPU, mémoire) avec psutil.
        """
        try:
            # Tentative d'import de psutil (peut être absent)
            try:
                import psutil
            except ImportError:
                psutil = None

            if psutil:
                detected = False
                try:
                    cores = psutil.cpu_count(logical=True)
                    mem_bytes = psutil.virtual_memory().total
                    if cores:
                        self.default_cpu_cores = max(1, cores)
                        calculated_mem_gb = max(2, int(mem_bytes / (1024**3) * 0.8))
                        self.default_max_memory_gb = max(28, calculated_mem_gb)
                        detected = True
                    else:
                        pass
                except Exception as e_psutil:
                    pass
                if not detected:
                    self.default_cpu_cores = 4
                    self.default_max_memory_gb = 28
            else:
                self.default_cpu_cores = 4
                self.default_max_memory_gb = 28
        except Exception as e_overall:
            self.default_cpu_cores = 4
            self.default_max_memory_gb = 28