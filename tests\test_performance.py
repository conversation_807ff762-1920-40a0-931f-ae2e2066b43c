#!/usr/bin/env python3
"""
Tests complets de performance du système
"""

import sys
import os
import unittest
import time
import psutil
import numpy as np
from pathlib import Path

# Ajouter le dossier du programme au path
PROG_DIR = r"C:\Users\<USER>\Desktop\travail\plateforme\notes_travail\prog"
sys.path.insert(0, PROG_DIR)

try:
    from hbp import HybridBaccaratPredictor
    from config import PredictorConfig
except ImportError as e:
    print(f"ERREUR: Impossible d'importer modules: {e}")
    sys.exit(1)


class TestPerformanceBasic(unittest.TestCase):
    """Tests de performance de base"""
    
    def setUp(self):
        """Initialisation avant chaque test"""
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        
        # Charger données réelles si disponibles
        historical_file = os.path.join(PROG_DIR, "historical_data.txt")
        if os.path.exists(historical_file):
            try:
                with open(historical_file, 'r') as f:
                    content = f.read().strip()
                    self.test_data = [int(x) for x in content.split(',')]
            except:
                self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 1000
        else:
            self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 1000
            
    def test_initialization_time(self):
        """Test temps d'initialisation"""
        start_time = time.time()
        
        # Créer nouveau prédicteur
        new_predictor = HybridBaccaratPredictor(self.config)
        
        end_time = time.time()
        init_time = end_time - start_time
        
        # Initialisation devrait être rapide (< 5 secondes)
        self.assertLess(init_time, 5.0)
        
    def test_data_loading_time(self):
        """Test temps de chargement des données"""
        # Créer fichier temporaire avec beaucoup de données
        large_data = [i % 2 for i in range(100000)]
        
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(','.join(map(str, large_data)))
            temp_file = f.name
            
        try:
            start_time = time.time()
            
            # Charger données
            with open(temp_file, 'r') as f:
                content = f.read().strip()
                loaded_data = [int(x) for x in content.split(',')]
                
            end_time = time.time()
            loading_time = end_time - start_time
            
            # Chargement devrait être rapide (< 2 secondes)
            self.assertLess(loading_time, 2.0)
            self.assertEqual(len(loaded_data), len(large_data))
            
        finally:
            os.unlink(temp_file)
            
    def test_prediction_time(self):
        """Test temps de prédiction"""
        # Entraîner d'abord (si possible)
        try:
            self.predictor.train_all_models(self.test_data[:1000])
        except:
            self.skipTest("Entraînement échoué")
            
        # Mesurer temps de prédiction
        recent_data = self.test_data[-50:]
        
        start_time = time.time()
        prediction = self.predictor.predict_hybrid(recent_data)
        end_time = time.time()
        
        prediction_time = end_time - start_time
        
        # Prédiction devrait être très rapide (< 1 seconde)
        self.assertLess(prediction_time, 1.0)


class TestPerformanceMemory(unittest.TestCase):
    """Tests de performance mémoire"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        self.process = psutil.Process(os.getpid())
        
    def test_memory_usage_initialization(self):
        """Test utilisation mémoire à l'initialisation"""
        memory_before = self.process.memory_info().rss
        
        # Créer nouveau prédicteur
        new_predictor = HybridBaccaratPredictor(self.config)
        
        memory_after = self.process.memory_info().rss
        memory_increase = memory_after - memory_before
        
        # Augmentation mémoire devrait être raisonnable (< 100MB)
        self.assertLess(memory_increase, 100 * 1024 * 1024)
        
    def test_memory_usage_training(self):
        """Test utilisation mémoire pendant entraînement"""
        memory_before = self.process.memory_info().rss
        
        # Entraîner avec données limitées
        test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 500
        
        try:
            self.predictor.train_all_models(test_data)
        except:
            pass  # Ignorer échecs d'entraînement pour test mémoire
            
        memory_after = self.process.memory_info().rss
        memory_increase = memory_after - memory_before
        
        # Augmentation mémoire devrait être contrôlée (< 500MB)
        self.assertLess(memory_increase, 500 * 1024 * 1024)
        
    def test_memory_leak_detection(self):
        """Test détection fuites mémoire"""
        memory_baseline = self.process.memory_info().rss
        
        # Effectuer opérations répétées
        for i in range(10):
            try:
                # Créer et détruire prédicteur
                temp_predictor = HybridBaccaratPredictor(self.config)
                
                # Faire quelques opérations
                test_data = [0, 1] * 100
                temp_predictor.prepare_training_data(test_data)
                
                # Nettoyer
                del temp_predictor
                
            except:
                pass
                
        # Forcer garbage collection
        import gc
        gc.collect()
        
        memory_final = self.process.memory_info().rss
        memory_increase = memory_final - memory_baseline
        
        # Augmentation mémoire devrait être minimale (< 50MB)
        self.assertLess(memory_increase, 50 * 1024 * 1024)


class TestPerformanceScaling(unittest.TestCase):
    """Tests de performance avec montée en charge"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        
    def test_scaling_data_size(self):
        """Test performance avec tailles de données croissantes"""
        sizes = [100, 500, 1000, 2000]
        times = []
        
        for size in sizes:
            test_data = [i % 2 for i in range(size)]
            
            start_time = time.time()
            
            try:
                # Tester préparation données
                self.predictor.prepare_training_data(test_data)
            except:
                pass
                
            end_time = time.time()
            processing_time = end_time - start_time
            times.append(processing_time)
            
        # Vérifier que le temps n'explose pas
        for i in range(1, len(times)):
            ratio = times[i] / times[i-1]
            size_ratio = sizes[i] / sizes[i-1]
            
            # Le temps ne devrait pas croître plus vite que les données
            self.assertLess(ratio, size_ratio * 2)
            
    def test_concurrent_predictions(self):
        """Test performance prédictions concurrentes"""
        import threading
        import queue
        
        # Entraîner d'abord
        try:
            test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 200
            self.predictor.train_all_models(test_data)
        except:
            self.skipTest("Entraînement échoué")
            
        results_queue = queue.Queue()
        
        def prediction_worker(worker_id):
            start_time = time.time()
            
            try:
                recent_data = [0, 1, 1, 0, 1] * 10
                prediction = self.predictor.predict_hybrid(recent_data)
                
                end_time = time.time()
                prediction_time = end_time - start_time
                
                results_queue.put((worker_id, prediction_time, True))
                
            except Exception as e:
                end_time = time.time()
                prediction_time = end_time - start_time
                results_queue.put((worker_id, prediction_time, False))
                
        # Créer threads de prédiction
        threads = []
        num_threads = 5
        
        overall_start = time.time()
        
        for i in range(num_threads):
            thread = threading.Thread(target=prediction_worker, args=(i,))
            threads.append(thread)
            thread.start()
            
        # Attendre tous les threads
        for thread in threads:
            thread.join(timeout=30)
            
        overall_end = time.time()
        overall_time = overall_end - overall_start
        
        # Collecter résultats
        results = []
        while not results_queue.empty():
            results.append(results_queue.get())
            
        # Vérifier performance
        self.assertEqual(len(results), num_threads)
        self.assertLess(overall_time, 10)  # Moins de 10 secondes total
        
        # Vérifier temps individuels
        for worker_id, pred_time, success in results:
            self.assertLess(pred_time, 5)  # Chaque prédiction < 5 secondes


class TestPerformanceBenchmark(unittest.TestCase):
    """Tests de benchmark de performance"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        
    def test_training_benchmark(self):
        """Benchmark temps d'entraînement"""
        data_sizes = [500, 1000, 2000]
        training_times = []
        
        for size in data_sizes:
            test_data = [i % 2 for i in range(size)]
            
            start_time = time.time()
            
            try:
                self.predictor.train_all_models(test_data)
                success = True
            except:
                success = False
                
            end_time = time.time()
            training_time = end_time - start_time
            training_times.append((size, training_time, success))
            
        # Afficher résultats benchmark
        print("\n=== BENCHMARK ENTRAÎNEMENT ===")
        for size, time_taken, success in training_times:
            status = "SUCCÈS" if success else "ÉCHEC"
            print(f"Taille: {size}, Temps: {time_taken:.2f}s, Statut: {status}")
            
        # Vérifier que les temps restent raisonnables
        for size, time_taken, success in training_times:
            # Maximum 5 minutes par entraînement
            self.assertLess(time_taken, 300)
            
    def test_prediction_benchmark(self):
        """Benchmark temps de prédiction"""
        # Entraîner d'abord
        try:
            test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 500
            self.predictor.train_all_models(test_data)
        except:
            self.skipTest("Entraînement échoué")
            
        # Mesurer prédictions multiples
        num_predictions = 100
        prediction_times = []
        
        for i in range(num_predictions):
            recent_data = test_data[-50:]
            
            start_time = time.time()
            
            try:
                prediction = self.predictor.predict_hybrid(recent_data)
                success = True
            except:
                success = False
                
            end_time = time.time()
            prediction_time = end_time - start_time
            prediction_times.append((prediction_time, success))
            
        # Calculer statistiques
        successful_times = [t for t, s in prediction_times if s]
        
        if successful_times:
            avg_time = np.mean(successful_times)
            max_time = np.max(successful_times)
            min_time = np.min(successful_times)
            
            print(f"\n=== BENCHMARK PRÉDICTION ===")
            print(f"Prédictions réussies: {len(successful_times)}/{num_predictions}")
            print(f"Temps moyen: {avg_time:.4f}s")
            print(f"Temps min: {min_time:.4f}s")
            print(f"Temps max: {max_time:.4f}s")
            
            # Vérifier performance
            self.assertLess(avg_time, 1.0)  # Moyenne < 1 seconde
            self.assertLess(max_time, 5.0)  # Maximum < 5 secondes


if __name__ == '__main__':
    print("=== TESTS PERFORMANCE ===")
    print(f"Répertoire programme: {PROG_DIR}")
    
    # Créer suite de tests
    suite = unittest.TestSuite()
    
    # Ajouter tests
    suite.addTest(unittest.makeSuite(TestPerformanceBasic))
    suite.addTest(unittest.makeSuite(TestPerformanceMemory))
    suite.addTest(unittest.makeSuite(TestPerformanceScaling))
    suite.addTest(unittest.makeSuite(TestPerformanceBenchmark))
    
    # Exécuter tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Afficher résultats
    print(f"\n=== RÉSULTATS PERFORMANCE ===")
    print(f"Tests exécutés: {result.testsRun}")
    print(f"Échecs: {len(result.failures)}")
    print(f"Erreurs: {len(result.errors)}")
    
    if result.failures:
        print("\nÉCHECS:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
            
    if result.errors:
        print("\nERREURS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
            
    sys.exit(0 if result.wasSuccessful() else 1)
