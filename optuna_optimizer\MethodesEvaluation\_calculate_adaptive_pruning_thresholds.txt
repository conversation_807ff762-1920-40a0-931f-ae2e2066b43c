# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 10519 à 10596
# Type: Méthode de la classe OptunaOptimizer

    def _calculate_adaptive_pruning_thresholds(self, base_thresholds, segment_key, segment_percentage, trial_count):
        """
        Calcule des seuils de pruning adaptatifs basés sur l'historique des performances.

        Cette méthode ajuste dynamiquement les seuils de pruning en fonction des performances
        observées sur les essais précédents, permettant un pruning plus agressif à mesure
        que l'optimisation progresse et que nous avons plus d'information sur les performances.

        Args:
            base_thresholds: Seuils de base
            segment_key: Clé du segment actuel
            segment_percentage: Pourcentage du segment par rapport au total
            trial_count: Nombre d'essais évalués

        Returns:
            dict: Seuils adaptés
        """
        # Copier les seuils de base
        adapted_thresholds = base_thresholds.copy()

        # Si nous n'avons pas assez d'historique, utiliser les seuils de base
        if not hasattr(self, 'performance_history') or segment_key not in self.performance_history['segment_performances']:
            return adapted_thresholds

        history = self.performance_history['segment_performances'][segment_key]

        # Si nous n'avons pas assez de données pour ce segment, utiliser les seuils de base
        if len(history['scores']) < 5:
            return adapted_thresholds

        # Calculer des statistiques sur les performances passées
        non_wait_accuracies = np.array(history['non_wait_accuracies'])
        wait_accuracies = np.array(history['wait_accuracies'])
        recommendation_rates = np.array(history['recommendation_rates'])
        wait_ratios = np.array(history['wait_ratios'])
        scores = np.array(history['scores'])

        # Calculer les percentiles pour chaque métrique
        # Plus nous avons d'essais, plus nous pouvons être stricts
        if trial_count < 10:
            # Phase initiale: seuils très permissifs pour explorer l'espace
            percentile = 10  # 10ème percentile (très permissif)
        elif trial_count < 30:
            # Phase intermédiaire: seuils modérés
            percentile = 20  # 20ème percentile
        else:
            # Phase avancée: seuils plus stricts pour se concentrer sur les meilleurs essais
            percentile = 30  # 30ème percentile

        # Ajuster les seuils en fonction de la progression
        # Plus nous sommes avancés dans l'évaluation, plus nous pouvons être stricts
        progression_factor = 1.0 - (0.5 * segment_percentage)  # Entre 0.5 et 1.0

        # Calculer les seuils adaptés
        non_wait_threshold = np.percentile(non_wait_accuracies, percentile) * progression_factor
        wait_threshold = np.percentile(wait_accuracies, percentile) * progression_factor
        recommendation_threshold = np.percentile(recommendation_rates, percentile) * progression_factor

        # Pour les ratios WAIT, nous voulons éviter les extrêmes
        min_wait_threshold = np.percentile(wait_ratios, percentile) * 0.9  # Légèrement plus permissif
        max_wait_threshold = np.percentile(wait_ratios, 100 - percentile) * 1.1  # Légèrement plus permissif

        # S'assurer que les seuils ne sont pas trop stricts
        adapted_thresholds['min_non_wait_accuracy'] = min(base_thresholds['min_non_wait_accuracy'], max(0.2, non_wait_threshold))
        adapted_thresholds['min_wait_accuracy'] = min(base_thresholds['min_wait_accuracy'], max(0.1, wait_threshold))
        adapted_thresholds['min_recommendation_rate'] = min(base_thresholds['min_recommendation_rate'], max(0.01, recommendation_threshold))
        adapted_thresholds['min_wait_ratio'] = min(base_thresholds['min_wait_ratio'], max(0.001, min_wait_threshold))
        adapted_thresholds['max_wait_ratio'] = max(base_thresholds['max_wait_ratio'], min(0.999, max_wait_threshold))

        # Journaliser les seuils adaptés
        logger.info(f"Seuils adaptés pour le segment {segment_key} (progression: {segment_percentage:.2f}, essais: {trial_count}):")
        logger.info(f"  - min_non_wait_accuracy: {adapted_thresholds['min_non_wait_accuracy']:.4f}")
        logger.info(f"  - min_wait_accuracy: {adapted_thresholds['min_wait_accuracy']:.4f}")
        logger.info(f"  - min_recommendation_rate: {adapted_thresholds['min_recommendation_rate']:.4f}")
        logger.info(f"  - min_wait_ratio: {adapted_thresholds['min_wait_ratio']:.4f}")
        logger.info(f"  - max_wait_ratio: {adapted_thresholds['max_wait_ratio']:.4f}")

        return adapted_thresholds