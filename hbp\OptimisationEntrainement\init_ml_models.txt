# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 1589 à 1795
# Type: Méthode de la classe HybridBaccaratPredictor

    def init_ml_models(self, reset_weights: bool = True) -> bool:
        # Vérifier si l'initialisation est déjà en cours pour éviter la récursion
        if hasattr(self, '_initializing_models') and self._initializing_models:
            logger.warning("init_ml_models: Initialisation déjà en cours. Évitement de la récursion.")
            return False

        # Définir le drapeau d'initialisation
        self._initializing_models = True

        logger.info(f"Initialisation/Réinitialisation des modèles ML (reset_weights={reset_weights})...")
        init_success = False
        locks_acquired = False
        try:
            # Acquérir tous les verrous nécessaires car on modifie tout l'état ML
            self.model_lock.acquire()
            self.weights_lock.acquire()
            locks_acquired = True
            logger.debug("init_ml_models: Verrous acquis.")

            # Initialiser le calculateur de confiance consécutive
            self.init_consecutive_confidence_calculator()

            # Initialiser l'optimiseur de placement des WAIT
            self.init_wait_placement_optimizer()

            # Vérifier si le modèle Markov doit être utilisé
            use_markov_model = getattr(self.config, 'use_markov_model', True)
            if not use_markov_model and self.markov is not None:
                self.markov = None
                logger.info("Modèle Markov désactivé (use_markov_model=False).")

            # 1. Historique des features (basé sur config)
            if hasattr(self.config, 'selected_features') and isinstance(self.config.selected_features, list):
                self.feature_names = self.config.selected_features[:]  # Utiliser l'attribut correct et faire une copie
                logger.info(f"Noms de features initialisés ({len(self.feature_names)}) depuis config.selected_features.")
            else:
                logger.error("Erreur config: 'selected_features' manquant ou invalide dans PredictorConfig. Initialisation features échouée.")
                self.feature_names = []
                raise ValueError("Configuration 'selected_features' manquante ou invalide.")

            # 2. Scaler
            self.feature_scaler = StandardScaler()
            logger.info("StandardScaler initialisé (non 'fit').")

            # 3. Modèles LGBM
            # S'assurer que n_jobs est valide
            num_cpu_jobs = -1  # Valeur par défaut si psutil n'est pas dispo ou erreur
            if psutil:
                try:
                    max_logical_cores = psutil.cpu_count(logical=True) or 1 #DECLARED HERE
                    target_cores = getattr(self, 'cpu_cores', None)
                    if target_cores and isinstance(target_cores, tk.IntVar):
                        cfg_jobs = target_cores.get()
                    else:
                        cfg_jobs = getattr(self.config, 'default_cpu_cores', -1)  # Utiliser default_cpu_cores comme guide

                    if cfg_jobs > 0:
                        num_cpu_jobs = min(cfg_jobs, max_logical_cores)
                    else:  # -1 ou 0 dans config/UI -> utilise tous les cores
                        num_cpu_jobs = -1  # Qui signifie tous les cores pour LGBM
                    logger.debug(f"Calcul n_jobs LGBM: Cible={cfg_jobs}, MaxLogique={max_logical_cores}, Résultat={num_cpu_jobs}")
                except Exception as e_cpu:
                    logger.warning(f"Erreur lecture CPU cores via psutil/UI {e_cpu}, utilisation de -1 pour n_jobs LGBM.")
            else:
                logger.warning("psutil non disponible, utilisation de -1 pour n_jobs LGBM.")

            lgbm_params_from_config = {}
            missing_lgbm_params = []
            lgbm_config_keys = [
                'lgbm_n_estimators', 'lgbm_learning_rate', 'lgbm_max_depth', 'lgbm_num_leaves',
                'lgbm_min_child_samples', 'lgbm_subsample', 'lgbm_colsample_bytree',
                'lgbm_reg_alpha', 'lgbm_reg_lambda'
            ]
            config_to_lgbm_map = {
                'lgbm_n_estimators': 'n_estimators',
                'lgbm_learning_rate': 'learning_rate',
                'lgbm_max_depth': 'max_depth',
                'lgbm_num_leaves': 'num_leaves',
                'lgbm_min_child_samples': 'min_child_samples',
                'lgbm_subsample': 'subsample',
                'lgbm_colsample_bytree': 'colsample_bytree',
                'lgbm_reg_alpha': 'reg_alpha',
                'lgbm_reg_lambda': 'reg_lambda'
            }

            for config_key in lgbm_config_keys:
                if hasattr(self.config, config_key):
                    lgbm_arg_name = config_to_lgbm_map.get(config_key, config_key)
                    lgbm_params_from_config[lgbm_arg_name] = getattr(self.config, config_key)
                else:
                    missing_lgbm_params.append(config_key)
                    logger.warning(f"Attribut de configuration LGBM '{config_key}' non trouvé dans PredictorConfig.")

            if not missing_lgbm_params:
                logger.info(f"Paramètres LGBM chargés depuis PredictorConfig: {lgbm_params_from_config}")
            else:
                logger.warning(f"Certains paramètres LGBM manquants dans config: {missing_lgbm_params}. LGBM utilisera ses défauts pour ceux-là.")

            # S'assurer que les paramètres LGBM sont cohérents entre l'optimisation et l'entraînement
            # Vérifier si des paramètres LGBM ont été modifiés par l'optimisation
            if hasattr(self.config, 'lgbm_params') and isinstance(self.config.lgbm_params, dict):
                for param_name, param_value in self.config.lgbm_params.items():
                    if param_name not in lgbm_params_from_config and param_name not in ['n_jobs', 'verbose', 'boosting_type', 'objective', 'metric']:
                        lgbm_params_from_config[param_name] = param_value
                        logger.warning(f"Paramètre LGBM '{param_name}' ajouté depuis config.lgbm_params: {param_value}")
                logger.info("Paramètres LGBM synchronisés avec config.lgbm_params")

            self.lgbm_base = LGBMClassifier(
                random_state=self.config.random_seed,
                n_jobs=num_cpu_jobs,
                **lgbm_params_from_config
            )
            self.calibrated_lgbm = None
            self.lgbm_uncertainty = None
            logger.info("Instances LGBM (base, calibrated, uncertainty) réinitialisées à None ou vierge (avec params config).")

            # 4. LSTM et composants associés
            try:
                 lstm_input_size = self.config.lstm_input_size
                 # Utiliser debug au lieu de info pour réduire la visibilité de ce message
                 logger.debug(f"Utilisation taille input LSTM: {lstm_input_size}")

                 # S'assurer que les dimensions du modèle LSTM sont cohérentes
                 # Si lstm_hidden_dim et lstm_hidden_size sont différents, les synchroniser
                 if hasattr(self.config, 'lstm_hidden_dim') and hasattr(self.config, 'lstm_hidden_size'):
                     if self.config.lstm_hidden_dim != self.config.lstm_hidden_size:
                         logger.warning(f"Synchronisation des dimensions LSTM: lstm_hidden_dim={self.config.lstm_hidden_dim}, lstm_hidden_size={self.config.lstm_hidden_size}")
                         # Utiliser lstm_hidden_dim comme valeur de référence
                         self.config.lstm_hidden_size = self.config.lstm_hidden_dim
                         logger.warning(f"Dimensions LSTM synchronisées: lstm_hidden_dim=lstm_hidden_size={self.config.lstm_hidden_dim}")

                 # Créer le modèle LSTM
                 lstm = EnhancedLSTMModel(input_size=lstm_input_size,
                    hidden_dim=self.config.lstm_hidden_dim,
                    num_layers=self.config.lstm_num_layers,
                    output_size=2,
                    dropout_prob=self.config.lstm_dropout,
                    bidirectional=self.config.lstm_bidirectional
                 )

                 # Optimiser le modèle LSTM pour une meilleure utilisation de la mémoire
                 # Utiliser training_mode=True pour conserver les gradients nécessaires à l'entraînement
                 lstm = optimize_lstm_memory(lstm, training_mode=True)

                 # Déplacer le modèle sur le device approprié
                 self.lstm = lstm.to(self.device)
                 for name, param in self.lstm.named_parameters():
                     if param.dim() > 1 :
                         if 'weight_ih' in name: init.xavier_uniform_(param.data)
                         elif 'weight_hh' in name: init.orthogonal_(param.data)
                     elif 'bias' in name: param.data.fill_(0)

                 self.optimizer = optim.AdamW(self.lstm.parameters(), lr=self.config.lstm_learning_rate, weight_decay=self.config.lstm_weight_decay)
                 self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, mode='min', factor=self.config.scheduler_factor, patience=self.config.scheduler_patience) #REMOVED verbose=False
                 logger.info(f"Modèle LSTM (input={lstm_input_size}), Optimizer, et Scheduler initialisés.")
            except AttributeError as ae_lstm:
                 logger.error(f"ERREUR ATTRIBUT pendant initialisation LSTM (config ok?): {ae_lstm}. LSTM désactivé.", exc_info=True)
                 self.lstm, self.optimizer, self.scheduler = None, None, None
            except Exception as e_lstm_init:
                 logger.error(f"ERREUR CRITIQUE initialisation LSTM: {e_lstm_init}. LSTM désactivé.", exc_info=True)
                 self.lstm, self.optimizer, self.scheduler = None, None, None
            if reset_weights:
                self.weights = self.config.initial_weights.copy()
                self._initialize_method_performance()
                self.best_accuracy = 0.5
                self.best_weights = self.weights.copy()
                self.early_stopping_counter = 0
                logger.info("Poids des méthodes, performances, et compteurs liés réinitialisés.")

            self.lgbm_cache = deque(maxlen=100)
            logger.info("Cache LGBM vidé suite à l'initialisation des modèles.")

            init_success = True

        except Exception as e:
            logger.critical(f"Erreur majeure pendant init_ml_models: {e}", exc_info=True)
            self.feature_scaler = None
            self.lgbm_base = None
            self.calibrated_lgbm = None
            self.lgbm_uncertainty = None
            self.lstm = None
            self.optimizer = None
            self.scheduler = None
            self.weights = self.config.initial_weights.copy()
            self._initialize_method_performance()
            if locks_acquired:
                try:
                    self.lgbm_cache = deque(maxlen=100)
                    logger.warning("Cache LGBM vidé suite à une ERREUR MAJEURE pendant init_ml_models.")
                except Exception as e_cache_clear_fail:
                    logger.error(f"Erreur lors du vidage du cache après erreur majeure: {e_cache_clear_fail}")
            init_success = False

        finally:
            if locks_acquired:
                try:
                    self.weights_lock.release()
                    self.model_lock.release()
                    logger.debug("init_ml_models: Verrous libérés (finally).")
                except RuntimeError as e_release:
                    logger.error(f"init_ml_models: Erreur libération verrou: {e_release}")

            # Réinitialiser le drapeau d'initialisation
            self._initializing_models = False

        logger.info(f"Fin initialisation/réinitialisation ML. Succès: {init_success}")
        return init_success