# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 470 à 507
# Type: Méthode de la classe MemoryManager

    def optimize_lstm_memory(lstm_model, training_mode=False):
        """
        Optimise la mémoire utilisée par un modèle LSTM.

        Args:
            lstm_model: Le modèle LSTM à optimiser
            training_mode: Si True, le modèle est en mode entraînement et les gradients sont conservés

        Returns:
            Le modèle LSTM optimisé
        """
        try:
            # 1. Configurer le mode du modèle en fonction du contexte
            if not training_mode:
                # En mode inférence, mettre en mode évaluation
                lstm_model.eval()

                # Désactiver le calcul des gradients pour économiser de la mémoire en mode inférence
                for param in lstm_model.parameters():
                    param.requires_grad = False
            else:
                # En mode entraînement, mettre en mode train
                lstm_model.train()

                # S'assurer que les gradients sont activés pour l'entraînement
                for param in lstm_model.parameters():
                    param.requires_grad = True

            # 2. Optimiser les paramètres LSTM si possible (pour les deux modes)
            for module in lstm_model.modules():
                if hasattr(module, 'flatten_parameters') and callable(module.flatten_parameters):
                    module.flatten_parameters()

            logger.info(f"Modèle LSTM optimisé pour la mémoire (mode {'entraînement' if training_mode else 'inférence'})")
        except Exception as e:
            logger.warning(f"Impossible d'optimiser le modèle LSTM: {e}")

        return lstm_model