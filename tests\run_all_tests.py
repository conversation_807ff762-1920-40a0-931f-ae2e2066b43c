#!/usr/bin/env python3
"""
Script principal pour exécuter tous les tests du système
"""

import sys
import os
import unittest
import time
import json
from pathlib import Path

# Ajouter le dossier du programme au path
PROG_DIR = r"C:\Users\<USER>\Desktop\travail\plateforme\notes_travail\prog"
sys.path.insert(0, PROG_DIR)

# Importer tous les modules de test
try:
    from test_config import *
    from test_data_manager import *
    from test_training import *
    from test_optimization import *
    from test_threading import *
    from test_performance import *
    from test_integration import *
except ImportError as e:
    print(f"ERREUR: Impossible d'importer modules de test: {e}")
    sys.exit(1)


class TestResultCollector:
    """Collecteur de résultats de tests"""
    
    def __init__(self):
        self.results = {}
        self.start_time = time.time()
        
    def add_result(self, test_name, result):
        """Ajouter résultat d'un test"""
        self.results[test_name] = {
            'tests_run': result.testsRun,
            'failures': len(result.failures),
            'errors': len(result.errors),
            'success': result.wasSuccessful(),
            'failure_details': [(str(test), traceback) for test, traceback in result.failures],
            'error_details': [(str(test), traceback) for test, traceback in result.errors]
        }
        
    def generate_report(self):
        """Générer rapport complet"""
        end_time = time.time()
        total_time = end_time - self.start_time
        
        report = {
            'execution_time': total_time,
            'total_tests': sum(r['tests_run'] for r in self.results.values()),
            'total_failures': sum(r['failures'] for r in self.results.values()),
            'total_errors': sum(r['errors'] for r in self.results.values()),
            'test_suites': self.results
        }
        
        return report


def run_test_suite(test_classes, suite_name):
    """Exécuter une suite de tests"""
    print(f"\n{'='*60}")
    print(f"EXÉCUTION SUITE: {suite_name}")
    print(f"{'='*60}")
    
    # Créer suite
    suite = unittest.TestSuite()
    
    for test_class in test_classes:
        suite.addTest(unittest.makeSuite(test_class))
    
    # Exécuter tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Afficher résumé
    print(f"\n--- RÉSUMÉ {suite_name} ---")
    print(f"Tests exécutés: {result.testsRun}")
    print(f"Échecs: {len(result.failures)}")
    print(f"Erreurs: {len(result.errors)}")
    print(f"Succès: {'OUI' if result.wasSuccessful() else 'NON'}")
    
    return result


def main():
    """Fonction principale"""
    print("="*80)
    print("SUITE COMPLÈTE DE TESTS - PLATEFORME BACCARAT")
    print("="*80)
    print(f"Répertoire programme: {PROG_DIR}")
    print(f"Heure début: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Initialiser collecteur
    collector = TestResultCollector()
    
    # Définir suites de tests
    test_suites = [
        {
            'name': 'CONFIGURATION',
            'classes': [TestConfiguration, TestConfigurationDefaults, TestConfigurationEdgeCases]
        },
        {
            'name': 'DATA MANAGER',
            'classes': [TestDataManager, TestDataManagerFeatures, TestDataManagerPerformance]
        },
        {
            'name': 'ENTRAÎNEMENT',
            'classes': [TestTrainingBasic, TestTrainingLGBM, TestTrainingLSTM, TestTrainingMarkov, TestTrainingIntegration]
        },
        {
            'name': 'OPTIMISATION',
            'classes': [TestOptimizationBasic, TestOptimizationParameters, TestOptimizationExecution, TestOptimizationResults]
        },
        {
            'name': 'THREADING',
            'classes': [TestThreadingBasic, TestThreadingLocks, TestThreadingPredictor, TestThreadingStress]
        },
        {
            'name': 'PERFORMANCE',
            'classes': [TestPerformanceBasic, TestPerformanceMemory, TestPerformanceScaling, TestPerformanceBenchmark]
        },
        {
            'name': 'INTÉGRATION',
            'classes': [TestIntegrationBasic, TestIntegrationModels, TestIntegrationOptimization, TestIntegrationRobustness, TestIntegrationEndToEnd]
        }
    ]
    
    # Exécuter chaque suite
    for suite_info in test_suites:
        try:
            result = run_test_suite(suite_info['classes'], suite_info['name'])
            collector.add_result(suite_info['name'], result)
        except Exception as e:
            print(f"ERREUR lors de l'exécution de {suite_info['name']}: {e}")
            # Créer résultat d'erreur factice
            class ErrorResult:
                def __init__(self):
                    self.testsRun = 0
                    self.failures = []
                    self.errors = [('Suite Error', str(e))]
                def wasSuccessful(self):
                    return False
            collector.add_result(suite_info['name'], ErrorResult())
    
    # Générer rapport final
    report = collector.generate_report()
    
    print("\n" + "="*80)
    print("RAPPORT FINAL")
    print("="*80)
    
    print(f"Temps total d'exécution: {report['execution_time']:.2f} secondes")
    print(f"Total tests exécutés: {report['total_tests']}")
    print(f"Total échecs: {report['total_failures']}")
    print(f"Total erreurs: {report['total_errors']}")
    
    # Détail par suite
    print(f"\nDÉTAIL PAR SUITE:")
    print("-" * 60)
    
    for suite_name, suite_result in report['test_suites'].items():
        status = "✓ SUCCÈS" if suite_result['success'] else "✗ ÉCHEC"
        print(f"{suite_name:20} | {suite_result['tests_run']:3d} tests | {suite_result['failures']:2d} échecs | {suite_result['errors']:2d} erreurs | {status}")
    
    # Afficher échecs et erreurs détaillés
    if report['total_failures'] > 0 or report['total_errors'] > 0:
        print(f"\nDÉTAILS DES PROBLÈMES:")
        print("-" * 60)
        
        for suite_name, suite_result in report['test_suites'].items():
            if suite_result['failures'] or suite_result['errors']:
                print(f"\n{suite_name}:")
                
                for test_name, traceback in suite_result['failure_details']:
                    print(f"  ÉCHEC: {test_name}")
                    print(f"    {traceback.split('AssertionError:')[-1].strip() if 'AssertionError:' in traceback else 'Détails non disponibles'}")
                
                for test_name, traceback in suite_result['error_details']:
                    print(f"  ERREUR: {test_name}")
                    print(f"    {traceback.split('Exception:')[-1].strip() if 'Exception:' in traceback else 'Détails non disponibles'}")
    
    # Sauvegarder rapport JSON
    try:
        report_file = "test_results.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        print(f"\nRapport détaillé sauvegardé: {report_file}")
    except Exception as e:
        print(f"Erreur sauvegarde rapport: {e}")
    
    # Recommandations
    print(f"\nRECOMMANDANTIONS:")
    print("-" * 60)
    
    if report['total_errors'] > 0:
        print("⚠️  ERREURS CRITIQUES détectées - Vérifier configuration et dépendances")
    
    if report['total_failures'] > 0:
        print("⚠️  ÉCHECS DE TESTS détectés - Vérifier logique métier et algorithmes")
    
    if report['total_errors'] == 0 and report['total_failures'] == 0:
        print("✅ TOUS LES TESTS PASSENT - Système potentiellement stable")
    else:
        print("❌ SYSTÈME NON PRÊT POUR PRODUCTION - Corriger problèmes identifiés")
    
    # Calcul score global
    total_tests = report['total_tests']
    if total_tests > 0:
        success_rate = (total_tests - report['total_failures'] - report['total_errors']) / total_tests
        print(f"\nSCORE GLOBAL: {success_rate:.1%}")
        
        if success_rate >= 0.95:
            print("🟢 EXCELLENT - Système très stable")
        elif success_rate >= 0.80:
            print("🟡 BON - Quelques améliorations nécessaires")
        elif success_rate >= 0.60:
            print("🟠 MOYEN - Corrections importantes requises")
        else:
            print("🔴 CRITIQUE - Refonte majeure nécessaire")
    
    print(f"\nHeure fin: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # Code de sortie
    if report['total_errors'] > 0 or report['total_failures'] > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == '__main__':
    main()
