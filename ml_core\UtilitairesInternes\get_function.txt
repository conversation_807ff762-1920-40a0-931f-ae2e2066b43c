# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 255 à 275
# Type: Méthode de la classe ModuleInterface

    def get_function(self, name: str) -> Callable:
        """
        Récupère une fonction enregistrée dans l'interface.

        Args:
            name: Nom de la fonction à récupérer

        Returns:
            La fonction enregistrée

        Raises:
            KeyError: Si la fonction n'est pas enregistrée
        """
        if name not in self._functions:
            if name in self._lazy_loaders:
                self._lazy_loaders[name]()  # Exécuter le chargeur paresseux

            if name not in self._functions:
                raise KeyError(f"Fonction '{name}' non enregistrée dans l'interface")

        return self._functions[name]