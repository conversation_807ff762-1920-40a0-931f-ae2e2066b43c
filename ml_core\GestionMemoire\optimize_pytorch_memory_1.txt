# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1275 à 1283
# Type: Méthode
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

def optimize_pytorch_memory():
    """
    Configure PyTorch pour utiliser la mémoire disponible de manière optimale.
    Cette fonction doit être appelée au début du programme.

    Returns:
        bool: True si l'optimisation a réussi, False sinon
    """
    return MemoryManager.optimize_pytorch_memory()