# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\models.py
# Lignes: 597 à 634
# Type: Méthode de la classe PersistentMarkov

    def export_models(self) -> Dict[str, Any]:
        """
        Exporte l'état actuel des modèles (global et session) et la configuration
        utilisée pour les créer, dans un format sérialisable (dictionnaire).

        Returns:
            Dict[str, Any]: Un dictionnaire contenant:
                            - 'config': {'max_order': int, 'smoothing': float}
                            - 'global': Liste de dictionnaires (un par ordre > 0)
                                        {state_tuple: {outcome: count}}
                            - 'session': Liste de dictionnaires (idem pour session)
        """
        with self.lock:
            logger.info("Exportation des modèles Markov...")

            # Utiliser directement la valeur flottante de max_order
            # Pas besoin de conversion, car nous acceptons maintenant les flottants

            # Convertir les defaultdict en dict standard pour la sérialisation
            # On n'exporte que les ordres 1 à max_order (index 0 est vide)
            global_export = []
            for order_model in self.global_models[1:]: # Exclure index 0
                global_export.append({state: dict(outcomes) for state, outcomes in order_model.items()})

            session_export = []
            for order_model in self.session_models[1:]: # Exclure index 0
                session_export.append({state: dict(outcomes) for state, outcomes in order_model.items()})

            export_data = {
                'config': {
                    'max_order': self.max_order,
                    'smoothing': self.smoothing
                },
                'global': global_export,
                'session': session_export
            }
            logger.info("Exportation Markov terminée.")
            return export_data