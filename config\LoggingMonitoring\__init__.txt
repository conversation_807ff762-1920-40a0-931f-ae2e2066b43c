# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\config.py
# Lignes: 24 à 66
# Type: Méthode de la classe SilentOptunaFilter

    def __init__(self):
        super().__init__()
        # Liste des messages répétitifs à filtrer même s'ils sont de niveau WARNING ou ERROR
        self.repetitive_patterns = [
            # Messages d'initialisation
            "Auto-update désactivé par défaut",
            "PersistentMarkov initialisé",
            "Initialisation de l'optimiseur de placement des WAIT",
            "WaitPlacementOptimizer initialisé",
            "Optimiseur de placement des WAIT initialisé",
            "Modèle LSTM optimisé pour la mémoire",
            "Optimisation de la mémoire PyTorch",
            "Impossible de définir le nombre de threads",
            "Configuration PyTorch",
            "Optimisation de la mémoire PyTorch terminée",
            "RÉINITIALISATION (SOFT)",
            "Modèles <PERSON> de session réinitialisés",

            # Messages de diagnostic d'entraînement
            "DIAGNOSTIC TRAIN:",
            "DIAGNOSTIC EVAL",
            "DIAGNOSTIC CONVERSION:",
            "DIAGNOSTIC DATASET:",
            "Gradient norm élevé:",
            "Nettoyage GC/CUDA effectué",
            "Incertitude détaillée:",
            "Poids: confidence_weight=",
            "Mise à jour des métriques cibles",
            "Métriques consécutives:",
            "Facteur de difficulté calculé",
            "Poids LSTM calculés:",
            "Poids d'échantillons calculés:",
            "_auto_update_callback: Cette méthode n'est plus utilisée",
            "Cache LGBM vidé après annulation",
            "Incohérence shape features LSTM",
            "Modèle 'calibrated_lgbm' non initialisé. Retour 50/50",
            "Modèle LSTM non initialisé. Retour 50/50",
            "Échec car modèle 'calibrated_lgbm' ou scaler non 'fit'",
            "Modèle LGBM non entraîné dans"
        ]

        # Compteur pour les messages répétitifs
        self.repetitive_counts = {}