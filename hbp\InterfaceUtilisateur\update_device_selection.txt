# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 4550 à 4590
# Type: Méthode de la classe HybridBaccaratPredictor

    def update_device_selection(self):
        """Met à jour le device utilisé par le prédicteur basé sur le choix UI.
           MODIFIÉ: Vide le cache LGBM après avoir potentiellement déplacé l'LSTM.
        """
        initial_device = self.device
        selected_device_type = self.device_choice.get()
        new_device = None
        lstm_moved = False # Flag pour savoir si to() a été appelé

        if selected_device_type == "cuda":
             if torch.cuda.is_available():
                 new_device = torch.device("cuda")
             else:
                 logger.warning("Tentative de sélectionner GPU mais CUDA n'est pas disponible. Retour à CPU.")
                 self.device_choice.set("cpu") # Mettre à jour la variable UI
                 new_device = torch.device("cpu")
        else: # "cpu" sélectionné
             new_device = torch.device("cpu")

        if new_device != initial_device:
             self.device = new_device
             logger.info(f"Device sélectionné mis à jour: {self.device}")
             # Déplacer le modèle LSTM existant vers le nouveau device si nécessaire
             if self.lstm: # Vérifier si l'instance LSTM existe
                  try:
                       with self.model_lock: # Verrou pour modifier le modèle LSTM
                            self.lstm.to(self.device)
                            lstm_moved = True # Marquer que le modèle a potentiellement changé de device
                       logger.info(f"Modèle LSTM déplacé vers {self.device}")
                  except Exception as e:
                       logger.error(f"Erreur lors du déplacement du modèle LSTM vers {self.device}: {e}")
             else:
                  logger.debug("Aucun modèle LSTM à déplacer.")

             if lstm_moved:
                 with self.model_lock: # Utiliser model_lock pour cohérence avec autres vidages
                     self.lgbm_cache = deque(maxlen=100)
                     logger.info(f"Cache LGBM vidé car le device a changé et l'LSTM a été déplacé vers {self.device}.")

        else:
            logger.debug(f"Device sélectionné ({self.device}) est identique au device actuel. Aucun changement.")