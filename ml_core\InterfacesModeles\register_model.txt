# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1077 à 1086
# Type: Méthode de la classe TrainOptimizeInterface

    def register_model(self, name: str, model: Any) -> None:
        """
        Enregistre un modèle dans l'interface.

        Args:
            name: Nom unique du modèle
            model: Modèle à enregistrer
        """
        self.models[name] = model
        logger.debug(f"Modèle '{name}' enregistré dans l'interface")