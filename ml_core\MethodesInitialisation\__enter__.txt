# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 551 à 558
# Type: Méthode de la classe PyTorchMemoryContext

    def __enter__(self):
        """
        Appelé à l'entrée du bloc with.
        Optimise la mémoire PyTorch si optimize_on_enter est True.
        """
        if self.optimize_on_enter:
            MemoryManager.optimize_pytorch_memory()
        return self