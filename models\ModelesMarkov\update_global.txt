# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\models.py
# Lignes: 498 à 535
# Type: Méthode de la classe PersistentMarkov

    def update_global(self, sequences: List[List[str]]) -> None:
        """
        Met à jour les modèles globaux (basés sur l'historique) avec plusieurs séquences.

        Args:
            sequences (List[List[str]]): Une liste de séquences de jeu, où chaque séquence
                                         est une liste de résultats ('player', 'banker').
        """
        if not sequences:
            logger.warning("update_global appelée avec une liste de séquences vide.")
            return

        with self.lock:
            logger.debug(f"Début mise à jour globale Markov avec {len(sequences)} séquences.")
            count = 0
            for seq in sequences:
                if not isinstance(seq, list):
                    logger.warning(f"Élément non-liste trouvé dans sequences: {type(seq)}. Ignoré.")
                    continue
                if not seq: continue # Ignorer séquences vides

                # Utiliser self.max_order_int défini dans le constructeur
                # Cela garantit la cohérence avec la taille des modèles initialisés

                # Apprendre jusqu'à self.max_order_int
                for order in range(1, self.max_order_int + 1): # Commence à l'ordre 1
                    # On a besoin d'au moins 'order' éléments pour former l'état et 1 pour l'issue
                    if len(seq) > order:
                        for i in range(order, len(seq)):
                            # L'état est la séquence de 'order' éléments précédents
                            state = tuple(seq[i - order : i])
                            outcome = seq[i]
                            # On ne compte que Player (P) et Banker (B) comme issues valides
                            if outcome in ('player', 'banker'):
                                self.global_models[order][state][outcome] += 1
                                count += 1
                            # else: logger.debug(f"Outcome global non P/B ignoré: {outcome}")
            logger.debug(f"Ajouté {count} transitions ('player'/'banker') aux modèles globaux Markov.")