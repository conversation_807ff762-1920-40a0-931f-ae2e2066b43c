# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 9939 à 10025
# Type: Méthode de la classe HybridBaccaratPredictor

    def calculate_sample_weights_from_metrics(self, X_features: np.ndarray) -> np.ndarray:
        """
        Calcule les poids d'échantillons basés sur les métriques de confiance et d'incertitude.

        Cette méthode utilise les modèles existants pour prédire la confiance et l'incertitude
        pour chaque échantillon, puis calcule des poids qui donnent plus d'importance aux
        échantillons avec une confiance élevée et une incertitude faible.

        Args:
            X_features (np.ndarray): Features pour lesquelles calculer les poids

        Returns:
            np.ndarray: Poids des échantillons
        """
        if X_features is None or len(X_features) == 0:
            logger.warning("calculate_sample_weights_from_metrics: Aucune feature fournie.")
            return None

        if self.feature_scaler is None or self.lgbm_base is None:
            logger.warning("calculate_sample_weights_from_metrics: Scaler ou modèle LGBM non disponible.")
            return None

        try:
            # Initialiser les poids à 1.0
            sample_weights = np.ones(len(X_features), dtype=np.float32)

            # Vérifier si le modèle LGBM est entraîné
            lgbm_model_fitted = False
            try:
                from sklearn.utils.validation import check_is_fitted
                check_is_fitted(self.lgbm_base)
                lgbm_model_fitted = True
            except Exception as e_fit:
                logger.warning(f"Modèle LGBM non entraîné dans calculate_sample_weights_from_metrics: {e_fit}")
                # Retourner des poids uniformes si le modèle n'est pas entraîné
                return sample_weights

            if lgbm_model_fitted:
                # Calculer les prédictions pour chaque échantillon
                y_pred_proba = self.lgbm_base.predict_proba(X_features)

                # Calculer la confiance pour chaque échantillon (distance à 0.5)
                confidence_scores = np.abs(y_pred_proba[:, 1] - 0.5) * 2.0  # Normaliser entre 0 et 1

                # Calculer l'incertitude pour chaque échantillon si le modèle d'incertitude est disponible
                uncertainty_scores = np.ones_like(confidence_scores) * 0.5  # Valeur par défaut
                if self.lgbm_uncertainty is not None:
                    try:
                        # Vérifier si le modèle d'incertitude est entraîné
                        check_is_fitted(self.lgbm_uncertainty)

                        # Obtenir les prédictions de chaque estimateur
                        estimator_probas = np.array([
                            estimator.predict_proba(X_features)[:, 1]
                            for estimator in self.lgbm_uncertainty.estimators_
                        ])

                        # Calculer la variance des probabilités (incertitude épistémique)
                        uncertainty_scores = np.var(estimator_probas, axis=0)
                        # Normaliser (variance max = 0.25 pour des proba entre 0 et 1)
                        uncertainty_scores = np.clip(uncertainty_scores * 4.0, 0.0, 1.0)
                    except Exception as e_unc:
                        logger.error(f"Erreur lors du calcul de l'incertitude: {e_unc}", exc_info=True)

                # Calculer les poids basés sur la confiance et l'incertitude
                # Formule: poids = confiance * (1 - incertitude)
                # Cela donne plus de poids aux échantillons avec une confiance élevée et une incertitude faible
                combined_weights = confidence_scores * (1.0 - uncertainty_scores)

                # Normaliser les poids pour qu'ils aient une moyenne de 1.0
                if np.sum(combined_weights) > 0:
                    normalized_weights = combined_weights * (len(combined_weights) / np.sum(combined_weights))
                    # Limiter les poids entre min et max pour éviter les valeurs extrêmes
                    min_weight = getattr(self.config, 'min_sample_weight', 0.2)
                    max_weight = getattr(self.config, 'max_sample_weight', 5.0)
                    sample_weights = np.clip(normalized_weights, min_weight, max_weight)

                logger.info(f"Poids d'échantillons calculés: min={np.min(sample_weights):.4f}, max={np.max(sample_weights):.4f}, mean={np.mean(sample_weights):.4f}")
            else:
                # Si le modèle n'est pas entraîné, appliquer une stratégie simplifiée
                logger.info("Modèle LGBM non entraîné, utilisation de poids uniformes.")

            return sample_weights

        except Exception as e:
            logger.error(f"Erreur lors du calcul des poids d'échantillons: {e}", exc_info=True)
            return None