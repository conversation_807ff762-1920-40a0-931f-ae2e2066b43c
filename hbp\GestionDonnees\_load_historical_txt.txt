# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 11157 à 11238
# Type: Méthode de la classe HybridBaccaratPredictor

    def _load_historical_txt(self, filepath: str) -> bool:
        """
        Charge données historique depuis .txt. Met à jour self.historical_data,
        self.loaded_historical, Markov global, ET self.historical_games_at_startup_or_reset.
        Retourne True/False. (Interne)
        """
        logger.info(f"Chargement interne historique: {filepath}")
        if not os.path.exists(filepath):
            logger.error(f"_load_historical_txt: Fichier non trouvé {filepath}")
            #Ajout
            if self.is_ui_available():
                self._update_progress(0, "Absence fichier historique.")
            return False

        new_historical_data = []
        valid_games = 0; skipped_short = 0; processed_lines = 0
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):  # Ligne 1-based pour logs
                    processed_lines += 1
                    line = line.strip()
                    if not line: continue

                    raw_outcomes = [o.strip().lower() for o in line.replace(" ", "").split(',') if o]
                    converted_game = []
                    valid_line = True  # Assumer valide au début de la ligne
                    for idx, outcome in enumerate(raw_outcomes):
                        if outcome in ('b', 'banker', '1'):  # 1 = Banker
                            converted_game.append('banker')
                        elif outcome in ('p', 'player', '0'):  # 0 = Player
                            converted_game.append('player')
                        # elif outcome in ('t', 'tie', '2'): # Ignorer les TIEs pour l'entraînement
                        #      logger.debug(f"Ligne {line_num}: Tie ignoré.")
                        else:
                            logger.warning(f"Ligne {line_num}, Col {idx+1}: Symbole invalide '{outcome}' ignoré.")
                            # On ne rejette pas toute la ligne, juste ce symbole

                    # GARDER les parties même si courtes après filtrage? Oui, pour l'instant.
                    # Utiliser min_rounds_per_game concerne plus le Markov global
                    if len(converted_game) > 0 : # Garder si au moins 1 coup converti
                        new_historical_data.append(converted_game)
                        valid_games += 1
                        if len(converted_game) < self.config.min_rounds_per_game:
                             skipped_short += 1  # Compter comme "courte" pour info, mais garder
                    else:  # Ligne vide après traitement
                         logger.debug(f"Ligne {line_num}: Vide après conversion/filtrage.")


            num_games_loaded = len(new_historical_data)
            # Verrous pour modifier état partagé
            with self.sequence_lock, self.markov_lock:  # Assurer que historique et compteur sont cohérents
                self.historical_data = new_historical_data
                self.loaded_historical = True
                self.historical_games_at_startup_or_reset = num_games_loaded #MAJ


                total_rounds = sum(len(g) for g in new_historical_data)
                logger.info(f"_load_historical_txt: Succès ({num_games_loaded} parties chargées, {total_rounds} coups totaux).")

                # Mettre à jour Markov global
                if self.markov:
                    try:
                        self.markov.update_global(self.historical_data)
                        logger.info("_load_historical_txt: Markov global mis à jour.")
                    except Exception as e_markov:
                         logger.error(f"_load_historical_txt: Erreur MàJ Markov global: {e_markov}", exc_info=True)
                else:
                    logger.warning("_load_historical_txt: self.markov non dispo, MàJ globale ignorée.")
            return True

        except UnicodeDecodeError:
            logger.error(f"_load_historical_txt: Erreur encodage (UTF-8 requis): {filepath}")
            with self.sequence_lock: self.historical_games_at_startup_or_reset = 0  # Reset compteur si erreur
            if self.is_ui_available():
                self._update_progress(0, "Erreur encodage historique.")
            return False
        except Exception as e:
            logger.error(f"_load_historical_txt: Erreur lecture/traitement: {e}", exc_info=True)
            with self.sequence_lock: self.historical_games_at_startup_or_reset = 0  # Reset compteur
            if self.is_ui_available():
                self._update_progress(0, "Erreur lecture historique.")
            return False