# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 14023 à 14065
# Type: Méthode de la classe MetaOptimizer

    def _identify_problematic_params(self, trial):
        """
        Identifie les paramètres potentiellement problématiques dans un essai.

        Args:
            trial: Essai Optuna

        Returns:
            list: Liste des noms de paramètres problématiques
        """
        # Liste pour stocker les paramètres problématiques
        problematic_params = []

        # Récupérer les paramètres et la valeur de l'essai
        params = trial.params
        value = trial.value

        # Si la valeur est None ou NaN, tous les paramètres sont potentiellement problématiques
        if value is None or (isinstance(value, float) and math.isnan(value)):
            return list(params.keys())

        # Vérifier chaque paramètre
        for param_name, param_value in params.items():
            # Vérifier si le paramètre a des valeurs extrêmes
            if isinstance(param_value, (int, float)):
                # Récupérer les limites du paramètre
                param_range = self._get_param_range(param_name)
                if param_range:
                    min_val, max_val = param_range

                    # Vérifier si la valeur est proche des limites
                    range_size = max_val - min_val
                    if range_size > 0:
                        relative_pos = (param_value - min_val) / range_size
                        if relative_pos < 0.05 or relative_pos > 0.95:
                            problematic_params.append(param_name)
                            continue

            # Vérifier si le paramètre est dans une région problématique connue
            if self._is_param_in_problematic_region(param_name, param_value):
                problematic_params.append(param_name)

        return problematic_params