# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1230 à 1263
# Type: Méthode de la classe TrainOptimizeInterface

    def get_best_model(self, metric_name: str, higher_is_better: bool = True) -> Tuple[str, float]:
        """
        Récupère le meilleur modèle selon une métrique.

        Args:
            metric_name: Nom de la métrique
            higher_is_better: Si True, une valeur plus élevée est meilleure

        Returns:
            Tuple (nom du meilleur modèle, valeur de la métrique)

        Raises:
            ValueError: Si aucun résultat n'est disponible pour cette métrique
        """
        best_model = None
        best_value = float('-inf') if higher_is_better else float('inf')

        for model_name, metrics in self.results.items():
            if metric_name in metrics:
                value = metrics[metric_name]

                if higher_is_better:
                    if value > best_value:
                        best_value = value
                        best_model = model_name
                else:
                    if value < best_value:
                        best_value = value
                        best_model = model_name

        if best_model is None:
            raise ValueError(f"Aucun résultat disponible pour la métrique '{metric_name}'")

        return best_model, best_value