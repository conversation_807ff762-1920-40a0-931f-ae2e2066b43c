# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 3037 à 3058
# Type: Méthode

def objective_consecutive(trial, config, evaluate_config_func, best_params_phase1=None, best_precision=None, logger=None):
    """
    Fonction objectif pour la Phase 2: Optimiser l'objectif 1 (recommandations NON-WAIT valides consécutives).
    Cette fonction a été simplifiée et n'est plus utilisée activement, mais reste présente pour éviter les erreurs de référence.

    Args:
        trial: Objet trial Optuna
        config: Configuration de base
        evaluate_config_func: Fonction pour évaluer une configuration
        best_params_phase1: Meilleurs paramètres de la phase 1
        best_precision: Meilleure précision obtenue en phase 1
        logger: Logger pour les messages

    Returns:
        float: Score d'optimisation pour l'objectif 1 (valeur par défaut)
    """
    if logger:
        logger.warning("La fonction objective_consecutive est obsolète et a été simplifiée.")
        logger.warning("Cette fonction n'est plus utilisée activement mais reste présente pour éviter les erreurs de référence.")

    # Retourner une valeur par défaut
    return 1e-7