# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1295 à 1306
# Type: Méthode
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

def optimize_lstm_memory(lstm_model, training_mode=False):
    """
    Optimise la mémoire utilisée par un modèle LSTM.

    Args:
        lstm_model: Le modèle LSTM à optimiser
        training_mode: Si True, le modèle est en mode entraînement et les gradients sont conservés

    Returns:
        Le modèle LSTM optimisé
    """
    return MemoryManager.optimize_lstm_memory(lstm_model, training_mode)