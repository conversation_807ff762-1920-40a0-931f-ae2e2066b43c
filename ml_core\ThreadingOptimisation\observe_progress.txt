# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1473 à 1477
# Type: Méthode de la classe ThreadedTrainer

                def observe_progress(progress, message):
                    # Appeler notre callback avec les mêmes paramètres
                    # sans créer de boucle de récursion
                    if self.progress_callback:
                        self.progress_callback(progress, message)