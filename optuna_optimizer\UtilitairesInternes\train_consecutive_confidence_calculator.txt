# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 130 à 132
# Type: Méthode de la classe IsolatedMetricsModule

    def train_consecutive_confidence_calculator(sequence_history, current_position, config=None):
        # Utiliser directement la fonction depuis ml_core.py
        return train_consecutive_confidence_calculator(sequence_history, current_position, config)