# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 12488 à 12603
# Type: Méthode de la classe OptunaOptimizer

    def _extract_dataset_features(self, sample_data=None):
        """
        Extrait des caractéristiques du jeu de données pour le méta-apprentissage.

        Args:
            sample_data: Échantillon de données à analyser (optionnel)

        Returns:
            Dict: Caractéristiques du jeu de données
        """
        import os
        import numpy as np

        # Caractéristiques par défaut
        features = {
            'total_lines': 0,
            'pattern_diversity': 0.0,
            'alternation_rate': 0.0,
            'p_frequency': 0.0,
            'b_frequency': 0.0,
            'longest_streak': 0,
            'avg_streak_length': 0.0,
            'entropy': 0.0
        }

        try:
            # Obtenir le nombre total de lignes
            historical_data_path = os.path.join(os.getcwd(), "historical_data.txt")
            if os.path.exists(historical_data_path):
                with open(historical_data_path, 'r') as f:
                    features['total_lines'] = sum(1 for _ in f)

            # Si aucun échantillon n'est fourni, utiliser les données disponibles
            if sample_data is None and hasattr(self, 'sequences'):
                sample_data = self.sequences[:min(1000, len(self.sequences))]

            # Si aucune donnée n'est disponible, retourner les caractéristiques par défaut
            if sample_data is None or not sample_data:
                return features

            # Extraire les séquences cibles
            sequences = []
            for seq in sample_data:
                if isinstance(seq, dict) and 'target_sequence' in seq:
                    sequences.append(seq['target_sequence'])

            if not sequences:
                return features

            # Calculer les caractéristiques
            all_elements = []
            pattern_counts = {}
            streak_lengths = []
            current_streak = 1
            alternations = 0

            # Parcourir toutes les séquences
            for seq in sequences:
                for i in range(len(seq)):
                    all_elements.append(seq[i])

                    # Compter les alternances
                    if i > 0 and seq[i] != seq[i-1]:
                        alternations += 1
                        streak_lengths.append(current_streak)
                        current_streak = 1
                    else:
                        current_streak += 1

                    # Compter les patterns de longueur 3
                    if i >= 2:
                        pattern = seq[i-2:i+1]
                        if pattern not in pattern_counts:
                            pattern_counts[pattern] = 0
                        pattern_counts[pattern] += 1

            # Ajouter le dernier streak
            if current_streak > 1:
                streak_lengths.append(current_streak)

            # Calculer les fréquences
            total_elements = len(all_elements)
            if total_elements > 0:
                p_count = sum(1 for e in all_elements if e == 'P')
                b_count = sum(1 for e in all_elements if e == 'B')
                features['p_frequency'] = p_count / total_elements
                features['b_frequency'] = b_count / total_elements

            # Calculer le taux d'alternance
            if total_elements > 1:
                features['alternation_rate'] = alternations / (total_elements - 1)

            # Calculer la diversité des patterns
            if pattern_counts:
                unique_patterns = len(pattern_counts)
                max_possible_patterns = 2**3  # Pour des séquences binaires de longueur 3
                features['pattern_diversity'] = unique_patterns / max_possible_patterns

            # Calculer les statistiques de streaks
            if streak_lengths:
                features['longest_streak'] = max(streak_lengths)
                features['avg_streak_length'] = sum(streak_lengths) / len(streak_lengths)

            # Calculer l'entropie (mesure de l'imprévisibilité)
            if total_elements > 0:
                p_prob = features['p_frequency']
                b_prob = features['b_frequency']
                if p_prob > 0 and b_prob > 0:
                    features['entropy'] = -(p_prob * np.log2(p_prob) + b_prob * np.log2(b_prob))

            logger.info(f"Caractéristiques du jeu de données extraites: {features}")
            return features

        except Exception as e:
            logger.error(f"Erreur lors de l'extraction des caractéristiques du jeu de données: {e}")
            return features