# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 540 à 549
# Type: Méthode de la classe PyTorchMemoryContext

    def __init__(self, optimize_on_enter=True, cleanup_on_exit=True):
        """
        Initialise le gestionnaire de contexte.

        Args:
            optimize_on_enter: Si True, optimise la mémoire à l'entrée du contexte
            cleanup_on_exit: Si True, nettoie la mémoire à la sortie du contexte
        """
        self.optimize_on_enter = optimize_on_enter
        self.cleanup_on_exit = cleanup_on_exit