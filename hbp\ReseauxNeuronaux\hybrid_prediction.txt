# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 8795 à 9937
# Type: Méthode de la classe HybridBaccaratPredictor

    def hybrid_prediction(self, lgbm_feat: Optional[List[float]], lstm_feat: Optional[np.ndarray], optimization_phase: Optional[int] = None) -> Dict:
        # Mesurer le temps d'exécution
        import time
        start_time = time.time()

        # Vérifier si nous sommes en mode optimisation avec collecteur de statistiques
        using_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
        """
        Effectue une prédiction hybride (Markov, LGBM, LSTM) avec calcul avancé
        de confiance et d'incertitude.

        Utilise:
        - Pondération bayésienne des modèles
        - Calcul d'incertitude épistémique et aléatoire
        - Analyse de sensibilité contextuelle
        - Méta-modèle de confiance basé sur la performance historique

        # Importer NotFittedError ici pour éviter les problèmes d'importation circulaire
        from sklearn.exceptions import NotFittedError

        Args:
            lgbm_feat (Optional[List[float]]): Features pour le modèle LGBM
            lstm_feat (Optional[np.ndarray]): Features pour le modèle LSTM
            optimization_phase (Optional[int]): Phase d'optimisation en cours
                                               1 = Phase 1 (Objectif 2: équilibre WAIT/NON-WAIT)
                                               2 = Phase 2 (Objectif 1: recommandations NON-WAIT valides consécutives)
                                               None = Utilisation normale (après optimisation)

        Returns:
            Dict: Dictionnaire contenant les prédictions, recommandations et mesures d'incertitude
        """
        # Si optimization_phase n'est pas fourni, essayer de le récupérer depuis la configuration
        if optimization_phase is None:
            optimization_phase = getattr(self.config, 'optimization_phase', None)
            if optimization_phase is not None:
                logger.debug(f"hybrid_prediction: Utilisation de optimization_phase={optimization_phase} depuis la configuration")

        # Logs de diagnostic pour faciliter le débogage
        if optimization_phase == 1:
            if not using_stats_collector:
                logger.debug("hybrid_prediction: Mode Phase 1 (Objectif 2: équilibre WAIT/NON-WAIT)")
        elif optimization_phase == 2:
            if not using_stats_collector:
                logger.debug("hybrid_prediction: Mode Phase 2 (Objectif 1: recommandations NON-WAIT valides consécutives)")
        else:
            if not using_stats_collector:
                logger.debug("hybrid_prediction: Mode normal (après optimisation ou non optimisé)")
        with self.model_lock:  # Protège accès aux modèles pour prédiction
            current_weights = self.get_weights()
            predictions: Dict[str, Any] = {
                'methods': {},
                'recommendation': 'wait',
                'player': 0.5,
                'banker': 0.5,
                'uncertainty': 0.5,  # Valeur par défaut
                'confidence_metrics': {},  # Nouveau: stocke les métriques détaillées de confiance
            }
            # Utiliser un epsilon depuis la configuration pour éviter les divisions par zéro
            epsilon = getattr(self.config, 'epsilon_value', 1e-9)

            # --- Prédictions individuelles ---
            # Initialiser les flags pour savoir quels modèles sont entraînés
            markov_trained = False
            lgbm_trained = False
            lstm_trained = False

            # Vérifier si le modèle Markov est activé
            use_markov_model = getattr(self.config, 'use_markov_model', True)

            # Markov (si activé)
            markov_pred = {'player': 0.5, 'banker': 0.5}
            p_markov_banker = 0.5  # Initialiser proba banker
            if use_markov_model and self.markov:
                try:
                    # Vérifier que la séquence est valide avant de l'utiliser
                    if not hasattr(self, 'sequence') or not isinstance(self.sequence, list) or len(self.sequence) < 1:
                        logger.warning("hybrid_prediction: Séquence invalide pour Markov (None, non-liste ou vide). Utilisation de prédiction 50/50.")
                        predictions['methods']['markov'] = {'player': 0.5, 'banker': 0.5}
                    else:
                        # Vérifier que tous les éléments de la séquence sont valides
                        valid_sequence = True
                        for i, item in enumerate(self.sequence):
                            if not isinstance(item, str) or item not in ('player', 'banker'):
                                logger.warning(f"hybrid_prediction: Élément invalide dans la séquence à l'index {i}: {item}. Utilisation de prédiction 50/50.")
                                valid_sequence = False
                                break

                        if valid_sequence:
                            with self.markov_lock:
                                # Utiliser les paramètres Markov de la configuration
                                markov_global_weight = getattr(self.config, 'markov_global_weight', None)
                                markov_context_weight = getattr(self.config, 'markov_context_weight', None)
                                markov_decay_factor = getattr(self.config, 'markov_decay_factor', None)

                                # Créer un dictionnaire avec les paramètres non None
                                markov_params = {}
                                if markov_global_weight is not None:
                                    markov_params['global_weight'] = markov_global_weight
                                if markov_context_weight is not None:
                                    markov_params['context_weight'] = markov_context_weight
                                if markov_decay_factor is not None:
                                    markov_params['decay_factor'] = markov_decay_factor

                                markov_pred = self.markov.get_combined_probs(self.sequence, **markov_params)
                            p_markov_banker = markov_pred.get('banker', 0.5)
                            predictions['methods']['markov'] = {
                                'player': markov_pred.get('player', 0.5),
                                'banker': p_markov_banker,
                            }
                            # Marquer le modèle Markov comme entraîné
                            markov_trained = True
                        else:
                            predictions['methods']['markov'] = {'player': 0.5, 'banker': 0.5}
                except Exception as e_markov:
                    logger.error(f"hybrid_prediction: Erreur prédiction Markov: {e_markov}", exc_info=False)
                    predictions['methods']['markov'] = {'player': 0.5, 'banker': 0.5}
                    p_markov_banker = 0.5
            else:
                # Si Markov est désactivé ou non disponible
                predictions['methods']['markov'] = {'player': 0.5, 'banker': 0.5}
                p_markov_banker = 0.5
                if not use_markov_model:
                    logger.debug("hybrid_prediction: Modèle Markov désactivé (use_markov_model=False).")

            # LGBM (Utilise le cache qui appelle maintenant predict_with_lgbm si besoin)
            try:
                lgbm_pred_dict = self._get_cached_lgbm_prediction(lgbm_feat)
                p_lgbm_banker = lgbm_pred_dict.get('banker', 0.5)  # Initialiser proba banker
                predictions['methods']['lgbm'] = {
                    'player': lgbm_pred_dict.get('player', 0.5),
                    'banker': p_lgbm_banker,
                }
                # Marquer le modèle LGBM comme entraîné
                lgbm_trained = True
            except NotFittedError as e_notfit:
                # Vérifier si nous sommes en phase d'entraînement ou d'optimisation Optuna
                is_training_phase = getattr(self, '_is_training', False)
                is_optuna_phase = getattr(self, 'is_optuna_running', False)
                log_method = logger.debug if (is_training_phase or is_optuna_phase) else logger.warning
                log_method(f"Modèle LGBM non entraîné dans hybrid_prediction: {e_notfit}")
                p_lgbm_banker = 0.5
                predictions['methods']['lgbm'] = {
                    'player': 0.5,
                    'banker': 0.5,
                }
            except Exception as e_lgbm:
                logger.error(f"Erreur prédiction LGBM dans hybrid_prediction: {e_lgbm}", exc_info=True)
                p_lgbm_banker = 0.5
                predictions['methods']['lgbm'] = {
                    'player': 0.5,
                    'banker': 0.5,
                }

            # LSTM
            try:
                lstm_pred_dict = self.predict_with_lstm(lstm_feat)
                p_lstm_banker = lstm_pred_dict.get('banker', 0.5)  # Initialiser proba banker
                predictions['methods']['lstm'] = {
                    'player': lstm_pred_dict.get('player', 0.5),
                    'banker': p_lstm_banker,
                }
                # Marquer le modèle LSTM comme entraîné
                lstm_trained = True
            except NotFittedError as e_notfit_lstm:
                # Vérifier si nous sommes en phase d'entraînement ou d'optimisation Optuna
                is_training_phase = getattr(self, '_is_training', False)
                is_optuna_phase = getattr(self, 'is_optuna_running', False)
                log_method = logger.debug if (is_training_phase or is_optuna_phase) else logger.warning
                log_method(f"Modèle LSTM non entraîné dans hybrid_prediction: {e_notfit_lstm}")
                p_lstm_banker = 0.5
                predictions['methods']['lstm'] = {
                    'player': 0.5,
                    'banker': 0.5,
                }
            except Exception as e_lstm:
                logger.error(f"Erreur prédiction LSTM dans hybrid_prediction: {e_lstm}", exc_info=True)
                p_lstm_banker = 0.5
                predictions['methods']['lstm'] = {
                    'player': 0.5,
                    'banker': 0.5,
                }

            # --- CALCUL AVANCÉ DE CONFIANCE POUR CHAQUE MODÈLE ---
            method_confidences = {}

            # Créer un dictionnaire pour suivre quels modèles sont entraînés
            trained_models = {
                'markov': markov_trained,
                'lgbm': lgbm_trained,
                'lstm': lstm_trained
            }

            # Journaliser les modèles entraînés seulement si nous sommes en mode debug explicite
            # ou si nous utilisons le collecteur de statistiques
            # ET que nous ne sommes pas en phase d'optimisation Optuna
            # Vérification stricte pour s'assurer que les messages ne s'affichent pas pendant l'optimisation
            is_optuna_phase = getattr(self, 'is_optuna_running', False)
            has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None

            # Ne pas afficher les messages si nous sommes en phase d'optimisation ou si nous avons un collecteur de statistiques
            if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                logger.debug(f"Modèles entraînés: Markov={markov_trained}, LGBM={lgbm_trained}, LSTM={lstm_trained}")

            # Calculer la confiance uniquement pour les modèles entraînés
            for method, method_probas in predictions['methods'].items():
                # Vérifier si le modèle est entraîné
                if trained_models.get(method, False):
                    prob_banker = method_probas.get('banker', 0.5)
                    confidence = self.calculate_model_confidence(prob_banker, method)
                    method_confidences[method] = confidence
                    # Stocker la confiance dans les prédictions pour référence
                    predictions['methods'][method]['confidence'] = confidence
                    predictions['methods'][method]['trained'] = True
                else:
                    # Marquer explicitement comme non entraîné
                    predictions['methods'][method]['trained'] = False
                    # Ne pas afficher ce message pendant l'optimisation
                    is_optuna_phase = getattr(self, 'is_optuna_running', False)
                    has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                    if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                        logger.debug(f"Modèle {method} non entraîné, exclu de la combinaison pondérée")

            # --- PONDÉRATION BAYÉSIENNE DES MODÈLES ---
            # Filtrer les poids pour n'inclure que les modèles entraînés
            filtered_weights = {k: v for k, v in current_weights.items() if trained_models.get(k, False)}

            # Si aucun modèle n'est entraîné, utiliser une distribution uniforme
            if not filtered_weights:
                logger.warning("Aucun modèle entraîné disponible. Utilisation de prédiction 50/50.")
                predictions['player'] = 0.5
                predictions['banker'] = 0.5
                # Sortir tôt avec une recommandation WAIT
                predictions['recommendation'] = 'wait'
                predictions['uncertainty'] = 1.0  # Incertitude maximale
                # Mesurer le temps d'exécution
                end_time = time.time()
                predictions['execution_time'] = end_time - start_time
                return predictions

            # Normaliser les poids filtrés
            total_weight = sum(filtered_weights.values())
            if total_weight > 0:
                normalized_weights = {k: v / total_weight for k, v in filtered_weights.items()}
            else:
                # Cas improbable où tous les poids sont nuls
                normalized_weights = {k: 1.0 / len(filtered_weights) for k in filtered_weights}

            # Calculer les poids bayésiens uniquement pour les modèles entraînés
            bayesian_weights = self.calculate_bayesian_weights(normalized_weights, method_confidences)

            # --- COMBINAISON PONDÉRÉE AVEC POIDS BAYÉSIENS ---
            combined_pred_num = {'player': 0.0, 'banker': 0.0}
            total_effective_weight_den = 0.0

            for method, base_weight in bayesian_weights.items():
                # Vérifier si le modèle est entraîné (redondant avec le filtrage précédent, mais pour plus de sécurité)
                if not trained_models.get(method, False):
                    # Ne pas afficher ce message pendant l'optimisation
                    is_optuna_phase = getattr(self, 'is_optuna_running', False)
                    has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                    if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                        logger.debug(f"Modèle {method} non entraîné, ignoré dans la combinaison pondérée")
                    continue

                method_probas = predictions['methods'].get(method)
                if method_probas:
                    prob_banker_m = method_probas.get('banker', 0.5)
                    prob_player_m = 1.0 - prob_banker_m
                    confidence = method_confidences.get(method, 0.5)

                    # Poids effectif basé sur la confiance calculée
                    min_factor = getattr(self.config, 'min_confidence_factor', 0.5)  # Poids minimum même si confiance nulle
                    range_factor = getattr(self.config, 'range_confidence_factor', 1.0)  # Facteur pour la partie confiance
                    effective_weight = base_weight * (min_factor + range_factor * confidence)

                    # Journaliser les poids pour le débogage seulement si nous sommes en mode debug explicite
                    # ou si nous utilisons le collecteur de statistiques
                    # ET que nous ne sommes pas en phase d'optimisation Optuna
                    # Vérification stricte pour s'assurer que les messages ne s'affichent pas pendant l'optimisation
                    is_optuna_phase = getattr(self, 'is_optuna_running', False)
                    has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None

                    # Ne pas afficher les messages si nous sommes en phase d'optimisation ou si nous avons un collecteur de statistiques
                    if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                        logger.debug(f"Modèle {method} (entraîné): poids={base_weight:.4f}, confiance={confidence:.4f}, poids effectif={effective_weight:.4f}")

                    combined_pred_num['player'] += effective_weight * prob_player_m
                    combined_pred_num['banker'] += effective_weight * prob_banker_m
                    total_effective_weight_den += effective_weight

            combined_pred = {'player': 0.5, 'banker': 0.5}
            if total_effective_weight_den > epsilon:
                combined_pred['player'] = combined_pred_num['player'] / total_effective_weight_den
                combined_pred['banker'] = combined_pred_num['banker'] / total_effective_weight_den
                # Normalisation finale pour assurer somme = 1
                total_final = combined_pred['player'] + combined_pred['banker']
                if abs(total_final - 1.0) > epsilon and total_final > epsilon:
                    combined_pred['player'] /= total_final
                    combined_pred['banker'] /= total_final
                elif total_final <= epsilon:  # Cas improbable de somme nulle
                    combined_pred['player'] = 0.5
                    combined_pred['banker'] = 0.5
                else:  # Assurer la cohérence si une seule proba est calculée
                    combined_pred['banker'] = 1.0 - combined_pred['player']
                # Clipper pour sécurité numérique
                combined_pred['player'] = np.clip(combined_pred['player'], 0.0, 1.0)
                combined_pred['banker'] = np.clip(combined_pred['banker'], 0.0, 1.0)
            else:
                logger.warning("hybrid_prediction: Poids effectif total quasi nul. Utilisation de 50/50.")

            predictions['player'] = combined_pred['player']
            predictions['banker'] = combined_pred['banker']

            # --- CALCUL AVANCÉ DE L'INCERTITUDE ---

            # 1. Incertitude épistémique (désaccord entre modèles)
            # N'utiliser que les probabilités des modèles entraînés
            prob_list = []
            if markov_trained:
                prob_list.append(p_markov_banker)
            if lgbm_trained:
                prob_list.append(p_lgbm_banker)
            if lstm_trained:
                prob_list.append(p_lstm_banker)

            # Si aucun modèle n'est entraîné ou un seul modèle, utiliser une incertitude élevée
            if len(prob_list) <= 1:
                # Ne pas afficher ce message pendant l'optimisation
                is_optuna_phase = getattr(self, 'is_optuna_running', False)
                has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                    logger.debug(f"Incertitude épistémique élevée car seulement {len(prob_list)} modèle(s) entraîné(s)")
                epistemic_uncertainty = 0.8  # Valeur élevée par défaut
            else:
                epistemic_uncertainty = self.calculate_epistemic_uncertainty(prob_list)

            predictions['confidence_metrics']['epistemic_uncertainty'] = epistemic_uncertainty
            predictions['confidence_metrics']['trained_models_count'] = len(prob_list)

            # 2. Incertitude aléatoire (entropie de la prédiction finale)
            aleatoric_uncertainty = self.calculate_aleatoric_uncertainty(predictions['banker'])
            predictions['confidence_metrics']['aleatoric_uncertainty'] = aleatoric_uncertainty

            # 3. Sensibilité contextuelle
            context_sensitivity = self.analyze_context_sensitivity(self.sequence, predictions['banker'])
            predictions['confidence_metrics']['context_sensitivity'] = context_sensitivity

            # 4. Utiliser le calculateur de confiance consécutive pour les manches 31-60
            consecutive_confidence = 0.0
            current_round = len(self.sequence)

            # Vérifier si nous sommes dans la plage de manches cibles (31-60)
            target_round_min = getattr(self.config, 'target_round_min', 31)
            target_round_max = getattr(self.config, 'target_round_max', 60)
            is_target_round = target_round_min <= current_round <= target_round_max

            # Si le calculateur de confiance consécutive n'existe pas, l'initialiser
            if is_target_round and not hasattr(self, 'consecutive_confidence_calculator'):
                logger.info(f"Manche {current_round}: Initialisation du calculateur de confiance consécutive manquant")
                self.init_consecutive_confidence_calculator()

            if is_target_round and hasattr(self, 'consecutive_confidence_calculator'):
                try:
                    # Vérifier si le calculateur est initialisé correctement
                    if hasattr(self.consecutive_confidence_calculator, 'calculate_confidence'):
                        # Extraire les features pour le calculateur de confiance consécutive
                        features = lgbm_feat if lgbm_feat is not None else []

                        # Calculer la confiance basée sur les recommandations NON-WAIT valides consécutives
                        confidence_result = self.consecutive_confidence_calculator.calculate_confidence(
                            features, current_round, self.config
                        )

                        consecutive_confidence = confidence_result.get("confidence", 0.0)
                        predictions['confidence_metrics']['consecutive_confidence'] = consecutive_confidence
                        predictions['confidence_metrics']['expected_consecutive'] = confidence_result.get("expected_consecutive", 0)
                        predictions['confidence_metrics']['similar_patterns_count'] = confidence_result.get("similar_patterns_count", 0)
                        predictions['confidence_metrics']['success_rate'] = confidence_result.get("success_rate", 0.0)

                        if not using_stats_collector:
                            logger.debug(f"Manche {current_round}: Confiance consécutive calculée: {consecutive_confidence:.4f}")
                    else:
                        # Calculateur non complètement initialisé
                        logger.warning("Calculateur de confiance consécutive non complètement initialisé (méthode calculate_confidence manquante).")
                        predictions['confidence_metrics']['consecutive_confidence'] = 0.5
                except Exception as e_calc:
                    logger.error(f"Erreur lors du calcul de la confiance consécutive: {e_calc}", exc_info=True)
                    predictions['confidence_metrics']['consecutive_confidence'] = 0.5

            # 5. Combinaison pondérée des différentes sources d'incertitude
            # Utiliser les poids depuis la configuration
            w_epistemic = getattr(self.config, 'w_epistemic', 0.4)    # Poids pour l'incertitude épistémique
            w_aleatoric = getattr(self.config, 'w_aleatoric', 0.3)    # Poids pour l'incertitude aléatoire
            w_sensitivity = getattr(self.config, 'w_sensitivity', 0.3)  # Poids pour la sensibilité contextuelle

            # Normaliser les poids pour s'assurer qu'ils somment à 1
            total_w = w_epistemic + w_aleatoric + w_sensitivity
            if total_w > 0:
                w_epistemic = w_epistemic / total_w
                w_aleatoric = w_aleatoric / total_w
                w_sensitivity = w_sensitivity / total_w

            # Calculer l'incertitude totale
            # Facteur global d'incertitude
            global_uncertainty_factor = getattr(self.config, 'global_uncertainty_factor', 1.0)  # Facteur global d'incertitude

            # Si les modèles ne sont pas entraînés, utiliser une incertitude élevée
            if not hasattr(self, '_models_are_trained'):
                # Définir la méthode si elle n'existe pas encore
                def _models_are_trained(self) -> bool:
                    """
                    Vérifie si les modèles ont été entraînés.

                    Returns:
                        bool: True si au moins un des modèles principaux (LGBM, LSTM) a été entraîné, False sinon.
                    """
                    with self.model_lock:
                        # Vérifier si LGBM est entraîné
                        lgbm_trained = (self.calibrated_lgbm is not None and
                                      hasattr(self.calibrated_lgbm, 'classes_'))

                        # Vérifier si LSTM est entraîné
                        lstm_trained = (self.lstm is not None and
                                      hasattr(self.lstm, 'trained') and
                                      getattr(self.lstm, 'trained', False))

                        # Considérer les modèles comme entraînés si au moins un des modèles principaux est entraîné
                        return lgbm_trained or lstm_trained

                # Ajouter la méthode à la classe
                import types
                self._models_are_trained = types.MethodType(_models_are_trained, self)

            # Ajouter une incertitude de base élevée si les modèles ne sont pas entraînés
            base_uncertainty = 0.0
            if not self._models_are_trained():
                base_uncertainty = getattr(self.config, 'base_uncertainty_non_trained', 0.5)  # Incertitude de base élevée si non entraîné
                # Ne pas afficher ce message pendant l'optimisation
                is_optuna_phase = getattr(self, 'is_optuna_running', False)
                has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                    logger.debug(f"Ajout d'une incertitude de base de {base_uncertainty:.2f} car les modèles ne sont pas entraînés")

            # Ajouter le poids pour la confiance consécutive
            w_consecutive = getattr(self.config, 'w_consecutive', 0.0)  # Poids pour la confiance consécutive

            # Ajuster les poids si nous sommes dans la plage de manches cibles (31-60)
            if is_target_round:
                # Augmenter l'importance de la confiance consécutive pour les manches cibles
                w_consecutive = getattr(self.config, 'w_consecutive_target', 0.3)

                # Réduire les autres poids proportionnellement
                total_other_weights = w_epistemic + w_aleatoric + w_sensitivity
                if total_other_weights > 0:
                    factor = (1.0 - w_consecutive) / total_other_weights
                    w_epistemic *= factor
                    w_aleatoric *= factor
                    w_sensitivity *= factor

            # Convertir la confiance consécutive en incertitude (1 - confiance)
            consecutive_uncertainty = 1.0 - consecutive_confidence

            # Calculer l'incertitude totale en combinant l'incertitude de base et les sources d'incertitude pondérées
            total_uncertainty = base_uncertainty + (
                w_epistemic * epistemic_uncertainty +
                w_aleatoric * aleatoric_uncertainty +
                w_sensitivity * context_sensitivity +
                w_consecutive * consecutive_uncertainty
            ) * global_uncertainty_factor

            # Stocker la confiance consécutive dans les métriques
            predictions['confidence_metrics']['consecutive_uncertainty'] = consecutive_uncertainty

            predictions['uncertainty'] = np.clip(total_uncertainty, 0.0, 1.0)

            # Logging simplifié des métriques d'incertitude - uniquement le total
            # Réduire la verbosité en ne loggant que le résultat final
            if predictions['uncertainty'] > 0.7:  # Ne logger que si l'incertitude est élevée
                if not using_stats_collector:
                    logger.info(f"Incertitude élevée détectée: {predictions['uncertainty']:.4f}")

            # --- SYSTÈME DE DÉCISION AVANCÉ BASÉ SUR DES FEATURES POUR LES OBJECTIFS 1 ET 2 ---
            # Calculer la confiance dans la prédiction
            confidence = np.maximum(predictions['player'], predictions['banker'])

            # Vérifier si les modèles ont été entraînés
            models_trained = self._models_are_trained()

            # 1. Récupérer les paramètres de configuration pour le système de décision
            if models_trained:
                # Utiliser le seuil de base configuré pour les modèles entraînés
                base_confidence_threshold = getattr(self.config, 'base_confidence_threshold',
                                                   self.config.min_confidence_for_recommendation)
            else:
                # Seuil plus élevé pour les modèles non entraînés
                multiplier = getattr(self.config, 'non_trained_threshold_multiplier', 1.5)
                base_confidence_threshold = min(1.0, getattr(self.config, 'base_confidence_threshold', 0.5) * multiplier)
                # Ne pas afficher ce message pendant l'optimisation
                is_optuna_phase = getattr(self, 'is_optuna_running', False)
                has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                    logger.debug(f"Utilisation d'un seuil de confiance plus élevé ({base_confidence_threshold:.4f}, multiplier={multiplier:.2f}) car les modèles ne sont pas entraînés")

            # Ajuster le seuil de confiance en fonction de la phase d'optimisation
            if optimization_phase is not None:
                if optimization_phase == 1:
                    # Phase 1: Objectif 2 - équilibre WAIT/NON-WAIT
                    # Utiliser un seuil de confiance spécifique pour cette phase
                    phase1_threshold_factor = getattr(self.config, 'phase1_threshold_factor', 1.2)
                    adjusted_threshold = min(1.0, base_confidence_threshold * phase1_threshold_factor)
                    # Ne pas afficher ce message pendant l'optimisation
                    is_optuna_phase = getattr(self, 'is_optuna_running', False)
                    has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                    if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                        logger.debug(f"Phase 1 (Objectif 2): Ajustement du seuil de confiance: {base_confidence_threshold:.4f} -> {adjusted_threshold:.4f} (facteur: {phase1_threshold_factor:.2f})")
                    base_confidence_threshold = adjusted_threshold
                elif optimization_phase == 2:
                    # Phase 2: Objectif 1 - recommandations NON-WAIT valides consécutives
                    # Utiliser un seuil de confiance spécifique pour cette phase
                    phase2_threshold_factor = getattr(self.config, 'phase2_threshold_factor', 0.8)
                    adjusted_threshold = min(1.0, base_confidence_threshold * phase2_threshold_factor)
                    # Ne pas afficher ce message pendant l'optimisation
                    is_optuna_phase = getattr(self, 'is_optuna_running', False)
                    has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                    if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                        logger.debug(f"Phase 2 (Objectif 1): Ajustement du seuil de confiance: {base_confidence_threshold:.4f} -> {adjusted_threshold:.4f} (facteur: {phase2_threshold_factor:.2f})")
                    base_confidence_threshold = adjusted_threshold

            # 2. Vérifier si nous sommes dans la plage de manches cibles (31-60)
            current_round = len(self.sequence)
            target_round_min = getattr(self.config, 'target_round_min', 31)
            target_round_max = getattr(self.config, 'target_round_max', 60)
            is_target_round = target_round_min <= current_round <= target_round_max

            # Ajustement du comportement en fonction de la phase d'optimisation
            if optimization_phase is not None:
                if optimization_phase == 1:
                    # Phase 1 (Objectif 2): Équilibre WAIT/NON-WAIT
                    # Utiliser les paramètres optimisés pour l'équilibre
                    # Ne pas afficher ce message pendant l'optimisation
                    is_optuna_phase = getattr(self, 'is_optuna_running', False)
                    has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                    if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                        logger.debug("Mode optimisation Phase 1 (Objectif 2): Équilibre WAIT/NON-WAIT")
                    # Pas de modification spécifique, utiliser les paramètres par défaut
                elif optimization_phase == 2:
                    # Phase 2 (Objectif 1): Recommandations NON-WAIT valides consécutives
                    # Utiliser les paramètres optimisés pour les recommandations consécutives
                    # Ne pas afficher ce message pendant l'optimisation
                    is_optuna_phase = getattr(self, 'is_optuna_running', False)
                    has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                    if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                        logger.debug("Mode optimisation Phase 2 (Objectif 1): Recommandations NON-WAIT valides consécutives")
                    # Réduire le seuil de base pour favoriser les recommandations NON-WAIT
                    base_confidence_threshold = max(0.5, base_confidence_threshold - 0.1)
                    # Ne pas afficher ce message pendant l'optimisation
                    is_optuna_phase = getattr(self, 'is_optuna_running', False)
                    has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                    if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                        logger.debug(f"Seuil de confiance réduit pour Phase 2: {base_confidence_threshold:.4f}")
                else:
                    logger.warning(f"Phase d'optimisation inconnue: {optimization_phase}, utilisation des paramètres par défaut")

            # 3. Extraire les features pour la décision WAIT/NON-WAIT
            # Ces features seront utilisées pour déterminer si une recommandation WAIT ou NON-WAIT doit être faite
            decision_features = {
                # Confiance dans la prédiction
                'confidence': confidence,

                # Incertitude (épistémique, aléatoire, sensibilité)
                'uncertainty': predictions['uncertainty'],
                'epistemic_uncertainty': predictions.get('epistemic_uncertainty', 0.0),
                'aleatoric_uncertainty': predictions.get('aleatoric_uncertainty', 0.0),
                'sensitivity': predictions.get('sensitivity', 0.0),

                # Position dans la partie
                'current_round': current_round,
                'is_target_round': int(is_target_round),
                'relative_position': (current_round - target_round_min) / (target_round_max - target_round_min) if is_target_round else 0.0,

                # Historique récent
                'recent_accuracy': predictions.get('recent_accuracy', 0.5),
                'recent_wait_ratio': predictions.get('recent_wait_ratio', 0.0),
                'consecutive_valid_count': getattr(self, 'consecutive_nonwait_valid', 0),
                'max_consecutive_valid': getattr(self, 'max_consecutive_nonwait_valid', 0),

                # Métriques de confiance consécutive
                'consecutive_confidence': predictions['confidence_metrics'].get('consecutive_confidence', 0.5) if 'confidence_metrics' in predictions else 0.5,
                'expected_consecutive': predictions['confidence_metrics'].get('expected_consecutive', 0) if 'confidence_metrics' in predictions else 0,
                'similar_patterns_count': predictions['confidence_metrics'].get('similar_patterns_count', 0) if 'confidence_metrics' in predictions else 0,
                'success_rate': predictions['confidence_metrics'].get('success_rate', 0.5) if 'confidence_metrics' in predictions else 0.5,
            }

            # 4. Utiliser le calculateur de confiance consécutive amélioré pour la décision
            wait_recommendation_strength = 0.0
            non_wait_recommendation_strength = 0.0

            if hasattr(self, 'consecutive_confidence_calculator') and self.consecutive_confidence_calculator:
                try:
                    # Vérifier si le calculateur est initialisé correctement
                    if hasattr(self.consecutive_confidence_calculator, 'calculate_confidence'):
                        # Extraire les features pour le calculateur de confiance consécutive
                        features_vector = self._extract_features_for_consecutive_calculator()

                        # Calculer la confiance consécutive avec le calculateur amélioré
                        consecutive_result = self.consecutive_confidence_calculator.calculate_confidence(
                            features_vector, current_round, self.config
                        )

                        # Récupérer les forces de recommandation WAIT et NON-WAIT
                        wait_recommendation_strength = consecutive_result.get('wait_recommendation_strength', 0.0)
                        non_wait_recommendation_strength = consecutive_result.get('non_wait_recommendation_strength', 0.0)

                        # Ajouter les métriques détaillées aux features de décision
                        decision_features.update({
                            'wait_recommendation_strength': wait_recommendation_strength,
                            'non_wait_recommendation_strength': non_wait_recommendation_strength,
                            'bell_curve_bonus': consecutive_result.get('bell_curve_bonus', 1.0),
                            'sequence_bonus': consecutive_result.get('sequence_bonus', 1.0),
                            'late_game_factor': consecutive_result.get('late_game_factor', 1.0),
                            'occurrence_factor': consecutive_result.get('occurrence_factor', 1.0),
                            'consecutive_factor': consecutive_result.get('consecutive_factor', 1.0),
                            'current_wait_ratio': consecutive_result.get('current_wait_ratio', self.config.default_wait_ratio),
                        })

                        # Ne pas afficher ce message pendant l'optimisation
                        is_optuna_phase = getattr(self, 'is_optuna_running', False)
                        has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                        if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                            logger.debug(f"Calculateur de confiance consécutive: wait_strength={wait_recommendation_strength:.4f}, " +
                                       f"non_wait_strength={non_wait_recommendation_strength:.4f}, " +
                                       f"bell_curve={consecutive_result.get('bell_curve_bonus', 1.0):.2f}, " +
                                       f"sequence_bonus={consecutive_result.get('sequence_bonus', 1.0):.2f}")
                    else:
                        # Calculateur non complètement initialisé
                        logger.warning("Calculateur de confiance consécutive non complètement initialisé (méthode calculate_confidence manquante).")
                        wait_recommendation_strength = 0.0
                        non_wait_recommendation_strength = 0.0
                        decision_features.update({
                            'wait_recommendation_strength': 0.0,
                            'non_wait_recommendation_strength': 0.0,
                            'current_wait_ratio': self.config.default_wait_ratio,
                        })
                except Exception as e_consecutive:
                    logger.warning(f"Erreur lors du calcul de la confiance consécutive: {e_consecutive}")
                    wait_recommendation_strength = 0.0
                    non_wait_recommendation_strength = 0.0
                    decision_features.update({
                        'wait_recommendation_strength': 0.0,
                        'non_wait_recommendation_strength': 0.0,
                        'current_wait_ratio': self.config.default_wait_ratio,
                    })

            # Vérifier si nous sommes dans la plage de manches cibles (31-60)
            # Si nous ne sommes pas dans cette plage, retourner une recommandation WAIT par défaut
            # tout en continuant à calculer les prédictions internes pour maintenir l'état des modèles
            if not is_target_round:
                if not using_stats_collector:
                    logger.info(f"Manche {current_round}: Hors de la plage cible (31-60), recommandation WAIT par défaut")
                predictions['recommendation'] = 'wait'
                # Stocker les métriques pour référence même si on ne les utilise pas pour la recommandation
                predictions['decision_score'] = 0.0
                predictions['decision_threshold'] = 1.0  # Seuil impossible à atteindre
                predictions['decision_features'] = decision_features
                predictions['uncertainty_value'] = predictions['uncertainty']
                predictions['bayesian_weights'] = bayesian_weights
                # Mesurer le temps total d'exécution
                total_time = time.time() - start_time
                if not using_stats_collector:
                    logger.info(f"DIAGNOSTIC - Temps total de prédiction hybride (hors plage cible): {total_time*1000:.1f}ms")
                return predictions

            # Si nous venons juste d'entrer dans la plage cible (manche 31 exactement),
            # réinitialiser tous les compteurs et métriques adaptatives pour garantir
            # que les prédictions commencent avec un état "propre"
            if current_round == target_round_min:
                if not using_stats_collector:
                    logger.warning(f"===== MANCHE {current_round}: ENTRÉE DANS LA PLAGE CIBLE (31-60) =====")
                    logger.warning(f"===== DÉBUT DES PRÉDICTIONS VÉRITABLES =====")

                # 1. Réinitialiser les compteurs de recommandations
                self.consecutive_wait_count = 0
                if hasattr(self, 'consecutive_nonwait_valid'):
                    self.consecutive_nonwait_valid = 0

                # 2. Réinitialiser ou ajuster les métriques adaptatives
                if 'current_wait_ratio' in decision_features:
                    # Réinitialiser à la valeur optimale
                    optimal_wait_ratio = getattr(self.config, 'optimal_wait_ratio', self.config.default_wait_ratio)
                    decision_features['current_wait_ratio'] = optimal_wait_ratio
                    if not using_stats_collector:
                        logger.info(f"Ratio WAIT réinitialisé à la valeur optimale: {optimal_wait_ratio:.2f}")

                # 3. Réinitialiser les métriques de confiance consécutive si nécessaire
                if hasattr(self, 'consecutive_confidence_calculator') and self.consecutive_confidence_calculator:
                    try:
                        if hasattr(self.consecutive_confidence_calculator, 'reset_metrics'):
                            self.consecutive_confidence_calculator.reset_metrics()
                            if not using_stats_collector:
                                logger.info("Métriques du calculateur de confiance consécutive réinitialisées")
                        else:
                            # Si la méthode reset_metrics n'existe pas, réinitialiser manuellement les attributs importants
                            if hasattr(self.consecutive_confidence_calculator, 'recent_data'):
                                self.consecutive_confidence_calculator.recent_data = []
                            if not using_stats_collector:
                                logger.info("Données récentes du calculateur de confiance consécutive réinitialisées")
                    except Exception as e_reset:
                        logger.error(f"Erreur lors de la réinitialisation des métriques de confiance consécutive: {e_reset}")

                # 4. Réinitialiser d'autres métriques ou états si nécessaire
                # (Ajouter ici d'autres réinitialisations spécifiques si besoin)

            # L'optimiseur de placement des WAIT a été supprimé
            # La décision WAIT/NON-WAIT est maintenant basée uniquement sur le score de décision
            # et la probabilité de validité de la recommandation

            # 6. Calculer le score de décision final basé sur toutes les features
            # Ce score détermine si une recommandation WAIT ou NON-WAIT doit être faite

            # Facteurs de pondération pour les différentes composantes
            confidence_weight = getattr(self.config, 'confidence_weight', 0.4)
            uncertainty_weight = getattr(self.config, 'uncertainty_weight', 0.2)
            consecutive_weight = self.config.consecutive_weight

            # Calculer la différence entre les probabilités (clarté de la prédiction)
            player_prob = predictions['player']
            banker_prob = predictions['banker']
            prob_diff = abs(player_prob - banker_prob)

            # Calculer le score de décision en intégrant la différence de probabilité
            # Plus la différence est grande, plus le score de décision est élevé
            # Cela favorise les recommandations NON-WAIT quand une issue est clairement plus probable
            prob_diff_weight = getattr(self.config, 'prob_diff_weight', 0.3)  # Poids pour la différence de probabilité

            decision_score = (
                confidence_weight * confidence +                           # Confiance dans la prédiction
                uncertainty_weight * (1.0 - predictions['uncertainty']) +  # Certitude de la prédiction
                consecutive_weight * non_wait_recommendation_strength +    # Force de recommandation basée sur l'historique
                prob_diff_weight * prob_diff * 2.0                         # Clarté de la prédiction (x2 pour normaliser)
            )

            # DIAGNOSTIC: Ajouter des logs détaillés pour comprendre les composantes du score de décision
            # Ne pas afficher ces messages pendant l'optimisation
            is_optuna_phase = getattr(self, 'is_optuna_running', False)
            has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
            if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                logger.critical(f"DIAGNOSTIC - Score de décision: {decision_score:.6f}")
                logger.critical(f"DIAGNOSTIC - Composantes du score: confidence={confidence_weight * confidence:.6f}, uncertainty={uncertainty_weight * (1.0 - predictions['uncertainty']):.6f}, consecutive={consecutive_weight * non_wait_recommendation_strength:.6f}, prob_diff={prob_diff_weight * prob_diff * 2.0:.6f}")

                # DIAGNOSTIC: Analyser les valeurs brutes pour identifier d'éventuels problèmes
                logger.critical(f"DIAGNOSTIC - Valeurs brutes: confidence={confidence:.6f}, uncertainty={predictions['uncertainty']:.6f}, non_wait_strength={non_wait_recommendation_strength:.6f}, prob_diff={prob_diff:.6f}")

            # DIAGNOSTIC: Vérifier si les valeurs sont dans des plages normales
            # Ne pas afficher ces messages pendant l'optimisation
            is_optuna_phase = getattr(self, 'is_optuna_running', False)
            has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
            if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                if confidence < 0.55:
                    logger.critical(f"DIAGNOSTIC - ALERTE: Confiance très basse ({confidence:.6f}), ce qui favorise les recommandations WAIT")
                if predictions['uncertainty'] > 0.4:
                    logger.critical(f"DIAGNOSTIC - ALERTE: Incertitude élevée ({predictions['uncertainty']:.6f}), ce qui favorise les recommandations WAIT")
                if non_wait_recommendation_strength < 0.3:
                    logger.critical(f"DIAGNOSTIC - ALERTE: Force de recommandation NON-WAIT très basse ({non_wait_recommendation_strength:.6f})")
                if prob_diff < 0.1:
                    logger.critical(f"DIAGNOSTIC - ALERTE: Différence de probabilité très faible ({prob_diff:.6f}), ce qui indique une prédiction peu claire")

            # Ajuster le score en fonction de la position dans la plage cible
            if is_target_round:
                # Position relative dans la plage cible (0 au début, 1 à la fin)
                relative_pos = decision_features['relative_position']

                # Bonus en forme de cloche (maximum au milieu de la plage)
                bell_curve_bonus_factor = getattr(self.config, 'bell_curve_bonus_factor', 0.2)
                bell_curve_bonus = 1.0 + bell_curve_bonus_factor * (1.0 - 4.0 * (relative_pos - 0.5) ** 2)

                # Appliquer le bonus
                decision_score *= bell_curve_bonus

                # Ne pas afficher ce message pendant l'optimisation
                is_optuna_phase = getattr(self, 'is_optuna_running', False)
                has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                    logger.debug(f"Bonus en cloche pour manche {current_round} (position relative: {relative_pos:.2f}): {bell_curve_bonus:.4f}")

            # 6. Déterminer le seuil de décision adaptatif
            # Ce seuil est ajusté dynamiquement en fonction de plusieurs facteurs

            # Seuil de base - Réduire légèrement pour favoriser les recommandations NON-WAIT
            base_reduction_factor = getattr(self.config, 'base_reduction_factor', 0.85)  # Utiliser la valeur de config.py

            # Ajuster le facteur de réduction en fonction de la phase d'optimisation
            if optimization_phase == 2:  # Phase 2 (Objectif 1): Recommandations NON-WAIT valides consécutives
                # Réduire davantage le seuil de base pour la Phase 2
                phase2_reduction_factor = getattr(self.config, 'phase2_reduction_factor', 0.85)  # Réduction de 15% au lieu de 10%
                base_reduction_factor = phase2_reduction_factor
                # Ne pas afficher ce message pendant l'optimisation
                is_optuna_phase = getattr(self, 'is_optuna_running', False)
                has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                    logger.debug(f"Facteur de réduction du seuil de base augmenté pour Phase 2: {base_reduction_factor:.2f}")

            # Seuil de confiance de base - Réduire pour favoriser les recommandations NON-WAIT
            base_confidence_threshold = getattr(self.config, 'base_confidence_threshold', 0.30)  # Utiliser la valeur de config.py

            decision_threshold = base_confidence_threshold * base_reduction_factor

            # Ajuster le seuil en fonction de l'incertitude - Réduire l'impact de l'incertitude
            uncertainty_factor = getattr(self.config, 'confidence_threshold_adjustment', 0.02)  # Utiliser la valeur de config.py

            # Réduire davantage l'impact de l'incertitude pour la Phase 2
            if optimization_phase == 2:
                phase2_uncertainty_reduction = getattr(self.config, 'phase2_uncertainty_reduction', 0.7)  # Réduire de 30% l'impact de l'incertitude
                uncertainty_factor *= phase2_uncertainty_reduction
                # Ne pas afficher ce message pendant l'optimisation
                is_optuna_phase = getattr(self, 'is_optuna_running', False)
                has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                    logger.debug(f"Impact de l'incertitude réduit pour Phase 2: {uncertainty_factor:.4f}")

            uncertainty_adjustment = predictions['uncertainty'] * uncertainty_factor
            decision_threshold += uncertainty_adjustment

            # Ajuster le seuil en fonction du ratio WAIT/NON-WAIT actuel
            wait_ratio_adjustment = 0.0
            if 'current_wait_ratio' in decision_features:
                current_wait_ratio = decision_features['current_wait_ratio']
                optimal_wait_ratio = getattr(self.config, 'optimal_wait_ratio', self.config.default_wait_ratio)
                wait_ratio_tolerance = getattr(self.config, 'wait_ratio_tolerance', 0.1)

                # Calculer l'écart par rapport au ratio optimal
                # S'assurer que optimal_wait_ratio n'est pas None
                if optimal_wait_ratio is None:
                    optimal_wait_ratio = self.config.default_wait_ratio  # Valeur par défaut
                ratio_deviation = current_wait_ratio - optimal_wait_ratio

                # Facteur d'ajustement pour le ratio WAIT
                wait_ratio_factor_high = getattr(self.config, 'wait_ratio_factor_high', 0.15)  # Facteur pour trop de WAIT
                wait_ratio_factor_low = getattr(self.config, 'wait_ratio_factor_low', 0.05)   # Facteur pour trop de NON-WAIT

                # Ajuster les facteurs en fonction de la phase d'optimisation
                if optimization_phase == 2:  # Phase 2 (Objectif 1)
                    # Augmenter l'impact du ratio WAIT pour la Phase 2
                    phase2_wait_ratio_high_factor = getattr(self.config, 'phase2_wait_ratio_high_factor', 1.5)  # Augmenter de 50%
                    phase2_wait_ratio_low_factor = getattr(self.config, 'phase2_wait_ratio_low_factor', 0.7)   # Réduire de 30%
                    wait_ratio_factor_high *= phase2_wait_ratio_high_factor
                    wait_ratio_factor_low *= phase2_wait_ratio_low_factor
                    # Ne pas afficher ce message pendant l'optimisation
                    is_optuna_phase = getattr(self, 'is_optuna_running', False)
                    has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                    if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                        logger.debug(f"Facteurs d'ajustement du ratio WAIT modifiés pour Phase 2: high={wait_ratio_factor_high:.4f}, low={wait_ratio_factor_low:.4f}")

                # Ajuster le seuil en fonction de l'écart - Renforcer l'ajustement pour corriger le déséquilibre
                if ratio_deviation > wait_ratio_tolerance:  # Trop de WAIT
                    # Réduire fortement le seuil pour favoriser les NON-WAIT
                    wait_ratio_adjustment = -ratio_deviation * wait_ratio_factor_high
                    decision_threshold += wait_ratio_adjustment
                    # Ne pas afficher ce message pendant l'optimisation
                    is_optuna_phase = getattr(self, 'is_optuna_running', False)
                    has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                    if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                        logger.debug(f"Ajustement ratio WAIT (trop de WAIT): {wait_ratio_adjustment:.4f} (ratio actuel: {current_wait_ratio:.2f}, optimal: {optimal_wait_ratio:.2f})")
                elif ratio_deviation < -wait_ratio_tolerance:  # Trop de NON-WAIT
                    # Augmenter légèrement le seuil pour favoriser les WAIT
                    wait_ratio_adjustment = -ratio_deviation * wait_ratio_factor_low
                    decision_threshold += wait_ratio_adjustment
                    # Ne pas afficher ce message pendant l'optimisation
                    is_optuna_phase = getattr(self, 'is_optuna_running', False)
                    has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                    if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                        logger.debug(f"Ajustement ratio WAIT (trop de NON-WAIT): {wait_ratio_adjustment:.4f} (ratio actuel: {current_wait_ratio:.2f}, optimal: {optimal_wait_ratio:.2f})")

            # Ajuster le seuil en fonction de la longueur de la séquence consécutive actuelle
            consecutive_adjustment = 0.0
            if decision_features['consecutive_valid_count'] > 0:
                # Plus la séquence consécutive est longue, plus le seuil est élevé (plus conservateur)
                # pour éviter de briser une longue séquence
                consecutive_length = decision_features['consecutive_valid_count']
                consecutive_threshold = getattr(self.config, 'consecutive_threshold', 5)

                # Ajuster le seuil de longueur consécutive en fonction de la phase d'optimisation
                if optimization_phase == 2:  # Phase 2 (Objectif 1)
                    # Augmenter le seuil pour la Phase 2 pour être moins conservateur
                    consecutive_threshold = max(3, consecutive_threshold - 1)
                    # Ne pas afficher ce message pendant l'optimisation
                    is_optuna_phase = getattr(self, 'is_optuna_running', False)
                    has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                    if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                        logger.debug(f"Seuil de longueur consécutive réduit pour Phase 2: {consecutive_threshold}")

                if consecutive_length >= consecutive_threshold:
                    # Facteur d'ajustement pour la séquence consécutive
                    consecutive_factor = getattr(self.config, 'consecutive_adjustment_factor', 0.01)

                    # Ajuster le facteur en fonction de la phase d'optimisation
                    if optimization_phase == 2:  # Phase 2 (Objectif 1)
                        # Réduire l'impact de la longueur consécutive pour la Phase 2
                        phase2_consecutive_factor = getattr(self.config, 'phase2_consecutive_factor', 0.7)  # Réduire de 30%
                        consecutive_factor *= phase2_consecutive_factor
                        # Ne pas afficher ce message pendant l'optimisation
                        is_optuna_phase = getattr(self, 'is_optuna_running', False)
                        has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                        if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                            logger.debug(f"Impact de la longueur consécutive réduit pour Phase 2: {consecutive_factor:.4f}")

                    # Augmenter progressivement le seuil avec la longueur de la séquence
                    consecutive_adjustment = min(0.1, consecutive_factor * (consecutive_length - consecutive_threshold + 1))
                    decision_threshold += consecutive_adjustment

                    # Ne pas afficher ce message pendant l'optimisation
                    is_optuna_phase = getattr(self, 'is_optuna_running', False)
                    has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                    if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                        logger.debug(f"Ajustement séquence consécutive: {consecutive_adjustment:.4f} (longueur: {consecutive_length})")

            # Limiter le seuil dans une plage raisonnable
            min_threshold = getattr(self.config, 'decision_threshold_min', 0.28)
            max_threshold = getattr(self.config, 'decision_threshold_max', 0.45)

            # Ajuster les limites en fonction de la phase d'optimisation
            if optimization_phase == 2:  # Phase 2 (Objectif 1)
                # Abaisser davantage les limites pour la Phase 2
                phase2_min_threshold = getattr(self.config, 'phase2_decision_threshold_min', 0.25)  # Utiliser la valeur de config.py
                phase2_max_threshold = getattr(self.config, 'phase2_decision_threshold_max', 0.40)  # Utiliser la valeur de config.py
                min_threshold = phase2_min_threshold
                max_threshold = phase2_max_threshold
                # Ne pas afficher ce message pendant l'optimisation
                is_optuna_phase = getattr(self, 'is_optuna_running', False)
                has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                    logger.debug(f"Limites de seuil ajustées pour Phase 2: min={min_threshold:.2f}, max={max_threshold:.2f}")

            # Vérification du seuil minimum pour s'assurer qu'il est approprié
            diagnostic_min_threshold = getattr(self.config, 'diagnostic_min_threshold', 0.25)

            # Enregistrer le seuil actuel pour analyse
            original_min_threshold = min_threshold

            # Analyser si le seuil est trop élevé par rapport au score de décision typique
            # Utiliser le niveau INFO au lieu de CRITICAL
            if min_threshold > 0.45:
                logger.warning(f"Seuil minimum élevé ({min_threshold:.4f} > 0.45), peut limiter les recommandations NON-WAIT")

            decision_threshold = np.clip(decision_threshold, min_threshold, max_threshold)

            # Ne pas afficher ce message pendant l'optimisation
            is_optuna_phase = getattr(self, 'is_optuna_running', False)
            has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
            if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                logger.debug(f"Seuil de décision: {decision_threshold:.4f} = Base({base_confidence_threshold:.4f}*{base_reduction_factor:.2f}) + " +
                           f"Incertitude({uncertainty_adjustment:.4f}) + RatioWAIT({wait_ratio_adjustment:.4f}) + " +
                           f"Consécutif({consecutive_adjustment:.4f})")

            # 7. Prendre la décision finale
            force_non_wait = False
            force_wait = False

            # Vérifier si nous sommes en phase 1 d'optimisation
            is_phase1 = False
            if optimization_phase == 1:
                is_phase1 = True
                # Ne pas afficher ce message pendant l'optimisation
                is_optuna_phase = getattr(self, 'is_optuna_running', False)
                has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                    logger.info(f"Phase 1 d'optimisation détectée: optimization_phase={optimization_phase}")
            elif hasattr(self.config, 'optimization_phase') and getattr(self.config, 'optimization_phase') == 1:
                is_phase1 = True
                # Ne pas afficher ce message pendant l'optimisation
                is_optuna_phase = getattr(self, 'is_optuna_running', False)
                has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                    logger.info(f"Phase 1 d'optimisation détectée via config: optimization_phase={getattr(self.config, 'optimization_phase')}")

            if is_phase1:  # Phase 1 (Objectif 2)
                # Ne plus forcer directement les recommandations WAIT/NON-WAIT
                # Laisser le système décider naturellement en fonction de la confiance et de l'incertitude
                # Ne pas afficher ce message pendant l'optimisation
                is_optuna_phase = getattr(self, 'is_optuna_running', False)
                has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                    logger.info(f"Phase 1: Recommandation naturelle basée sur la confiance et l'incertitude (manche {current_round})")

            # Logique normale pour la Phase 2 (Objectif 1) et l'utilisation normale
            # Ne plus forcer de recommandations basées sur le ratio WAIT
            if 'current_wait_ratio' in decision_features:
                current_wait_ratio = decision_features['current_wait_ratio']
                # Ne pas afficher ce message pendant l'optimisation
                is_optuna_phase = getattr(self, 'is_optuna_running', False)
                has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                    logger.debug(f"Ratio WAIT actuel: {current_wait_ratio:.2f} (aucun forçage appliqué)")

            # Logs simplifiés pour le débogage - uniquement si nécessaire
            if decision_score > decision_threshold - 0.05 and decision_score < decision_threshold + 0.05:
                # Ne logger que les cas proches du seuil de décision (zone critique)
                # Ne pas afficher ce message pendant l'optimisation
                is_optuna_phase = getattr(self, 'is_optuna_running', False)
                has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                    logger.info(f"Décision critique - Manche {current_round}: score={decision_score:.4f}, seuil={decision_threshold:.4f}, " +
                              f"player={predictions['player']:.4f}, banker={predictions['banker']:.4f}")

            # Ne plus forcer de recommandations NON-WAIT après un certain nombre de WAIT consécutifs
            # Laisser le système décider naturellement en fonction de la confiance et de l'incertitude
            if not hasattr(self, 'consecutive_wait_count'):
                self.consecutive_wait_count = 0
            # Ne pas afficher ce message pendant l'optimisation
            is_optuna_phase = getattr(self, 'is_optuna_running', False)
            has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
            if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                logger.debug(f"Nombre actuel de WAIT consécutifs: {self.consecutive_wait_count} (aucun forçage appliqué)")

            # Prendre la décision finale selon la logique suivante:
            # 1. Une recommandation WAIT doit être faite pour optimiser une recommandation NON-WAIT
            # 2. Une recommandation NON-WAIT doit être faite si la probabilité d'une issue est clairement supérieure
            # 3. Même si une recommandation NON-WAIT est possible, si elle a peu de probabilité d'être valide,
            #    une recommandation WAIT doit être faite à la place

            # Vérifier quelle probabilité est la plus élevée
            player_prob = predictions['player']
            banker_prob = predictions['banker']

            # Calculer la différence entre les probabilités (mesure de la clarté de la prédiction)
            prob_diff = abs(player_prob - banker_prob)

            # Calculer la probabilité que la recommandation soit valide
            # Plus la différence est grande, plus la probabilité est élevée
            # Plus la confiance est élevée, plus la probabilité est élevée
            # Moins l'incertitude est élevée, plus la probabilité est élevée
            validity_confidence_weight = getattr(self.config, 'validity_confidence_weight', 0.5)
            validity_prob_diff_weight = getattr(self.config, 'validity_prob_diff_weight', 0.3)
            validity_certainty_weight = getattr(self.config, 'validity_certainty_weight', 0.2)

            validity_probability = (
                confidence * validity_confidence_weight +                          # Confiance dans la prédiction
                prob_diff * validity_prob_diff_weight +                           # Clarté de la prédiction
                (1.0 - predictions['uncertainty']) * validity_certainty_weight    # Certitude de la prédiction
            )

            # Log pour les composantes de la validité
            # Ne pas afficher ce message pendant l'optimisation
            is_optuna_phase = getattr(self, 'is_optuna_running', False)
            has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
            if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                logger.debug(f"Validité: prob_diff={prob_diff:.6f}, validity_probability={validity_probability:.6f}")

            # Déterminer si une recommandation NON-WAIT a une bonne probabilité d'être valide
            # Le seuil de validité est toujours égal au seuil de décision
            validity_threshold = decision_threshold

            # Log pour les probabilités
            # Ne pas afficher ce message pendant l'optimisation
            is_optuna_phase = getattr(self, 'is_optuna_running', False)
            has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
            if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                logger.debug(f"Probabilités: player={player_prob:.6f}, banker={banker_prob:.6f}, diff={prob_diff:.6f}")

            if force_wait:
                # Forcer une recommandation WAIT (cas spécial)
                predictions['recommendation'] = 'wait'
                # Ne pas afficher ce message pendant l'optimisation
                is_optuna_phase = getattr(self, 'is_optuna_running', False)
                has_stats_collector = hasattr(self, 'optimization_stats_collector') and self.optimization_stats_collector is not None
                if not is_optuna_phase and not has_stats_collector and getattr(self, 'debug_verbose', False):
                    logger.debug(f"Recommandation WAIT forcée (score={decision_score:.4f})")
                # Incrémenter le compteur de WAIT consécutifs
                self.consecutive_wait_count = getattr(self, 'consecutive_wait_count', 0) + 1
            elif (decision_score >= decision_threshold or force_non_wait) and validity_probability >= validity_threshold:
                # Score de décision suffisant ET probabilité de validité suffisante pour faire une recommandation NON-WAIT

                # Déterminer la recommandation en fonction de la probabilité la plus élevée
                predictions['recommendation'] = 'player' if player_prob > banker_prob else 'banker'

                if not using_stats_collector:
                    logger.debug(f"Recommandation NON-WAIT: {predictions['recommendation']} (score={decision_score:.4f} >= " +
                               f"seuil={decision_threshold:.4f}, validité={validity_probability:.4f} >= {validity_threshold:.4f}, " +
                               f"player={player_prob:.6f}, banker={banker_prob:.6f})")
                # Réinitialiser le compteur de WAIT consécutifs
                self.consecutive_wait_count = 0
            else:
                # Soit le score de décision est insuffisant, soit la probabilité de validité est insuffisante
                # Dans les deux cas, recommander WAIT
                predictions['recommendation'] = 'wait'

                # Déterminer la raison du WAIT pour le log
                if decision_score < decision_threshold:
                    reason = f"score={decision_score:.4f} < seuil={decision_threshold:.4f}"
                else:
                    reason = f"validité={validity_probability:.4f} < seuil={validity_threshold:.4f}"

                if not using_stats_collector:
                    logger.debug(f"Recommandation WAIT ({reason})")
                # Incrémenter le compteur de WAIT consécutifs
                self.consecutive_wait_count = getattr(self, 'consecutive_wait_count', 0) + 1

            # Log pour suivre le compteur de WAIT consécutifs
            if not using_stats_collector:
                logger.debug(f"Compteur WAIT consécutifs: {self.consecutive_wait_count}")

            # Stocker la phase d'optimisation dans les prédictions pour référence
            if optimization_phase is not None:
                predictions['optimization_phase'] = optimization_phase

            # 8. Stocker les métriques pour référence
            predictions['decision_score'] = decision_score
            predictions['decision_threshold'] = decision_threshold
            predictions['decision_features'] = decision_features
            predictions['uncertainty_value'] = predictions['uncertainty']
            predictions['bayesian_weights'] = bayesian_weights

            # Collecter les statistiques si nous sommes en mode optimisation avec collecteur de statistiques
            if using_stats_collector:
                # Déterminer si c'est une recommandation WAIT
                is_wait = predictions['recommendation'] == 'wait'

                # Enregistrer les statistiques de cette prédiction
                self.optimization_stats_collector.record_prediction(
                    phase=optimization_phase or 0,
                    is_wait=is_wait,
                    decision_threshold=decision_threshold,
                    confidence=confidence,
                    uncertainty=predictions['uncertainty'],
                    wait_ratio=decision_features.get('current_wait_ratio', 0.0),
                    consecutive_wait=self.consecutive_wait_count,
                    player_prob=predictions['player'],
                    banker_prob=predictions['banker'],
                    recommendation_score=decision_score
                )

                # Enregistrer les alertes de diagnostic
                if predictions['uncertainty'] > getattr(self.config, 'transition_uncertainty_threshold', 0.65):
                    self.optimization_stats_collector.record_diagnostic_alert("ALERTE: Incertitude élevée")

                if non_wait_recommendation_strength < 0.1:
                    self.optimization_stats_collector.record_diagnostic_alert("ALERTE: Force de recommandation NON-WAIT très basse")

            # Mesurer le temps total d'exécution
            total_time = time.time() - start_time
            if not using_stats_collector:
                logger.debug(f"Temps total de prédiction hybride: {total_time*1000:.1f}ms")

            return predictions