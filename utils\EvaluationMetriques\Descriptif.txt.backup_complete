DESCRIPTIF DÉTAILLÉ DES MÉTHODES - ÉVALUATION ET MÉTRIQUES
================================================================================

Ce fichier contient la description détaillée des méthodes d'évaluation,
de calcul de métriques et d'analyse de performance.

DOMAINE FONCTIONNEL : Évaluation performance, métriques, analyse résultats

TOTAL : 4 MÉTHODES ANALYSÉES

================================================================================

1. evaluate_kpis.txt (evaluate_kpis - ÉVALUATION INDICATEURS PERFORMANCE AVANCÉS)
   - Lignes 2029-2337 dans utils.py (309 lignes)
   - FONCTION : Évalue KPIs avancés avec métriques sophistiquées pour analyse performance complète système
   - PARAMÈTRES :
     * y_true (array) - Valeurs réelles (0 pour banker, 1 pour player)
     * y_pred (array) - Valeurs prédites (0 pour banker, 1 pour player)
     * probas (array) - Probabilités prédites pour banker
     * recommendations (List[str]) - Recommandations ('banker', 'player', 'WAIT')
     * target_rounds (tuple, optionnel) - Plage manches à considérer
     * window_size (int, optionnel) - Taille fenêtre métriques stabilité
     * stability_threshold (float, optionnel) - Seuil variation instable
     * context_analysis (bool, défaut=True) - Active analyse contextuelle
     * config (optionnel) - Configuration prédicteur pour paramètres
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION DIMENSIONS :** Vérifie cohérence longueurs tous arrays d'entrée
     * **FILTRAGE MANCHES :** Applique target_rounds si spécifié pour analyse ciblée
     * **MÉTRIQUES DISTRIBUTION :** Accuracy sur fenêtres glissantes avec mean/std/min/max/range
     * **PERFORMANCE CONTEXTUELLE :** Analyse alternance, répétition, séries banker/player
     * **STABILITÉ TEMPORELLE :** Variance probabilités, taux changement prédictions, zones stables
     * **SÉQUENCES NON-WAIT :** Identifie séquences recommandations NON-WAIT valides consécutives
     * **MÉTRIQUES WAIT :** Ratio, précision décisions, opportunités manquées, efficacité
     * **RÉCUPÉRATION WAIT :** Taux succès après recommandations WAIT
     * **SCORES COMPOSITES :** sequence_efficiency_score et objective1_stability_score
     * **EXCELLENCE :** Haute confiance, séquences correctes consécutives maximales
   - RETOUR : Dict[str, Dict] - Dictionnaire structuré avec performance_distribution, context_performance, stability_metrics, excellence_metrics
   - UTILITÉ : Analyse exhaustive multi-dimensionnelle performance avec focus objectif 1 et optimisations avancées

2. get_recent_performance_metrics.txt (get_recent_performance_metrics - MÉTRIQUES PERFORMANCE RÉCENTES)
   - Lignes 2498-2539 dans utils.py (42 lignes)
   - FONCTION : Calcule métriques performance sur données récentes pour suivi tendances
   - PARAMÈTRES :
     * self - Instance classe avec recent_data
     * window_size (int, défaut=50) - Taille fenêtre analyse récente
   - FONCTIONNEMENT DÉTAILLÉ :
     * **EXTRACTION RÉCENTE :** Sélectionne window_size dernières entrées
     * **CALCUL MÉTRIQUES :** Accuracy, precision, recall sur données récentes
     * **TENDANCES :** Analyse évolution performance dans le temps
     * **COMPARAISON :** Compare performance récente vs historique
   - RETOUR : Dict[str, float] - Métriques performance récentes
   - UTILITÉ : Suivi temps réel performance pour détection dégradations

3. get_stats.txt (get_stats - STATISTIQUES SYSTÈME)
   - Lignes 2541-2582 dans utils.py (42 lignes)
   - FONCTION : Génère statistiques complètes système avec analyse détaillée
   - PARAMÈTRES :
     * self - Instance classe avec données historiques
     * include_patterns (bool, défaut=True) - Inclure statistiques patterns
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STATS GLOBALES :** Nombre total prédictions, accuracy globale
     * **STATS PATTERNS :** Analyse patterns les plus/moins performants
     * **DISTRIBUTION :** Répartition résultats par catégorie
     * **MÉTRIQUES AVANCÉES :** Entropie, diversité, stabilité
   - RETOUR : Dict[str, Any] - Statistiques système complètes
   - UTILITÉ : Vue d'ensemble performance système pour analyse approfondie

4. get_stats_1.txt (get_stats - STATISTIQUES SYSTÈME - DOUBLON 1)
   - Lignes 2541-2582 dans utils.py (42 lignes)
   - FONCTION : Génère statistiques complètes système avec analyse détaillée - Version identique
   - PARAMÈTRES :
     * self - Instance classe avec données historiques
     * include_patterns (bool, défaut=True) - Inclure statistiques patterns
   - FONCTIONNEMENT DÉTAILLÉ :
     * **STATS GLOBALES :** Nombre total prédictions, accuracy globale
     * **STATS PATTERNS :** Analyse patterns les plus/moins performants
     * **DISTRIBUTION :** Répartition résultats par catégorie
     * **MÉTRIQUES AVANCÉES :** Entropie, diversité, stabilité
   - RETOUR : Dict[str, Any] - Statistiques système complètes
   - UTILITÉ : Vue d'ensemble performance système pour analyse approfondie
