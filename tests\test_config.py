#!/usr/bin/env python3
"""
Tests complets pour la configuration du système
"""

import sys
import os
import unittest
import tempfile
import shutil
from pathlib import Path

# Ajouter le dossier du programme au path
PROG_DIR = r"C:\Users\<USER>\Desktop\travail\plateforme\notes_travail\prog"
sys.path.insert(0, PROG_DIR)

try:
    from config import PredictorConfig
except ImportError as e:
    print(f"ERREUR: Impossible d'importer config: {e}")
    sys.exit(1)


class TestConfiguration(unittest.TestCase):
    """Tests de la configuration du système"""

    def setUp(self):
        """Initialisation avant chaque test"""
        self.config = PredictorConfig()

    def test_config_creation(self):
        """Test création configuration de base"""
        self.assertIsNotNone(self.config)
        self.assertIsInstance(self.config, PredictorConfig)

    def test_config_attributes_exist(self):
        """Test existence des attributs critiques"""
        critical_attrs = [
            'lstm_sequence_length',
            'lstm_input_size',
            'lgbm_cache_max_size',
            'weight_markov',
            'weight_lgbm',
            'weight_lstm',
            'viability_min_accuracy',
            'viability_min_wait_ratio',
            'viability_max_wait_ratio'
        ]

        for attr in critical_attrs:
            with self.subTest(attr=attr):
                self.assertTrue(hasattr(self.config, attr),
                              f"Attribut manquant: {attr}")

    def test_config_values_validity(self):
        """Test validité des valeurs de configuration"""
        # Test valeurs numériques positives
        positive_attrs = [
            'lstm_sequence_length',
            'lstm_input_size',
            'lgbm_cache_max_size'
        ]

        for attr in positive_attrs:
            with self.subTest(attr=attr):
                value = getattr(self.config, attr)
                self.assertGreater(value, 0,
                                 f"{attr} doit être positif, got {value}")

    def test_weights_sum_to_one(self):
        """Test que les poids des modèles somment à 1"""
        total_weight = (self.config.weight_markov +
                       self.config.weight_lgbm +
                       self.config.weight_lstm)

        self.assertAlmostEqual(total_weight, 1.0, places=6,
                              msg=f"Poids totaux = {total_weight}, attendu 1.0")

    def test_viability_thresholds_coherent(self):
        """Test cohérence des seuils de viabilité"""
        self.assertLessEqual(self.config.viability_min_wait_ratio,
                            self.config.viability_max_wait_ratio,
                            "Min wait ratio > Max wait ratio")

        self.assertGreaterEqual(self.config.viability_min_accuracy, 0.0)
        self.assertLessEqual(self.config.viability_min_accuracy, 1.0)

    def test_config_serialization(self):
        """Test sérialisation/désérialisation configuration"""
        # Test que la config peut être convertie en dict
        try:
            config_dict = vars(self.config)
            self.assertIsInstance(config_dict, dict)
            self.assertGreater(len(config_dict), 0)
        except Exception as e:
            self.fail(f"Échec sérialisation config: {e}")


class TestConfigurationDefaults(unittest.TestCase):
    """Tests des valeurs par défaut de configuration"""

    def setUp(self):
        self.config = PredictorConfig()

    def test_lstm_defaults(self):
        """Test valeurs par défaut LSTM"""
        self.assertGreaterEqual(self.config.lstm_sequence_length, 5)
        self.assertLessEqual(self.config.lstm_sequence_length, 50)

        self.assertGreaterEqual(self.config.lstm_input_size, 5)
        self.assertLessEqual(self.config.lstm_input_size, 100)

    def test_cache_defaults(self):
        """Test valeurs par défaut cache"""
        self.assertGreaterEqual(self.config.lgbm_cache_max_size, 100)
        self.assertLessEqual(self.config.lgbm_cache_max_size, 10000)

    def test_weights_defaults(self):
        """Test valeurs par défaut des poids"""
        weights = [
            self.config.weight_markov,
            self.config.weight_lgbm,
            self.config.weight_lstm
        ]

        for weight in weights:
            self.assertGreaterEqual(weight, 0.0)
            self.assertLessEqual(weight, 1.0)


class TestConfigurationEdgeCases(unittest.TestCase):
    """Tests des cas limites de configuration"""

    def setUp(self):
        self.config = PredictorConfig()

    def test_extreme_values_handling(self):
        """Test gestion valeurs extrêmes"""
        # Test modification valeurs extrêmes
        original_lstm_length = self.config.lstm_sequence_length

        # Test valeur très grande
        self.config.lstm_sequence_length = 1000000
        self.assertEqual(self.config.lstm_sequence_length, 1000000)

        # Test valeur zéro
        self.config.lstm_sequence_length = 0
        self.assertEqual(self.config.lstm_sequence_length, 0)

        # Restaurer valeur originale
        self.config.lstm_sequence_length = original_lstm_length

    def test_negative_values_handling(self):
        """Test gestion valeurs négatives"""
        original_cache_size = self.config.lgbm_cache_max_size

        # Test valeur négative
        self.config.lgbm_cache_max_size = -100
        self.assertEqual(self.config.lgbm_cache_max_size, -100)

        # Restaurer valeur originale
        self.config.lgbm_cache_max_size = original_cache_size


if __name__ == '__main__':
    print("=== TESTS CONFIGURATION ===")
    print(f"Répertoire programme: {PROG_DIR}")
    print(f"Python path: {sys.path[0]}")

    # Créer suite de tests
    suite = unittest.TestSuite()

    # Ajouter tests de configuration
    suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestConfiguration))
    suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestConfigurationDefaults))
    suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestConfigurationEdgeCases))

    # Exécuter tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Afficher résultats
    print(f"\n=== RÉSULTATS ===")
    print(f"Tests exécutés: {result.testsRun}")
    print(f"Échecs: {len(result.failures)}")
    print(f"Erreurs: {len(result.errors)}")

    if result.failures:
        print("\nÉCHECS:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")

    if result.errors:
        print("\nERREURS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")

    # Code de sortie
    sys.exit(0 if result.wasSuccessful() else 1)
