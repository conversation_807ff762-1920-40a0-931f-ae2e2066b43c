# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 1165 à 1384
# Type: Méthode de la classe HybridBaccaratPredictor

    def show_optimization_results(self):
        logger_instance = getattr(self, 'logger', logging.getLogger(__name__)) # Utiliser le logger de l'instance
        ui_available = self.is_ui_available()

        # Vérifier si une instance d'optimizer avec un 'study' existe.
        # Utiliser self.optimizer qui est prévu par le code (même si OptunaOptimizer écrase parfois le nom)
        # Ou vérifier self.current_optimizer_instance qui est utilisé pendant l'exécution async
        optimizer_to_use = getattr(self, 'current_optimizer_instance', None)
        if optimizer_to_use is None:
             optimizer_to_use = getattr(self, 'optimizer', None) # Fallback

        if optimizer_to_use is None or not hasattr(optimizer_to_use, 'study') or optimizer_to_use.study is None:
            logger_instance.warning("show_optimization_results: Aucune étude Optuna trouvée (current_optimizer_instance ou self.optimizer).")
            if ui_available:
                messagebox.showwarning("Information", "Aucune optimisation Optuna disponible à afficher.", parent=self.root if ui_available else None)
            return

        # Vérifier si nous avons accès à l'étude de la phase 2 (objectif 1)
        study_phase2 = getattr(optimizer_to_use, 'study_phase2', None)
        if study_phase2 is not None and hasattr(study_phase2, 'trials') and study_phase2.trials:
            # Utiliser l'étude de la phase 2 (objectif 1) si disponible
            study_obj = study_phase2
            study_name = "phase2_objectif1"
            logger_instance.info("Utilisation de l'étude de la phase 2 (objectif 1: recommandations NON-WAIT valides consécutives)")
        else:
            # Sinon, utiliser l'étude principale
            study_obj = optimizer_to_use.study
            study_name = "principale"
            logger_instance.info("Utilisation de l'étude principale (pas d'étude spécifique à l'objectif 1 trouvée)")

        # Générer un rapport d'optimisation détaillé
        if study_obj.best_trial:
            report = self.generate_optimization_report(study_obj, study_obj.best_trial)
            report_path = self.save_optimization_report(report, study_name)
            logger_instance.info(f"Rapport d'optimisation généré et sauvegardé dans {report_path}")

            # Afficher le rapport dans une fenêtre
            if ui_available:
                self.show_text_report(report, f"Rapport d'optimisation - Étude {study_name}")

        logger_instance.info("Affichage des résultats Optuna via Plotly (focus sur l'objectif 1)...")
        try:
            from plotly.offline import plot
            import plotly.graph_objects as go
            from plotly.subplots import make_subplots
            import tempfile
            import webbrowser
            import optuna.visualization as vis # Utiliser alias pour clarté
            import pandas as pd
            import numpy as np

            result_window = tk.Toplevel(self.root)
            result_window.title("Résultats d'optimisation Optuna - Objectif 1")
            result_window.geometry("1000x800")
            result_window.transient(self.root)

            container = ttk.Frame(result_window)
            container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            try:
                if not study_obj.trials:
                     raise ValueError("L'étude Optuna ne contient aucun essai terminé.")

                # Créer un DataFrame avec les métriques importantes pour l'objectif 1
                trials_data = []
                for trial in study_obj.trials:
                    if trial.state == optuna.trial.TrialState.COMPLETE:
                        trial_data = {
                            'Numéro': trial.number,
                            'Score': trial.value,
                            'Max Consécutives': trial.user_attrs.get('max_consecutive', 0),
                            'Précision NON-WAIT': trial.user_attrs.get('precision_non_wait', 0.0),
                            'Ratio WAIT': trial.user_attrs.get('wait_ratio', 0.0),
                            'Efficacité WAIT': trial.user_attrs.get('wait_efficiency', 0.0),
                            'Récupération après WAIT': trial.user_attrs.get('recovery_rate_after_wait', 0.0)
                        }
                        trials_data.append(trial_data)

                # Créer un DataFrame pour faciliter la visualisation
                df = pd.DataFrame(trials_data)

                # Trier par score décroissant
                df = df.sort_values('Score', ascending=False)

                # Créer une figure avec 3 sous-graphiques
                combined_fig = make_subplots(
                    rows=3, cols=1,
                    subplot_titles=(
                        "Historique d'Optimisation - Objectif 1 (Recommandations NON-WAIT valides consécutives)",
                        "Importance des Hyperparamètres pour l'Objectif 1",
                        "Métriques Clés par Essai pour l'Objectif 1"
                    ),
                    vertical_spacing=0.1,
                    row_heights=[0.3, 0.3, 0.4]
                )

                # 1. Historique d'optimisation
                fig1 = vis.plot_optimization_history(study_obj)
                for trace in fig1.data:
                    combined_fig.add_trace(trace, row=1, col=1)

                # 2. Importance des paramètres
                fig2 = vis.plot_param_importances(study_obj)
                for trace in fig2.data:
                    combined_fig.add_trace(trace, row=2, col=1)

                # 3. Graphique des métriques clés pour l'objectif 1
                # Créer un graphique en barres pour les métriques clés du meilleur essai
                if not df.empty:
                    best_trial = df.iloc[0]
                    metrics = ['Max Consécutives', 'Précision NON-WAIT', 'Ratio WAIT', 'Efficacité WAIT', 'Récupération après WAIT']
                    values = [best_trial['Max Consécutives'], best_trial['Précision NON-WAIT'],
                              best_trial['Ratio WAIT'], best_trial['Efficacité WAIT'],
                              best_trial['Récupération après WAIT']]

                    # Normaliser les valeurs pour l'affichage
                    normalized_values = []
                    for i, metric in enumerate(metrics):
                        if metric == 'Max Consécutives':
                            # Normaliser entre 0 et 1 en supposant que 20 est une bonne valeur maximale
                            normalized_values.append(min(1.0, values[i] / 20.0))
                        else:
                            # Les autres métriques sont déjà entre 0 et 1
                            normalized_values.append(values[i])

                    # Ajouter le graphique en barres
                    combined_fig.add_trace(
                        go.Bar(
                            x=metrics,
                            y=normalized_values,
                            text=[f"{values[i]:.2f}" if i > 0 else f"{int(values[i])}" for i in range(len(values))],
                            textposition='auto',
                            name='Meilleur Essai',
                            marker_color='rgb(26, 118, 255)'
                        ),
                        row=3, col=1
                    )

                    # Ajouter une ligne horizontale à 0.5 pour référence
                    combined_fig.add_shape(
                        type="line",
                        x0=-0.5,
                        y0=0.5,
                        x1=4.5,
                        y1=0.5,
                        line=dict(color="red", width=1, dash="dash"),
                        row=3, col=1
                    )

                    # Ajouter une annotation pour le score du meilleur essai
                    combined_fig.add_annotation(
                        x=2,
                        y=0.9,
                        text=f"Score du meilleur essai: {best_trial['Score']:.4f}",
                        showarrow=False,
                        font=dict(size=14, color="black"),
                        bgcolor="rgba(255, 255, 255, 0.8)",
                        bordercolor="black",
                        borderwidth=1,
                        row=3, col=1
                    )

                # Mise en page globale
                combined_fig.update_layout(
                    height=1000,
                    title_text="Résultats d'optimisation Optuna - Focus sur l'Objectif 1",
                    showlegend=False
                )

                # Ajuster les axes y pour le graphique des métriques
                combined_fig.update_yaxes(title_text="Valeur normalisée", range=[0, 1], row=3, col=1)

                with tempfile.NamedTemporaryFile(suffix=".html", delete=False, mode='w', encoding='utf-8') as temp_file:
                    html_content = combined_fig.to_html(full_html=True, include_plotlyjs='cdn')
                    temp_file.write(html_content)
                    temp_file_path = temp_file.name

                logger_instance.info(f"Graphiques Plotly sauvegardés dans {temp_file_path}, ouverture navigateur...")
                webbrowser.open(f"file://{os.path.realpath(temp_file_path)}")

            except ImportError as ie_plot:
                 logger_instance.error(f"Erreur Import pour visualisation Optuna: {ie_plot}", exc_info=True)
                 messagebox.showerror("Erreur Dépendance",
                     f"Impossible d'afficher les graphiques.\nBibliothèque manquante: {ie_plot.name}\n"
                     "Installez-la avec: pip install plotly",
                     parent=result_window)
                 result_window.destroy()
                 return
            except ValueError as ve_plot: # Ex: pas d'essais
                  logger_instance.error(f"Erreur Données pour visualisation Optuna: {ve_plot}", exc_info=True)
                  messagebox.showerror("Erreur Données",
                      f"Impossible d'afficher les graphiques:\n{ve_plot}",
                      parent=result_window)
                  result_window.destroy()
                  return
            except Exception as e_plot:
                logger_instance.error(f"Erreur création/affichage graphiques Plotly: {e_plot}", exc_info=True)
                messagebox.showerror("Erreur Graphique",
                    f"Impossible d'afficher les graphiques.\nErreur: {str(e_plot)[:200]}...",
                    parent=result_window)
                result_window.destroy()
                return

            # Ajouter un bouton pour générer un rapport d'optimisation détaillé
            button_frame = ttk.Frame(result_window)
            button_frame.pack(fill=tk.X, pady=10, padx=10)

            ttk.Button(button_frame, text="Générer Rapport Détaillé",
                      command=lambda: self.show_text_report(
                          self.generate_optimization_report(study_obj, study_obj.best_trial),
                          "Rapport d'optimisation détaillé"
                      )).pack(side=tk.LEFT, padx=5)

            ttk.Button(button_frame, text="Fermer",
                      command=result_window.destroy).pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            logger_instance.error(f"Erreur globale affichage résultats Optuna: {e}", exc_info=True)
            if ui_available:
                 messagebox.showerror("Erreur", f"Impossible d'afficher les résultats:\n{str(e)[:200]}", parent=self.root)