# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 446 à 555
# Type: Méthode de la classe ConsecutiveConfidenceCalculator
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, config=None):
        # Dictionnaire pour stocker les patterns et leurs statistiques de succès
        self.pattern_stats = defaultdict(lambda: {"total": 0, "success": 0, "consecutive_lengths": [], "max_consecutive": 0})

        # Historique des recommandations et résultats récents pour le calcul du ratio WAIT/NON-WAIT
        self.recent_recommendations = []
        self.recent_outcomes = []

        # Récupérer les paramètres depuis la configuration
        # Utiliser les valeurs par défaut uniquement si config est None
        if config is None:
            # Valeurs par défaut si aucune configuration n'est fournie
            self.max_recent_history = 100
            self.min_occurrences = 5
            self.max_pattern_length = 10
            self.target_round_min = 31
            self.target_round_max = 60
            self.late_game_factor = 1.2
            self.occurrence_factor_divisor = 100.0
            self.consecutive_factor_divisor = 10.0
            self.max_occurrence_factor = 1.5
            self.max_consecutive_factor = 1.5
            self.pattern_similarity_threshold = 0.7
            self.max_similar_patterns = 10
            self.optimal_wait_ratio = 0.4
            self.wait_ratio_tolerance = 0.1
            self.sequence_bonus_threshold = 5
            self.sequence_bonus_factor = 0.15
            self.success_rate_weight = 0.4
            self.consecutive_length_weight = 0.4
            self.pattern_frequency_weight = 0.2
            self.wait_success_rate_threshold = 0.6
            self.wait_success_rate_factor = 2.0
            self.wait_consecutive_threshold = 2.0
            self.wait_consecutive_factor = 0.2
            self.wait_occurrences_threshold = 10
            self.wait_occurrences_factor = 0.02
            self.wait_recommendation_threshold = 0.7
            self.success_rate_wait_threshold = 0.55
            self.confidence_wait_threshold = 0.6
            self.avg_consecutive_wait_threshold = 1.5
            self.wait_ratio_min_threshold = 0.3
            self.wait_ratio_max_threshold = 0.5
            self.consecutive_focus_factor = 1.5
            self.consecutive_confidence_adjustment = 0.05
            self.consecutive_error_penalty = 0.1
            self.consecutive_recovery_rate = 0.05
            self.max_consecutive_target = 10
            self.position_range_lower = 0.3
            self.position_range_upper = 0.7
            self.mid_range_confidence_threshold = 0.75
        else:
            # Récupérer tous les paramètres depuis la configuration
            self.max_recent_history = getattr(config, 'max_recent_history', 100)
            self.min_occurrences = getattr(config, 'min_occurrences', 5)
            self.max_pattern_length = getattr(config, 'max_pattern_length', 10)
            self.target_round_min = getattr(config, 'target_round_min', 31)
            self.target_round_max = getattr(config, 'target_round_max', 60)
            self.late_game_factor = getattr(config, 'late_game_factor', 1.2)
            self.occurrence_factor_divisor = getattr(config, 'occurrence_factor_divisor', 100.0)
            self.consecutive_factor_divisor = getattr(config, 'consecutive_factor_divisor', 10.0)
            self.max_occurrence_factor = getattr(config, 'max_occurrence_factor', 1.5)
            self.max_consecutive_factor = getattr(config, 'max_consecutive_factor', 1.5)
            self.pattern_similarity_threshold = getattr(config, 'pattern_similarity_threshold', 0.7)
            self.max_similar_patterns = getattr(config, 'max_similar_patterns', 10)
            self.optimal_wait_ratio = getattr(config, 'optimal_wait_ratio', 0.4)
            self.wait_ratio_tolerance = getattr(config, 'wait_ratio_tolerance', 0.1)
            self.sequence_bonus_threshold = getattr(config, 'sequence_bonus_threshold', 5)
            self.sequence_bonus_factor = getattr(config, 'sequence_bonus_factor', 0.15)
            self.success_rate_weight = getattr(config, 'success_rate_weight', 0.4)
            self.consecutive_length_weight = getattr(config, 'consecutive_length_weight', 0.4)
            self.pattern_frequency_weight = getattr(config, 'pattern_frequency_weight', 0.2)
            self.wait_success_rate_threshold = getattr(config, 'wait_success_rate_threshold', 0.6)
            self.wait_success_rate_factor = getattr(config, 'wait_success_rate_factor', 2.0)
            self.wait_consecutive_threshold = getattr(config, 'wait_consecutive_threshold', 2.0)
            self.wait_consecutive_factor = getattr(config, 'wait_consecutive_factor', 0.2)
            self.wait_occurrences_threshold = getattr(config, 'wait_occurrences_threshold', 10)
            self.wait_occurrences_factor = getattr(config, 'wait_occurrences_factor', 0.02)
            self.wait_recommendation_threshold = getattr(config, 'wait_recommendation_threshold', 0.7)
            self.success_rate_wait_threshold = getattr(config, 'success_rate_wait_threshold', 0.55)
            self.confidence_wait_threshold = getattr(config, 'confidence_wait_threshold', 0.6)
            self.avg_consecutive_wait_threshold = getattr(config, 'avg_consecutive_wait_threshold', 1.5)
            self.wait_ratio_min_threshold = getattr(config, 'wait_ratio_min_threshold', 0.3)
            self.wait_ratio_max_threshold = getattr(config, 'wait_ratio_max_threshold', 0.5)
            self.consecutive_focus_factor = getattr(config, 'consecutive_focus_factor', 1.5)
            self.consecutive_confidence_adjustment = getattr(config, 'consecutive_confidence_adjustment', 0.05)
            self.consecutive_error_penalty = getattr(config, 'consecutive_error_penalty', 0.1)
            self.consecutive_recovery_rate = getattr(config, 'consecutive_recovery_rate', 0.05)
            self.max_consecutive_target = getattr(config, 'max_consecutive_target', 10)
            self.position_range_lower = getattr(config, 'position_range_lower', 0.3)
            self.position_range_upper = getattr(config, 'position_range_upper', 0.7)
            self.mid_range_confidence_threshold = getattr(config, 'mid_range_confidence_threshold', 0.75)

        # Compteurs pour le suivi des performances
        self.total_recommendations = 0
        self.wait_recommendations = 0
        self.non_wait_recommendations = 0
        self.correct_recommendations = 0
        self.current_consecutive_valid = 0
        self.max_consecutive_valid = 0
        self.current_consecutive_errors = 0
        self.last_recommendation_was_valid = False
        self.last_recommendation_was_wait = False
        self.confidence_adjustment = 0.0  # Ajustement dynamique du seuil de confiance

        # Dictionnaire pour stocker les performances par manche
        self.round_performance = {}

        # Initialiser le logger
        self.logger = logging.getLogger(__name__)