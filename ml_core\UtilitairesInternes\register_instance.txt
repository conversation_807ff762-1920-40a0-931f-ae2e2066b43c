# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 310 à 319
# Type: Méthode de la classe ModuleInterface

    def register_instance(self, name: str, instance: Any) -> None:
        """
        Enregistre une instance dans l'interface.

        Args:
            name: Nom unique de l'instance
            instance: Instance à enregistrer
        """
        self._instances[name] = instance
        logger.debug(f"Instance '{name}' enregistrée dans l'interface")