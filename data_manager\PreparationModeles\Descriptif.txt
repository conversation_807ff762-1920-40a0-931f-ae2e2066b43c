DESCRIPTIF DÉTAILLÉ DES MÉTHODES - PRÉPARATION MODÈLES
================================================================================

Ce fichier contient la description détaillée des méthodes de préparation des modèles du système data_manager.py.

ÉTAT DOCUMENTATION : PREMIÈRE VAGUE FONCTIONNELLE
- **Couverture** : Méthodes de préparation des modèles documentées
- **Niveau** : Documentation fonctionnelle standardisée
- **Qualité** : 15-25 lignes par méthode minimum

DOMAINE FONCTIONNEL : PRÉPARATION MODÈLES
- Interface avec les modèles LGBM et LSTM
- Agrégation et conversion des données
- Validation des formats et shapes
- Préparation pour l'entraînement

================================================================================

1. prepare_data_for_model.txt (BaccaratSequenceManager.prepare_data_for_model - PRÉPARATION DONNÉES MODÈLES)
   - Lignes 192-291 dans data_manager.py (100 lignes)
   - FONCTION : Prépare données X_lgbm, y, X_lstm, préfixes et origines agrégées depuis liste séquences P/B avec filtrage min_target_hand_index
   - PARAMÈTRES :
     * self - Instance de BaccaratSequenceManager
     * list_of_pb_sequences (List[List[str]]) - Liste séquences P/B (chaque séquence est List[str])
   - FONCTIONNEMENT DÉTAILLÉ :
     * **INITIALISATION GLOBALES** : `all_X_lgbm, all_y, all_X_lstm, all_prefixes, all_origins = [], [], [], [], []`
     * **OFFSET GLOBAL** : `current_global_offset = 0` pour tracking indices globaux
     * **CALCUL NOMBRE** : `num_games = len(list_of_pb_sequences)`
     * **LOGGING DÉBUT** : `self.logger.info(f"Manager: Préparation données filtrées depuis {num_games} séquences P/B...")`
     * **BOUCLE SÉQUENCES** : `for game_idx, shoe_seq in enumerate(list_of_pb_sequences):`
     * **VALIDATION TYPE** : `if not isinstance(shoe_seq, list): self.logger.warning(f"Manager: Élément {game_idx} n'est pas une liste, ignoré."); continue`
     * **GÉNÉRATION SABOT** : `try: X_lgbm_shoe, y_shoe, X_lstm_shoe, prefix_shoe, origin_shoe = self._generate_filtered_data_for_shoe(shoe_seq, current_global_offset)`
     * **AGRÉGATION RÉSULTATS** : `all_X_lgbm.extend(X_lgbm_shoe)`, `all_y.extend(y_shoe)`, `all_X_lstm.extend(X_lstm_shoe)`, `all_prefixes.extend(prefix_shoe)`, `all_origins.extend(origin_shoe)`
     * **GESTION ERREURS** : `except Exception as e_gen: self.logger.error(f"Manager: Erreur génération données sabot {game_idx}: {e_gen}", exc_info=True)`
     * **MISE À JOUR OFFSET** : `current_global_offset += len(shoe_seq)` commentaire "L'offset est le début du *prochain* sabot"
     * **VÉRIFICATION VIDE** : `if not all_y: self.logger.warning("Manager: Aucune donnée valide générée après filtrage et génération."); return None, None, None, None, None`
     * **CONVERSION NUMPY** : `try: X_lgbm_np = np.array(all_X_lgbm, dtype=np.float64)` commentaire "Utiliser float64 pour LGBM"
     * **CONVERSION Y** : `y_np = np.array(all_y, dtype=np.int64)`
     * **CONVERSION LSTM** : `X_lstm_np = np.stack(all_X_lstm, axis=0).astype(np.float32)` commentaire "LSTM float32 OK"
     * **ASSIGNATION LISTES** : `list_of_prefixes_final = all_prefixes`, `list_of_origins_final = all_origins`
     * **VALIDATION ÉTIQUETTES** : `unique_labels = np.unique(y_np); if not np.all(np.isin(unique_labels, [0, 1])): raise ValueError(...)`
     * **CALCUL ÉCHANTILLONS** : `num_samples_final = len(y_np)`
     * **LOGGING FINAL** : Messages info détaillés sur shapes X_lgbm, y, X_lstm et nombres préfixes/origines
     * **RETOUR SUCCÈS** : `return X_lgbm_np, y_np, X_lstm_np, list_of_prefixes_final, list_of_origins_final`
     * **GESTION EXCEPTIONS** : `except ValueError as e_val:` et `except Exception as e_gen:` avec logging et return None
   - RETOUR : Tuple[Optional[np.ndarray], Optional[np.ndarray], Optional[np.ndarray], Optional[List[List[str]]], Optional[List[int]]] - (X_lgbm, y, X_lstm, prefixes, origins) ou (None, None, None, None, None)
   - UTILITÉ : Méthode principale pour agrégation données multi-sabots. Essentielle pour interface modèles ML. Critique pour validation finale et conversion NumPy.