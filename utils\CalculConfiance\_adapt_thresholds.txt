# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 3766 à 3795
# Type: Méthode de la classe WaitPlacementOptimizer

    def _adapt_thresholds(self):
        """
        Adapte les seuils en fonction des performances récentes.
        """
        # Calculer les taux de succès
        wait_success_rate = self.correct_wait_decisions / self.total_waits if self.total_waits > 0 else 0
        non_wait_success_rate = self.correct_non_wait_decisions / (self.total_decisions - self.total_waits) if (self.total_decisions - self.total_waits) > 0 else 0

        # Calculer le ratio WAIT optimal
        if wait_success_rate > non_wait_success_rate:
            # Si les WAIT sont plus efficaces, augmenter légèrement le seuil
            optimal_ratio = min(self.wait_ratio_max, self.current_wait_ratio + self.learning_rate)
        else:
            # Si les NON-WAIT sont plus efficaces, diminuer légèrement le seuil
            optimal_ratio = max(self.wait_ratio_min, self.current_wait_ratio - self.learning_rate)

        # Ajuster le seuil d'erreur
        if self.current_wait_ratio < optimal_ratio:
            # Nous voulons plus de WAIT, réduire le seuil
            self.error_pattern_threshold = max(0.3, self.error_pattern_threshold - self.learning_rate)
        elif self.current_wait_ratio > optimal_ratio:
            # Nous voulons moins de WAIT, augmenter le seuil
            self.error_pattern_threshold = min(0.9, self.error_pattern_threshold + self.learning_rate)

        # Ajuster le seuil de transition
        self.transition_uncertainty_threshold = self.error_pattern_threshold

        self.logger.debug(f"Seuils adaptés: error_pattern_threshold={self.error_pattern_threshold:.4f}, "
                         f"transition_uncertainty_threshold={self.transition_uncertainty_threshold:.4f}, "
                         f"optimal_ratio={optimal_ratio:.4f}")