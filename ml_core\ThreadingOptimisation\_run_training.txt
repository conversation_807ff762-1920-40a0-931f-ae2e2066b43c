# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1432 à 1531
# Type: Méthode de la classe ThreadedTrainer

    def _run_training(self, X_lgbm, y_lgbm, X_lstm, y_lstm, config_override=None):
        """
        Exécute l'entraînement dans le thread.
        Cette méthode est appelée par le thread et ne doit pas être appelée directement.

        Args:
            X_lgbm: Features pour LGBM
            y_lgbm: Labels pour LGBM
            X_lstm: Features pour LSTM
            y_lstm: Labels pour LSTM
            config_override: Configuration à utiliser pour l'entraînement (optionnel)
        """
        try:
            # Configurer le flag d'arrêt dans l'instance d'entraînement
            if hasattr(self.trainer_instance, 'stop_training'):
                # Créer une propriété dynamique qui vérifie l'état du stop_event
                self.trainer_instance.stop_training = False

                # Sauvegarder la méthode originale
                original_get_stop_training = None
                if hasattr(type(self.trainer_instance), 'stop_training') and isinstance(type(self.trainer_instance).stop_training, property):
                    original_get_stop_training = type(self.trainer_instance).stop_training.fget

                # Créer une fonction de vérification qui combine le stop_event et le flag stop_training
                def check_stop_training(self_instance):
                    return self_instance.stop_training or self.stop_event.is_set()

                # Remplacer temporairement la propriété stop_training
                if original_get_stop_training:
                    type(self.trainer_instance).stop_training = property(check_stop_training)

            # Configurer le callback de progression
            # Au lieu de remplacer la méthode _update_progress, nous allons utiliser
            # un mécanisme d'observation pour éviter la récursion infinie
            self._original_update_progress = None

            if self.progress_callback and hasattr(self.trainer_instance, '_update_progress'):
                # Sauvegarder la référence à la méthode originale sans la remplacer
                self._original_update_progress = self.trainer_instance._update_progress

                # Créer un observateur qui sera appelé par _train_models_async
                def observe_progress(progress, message):
                    # Appeler notre callback avec les mêmes paramètres
                    # sans créer de boucle de récursion
                    if self.progress_callback:
                        self.progress_callback(progress, message)

                # Ajouter l'observateur comme attribut de l'instance d'entraînement
                # pour qu'il puisse être appelé directement par _train_models_async
                self.trainer_instance._progress_observer = observe_progress

            # Exécuter l'entraînement
            logger.info("Début de l'entraînement principal...")
            self.trainer_instance._train_models_async(
                X_lgbm, y_lgbm, X_lstm, y_lstm,
                config_override=config_override if config_override else {}
            )

            # Vérifier si l'entraînement a été interrompu
            if self.stop_event.is_set():
                logger.warning("Entraînement interrompu par l'utilisateur")
                self.result = {
                    'success': False,
                    'message': "Entraînement interrompu par l'utilisateur",
                    'duration': time.time() - self.start_time
                }
            else:
                logger.info("Entraînement terminé avec succès")
                self.result = {
                    'success': True,
                    'message': "Entraînement terminé avec succès",
                    'duration': time.time() - self.start_time
                }

            # Appeler le callback si l'entraînement s'est terminé
            if self.callback:
                self.callback(self.result)

        except Exception as e:
            logger.error(f"Erreur lors de l'entraînement: {e}", exc_info=True)
            self.error = e
            self.result = {
                'success': False,
                'message': str(e),
                'duration': time.time() - self.start_time
            }

            # Appeler le callback d'erreur si une erreur s'est produite
            if self.error_callback:
                self.error_callback(e)
        finally:
            # Restaurer les méthodes originales
            if hasattr(self.trainer_instance, 'stop_training') and original_get_stop_training:
                type(self.trainer_instance).stop_training = property(original_get_stop_training)

            # Supprimer l'observateur de progression
            if hasattr(self.trainer_instance, '_progress_observer'):
                delattr(self.trainer_instance, '_progress_observer')

            self.is_running = False