# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 1083 à 1132
# Type: Méthode de la classe ConsecutiveConfidenceCalculator

    def register_training_data(self,
                              features_list: List[List[float]],
                              actual_outcomes: List[str],
                              predictions: List[str],
                              confidences: List[float],
                              non_wait_mask: List[bool]) -> None:
        """
        Enregistre les données d'entraînement pour analyse ultérieure.

        Args:
            features_list: Liste des vecteurs de features pour chaque position
            actual_outcomes: Résultats réels ('banker' ou 'player')
            predictions: Prédictions du modèle ('banker' ou 'player')
            confidences: Scores de confiance pour chaque prédiction
            non_wait_mask: Masque indiquant les positions où une recommandation NON-WAIT a été faite
        """
        if len(features_list) != len(actual_outcomes) or len(actual_outcomes) != len(predictions) or \
           len(predictions) != len(confidences) or len(confidences) != len(non_wait_mask):
            logger.error("Dimensions incohérentes dans register_training_data")
            return

        # Parcourir les données et extraire les patterns
        for i in range(len(features_list)):
            # Ne considérer que les positions avec recommandation NON-WAIT
            if non_wait_mask[i]:
                # Extraire le pattern (vecteur de features)
                pattern = tuple(features_list[i])

                # Vérifier si la prédiction était correcte
                is_correct = predictions[i] == actual_outcomes[i]

                # Mettre à jour les statistiques
                self.pattern_stats[pattern]["total"] += 1
                if is_correct:
                    self.pattern_stats[pattern]["success"] += 1

                # Calculer les séquences consécutives
                if i > 0 and non_wait_mask[i-1] and predictions[i-1] == actual_outcomes[i-1]:
                    # Continuer une séquence existante
                    current_length = 1
                    j = i - 1
                    while j >= 0 and (non_wait_mask[j] and predictions[j] == actual_outcomes[j]):
                        current_length += 1
                        j -= 1

                    # Enregistrer la longueur de la séquence
                    if is_correct:
                        self.pattern_stats[pattern]["consecutive_lengths"].append(current_length)

        logger.info(f"Données d'entraînement enregistrées: {len(features_list)} positions, {sum(non_wait_mask)} recommandations NON-WAIT")