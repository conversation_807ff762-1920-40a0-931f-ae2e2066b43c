"""
Module de gestion des données pour le prédicteur de Baccarat.
Contient la classe BaccaratSequenceManager pour la préparation et l'évaluation des données.
"""

import logging
import os
from typing import List, Dict, Tuple, Optional, Any, Callable

import numpy as np
from sklearn.metrics import accuracy_score, log_loss

logger = logging.getLogger(__name__)

class BaccaratSequenceManager:
    """
    Gère la création de séquences de données de Baccarat filtrées et
    l'évaluation de modèles sur ces données.
    MODIFIÉE pour intégrer la génération de features hybrides, le filtrage,
    et l'utilisation d'une fenêtre adaptative au lieu d'une fenêtre glissante.

    Utilise une fenêtre adaptative pour les modèles LGBM et LSTM :
    - Pour calculer les probabilités de la manche N, utilise les N-1 manches précédentes.
    - Par exemple, pour la manche 21, utilise les 20 manches précédentes.
    - Pour la manche 34, utilise les 33 manches précédentes.

    Filtre les données pour ne garder que les cibles (y) dont l'index
    est >= min_target_hand_index.
    """
    def __init__(self,
                 sequence_length: int, # Utilisé pour la validation interne et log
                 min_target_hand_index: int,
                 # Fonction pour créer les features [LGBM], [LSTM]
                 hybrid_feature_creator: Callable[[List[str]], Tuple[Optional[List[float]], Optional[np.ndarray]]],
                 # Infos pour validation des shapes retournées par la fonction
                 lgbm_feature_count: int,
                 lstm_seq_len: int,
                 lstm_feature_count: int,
                 parent_logger = None # Optionnel: passer un logger
                ):
        """
        Initialise le gestionnaire de séquences modifié.
        Utilise une fenêtre adaptative pour les modèles LGBM et LSTM.

        Args:
            sequence_length (int): Taille maximale de la matrice de sortie pour les features LSTM.
                                  Avec la fenêtre adaptative, toute la séquence disponible est utilisée,
                                  mais la matrice de sortie a une taille fixe.
            min_target_hand_index (int): Index (0-based) minimum de la main cible (y) à inclure.
            hybrid_feature_creator: Fonction (ex: predictor.create_hybrid_features) qui prend une
                                    séquence P/B et retourne (list[float]|None, np.ndarray|None).
            lgbm_feature_count (int): Nombre attendu de features LGBM.
            lstm_seq_len (int): Longueur attendue de la séquence pour les features LSTM.
            lstm_feature_count (int): Nombre attendu de features par pas de temps LSTM.
            parent_logger: Logger externe optionnel.
        """
        if not isinstance(sequence_length, int) or sequence_length <= 0:
            raise ValueError("sequence_length doit être un entier positif.")
        if not isinstance(min_target_hand_index, int) or min_target_hand_index < 0:
             raise ValueError("min_target_hand_index doit être un entier positif ou nul.")
        if not callable(hybrid_feature_creator):
             raise TypeError("hybrid_feature_creator doit être une fonction callable.")
        if not isinstance(lgbm_feature_count, int) or lgbm_feature_count <= 0:
             raise ValueError("lgbm_feature_count doit être un entier positif.")
        if not isinstance(lstm_seq_len, int) or lstm_seq_len <= 0:
             raise ValueError("lstm_seq_len doit être un entier positif.")
        if not isinstance(lstm_feature_count, int) or lstm_feature_count <= 0:
             raise ValueError("lstm_feature_count doit être un entier positif.")

        self.sequence_length = sequence_length # = lstm_seq_len pour create_hybrid_features
        self.min_target_hand_index = min_target_hand_index
        self.hybrid_feature_creator = hybrid_feature_creator
        self.lgbm_feature_count = lgbm_feature_count
        self.lstm_seq_len = lstm_seq_len
        # Garder le LSTM Feature Count dans l'instance
        self.lstm_feature_count = lstm_feature_count
        #Mettre à jour la shape
        self.lstm_expected_shape = (lstm_seq_len, lstm_feature_count)

        self.logger = parent_logger if parent_logger else logging.getLogger(__name__)
        self.logger.info(f"BaccaratSequenceManager (Modifié) initialisé:")
        self.logger.info(f"  - Longueur séquence requise: {self.sequence_length}")
        self.logger.info(f"  - Index minimum cible (0-based): {self.min_target_hand_index}")
        self.logger.info(f"  - Générateur Features: {self.hybrid_feature_creator.__name__}")
        self.logger.info(f"  - Validation: LGBM Feat Count={self.lgbm_feature_count}, LSTM Shape={self.lstm_expected_shape}")

    def _generate_filtered_data_for_shoe(self,
                                        shoe_pb_sequence: List[str],
                                        game_index_offset: int # Pour calculer l'index global
                                        ) -> Tuple[List, List, List, List, List]:
        """
        (Méthode interne) Génère les features (LGBM, LSTM), labels (y), séquences préfixes,
        et indices d'origine pour UN SEUL sabot, en appliquant le filtre min_target_hand_index.
        MODIFIÉ: Garantit que toutes les manches 31-60 sont utilisées pour l'entraînement.
        Utilise une fenêtre adaptative pour les modèles LGBM et LSTM.

        Args:
            shoe_pb_sequence (List[str]): Séquence P/B ('player'/'banker') du sabot.
            game_index_offset (int): Index global du premier coup potentiel de ce sabot.

        Returns:
            tuple: Listes contenant [X_lgbm], [y], [X_lstm], [prefix_seq], [origin_idx] pour ce sabot.
        """
        X_lgbm_list, y_list, X_lstm_list, prefix_list, origin_list = [], [], [], [], []
        game_len = len(shoe_pb_sequence)

        # MODIFICATION: Vérifier si ce sabot a 60 manches (sans log pour éviter de surcharger les logs)
        if game_len != 60:
            self.logger.warning(f"Sabot de longueur {game_len} != 60 détecté (offset={game_index_offset})")

        # Nous avons besoin d'au moins 2 éléments pour générer des features
        # (1 pour l'historique et 1 pour la prédiction)
        if game_len < 2:
            self.logger.warning(f"Sabot trop court ({game_len} < 2), ignoré.")
            return X_lgbm_list, y_list, X_lstm_list, prefix_list, origin_list

        # MODIFICATION: Définir les limites des manches cibles (31-60)
        target_round_min = 31
        target_round_max = 60

        # Itérer sur les indices des coups CIBLES possibles dans ce sabot
        # On commence à l'index 1 (pour avoir au moins 1 élément d'historique)
        for i in range(1, game_len):
            # 'i' est l'index (0-based) de la main à prédire (la CIBLE y)
            # La position 1-indexée est i+1

            # --- >>> FILTRE PRINCIPAL <<< ---
            # MODIFICATION: Toujours traiter les manches 31-60 (positions 1-indexées)
            position_1_indexed = i + 1
            is_target_round = (position_1_indexed >= target_round_min and position_1_indexed <= target_round_max)

            if i >= self.min_target_hand_index or is_target_round:  # MODIFICATION: Ajouter la condition is_target_round
                # Séquence d'entrée pour le générateur de features (jusqu'à i-1)
                # Utilise toute la séquence disponible jusqu'à i-1 (fenêtre adaptative)
                input_sequence = shoe_pb_sequence[:i]
                actual_outcome = shoe_pb_sequence[i] # La cible à l'index i

                # Appeler la fonction de génération de features fournie
                features_lgbm, features_lstm_np = self.hybrid_feature_creator(input_sequence)

                # --- Validation des features générées ---
                # Utiliser self.lgbm_feature_count pour la validation
                valid_lgbm = (features_lgbm is not None and len(features_lgbm) == self.lgbm_feature_count)

                # Utiliser maintenant la nouvelle propriété : self.lstm_feature_count
                valid_lstm = (features_lstm_np is not None and features_lstm_np.shape == self.lstm_expected_shape)

                if valid_lgbm and valid_lstm:
                    # IMPORTANT: Utiliser UNIQUEMENT le système zero-based standard de PyTorch:
                    # - 0 = Player
                    # - 1 = Banker
                    label = 1 if actual_outcome == 'banker' else 0
                    global_origin_index = game_index_offset + i # Index global approximatif

                    # Nous ne loggons plus les conversions d'étiquettes ni les manches cibles individuellement
                    # pour éviter de surcharger les logs

                    X_lgbm_list.append(features_lgbm)
                    y_list.append(label)
                    X_lstm_list.append(features_lstm_np)
                    prefix_list.append(input_sequence[:]) # Copie du préfixe
                    origin_list.append(global_origin_index)
                else:
                    # Log l'échec si la génération ou la validation échoue
                    reason = []
                    if features_lgbm is None: reason.append("LGBM feats None")
                    elif not valid_lgbm: reason.append(f"LGBM len {len(features_lgbm)}!={self.lgbm_feature_count}")
                    if features_lstm_np is None: reason.append("LSTM feats None")
                    elif not valid_lstm: reason.append(f"LSTM shape {features_lstm_np.shape}!={self.lstm_expected_shape}")

                    # Conserver uniquement le log d'erreur pour les manches cibles (important pour le débogage)
                    if is_target_round:
                        self.logger.warning(f"Manche cible {position_1_indexed} (31-60) ignorée: {', '.join(reason)}")
                    else:
                        self.logger.debug(f"  Sabot Idx~{game_index_offset}, Coup Idx {i}: Ignoré ({', '.join(reason)})")

            # else: Coup filtré car i < min_target_hand_index et pas dans la plage 31-60

        # Vérifier si toutes les manches 31-60 ont été traitées (log concis)
        if game_len >= target_round_max:
            # Calculer combien de manches cibles ont été traitées
            target_indices = [i for i in range(len(origin_list)) if (origin_list[i] - game_index_offset + 1) >= target_round_min and (origin_list[i] - game_index_offset + 1) <= target_round_max]
            target_count = len(target_indices)
            expected_target_count = target_round_max - target_round_min + 1

            # Log uniquement si des manches sont manquantes (pour réduire la verbosité)
            if target_count < expected_target_count:
                self.logger.warning(f"ATTENTION: Seulement {target_count}/{expected_target_count} manches cibles (31-60) traitées pour ce sabot")

        return X_lgbm_list, y_list, X_lstm_list, prefix_list, origin_list

    def prepare_data_for_model(self,
                                list_of_pb_sequences: List[List[str]]
                                ) -> Tuple[Optional[np.ndarray], Optional[np.ndarray], Optional[np.ndarray],
                                           Optional[List[List[str]]], Optional[List[int]]]:
        """
        Prépare les données X_lgbm, y, X_lstm, préfixes et origines agrégées
        à partir d'une liste de séquences P/B, en appliquant le filtrage
        par 'min_target_hand_index' via la génération de features.

        Args:
            list_of_pb_sequences (list): Liste de séquences P/B (chaque séquence est une List[str]).

        Returns:
            tuple: Un tuple contenant 5 éléments:
                   - X_lgbm (np.ndarray): Shape (nb_samples, nb_features_lgbm) ou None.
                   - y (np.ndarray): Shape (nb_samples,) ou None.
                   - X_lstm (np.ndarray): Shape (nb_samples, lstm_seq_len, lstm_features) ou None.
                   - list_of_prefixes (List[List[str]]): Liste des séquences préfixes correspondantes ou None.
                   - list_of_origins (List[int]): Liste des indices d'origine globaux correspondants ou None.
                   Retourne (None, None, None, None, None) si aucune donnée valide n'est générée.
        """
        all_X_lgbm, all_y, all_X_lstm, all_prefixes, all_origins = [], [], [], [], []
        current_global_offset = 0
        num_games = len(list_of_pb_sequences)

        self.logger.info(f"Manager: Préparation données filtrées depuis {num_games} séquences P/B...")

        for game_idx, shoe_seq in enumerate(list_of_pb_sequences):
            if not isinstance(shoe_seq, list):
                self.logger.warning(f"Manager: Élément {game_idx} n'est pas une liste, ignoré.")
                continue

            # Générer les données filtrées pour ce sabot
            try:
                X_lgbm_shoe, y_shoe, X_lstm_shoe, prefix_shoe, origin_shoe = self._generate_filtered_data_for_shoe(
                    shoe_seq,
                    current_global_offset
                )
                # Ajouter les résultats aux listes globales
                all_X_lgbm.extend(X_lgbm_shoe)
                all_y.extend(y_shoe)
                all_X_lstm.extend(X_lstm_shoe)
                all_prefixes.extend(prefix_shoe)
                all_origins.extend(origin_shoe)

            except Exception as e_gen:
                self.logger.error(f"Manager: Erreur génération données sabot {game_idx}: {e_gen}", exc_info=True)
                # Continuer avec le sabot suivant

            # Mettre à jour l'offset global pour le prochain sabot
            # L'offset est le début du *prochain* sabot, donc nombre de coups total du sabot actuel
            current_global_offset += len(shoe_seq)

        # --- Conversion finale en tableaux NumPy ---
        if not all_y: # Si aucune donnée n'a été générée/conservée après filtrage
            self.logger.warning("Manager: Aucune donnée valide générée après filtrage et génération.")
            return None, None, None, None, None

        try:
            X_lgbm_np = np.array(all_X_lgbm, dtype=np.float64) # Utiliser float64 pour LGBM
            y_np = np.array(all_y, dtype=np.int64)
            X_lstm_np = np.stack(all_X_lstm, axis=0).astype(np.float32) # LSTM float32 OK

            # list_of_prefixes et list_of_origins sont déjà des listes
            list_of_prefixes_final = all_prefixes
            list_of_origins_final = all_origins

            # --- Vérifications finales des tailles ---
            num_samples_final = len(y_np)
            if not (X_lgbm_np.shape[0] == num_samples_final and
                    X_lstm_np.shape[0] == num_samples_final and
                    len(list_of_prefixes_final) == num_samples_final and
                    len(list_of_origins_final) == num_samples_final):
                 raise ValueError(f"Incohérence taille finale! y:{num_samples_final}, "
                                  f"X_lgbm:{X_lgbm_np.shape[0]}, X_lstm:{X_lstm_np.shape[0]}, "
                                  f"prefixes:{len(list_of_prefixes_final)}, origins:{len(list_of_origins_final)}")

            # Vérifier que les étiquettes sont bien des indices 0-based (0 = Player, 1 = Banker)
            unique_labels = np.unique(y_np)
            if not np.all(np.isin(unique_labels, [0, 1])):
                self.logger.error(f"ERREUR CRITIQUE: Les étiquettes contiennent des valeurs invalides: {unique_labels}. "
                                 f"Doivent être uniquement 0 (Player) ou 1 (Banker).")
                raise ValueError(f"Étiquettes invalides détectées: {unique_labels}. Doivent être 0 ou 1.")

            self.logger.info(f"Manager: Préparation données filtrées terminée.")
            self.logger.info(f"  - Nombre total d'échantillons générés/filtrés : {num_samples_final}")
            self.logger.info(f"  - Shape X_lgbm: {X_lgbm_np.shape}")
            self.logger.info(f"  - Shape y: {y_np.shape}")
            self.logger.info(f"  - Shape X_lstm: {X_lstm_np.shape}")
            self.logger.info(f"  - Nombre Préfixes: {len(list_of_prefixes_final)}")
            self.logger.info(f"  - Nombre Origines: {len(list_of_origins_final)}")

            return X_lgbm_np, y_np, X_lstm_np, list_of_prefixes_final, list_of_origins_final

        except ValueError as ve:
            self.logger.error(f"Manager: Erreur valeur pendant conversion/vérif NumPy finale: {ve}", exc_info=True)
            return None, None, None, None, None
        except Exception as e_np:
            self.logger.error(f"Manager: Erreur inattendue pendant conversion NumPy finale: {e_np}", exc_info=True)
            return None, None, None, None, None

    # La méthode evaluate_performance reste inchangée, mais elle est moins utile
    # si le manager ne fait plus que la préparation des données brutes.
    # Elle pourrait être utilisée pour évaluer un modèle sur des données
    # préparées par CE manager (donc déjà filtrées).
    def evaluate_performance(self, y_true: np.ndarray, y_pred_proba: np.ndarray) -> dict:
        """
        Évalue la performance (loss, accuracy) sur des données DÉJÀ FILTRÉES.
        MODIFIÉ: Utilise Scikit-learn au lieu de TensorFlow.
        Suppose que y_true et y_pred_proba correspondent
        exclusivement aux mains cibles >= min_target_hand_index.

        Args:
            y_true (np.ndarray): Les résultats réels (0 ou 1) des mains évaluées.
                                 Doit être 1D.
            y_pred_proba (np.ndarray): Les probabilités prédites pour la classe 1
                                       des mains évaluées. Doit être 1D.

        Returns:
            dict: Un dictionnaire contenant 'loss' et 'accuracy'.
                  Retourne des NaNs si les inputs sont vides ou invalides.
        """
        if not isinstance(y_true, np.ndarray) or not isinstance(y_pred_proba, np.ndarray):
             self.logger.warning("Attention evaluate_performance: y_true et y_pred_proba doivent être des np.ndarray.")
             return {'loss': np.nan, 'accuracy': np.nan}

        if y_true.ndim != 1 or y_pred_proba.ndim != 1:
             self.logger.warning(f"Attention evaluate_performance: y_true ({y_true.ndim}D) et y_pred_proba ({y_pred_proba.ndim}D) doivent être 1D.")
             # Essayer d'aplatir si possible (ex: sortie modèle (N, 1))
             if y_pred_proba.ndim == 2 and y_pred_proba.shape[1] == 1:
                 y_pred_proba = y_pred_proba.flatten()
                 if y_true.ndim != 1 or y_pred_proba.ndim != 1: # Re-vérifier après flatten
                     return {'loss': np.nan, 'accuracy': np.nan}
             elif y_true.ndim != 1: # Si y_true n'est pas 1D
                 return {'loss': np.nan, 'accuracy': np.nan}


        if y_true.shape[0] == 0 or y_pred_proba.shape[0] == 0:
            self.logger.warning("Attention evaluate_performance: Données d'évaluation vides. Retour de NaN.")
            return {'loss': np.nan, 'accuracy': np.nan}

        if y_true.shape != y_pred_proba.shape:
             self.logger.warning(f"Attention evaluate_performance: Shapes incompatibles - y_true: {y_true.shape}, y_pred_proba: {y_pred_proba.shape}. Retour de NaN.")
             return {'loss': np.nan, 'accuracy': np.nan}

        # Vérifier si y_true contient bien 0 et 1 (ou juste une classe)
        unique_labels = np.unique(y_true)
        if not np.all(np.isin(unique_labels, [0, 1])):
             self.logger.warning(f"Attention evaluate_performance: y_true contient des labels inattendus: {unique_labels}. Calcul métriques peut échouer.")
             # Ne retourne pas NaN ici, laisse sklearn gérer, mais logue l'avertissement.

        # Préparer les prédictions de classe pour accuracy_score
        try:
             y_pred_class = (y_pred_proba >= 0.5).astype(int)
        except Exception as e_astype:
             self.logger.error(f"Erreur conversion y_pred_proba en classes: {e_astype}")
             return {'loss': np.nan, 'accuracy': np.nan}

        loss = np.nan
        accuracy = np.nan

        try:
             # --- Calcul de la Perte (Log Loss avec Scikit-learn) ---
             # Gérer le cas où il n'y a qu'une seule classe dans y_true (log_loss peut échouer)
             if len(unique_labels) < 2:
                  self.logger.warning(f"Calcul LogLoss ignoré car une seule classe présente dans y_true ({unique_labels}).")
                  loss = np.nan # Ou 0.0 si on considère la prédiction parfaite? Nan est plus sûr.
             else:
                  # log_loss de sklearn gère le clipping interne pour éviter log(0)
                  loss = log_loss(y_true, y_pred_proba, eps=1e-15) # eps similaire à celui de Keras

             # --- Calcul de l'Accuracy (avec Scikit-learn) ---
             accuracy = accuracy_score(y_true, y_pred_class)

             # Vérifier si les résultats sont finis
             if not np.isfinite(loss): loss = np.nan
             if not np.isfinite(accuracy): accuracy = np.nan

             return {'loss': loss, 'accuracy': accuracy}

        except ValueError as ve:
             # Peut arriver si y_pred_proba sort de [0, 1] ou si labels inattendus
             self.logger.error(f"Erreur valeur pendant calcul métriques sklearn: {ve}")
             return {'loss': np.nan, 'accuracy': np.nan}
        except Exception as e:
            self.logger.error(f"Erreur inattendue durant calcul métriques sklearn: {e}", exc_info=True)
            return {'loss': np.nan, 'accuracy': np.nan}
