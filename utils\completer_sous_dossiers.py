#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour compléter les fichiers Descriptif.txt des sous-dossiers
en extrayant les descriptions du fichier principal
"""

import os
import re
import shutil
from pathlib import Path

def backup_file(filepath):
    """Crée une sauvegarde du fichier avant modification"""
    backup_path = f"{filepath}.backup_complete"
    shutil.copy2(filepath, backup_path)
    print(f"Sauvegarde créée: {backup_path}")
    return backup_path

def extraire_sections_du_principal():
    """Extrait toutes les sections du fichier principal"""
    print("📖 Extraction des sections du fichier principal...")
    
    with open("Descriptif.txt", 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Dictionnaire pour stocker les sections
    sections = {}
    
    # Pattern pour détecter les sections
    section_pattern = r'^SECTION\s+\d+\s*:\s*([A-Z]+)\s*\(.*?\)$'
    
    # Trouver toutes les sections
    section_matches = list(re.finditer(section_pattern, content, re.MULTILINE))
    
    for i, match in enumerate(section_matches):
        section_name = match.group(1).lower()
        section_start = match.end()
        
        # Trouver la fin de la section (début de la section suivante ou fin du fichier)
        if i + 1 < len(section_matches):
            section_end = section_matches[i + 1].start()
        else:
            section_end = len(content)
        
        section_content = content[section_start:section_end].strip()
        
        # Extraire les méthodes de cette section
        methodes = extraire_methodes_section(section_content, section_name)
        sections[section_name] = methodes
        
        print(f"  ✅ Section {section_name}: {len(methodes)} méthodes extraites")
    
    return sections

def extraire_methodes_section(section_content, section_name):
    """Extrait les méthodes d'une section"""
    methodes = []
    
    # Pattern pour détecter les méthodes (numéro. nom_fichier.txt)
    methode_pattern = r'^(\d+)\.\s+([a-zA-Z_][a-zA-Z0-9_]*\.txt)\s+\((.*?)\)$'
    
    # Diviser le contenu en blocs de méthodes
    lines = section_content.split('\n')
    current_methode = None
    current_content = []
    
    for line in lines:
        methode_match = re.match(methode_pattern, line)
        
        if methode_match:
            # Sauvegarder la méthode précédente si elle existe
            if current_methode:
                methodes.append({
                    'numero': current_methode['numero'],
                    'fichier': current_methode['fichier'],
                    'titre': current_methode['titre'],
                    'contenu': '\n'.join(current_content).strip()
                })
            
            # Commencer une nouvelle méthode
            current_methode = {
                'numero': int(methode_match.group(1)),
                'fichier': methode_match.group(2),
                'titre': methode_match.group(3)
            }
            current_content = [line]
        else:
            # Ajouter la ligne au contenu de la méthode actuelle
            if current_methode:
                current_content.append(line)
    
    # Ajouter la dernière méthode
    if current_methode:
        methodes.append({
            'numero': current_methode['numero'],
            'fichier': current_methode['fichier'],
            'titre': current_methode['titre'],
            'contenu': '\n'.join(current_content).strip()
        })
    
    return methodes

def completer_fichier_sous_dossier(dossier, section_name, methodes):
    """Complète le fichier Descriptif.txt d'un sous-dossier"""
    fichier_path = f"{dossier}/Descriptif.txt"
    
    if not os.path.exists(fichier_path):
        print(f"⚠️  Fichier non trouvé: {fichier_path}")
        return False
    
    print(f"\n📝 Traitement de {fichier_path}...")
    
    # Créer une sauvegarde
    backup_path = backup_file(fichier_path)
    
    try:
        # Lire le fichier existant
        with open(fichier_path, 'r', encoding='utf-8') as f:
            existing_content = f.read()
        
        # Trouver les fichiers présents dans le dossier
        fichiers_dossier = []
        if os.path.exists(dossier):
            fichiers_dossier = [f for f in os.listdir(dossier) if f.endswith('.txt') and f != 'Descriptif.txt']
        
        print(f"  📁 Fichiers dans {dossier}: {len(fichiers_dossier)}")
        
        # Construire le nouveau contenu
        nouveau_contenu = construire_nouveau_contenu(existing_content, methodes, fichiers_dossier, section_name)
        
        # Écrire le nouveau contenu
        with open(fichier_path, 'w', encoding='utf-8') as f:
            f.write(nouveau_contenu)
        
        print(f"  ✅ Fichier complété avec succès")
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur: {e}")
        # Restaurer la sauvegarde
        if os.path.exists(backup_path):
            shutil.copy2(backup_path, fichier_path)
        return False

def construire_nouveau_contenu(existing_content, methodes, fichiers_dossier, section_name):
    """Construit le nouveau contenu du fichier Descriptif.txt"""
    
    # Garder l'en-tête existant jusqu'à la première méthode
    lines = existing_content.split('\n')
    header_lines = []
    
    for line in lines:
        if re.match(r'^\d+\.', line):
            break
        header_lines.append(line)
    
    # Construire le nouveau contenu
    nouveau_contenu = '\n'.join(header_lines).rstrip() + '\n\n'
    
    # Ajouter les méthodes correspondantes
    numero = 1
    for fichier in fichiers_dossier:
        # Chercher la méthode correspondante
        methode_trouvee = None
        for methode in methodes:
            if methode['fichier'] == fichier:
                methode_trouvee = methode
                break
        
        if methode_trouvee:
            # Remplacer le numéro par le nouveau numéro séquentiel
            contenu_methode = methode_trouvee['contenu']
            contenu_methode = re.sub(r'^\d+\.', f'{numero}.', contenu_methode)
            nouveau_contenu += contenu_methode + '\n\n'
            print(f"    ✅ {numero}. {fichier} - Description complète ajoutée")
        else:
            # Créer une entrée basique si pas trouvée
            nouveau_contenu += f"{numero}. {fichier} (DESCRIPTION À COMPLÉTER)\n"
            nouveau_contenu += f"   - FONCTION : Description manquante pour {fichier}\n\n"
            print(f"    ⚠️  {numero}. {fichier} - Description manquante")
        
        numero += 1
    
    return nouveau_contenu.rstrip() + '\n'

def main():
    """Fonction principale"""
    print("🔧 Script de complétion des fichiers Descriptif.txt des sous-dossiers")
    print("=" * 80)
    
    # Mapping des sections vers les dossiers
    mapping_sections = {
        'reseauxneuronaux': 'ReseauxNeuronaux',
        'calculconfiance': 'CalculConfiance', 
        'optimisationentrainement': 'OptimisationEntrainement',
        'gestiondonnees': 'GestionDonnees',
        'evaluationmetriques': 'EvaluationMetriques',
        'utilitairesfonctions': 'UtilitairesFonctions',
        'anciennesclasses': 'anciennesclasses'
    }
    
    # Extraire les sections du fichier principal
    sections = extraire_sections_du_principal()
    
    # Compléter chaque sous-dossier
    resultats = []
    
    for section_name, dossier in mapping_sections.items():
        if section_name in sections:
            methodes = sections[section_name]
            succes = completer_fichier_sous_dossier(dossier, section_name, methodes)
            resultats.append((dossier, succes, len(methodes)))
        else:
            print(f"⚠️  Section {section_name} non trouvée dans le fichier principal")
            resultats.append((dossier, False, 0))
    
    # Résumé final
    print("\n" + "=" * 80)
    print("📊 RÉSUMÉ FINAL")
    print("=" * 80)
    
    succes_count = 0
    total_methodes = 0
    
    for dossier, succes, nb_methodes in resultats:
        status = "✅ SUCCÈS" if succes else "❌ ÉCHEC"
        print(f"{status}: {dossier} ({nb_methodes} méthodes)")
        if succes:
            succes_count += 1
            total_methodes += nb_methodes
    
    print(f"\n🎯 Résultat: {succes_count}/{len(resultats)} dossiers traités avec succès")
    print(f"📝 Total: {total_methodes} descriptions de méthodes copiées")
    
    if succes_count == len(resultats):
        print("🎉 Tous les fichiers Descriptif.txt des sous-dossiers ont été complétés!")
    else:
        print("⚠️  Certains fichiers n'ont pas pu être traités")

if __name__ == "__main__":
    main()
