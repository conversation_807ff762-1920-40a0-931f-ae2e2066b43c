# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\data_manager.py
# Lignes: 30 à 85
# Type: Méthode de la classe BaccaratSequenceManager

    def __init__(self,
                 sequence_length: int, # Utilisé pour la validation interne et log
                 min_target_hand_index: int,
                 # Fonction pour créer les features [LGBM], [LSTM]
                 hybrid_feature_creator: Callable[[List[str]], Tuple[Optional[List[float]], Optional[np.ndarray]]],
                 # Infos pour validation des shapes retournées par la fonction
                 lgbm_feature_count: int,
                 lstm_seq_len: int,
                 lstm_feature_count: int,
                 parent_logger = None # Optionnel: passer un logger
                ):
        """
        Initialise le gestionnaire de séquences modifié.
        Utilise une fenêtre adaptative pour les modèles LGBM et LSTM.

        Args:
            sequence_length (int): Taille maximale de la matrice de sortie pour les features LSTM.
                                  Avec la fenêtre adaptative, toute la séquence disponible est utilisée,
                                  mais la matrice de sortie a une taille fixe.
            min_target_hand_index (int): Index (0-based) minimum de la main cible (y) à inclure.
            hybrid_feature_creator: Fonction (ex: predictor.create_hybrid_features) qui prend une
                                    séquence P/B et retourne (list[float]|None, np.ndarray|None).
            lgbm_feature_count (int): Nombre attendu de features LGBM.
            lstm_seq_len (int): Longueur attendue de la séquence pour les features LSTM.
            lstm_feature_count (int): Nombre attendu de features par pas de temps LSTM.
            parent_logger: Logger externe optionnel.
        """
        if not isinstance(sequence_length, int) or sequence_length <= 0:
            raise ValueError("sequence_length doit être un entier positif.")
        if not isinstance(min_target_hand_index, int) or min_target_hand_index < 0:
             raise ValueError("min_target_hand_index doit être un entier positif ou nul.")
        if not callable(hybrid_feature_creator):
             raise TypeError("hybrid_feature_creator doit être une fonction callable.")
        if not isinstance(lgbm_feature_count, int) or lgbm_feature_count <= 0:
             raise ValueError("lgbm_feature_count doit être un entier positif.")
        if not isinstance(lstm_seq_len, int) or lstm_seq_len <= 0:
             raise ValueError("lstm_seq_len doit être un entier positif.")
        if not isinstance(lstm_feature_count, int) or lstm_feature_count <= 0:
             raise ValueError("lstm_feature_count doit être un entier positif.")

        self.sequence_length = sequence_length # = lstm_seq_len pour create_hybrid_features
        self.min_target_hand_index = min_target_hand_index
        self.hybrid_feature_creator = hybrid_feature_creator
        self.lgbm_feature_count = lgbm_feature_count
        self.lstm_seq_len = lstm_seq_len
        # Garder le LSTM Feature Count dans l'instance
        self.lstm_feature_count = lstm_feature_count
        #Mettre à jour la shape
        self.lstm_expected_shape = (lstm_seq_len, lstm_feature_count)

        self.logger = parent_logger if parent_logger else logging.getLogger(__name__)
        self.logger.info(f"BaccaratSequenceManager (Modifié) initialisé:")
        self.logger.info(f"  - Longueur séquence requise: {self.sequence_length}")
        self.logger.info(f"  - Index minimum cible (0-based): {self.min_target_hand_index}")
        self.logger.info(f"  - Générateur Features: {self.hybrid_feature_creator.__name__}")
        self.logger.info(f"  - Validation: LGBM Feat Count={self.lgbm_feature_count}, LSTM Shape={self.lstm_expected_shape}")