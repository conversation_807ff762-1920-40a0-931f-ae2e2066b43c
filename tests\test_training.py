#!/usr/bin/env python3
"""
Tests complets pour l'entraînement des modèles
"""

import sys
import os
import unittest
import tempfile
import shutil
import numpy as np
import time
from pathlib import Path

# Ajouter le dossier du programme au path
PROG_DIR = r"C:\Users\<USER>\Desktop\travail\plateforme\notes_travail\prog"
sys.path.insert(0, PROG_DIR)

try:
    from hbp import HybridBaccaratPredictor
    from config import PredictorConfig
except ImportError as e:
    print(f"ERREUR: Impossible d'importer modules: {e}")
    sys.exit(1)


class TestTrainingBasic(unittest.TestCase):
    """Tests de base pour l'entraînement"""
    
    def setUp(self):
        """Initialisation avant chaque test"""
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        
        # Données de test minimales
        self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 20  # 200 éléments
        
    def test_predictor_creation(self):
        """Test création du prédicteur"""
        self.assertIsNotNone(self.predictor)
        self.assertIsInstance(self.predictor, HybridBaccaratPredictor)
        
    def test_training_initialization(self):
        """Test initialisation de l'entraînement"""
        try:
            # Tenter d'initialiser l'entraînement
            result = self.predictor.initialize_training()
            
            # Devrait retourner True ou ne pas lever d'exception
            if result is not None:
                self.assertTrue(result)
                
        except Exception as e:
            self.fail(f"Échec initialisation entraînement: {e}")
            
    def test_data_preparation(self):
        """Test préparation des données pour l'entraînement"""
        try:
            # Préparer données
            prepared_data = self.predictor.prepare_training_data(self.test_data)
            
            self.assertIsNotNone(prepared_data)
            
        except Exception as e:
            self.fail(f"Échec préparation données: {e}")
            
    def test_model_creation(self):
        """Test création des modèles"""
        try:
            # Créer modèles
            models_created = self.predictor.create_models()
            
            if models_created is not None:
                self.assertTrue(models_created)
                
        except Exception as e:
            self.fail(f"Échec création modèles: {e}")


class TestTrainingLGBM(unittest.TestCase):
    """Tests spécifiques pour l'entraînement LGBM"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 50  # 500 éléments
        
    def test_lgbm_feature_creation(self):
        """Test création features LGBM"""
        try:
            features = self.predictor.create_lgbm_features(self.test_data)
            
            if features is not None:
                self.assertIsInstance(features, (list, np.ndarray))
                self.assertGreater(len(features), 0)
                
        except Exception as e:
            self.fail(f"Échec création features LGBM: {e}")
            
    def test_lgbm_training(self):
        """Test entraînement modèle LGBM"""
        try:
            # Préparer données
            features = self.predictor.create_lgbm_features(self.test_data)
            
            if features is not None:
                # Tenter entraînement
                result = self.predictor.train_lgbm_model(features, self.test_data[1:])
                
                if result is not None:
                    self.assertTrue(result)
                    
        except Exception as e:
            self.fail(f"Échec entraînement LGBM: {e}")
            
    def test_lgbm_prediction(self):
        """Test prédiction LGBM après entraînement"""
        try:
            # Entraîner d'abord
            features = self.predictor.create_lgbm_features(self.test_data)
            
            if features is not None:
                self.predictor.train_lgbm_model(features, self.test_data[1:])
                
                # Tester prédiction
                test_features = features[:10]  # Prendre quelques features
                prediction = self.predictor.predict_lgbm(test_features)
                
                if prediction is not None:
                    self.assertIsInstance(prediction, (float, int, dict))
                    
        except Exception as e:
            self.fail(f"Échec prédiction LGBM: {e}")


class TestTrainingLSTM(unittest.TestCase):
    """Tests spécifiques pour l'entraînement LSTM"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 50  # 500 éléments
        
    def test_lstm_sequence_creation(self):
        """Test création séquences LSTM"""
        try:
            sequences = self.predictor.create_lstm_sequences(self.test_data)
            
            if sequences is not None:
                self.assertIsInstance(sequences, (list, np.ndarray))
                
        except Exception as e:
            self.fail(f"Échec création séquences LSTM: {e}")
            
    def test_lstm_training(self):
        """Test entraînement modèle LSTM"""
        try:
            # Préparer séquences
            sequences = self.predictor.create_lstm_sequences(self.test_data)
            
            if sequences is not None:
                # Tenter entraînement
                result = self.predictor.train_lstm_model(sequences)
                
                if result is not None:
                    self.assertTrue(result)
                    
        except Exception as e:
            self.fail(f"Échec entraînement LSTM: {e}")
            
    def test_lstm_prediction(self):
        """Test prédiction LSTM après entraînement"""
        try:
            # Entraîner d'abord
            sequences = self.predictor.create_lstm_sequences(self.test_data)
            
            if sequences is not None:
                self.predictor.train_lstm_model(sequences)
                
                # Tester prédiction
                test_sequence = self.test_data[-self.config.lstm_sequence_length:]
                prediction = self.predictor.predict_lstm(test_sequence)
                
                if prediction is not None:
                    self.assertIsInstance(prediction, (float, int, dict))
                    
        except Exception as e:
            self.fail(f"Échec prédiction LSTM: {e}")


class TestTrainingMarkov(unittest.TestCase):
    """Tests spécifiques pour l'entraînement Markov"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 30  # 300 éléments
        
    def test_markov_chain_creation(self):
        """Test création chaîne de Markov"""
        try:
            result = self.predictor.build_markov_chain(self.test_data)
            
            if result is not None:
                self.assertTrue(result)
                
        except Exception as e:
            self.fail(f"Échec création chaîne Markov: {e}")
            
    def test_markov_prediction(self):
        """Test prédiction Markov"""
        try:
            # Construire chaîne d'abord
            self.predictor.build_markov_chain(self.test_data)
            
            # Tester prédiction
            recent_sequence = self.test_data[-5:]
            prediction = self.predictor.predict_markov(recent_sequence)
            
            if prediction is not None:
                self.assertIsInstance(prediction, (float, int, dict))
                
        except Exception as e:
            self.fail(f"Échec prédiction Markov: {e}")


class TestTrainingIntegration(unittest.TestCase):
    """Tests d'intégration pour l'entraînement complet"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        
        # Charger vraies données si disponibles
        historical_file = os.path.join(PROG_DIR, "historical_data.txt")
        if os.path.exists(historical_file):
            try:
                with open(historical_file, 'r') as f:
                    content = f.read().strip()
                    self.test_data = [int(x) for x in content.split(',')]
            except:
                self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 100
        else:
            self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 100
            
    def test_full_training_pipeline(self):
        """Test pipeline complet d'entraînement"""
        try:
            # Test entraînement complet
            result = self.predictor.train_all_models(self.test_data)
            
            if result is not None:
                self.assertTrue(result)
                
        except Exception as e:
            self.fail(f"Échec pipeline complet: {e}")
            
    def test_hybrid_prediction(self):
        """Test prédiction hybride après entraînement"""
        try:
            # Entraîner tous les modèles
            self.predictor.train_all_models(self.test_data)
            
            # Tester prédiction hybride
            recent_data = self.test_data[-20:]
            prediction = self.predictor.predict_hybrid(recent_data)
            
            if prediction is not None:
                self.assertIsInstance(prediction, dict)
                self.assertIn('player', prediction)
                self.assertIn('banker', prediction)
                
        except Exception as e:
            self.fail(f"Échec prédiction hybride: {e}")
            
    def test_training_performance(self):
        """Test performance de l'entraînement"""
        start_time = time.time()
        
        try:
            # Entraîner avec timeout
            result = self.predictor.train_all_models(self.test_data[:1000])  # Limiter données
            
            end_time = time.time()
            training_time = end_time - start_time
            
            # L'entraînement devrait prendre moins de 5 minutes
            self.assertLess(training_time, 300)
            
        except Exception as e:
            self.fail(f"Échec test performance: {e}")


if __name__ == '__main__':
    print("=== TESTS ENTRAÎNEMENT ===")
    print(f"Répertoire programme: {PROG_DIR}")
    
    # Créer suite de tests
    suite = unittest.TestSuite()
    
    # Ajouter tests
    suite.addTest(unittest.makeSuite(TestTrainingBasic))
    suite.addTest(unittest.makeSuite(TestTrainingLGBM))
    suite.addTest(unittest.makeSuite(TestTrainingLSTM))
    suite.addTest(unittest.makeSuite(TestTrainingMarkov))
    suite.addTest(unittest.makeSuite(TestTrainingIntegration))
    
    # Exécuter tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Afficher résultats
    print(f"\n=== RÉSULTATS ENTRAÎNEMENT ===")
    print(f"Tests exécutés: {result.testsRun}")
    print(f"Échecs: {len(result.failures)}")
    print(f"Erreurs: {len(result.errors)}")
    
    if result.failures:
        print("\nÉCHECS:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
            
    if result.errors:
        print("\nERREURS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
            
    sys.exit(0 if result.wasSuccessful() else 1)
