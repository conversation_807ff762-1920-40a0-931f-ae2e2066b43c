# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 321 à 342
# Type: Méthode de la classe FocalLoss
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

            def __init__(self, gamma=2.0, alpha=None, label_smoothing=0.0):
                super(FocalLoss, self).__init__()
                self.gamma = gamma
                self.label_smoothing = label_smoothing

                # Gestion du paramètre alpha (poids des classes)
                if alpha is not None:
                    if isinstance(alpha, float):
                        # Si alpha est un float, on crée un tensor [1-alpha, alpha] pour les classes 0 et 1
                        self.alpha = torch.tensor([1.0-alpha, alpha])
                    else:
                        # Sinon on utilise directement le tensor fourni
                        self.alpha = alpha
                else:
                    self.alpha = None

                # Créer la perte d'entropie croisée avec les poids appropriés
                self.ce_loss = nn.CrossEntropyLoss(
                    weight=self.alpha,
                    reduction='none',
                    label_smoothing=label_smoothing
                )