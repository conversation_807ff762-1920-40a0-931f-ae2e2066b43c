# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\models.py
# Lignes: 537 à 594
# Type: Méthode de la classe Persistent<PERSON><PERSON><PERSON>

    def update_session(self, sequence: List[str]) -> None:
        """
        Met à jour les modèles de session avec la séquence de jeu actuelle.
        Seul le dernier coup et les états le précédant sont ajoutés.

        Args:
            sequence (List[str]): La séquence de jeu en cours.
        """
        # Vérifications de sécurité
        if not isinstance(sequence, list):
            logger.warning("update_session: sequence n'est pas une liste.")
            return

        if not sequence:
            # logger.debug("update_session appelée avec une séquence vide.")
            return

        try:
            with self.lock:
                # logger.debug(f"Mise à jour session <PERSON>ov (longueur séquence: {len(sequence)})")

                # Vérifier que le dernier élément est valide
                if len(sequence) == 0:
                    return

                last_outcome = sequence[-1]
                if not isinstance(last_outcome, str):
                    logger.warning(f"update_session: dernier élément n'est pas une chaîne: {type(last_outcome)}")
                    return

                if last_outcome not in ('player', 'banker'):
                    # logger.debug(f"Outcome session non P/B ignoré: {last_outcome}")
                    return # Ignorer autres issues comme 'tie' etc.

                # Utiliser self.max_order_int pour l'itération (déjà un entier)

                # Apprendre jusqu'à self.max_order_int pour les modèles de session
                for order in range(1, self.max_order_int + 1): # Commence à l'ordre 1
                    # L'état précède le dernier outcome. Il faut donc len > order.
                    if len(sequence) > order:
                        # Vérifier que tous les éléments de l'état sont valides
                        state_elements = sequence[-(order + 1) : -1]
                        valid_state = True
                        for elem in state_elements:
                            if not isinstance(elem, str) or elem not in ('player', 'banker'):
                                valid_state = False
                                break

                        if not valid_state:
                            continue

                        # L'état est la séquence de 'order' éléments *avant* le dernier
                        state = tuple(state_elements)
                        # Incrémenter le compteur pour cet état menant à ce résultat
                        self.session_models[order][state][last_outcome] += 1
                        # logger.debug(f"  Session Order {order}: State={state}, Outcome={last_outcome}, New Count={self.session_models[order][state][last_outcome]}")
        except Exception as e:
            logger.error(f"Erreur dans update_session: {e}")