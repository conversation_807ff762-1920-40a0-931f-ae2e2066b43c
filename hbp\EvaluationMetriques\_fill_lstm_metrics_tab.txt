# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 7097 à 7151
# Type: Méthode de la classe HybridBaccaratPredictor

    def _fill_lstm_metrics_tab(self, parent_frame):
        """Remplit l'onglet des métriques LSTM."""
        # Créer un cadre pour les métriques de base
        basic_metrics_frame = ttk.LabelFrame(parent_frame, text="Métriques de Base")
        basic_metrics_frame.pack(fill=tk.X, expand=False, padx=10, pady=5)

        # Créer des variables pour les métriques
        self.lstm_metric_vars = {
            'train_loss': tk.StringVar(value="Perte d'entraînement: N/A"),
            'val_loss': tk.StringVar(value="Perte de validation: N/A"),
            'train_accuracy': tk.StringVar(value="Exactitude d'entraînement: N/A"),
            'val_accuracy': tk.StringVar(value="Exactitude de validation: N/A"),
            'precision': tk.StringVar(value="Précision: N/A"),
            'recall': tk.StringVar(value="Rappel: N/A"),
            'f1': tk.StringVar(value="F1-Score: N/A"),
            'objective1': tk.StringVar(value="Obj1 (Consécutives 31-60): N/A"),
            'objective2': tk.StringVar(value="Obj2 (Précision 31-60): N/A"),
        }

        # Ajouter les étiquettes pour les métriques de base
        for i, (key, var) in enumerate(self.lstm_metric_vars.items()):
            ttk.Label(basic_metrics_frame, textvariable=var).grid(row=i//3, column=i%3, padx=10, pady=5, sticky=tk.W)

        # Créer un cadre pour la matrice de confusion
        confusion_frame = ttk.LabelFrame(parent_frame, text="Matrice de Confusion")
        confusion_frame.pack(fill=tk.X, expand=False, padx=10, pady=5)

        # Créer une grille pour la matrice de confusion
        self.lstm_cm_vars = []
        for i in range(2):
            row_vars = []
            for j in range(2):
                var = tk.StringVar(value="N/A")
                row_vars.append(var)
                ttk.Label(confusion_frame, textvariable=var, width=10, anchor='center',
                         borderwidth=1, relief="solid").grid(row=i+1, column=j+1, padx=5, pady=5)
            self.lstm_cm_vars.append(row_vars)

        # Ajouter les étiquettes pour les classes
        ttk.Label(confusion_frame, text="").grid(row=0, column=0)
        ttk.Label(confusion_frame, text="Prédit: Banker").grid(row=0, column=1)
        ttk.Label(confusion_frame, text="Prédit: Player").grid(row=0, column=2)
        ttk.Label(confusion_frame, text="Réel: Banker").grid(row=1, column=0)
        ttk.Label(confusion_frame, text="Réel: Player").grid(row=2, column=0)

        # Créer un cadre pour les courbes d'apprentissage
        learning_curves_frame = ttk.LabelFrame(parent_frame, text="Courbes d'Apprentissage")
        learning_curves_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Créer un widget Canvas pour afficher les courbes d'apprentissage
        self.lstm_curves_canvas = tk.Canvas(learning_curves_frame, bg='white')
        self.lstm_curves_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Mettre à jour les métriques LSTM
        self._update_lstm_metrics()