# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 2092 à 2099
# Type: Méthode de la classe HybridBaccaratPredictor

    def _update_weights_display(self):
        """Met à jour l'affichage textuel des poids des modèles."""
        with self.model_lock: # Protéger l'accès à self.weights
            if not hasattr(self, 'stats_vars'): return # UI pas encore prête
            weights_text_parts = []
            for method, weight in self.weights.items():
                 weights_text_parts.append(f"{method.upper()}({weight*100:.1f}%)")
            self.stats_vars['model_weights'].set(f"Poids: {' | '.join(weights_text_parts)}")