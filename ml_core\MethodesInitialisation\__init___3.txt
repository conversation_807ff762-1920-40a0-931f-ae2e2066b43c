# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1597 à 1614
# Type: Méthode de la classe ThreadedOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, optimizer_class, callback=None, error_callback=None):
        """
        Initialise l'optimiseur threadé.

        Args:
            optimizer_class: Classe d'optimiseur à utiliser
            callback: Fonction à appeler lorsque l'optimisation est terminée
            error_callback: Fonction à appeler en cas d'erreur
        """
        self.optimizer_class = optimizer_class
        self.optimizer_instance = None
        self.callback = callback
        self.error_callback = error_callback
        self.thread = None
        self.stop_event = threading.Event()
        self.is_running = False
        self.result = None
        self.error = None