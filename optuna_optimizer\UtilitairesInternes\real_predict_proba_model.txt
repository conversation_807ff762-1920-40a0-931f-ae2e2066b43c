# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 2694 à 2704
# Type: Méthode de la classe OptunaOptimizer

        def real_predict_proba_model(self, X):
            # Vérifier que le modèle a été entraîné
            if not hasattr(self, 'model_'):
                raise RuntimeError("Le modèle n'a pas été entraîné. Appelez fit() d'abord.")

            # Vérifier que le modèle supporte predict_proba
            if not hasattr(self.model_, 'predict_proba'):
                raise RuntimeError("Le modèle ne supporte pas predict_proba.")

            # Faire des prédictions de probabilités
            return self.model_.predict_proba(X)