# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\models.py
# Lignes: 229 à 430
# Type: Méthode de la classe PersistentMarkov

    def get_combined_probs(self, sequence: List[str], global_weight: float = None, context_weight: float = None, decay_factor: float = None) -> Dict[str, float]:
        """
        Combine les probabilités globales et de session pour prédire le prochain coup.
        Utilise le lissage configuré, pondère les différents ordres et enrichit avec contexte.

        Args:
            sequence (List[str]): La séquence de jeu actuelle jusqu'au coup t-1.
            global_weight (float, optional): Le poids à accorder aux modèles globaux (entre 0 et 1).
                                            Le poids de session sera (1 - global_weight).
                                            Si None, utilise la valeur par défaut 0.6.
            context_weight (float, optional): Le poids à accorder au contexte local dans le modèle de Markov.
                                            Si None, utilise la valeur par défaut 0.8.
            decay_factor (float, optional): Le facteur de décroissance pour l'influence des états passés.
                                          Si None, utilise la valeur par défaut 0.95.

        Returns:
            Dict[str, float]: Un dictionnaire avec les probabilités prédites pour 'player' et 'banker'.
                              Retourne {'player': 0.5, 'banker': 0.5} si pas assez de données.
        """
        # Vérification de sécurité pour éviter les erreurs d'index
        if not isinstance(sequence, list):
            logger.warning("get_combined_probs: sequence n'est pas une liste. Retour 50/50.")
            return {'player': 0.5, 'banker': 0.5}

        if not sequence or len(sequence) < 1:
            logger.debug("get_combined_probs: sequence vide ou trop courte. Retour 50/50.")
            return {'player': 0.5, 'banker': 0.5}

        # Vérifier que tous les éléments de la séquence sont des chaînes valides
        for i, item in enumerate(sequence):
            if not isinstance(item, str):
                logger.warning(f"get_combined_probs: élément non-string à l'index {i}: {type(item)}. Retour 50/50.")
                return {'player': 0.5, 'banker': 0.5}
            if item not in ('player', 'banker'):
                logger.warning(f"get_combined_probs: valeur invalide à l'index {i}: '{item}'. Retour 50/50.")
                return {'player': 0.5, 'banker': 0.5}

        # Utiliser la valeur par défaut depuis la configuration si global_weight est None
        if global_weight is None:
            # Récupérer la valeur depuis la configuration si disponible, sinon utiliser 0.15 comme valeur par défaut
            from config import PredictorConfig
            config = PredictorConfig()
            global_weight = getattr(config, 'markov_global_weight', 0.15)

        # Vérifier et clamper global_weight
        global_weight = max(0.0, min(1.0, global_weight))
        session_weight = 1.0 - global_weight

        # Poids décroissant pour les ordres plus élevés
        # Récupérer l'exposant depuis la configuration
        from config import PredictorConfig
        config = PredictorConfig()
        order_weight_exponent = getattr(config, 'markov_order_weight_exponent', 1.5)

        # Utiliser self.max_order_int pour les boucles (déjà un entier)

        # Calculer les poids avec l'exposant configuré
        order_weights = {}
        for order in range(1, self.max_order_int + 1):
            base_weight = 1.0 / (order ** order_weight_exponent)
            if order == self.max_order_int:
                # Donner un poids légèrement plus élevé à l'ordre maximal
                order_weights[order] = base_weight * 1.1
            else:
                order_weights[order] = base_weight

        total_order_weight = sum(order_weights.values())
        norm_order_weights = {o: w / total_order_weight for o, w in order_weights.items()} if total_order_weight > 1e-9 else {}

        final_probs = {'player': 0.0, 'banker': 0.0}
        accumulated_weight = 0.0
        num_classes = 2

        # Récupérer le poids du contexte depuis les paramètres ou la configuration
        if context_weight is None:
            # Récupérer la valeur depuis la configuration si disponible, sinon utiliser 0.8 comme valeur par défaut
            from config import PredictorConfig
            config = PredictorConfig()
            context_weight = getattr(config, 'markov_context_weight', 0.8)

        # Vérifier et clamper context_weight
        context_weight = max(0.0, min(1.0, context_weight))

        # Récupérer le facteur de décroissance depuis les paramètres ou la configuration
        if decay_factor is None:
            # Récupérer la valeur depuis la configuration si disponible, sinon utiliser 0.95 comme valeur par défaut
            from config import PredictorConfig
            config = PredictorConfig()
            decay_factor = getattr(config, 'markov_decay_factor', 0.95)

        # Vérifier et clamper decay_factor
        decay_factor = max(0.0, min(1.0, decay_factor))

        # Analyser le contexte de la séquence
        context_factor = self._analyze_sequence_context(sequence)

        # Appliquer le poids du contexte configuré
        context_factor = context_factor * context_weight

        with self.lock:
            # Utiliser self.max_order_int pour l'itération (déjà un entier)

            # Itérer sur les ordres de 1 à self.max_order_int
            for order in range(1, self.max_order_int + 1):
                # Vérifier que la séquence est suffisamment longue pour cet ordre
                if len(sequence) >= order:
                    # Vérifier que l'ordre est disponible dans les modèles
                    if order >= len(self.global_models) or order >= len(self.session_models):
                        logger.warning(f"get_combined_probs: Ordre {order} non disponible (max global: {len(self.global_models)-1}, max session: {len(self.session_models)-1}). Ignoré.")
                        continue

                    try:
                        state = tuple(sequence[-order:])

                        # --- Calcul des probabilités globales avec lissage ---
                        global_counts = self.global_models[order].get(state, defaultdict(int))
                        global_total = sum(global_counts.values())
                    except IndexError as e:
                        logger.warning(f"get_combined_probs: Erreur d'index pour l'ordre {order}: {e}")
                        continue  # Passer à l'ordre suivant
                    denominator_global = global_total + self.smoothing * num_classes
                    global_p_player = (global_counts.get('player', 0) + self.smoothing) / denominator_global
                    global_p_banker = (global_counts.get('banker', 0) + self.smoothing) / denominator_global
                    norm_factor_g = global_p_player + global_p_banker
                    if norm_factor_g > 1e-9:
                        global_p_player /= norm_factor_g
                        global_p_banker /= norm_factor_g

                    # --- Calcul des probabilités de session avec lissage ---
                    session_counts = self.session_models[order].get(state, defaultdict(int))
                    session_total = sum(session_counts.values())
                    denominator_session = session_total + self.smoothing * num_classes
                    session_p_player = (session_counts.get('player', 0) + self.smoothing) / denominator_session
                    session_p_banker = (session_counts.get('banker', 0) + self.smoothing) / denominator_session
                    norm_factor_s = session_p_player + session_p_banker
                    if norm_factor_s > 1e-9:
                        session_p_player /= norm_factor_s
                        session_p_banker /= norm_factor_s

                    # --- Combinaison pondérée (global vs session) pour cet ordre ---
                    # Adapter les poids en fonction du facteur contextuel et du facteur de décroissance
                    # Appliquer le facteur de décroissance pour les ordres plus élevés
                    order_decay = decay_factor ** (order - 1)

                    # Adapter le poids global en fonction du contexte et du facteur de décroissance
                    adaptive_global_weight = global_weight * (1 - context_factor) * order_decay
                    adaptive_session_weight = 1.0 - adaptive_global_weight

                    combined_p_player = adaptive_global_weight * global_p_player + adaptive_session_weight * session_p_player
                    combined_p_banker = adaptive_global_weight * global_p_banker + adaptive_session_weight * session_p_banker
                    norm_factor_c = combined_p_player + combined_p_banker
                    if norm_factor_c > 1e-9:
                        combined_p_player /= norm_factor_c
                        combined_p_banker = 1.0 - combined_p_player

                    # --- Pondération par l'ordre et Accumulation ---
                    order_w = norm_order_weights.get(order, 0)
                    if order_w > 0:
                        # Adaptation dynamique du poids de l'ordre en fonction du contexte
                        adaptive_order_weight = order_w

                        # Récupérer les paramètres depuis la configuration
                        from config import PredictorConfig
                        config = PredictorConfig()
                        short_order_threshold = getattr(config, 'markov_short_order_threshold', 2)
                        high_volatility_threshold = getattr(config, 'markov_high_volatility_threshold', 0.5)
                        short_order_boost_factor = getattr(config, 'markov_short_order_boost_factor', 0.5)
                        long_order_threshold = getattr(config, 'markov_long_order_threshold', 3)
                        low_volatility_threshold = getattr(config, 'markov_low_volatility_threshold', 0.5)
                        long_order_boost_factor = getattr(config, 'markov_long_order_boost_factor', 0.5)

                        if order <= short_order_threshold and context_factor > high_volatility_threshold:
                            # Donner plus de poids aux ordres courts en cas de forte volatilité
                            adaptive_order_weight *= (1 + context_factor * short_order_boost_factor)
                        elif order >= long_order_threshold and context_factor < low_volatility_threshold:
                            # Donner plus de poids aux ordres longs en cas de faible volatilité
                            adaptive_order_weight *= (1 + (1 - context_factor) * long_order_boost_factor)

                        final_probs['player'] += combined_p_player * adaptive_order_weight
                        final_probs['banker'] += combined_p_banker * adaptive_order_weight
                        accumulated_weight += adaptive_order_weight

        # --- Normalisation Finale et Gestion Poids Manquants ---
        if accumulated_weight > 1e-9:
            missing_weight = max(0.0, 1.0 - accumulated_weight)
            final_probs['player'] = final_probs['player'] + (0.5 * missing_weight)
            final_probs['banker'] = final_probs['banker'] + (0.5 * missing_weight)

            total_final = final_probs['player'] + final_probs['banker']
            if total_final > 1e-9:
                final_probs['player'] /= total_final
                final_probs['banker'] /= total_final
            else:
                final_probs = {'player': 0.5, 'banker': 0.5}
        else:
            final_probs = {'player': 0.5, 'banker': 0.5}

        # Clip final par sécurité
        final_probs['player'] = max(0.0, min(1.0, final_probs['player']))
        final_probs['banker'] = 1.0 - final_probs['player']

        return final_probs