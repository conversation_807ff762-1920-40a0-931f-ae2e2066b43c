# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1164 à 1173
# Type: Méthode de la classe TrainOptimizeInterface

    def set_hyperparameters(self, model_name: str, hyperparameters: Dict[str, Any]) -> None:
        """
        Définit les hyperparamètres pour un modèle.

        Args:
            model_name: Nom du modèle
            hyperparameters: Dictionnaire des hyperparamètres
        """
        self.hyperparameters[model_name] = hyperparameters
        logger.debug(f"Hyperparamètres définis pour le modèle '{model_name}'")