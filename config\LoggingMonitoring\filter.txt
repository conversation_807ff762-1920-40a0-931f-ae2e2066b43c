# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\config.py
# Lignes: 68 à 118
# Type: Méthode de la classe SilentOptunaFilter

    def filter(self, record):
        # Vérifier si le thread actuel est un thread Optuna
        is_optuna_thread = "OptunaOptimization" in threading.current_thread().name

        # Vérifier si le message est répétitif
        message = record.getMessage()
        is_repetitive = any(pattern in message for pattern in self.repetitive_patterns)

        # Corriger le niveau de log pour les messages DIAGNOSTIC qui utilisent CRITICAL incorrectement
        if "DIAGNOSTIC" in message and record.levelno == logging.CRITICAL:
            # Rétrograder au niveau INFO pour éviter de déclencher des alertes
            record.levelno = logging.INFO
            record.levelname = "INFO"

        # Si c'est un message répétitif, le compter et ne l'afficher que la première fois
        if is_repetitive:
            if message not in self.repetitive_counts:
                self.repetitive_counts[message] = 1
                # Afficher le message la première fois avec une note
                if record.levelno >= logging.WARNING:
                    record.msg = f"{record.msg} (messages similaires seront filtrés)"
                # Ne pas afficher les messages de diagnostic même la première fois s'ils sont trop verbeux
                if any(verbose_pattern in message for verbose_pattern in [
                    "DIAGNOSTIC TRAIN:", "DIAGNOSTIC EVAL", "Gradient norm élevé:",
                    "Incertitude détaillée:", "Poids: confidence_weight="
                ]):
                    return False
                return True
            else:
                self.repetitive_counts[message] += 1
                # Ne pas afficher les messages répétitifs
                return False

        # Toujours afficher les logs de niveau WARNING et ERROR s'ils ne sont pas répétitifs
        if record.levelno >= logging.WARNING:
            # Filtrer certains messages WARNING qui sont normaux et attendus
            if "Erreur lors de l'optimisation de la mémoire PyTorch" in message:
                return False
            return True

        # Toujours afficher les logs contenant des mots-clés importants
        important_keywords = [
            "VIABLE", "ESSAI", "OPTIMISATION", "WAIT", "NON-WAIT", "VAGUE", "Progression",
            "Epoch", "Val Loss", "Val Accuracy", "Train Loss", "Train Accuracy",
            "Objectif 1", "Objectif 2", "Score composite", "Early stopping"
        ]
        if any(keyword in message for keyword in important_keywords):
            return True

        # Supprimer les autres logs des threads Optuna
        return not is_optuna_thread