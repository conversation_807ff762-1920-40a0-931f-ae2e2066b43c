# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 179 à 202
# Type: Méthode de la classe OptunaMessageFilter

    def filter(self, record):
        if not hasattr(record, 'msg'):
            return True

        # Si msg est un objet, le convertir en chaîne
        if not isinstance(record.msg, str):
            try:
                record.msg = str(record.msg)
            except:
                return True

        # Vérifier si le message concerne un essai terminé
        if "Trial" in record.msg and "finished with value:" in record.msg and "and parameters:" in record.msg:
            # Extraire uniquement la partie du message avant "and parameters:"
            record.msg = record.msg.split(" and parameters:")[0]
            return True

        # Vérifier également avec regex pour plus de robustesse
        match = self.trial_pattern.match(record.msg)
        if match:
            # Remplacer le message pour n'afficher que le score, sans les paramètres
            record.msg = match.group(1)

        return True