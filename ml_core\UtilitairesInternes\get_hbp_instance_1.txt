# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1308 à 1319
# Type: Méthode
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

def get_hbp_instance(trial_id=None, config=None):
    """
    Importe et crée une instance de HybridBaccaratPredictor de manière conditionnelle.

    Args:
        trial_id: Identifiant de l'essai Optuna (optionnel)
        config: Configuration à utiliser pour l'instance HBP (optionnel)

    Returns:
        HybridBaccaratPredictor: Une instance de HybridBaccaratPredictor
    """
    return ModelProvider.get_hbp_instance(trial_id, config)