# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\utils.py
# Lignes: 3833 à 3895
# Type: Méthode de la classe WaitPlacementOptimizer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, config=None):
        """
        Initialise l'optimiseur de placement des WAIT.

        Args:
            config (PredictorConfig, optional): Configuration du prédicteur.
        """
        # Dictionnaire pour stocker les patterns d'erreur
        self.error_patterns = {}

        # Dictionnaire pour stocker les patterns de transition
        self.transition_patterns = {}

        # Historique des recommandations et résultats
        self.recommendation_history = []
        self.prediction_history = []
        self.outcome_history = []
        self.confidence_history = []
        self.uncertainty_history = []
        self.wait_efficiency_history = []

        # Compteurs pour le suivi des performances
        self.total_decisions = 0
        self.total_waits = 0
        self.effective_waits = 0
        self.missed_opportunities = 0
        self.correct_wait_decisions = 0
        self.correct_non_wait_decisions = 0
        self.current_consecutive_valid = 0
        self.max_consecutive_valid = 0
        self.current_wait_ratio = 0.0

        # Récupérer les paramètres depuis la configuration
        if config is None:
            # Valeurs par défaut si aucune configuration n'est fournie
            self.max_history_size = 100
            self.error_pattern_threshold = 0.6
            self.transition_uncertainty_threshold = 0.6
            self.confidence_threshold = 0.7
            self.uncertainty_threshold = 0.4
            self.wait_ratio_min = 0.3
            self.wait_ratio_max = 0.5
            self.learning_rate = 0.02
            self.adaptive_thresholds = True
            self.target_round_min = 31
            self.target_round_max = 60
        else:
            # Récupérer tous les paramètres depuis la configuration
            self.max_history_size = getattr(config, 'wait_optimizer_max_history', 100)
            self.error_pattern_threshold = getattr(config, 'wait_optimizer_error_threshold', 0.6)
            self.transition_uncertainty_threshold = getattr(config, 'wait_optimizer_transition_threshold', 0.6)
            self.confidence_threshold = getattr(config, 'wait_optimizer_confidence_threshold', 0.7)
            self.uncertainty_threshold = getattr(config, 'wait_optimizer_uncertainty_threshold', 0.4)
            self.wait_ratio_min = getattr(config, 'wait_ratio_min_threshold', 0.3)
            self.wait_ratio_max = getattr(config, 'wait_ratio_max_threshold', 0.5)
            self.learning_rate = getattr(config, 'wait_optimizer_learning_rate', 0.02)
            self.adaptive_thresholds = getattr(config, 'wait_optimizer_adaptive_thresholds', True)
            self.target_round_min = getattr(config, 'target_round_min', 31)
            self.target_round_max = getattr(config, 'target_round_max', 60)

        # Initialiser le logger
        self.logger = logging.getLogger(__name__)
        self.logger.info("WaitPlacementOptimizer initialisé avec succès.")