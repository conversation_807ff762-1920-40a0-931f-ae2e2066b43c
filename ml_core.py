"""
Module central pour le machine learning.
Ce module centralise toutes les fonctionnalités communes utilisées à la fois dans l'entraînement et l'optimisation:
1. La gestion de la mémoire PyTorch (optimisation et nettoyage)
2. Le calcul de confiance et d'incertitude des prédictions
3. Des fonctions utilitaires pour obtenir des instances de modèles
4. La gestion des dépendances entre les modules
5. La configuration et la journalisation

Ce module remplace et étend les fonctionnalités de trainops.py.
"""

import os
import gc
import logging
import multiprocessing
import json
import importlib.util
import abc
import time
import threading
from datetime import datetime
import numpy as np
from typing import List, Dict, Optional, Any, Union, Callable, Type, Tuple

import torch

# Configuration du logger
logger = logging.getLogger(__name__)

#############################################
# GESTIONNAIRE DE CONFIGURATION
#############################################

class ConfigManager:
    """
    Gestionnaire de configuration centralisé.
    Permet de charger la configuration depuis différentes sources et d'y accéder de manière cohérente.
    """

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ConfigManager, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        """Initialise le gestionnaire de configuration."""
        self._config = {}
        self._config_sources = {}
        self._validators = {}
        self._default_values = {}

    def load_from_python_file(self, file_path: str, namespace: str = 'default') -> bool:
        """
        Charge la configuration depuis un fichier Python.

        Args:
            file_path: Chemin vers le fichier Python
            namespace: Espace de noms pour la configuration

        Returns:
            True si le chargement a réussi, False sinon
        """
        try:
            # Charger le module Python
            spec = importlib.util.spec_from_file_location("config_module", file_path)
            config_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(config_module)

            # Extraire les attributs publics (non commençant par _)
            config_dict = {
                key: value for key, value in vars(config_module).items()
                if not key.startswith('_') and not callable(value)
            }

            # Stocker la configuration
            self._config[namespace] = config_dict
            self._config_sources[namespace] = file_path

            logger.info(f"Configuration chargée depuis {file_path} dans l'espace de noms '{namespace}'")
            return True

        except Exception as e:
            logger.error(f"Erreur lors du chargement de la configuration depuis {file_path}: {e}")
            return False

    def get(self, key: str, default: Any = None, namespace: str = 'default') -> Any:
        """
        Récupère une valeur de configuration.

        Args:
            key: Clé de configuration
            default: Valeur par défaut si la clé n'existe pas
            namespace: Espace de noms pour la configuration

        Returns:
            La valeur de configuration ou la valeur par défaut
        """
        if namespace not in self._config:
            return default

        return self._config[namespace].get(key, default)

    def set(self, key: str, value: Any, namespace: str = 'default') -> None:
        """
        Définit une valeur de configuration.

        Args:
            key: Clé de configuration
            value: Valeur de configuration
            namespace: Espace de noms pour la configuration
        """
        if namespace not in self._config:
            self._config[namespace] = {}

        self._config[namespace][key] = value

        logger.debug(f"Configuration '{key}' définie à '{value}' dans l'espace de noms '{namespace}'")

# Créer une instance singleton
config_manager = ConfigManager()

#############################################
# GESTIONNAIRE DE JOURNALISATION
#############################################

class LoggingManager:
    """
    Gestionnaire de journalisation centralisé.
    Permet de configurer et d'accéder aux loggers de manière cohérente.
    """

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(LoggingManager, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        """Initialise le gestionnaire de journalisation."""
        self._loggers = {}
        self._handlers = {}
        self._formatters = {}
        self._log_directory = 'logs'

        # Créer le répertoire de logs s'il n'existe pas
        os.makedirs(self._log_directory, exist_ok=True)

    def configure_root_logger(self, level: int = logging.INFO, console: bool = True, file: bool = True) -> None:
        """
        Configure le logger racine.

        Args:
            level: Niveau de journalisation
            console: Si True, ajoute un handler pour la console
            file: Si True, ajoute un handler pour un fichier
        """
        # Configurer le logger racine
        root_logger = logging.getLogger()
        root_logger.setLevel(level)

        # Supprimer les handlers existants
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # Créer un formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        self._formatters['default'] = formatter

        # Ajouter un handler pour la console
        if console:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
            self._handlers['console'] = console_handler

        # Ajouter un handler pour un fichier
        if file:
            log_file = os.path.join(self._log_directory, f'app_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
            self._handlers['file'] = file_handler

        self._loggers['root'] = root_logger

        root_logger.info(f"Logger racine configuré avec niveau {logging.getLevelName(level)}")

    def get_logger(self, name: str, level: Optional[int] = None) -> logging.Logger:
        """
        Récupère un logger avec le nom donné.

        Args:
            name: Nom du logger
            level: Niveau de journalisation (si None, utilise le niveau du logger parent)

        Returns:
            Le logger
        """
        logger = logging.getLogger(name)

        if level is not None:
            logger.setLevel(level)

        self._loggers[name] = logger

        return logger

# Créer une instance singleton
logging_manager = LoggingManager()

#############################################
# GESTIONNAIRE DE DÉPENDANCES
#############################################

class ModuleInterface:
    """
    Classe singleton qui gère les dépendances entre les modules.
    Permet d'enregistrer et de récupérer des fonctions, des classes et des instances
    sans avoir à les importer directement.
    """

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ModuleInterface, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        """Initialise les dictionnaires de dépendances."""
        self._functions = {}
        self._classes = {}
        self._instances = {}
        self._factories = {}
        self._lazy_loaders = {}

    def register_function(self, name: str, function: Callable) -> None:
        """
        Enregistre une fonction dans l'interface.

        Args:
            name: Nom unique de la fonction
            function: Fonction à enregistrer
        """
        self._functions[name] = function
        logger.debug(f"Fonction '{name}' enregistrée dans l'interface")

    def get_function(self, name: str) -> Callable:
        """
        Récupère une fonction enregistrée dans l'interface.

        Args:
            name: Nom de la fonction à récupérer

        Returns:
            La fonction enregistrée

        Raises:
            KeyError: Si la fonction n'est pas enregistrée
        """
        if name not in self._functions:
            if name in self._lazy_loaders:
                self._lazy_loaders[name]()  # Exécuter le chargeur paresseux

            if name not in self._functions:
                raise KeyError(f"Fonction '{name}' non enregistrée dans l'interface")

        return self._functions[name]

    def register_class(self, name: str, cls: Type) -> None:
        """
        Enregistre une classe dans l'interface.

        Args:
            name: Nom unique de la classe
            cls: Classe à enregistrer
        """
        self._classes[name] = cls
        logger.debug(f"Classe '{name}' enregistrée dans l'interface")

    def get_class(self, name: str) -> Type:
        """
        Récupère une classe enregistrée dans l'interface.

        Args:
            name: Nom de la classe à récupérer

        Returns:
            La classe enregistrée

        Raises:
            KeyError: Si la classe n'est pas enregistrée
        """
        if name not in self._classes:
            if name in self._lazy_loaders:
                self._lazy_loaders[name]()  # Exécuter le chargeur paresseux

            if name not in self._classes:
                raise KeyError(f"Classe '{name}' non enregistrée dans l'interface")

        return self._classes[name]

    def register_instance(self, name: str, instance: Any) -> None:
        """
        Enregistre une instance dans l'interface.

        Args:
            name: Nom unique de l'instance
            instance: Instance à enregistrer
        """
        self._instances[name] = instance
        logger.debug(f"Instance '{name}' enregistrée dans l'interface")

    def get_instance(self, name: str) -> Any:
        """
        Récupère une instance enregistrée dans l'interface.

        Args:
            name: Nom de l'instance à récupérer

        Returns:
            L'instance enregistrée

        Raises:
            KeyError: Si l'instance n'est pas enregistrée
        """
        if name not in self._instances:
            if name in self._lazy_loaders:
                self._lazy_loaders[name]()  # Exécuter le chargeur paresseux

            if name not in self._instances:
                raise KeyError(f"Instance '{name}' non enregistrée dans l'interface")

        return self._instances[name]

    def register_lazy_loader(self, name: str, loader: Callable) -> None:
        """
        Enregistre un chargeur paresseux qui sera exécuté uniquement lorsque la dépendance sera demandée.

        Args:
            name: Nom de la dépendance à charger paresseusement
            loader: Fonction qui charge la dépendance
        """
        self._lazy_loaders[name] = loader
        logger.debug(f"Chargeur paresseux pour '{name}' enregistré dans l'interface")

# Créer une instance singleton
interface = ModuleInterface()

#############################################
# GESTIONNAIRE DE MÉMOIRE
#############################################

class MemoryManager:
    """
    Classe responsable de la gestion de la mémoire pour les modèles PyTorch.
    Centralise toutes les opérations liées à l'optimisation et au nettoyage de la mémoire.
    """

    @staticmethod
    def optimize_pytorch_memory():
        """
        Configure PyTorch pour utiliser la mémoire disponible de manière optimale.
        Cette fonction doit être appelée au début du programme.
        """
        logger.info("Optimisation de la mémoire PyTorch...")

        # 0. Désactiver la compilation JIT (Just-In-Time) pour éviter l'erreur de compilateur
        os.environ['PYTORCH_JIT'] = '0'
        os.environ['TORCH_COMPILE_DISABLE'] = '1'

        # Désactiver les optimisations qui nécessitent un compilateur C++
        if hasattr(torch._C, '_jit_set_profiling_executor'):
            torch._C._jit_set_profiling_executor(False)
        if hasattr(torch._C, '_jit_set_profiling_mode'):
            torch._C._jit_set_profiling_mode(False)

        # 1. Activer la libération de mémoire inutilisée
        if hasattr(torch.cuda, 'empty_cache'):
            torch.cuda.empty_cache()

        # 2. Désactiver le garbage collector pendant l'entraînement pour éviter les pauses
        gc.disable()

        # 3. Configurer PyTorch pour utiliser la mémoire de manière plus agressive
        # Cette variable d'environnement permet à PyTorch d'allouer plus de mémoire
        os.environ['PYTORCH_NO_CUDA_MEMORY_CACHING'] = '1'

        # 4. Configurer PyTorch pour utiliser le mode de mémoire partagée
        # Cela permet une meilleure utilisation de la mémoire entre les processus
        torch.multiprocessing.set_sharing_strategy('file_system')

        # 5. Configurer PyTorch pour utiliser la mémoire de manière plus efficace
        # Cela permet d'éviter les erreurs de mémoire en utilisant un algorithme plus efficace
        if hasattr(torch, 'backends') and hasattr(torch.backends, 'cudnn'):
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False

        # 6. Configurer PyTorch pour utiliser la mémoire de manière plus agressive
        # Cela permet d'éviter les erreurs de mémoire en utilisant un algorithme plus agressif
        if hasattr(torch, 'set_flush_denormal'):
            torch.set_flush_denormal(True)

        # 7. Configurer PyTorch pour utiliser la mémoire de manière plus efficace
        # Cela permet d'éviter les erreurs de mémoire en utilisant un algorithme plus efficace
        if hasattr(torch, 'set_num_threads'):
            # Utiliser 80% des cœurs disponibles
            num_cores = multiprocessing.cpu_count()
            torch.set_num_threads(max(1, int(num_cores * 0.8)))

        # 8. Configurer PyTorch pour utiliser la mémoire de manière plus efficace
        # Cela permet d'éviter les erreurs de mémoire en utilisant un algorithme plus efficace
        if hasattr(torch, 'set_num_interop_threads'):
            try:
                # Vérifier si nous pouvons définir le nombre de threads d'interopérabilité
                # Cette opération doit être effectuée avant toute opération parallèle
                # Utiliser 80% des cœurs disponibles
                num_cores = multiprocessing.cpu_count()
                torch.set_num_interop_threads(max(1, int(num_cores * 0.8)))
            except RuntimeError as e:
                # Ignorer l'erreur si les threads d'interopérabilité ont déjà été définis
                # ou si un travail parallèle a déjà commencé
                # C'est un avertissement normal et attendu si PyTorch a déjà commencé des opérations parallèles
                logger.debug(f"Impossible de définir le nombre de threads d'interopérabilité: {e}")
                # Ne pas lever d'exception, continuer l'exécution

        # Afficher le nombre de threads configurés
        logger.info(f"Configuration PyTorch: num_threads={torch.get_num_threads()}")

        # Afficher un message pour indiquer que l'optimisation est terminée
        logger.info("Optimisation de la mémoire PyTorch terminée.")

        # Ajouter un message pour expliquer que le message d'erreur sur les threads d'interopérabilité est normal
        logger.debug("Note: Un message 'Impossible de définir le nombre de threads d'interopérabilité' est normal si PyTorch a déjà commencé des opérations parallèles.")

        return True

    @staticmethod
    def cleanup_pytorch_memory():
        """
        Nettoie la mémoire PyTorch après utilisation.
        Cette fonction doit être appelée à la fin de chaque époque d'entraînement.
        """
        # 1. Forcer la libération de la mémoire inutilisée
        if hasattr(torch.cuda, 'empty_cache'):
            torch.cuda.empty_cache()

        # 2. Réactiver le garbage collector et forcer une collection
        gc.enable()
        gc.collect()

        # 3. Libérer la mémoire des tenseurs inutilisés
        for obj in gc.get_objects():
            try:
                if torch.is_tensor(obj):
                    obj.detach_()
            except:
                pass

        return True

    @staticmethod
    def optimize_lstm_memory(lstm_model, training_mode=False):
        """
        Optimise la mémoire utilisée par un modèle LSTM.

        Args:
            lstm_model: Le modèle LSTM à optimiser
            training_mode: Si True, le modèle est en mode entraînement et les gradients sont conservés

        Returns:
            Le modèle LSTM optimisé
        """
        try:
            # 1. Configurer le mode du modèle en fonction du contexte
            if not training_mode:
                # En mode inférence, mettre en mode évaluation
                lstm_model.eval()

                # Désactiver le calcul des gradients pour économiser de la mémoire en mode inférence
                for param in lstm_model.parameters():
                    param.requires_grad = False
            else:
                # En mode entraînement, mettre en mode train
                lstm_model.train()

                # S'assurer que les gradients sont activés pour l'entraînement
                for param in lstm_model.parameters():
                    param.requires_grad = True

            # 2. Optimiser les paramètres LSTM si possible (pour les deux modes)
            for module in lstm_model.modules():
                if hasattr(module, 'flatten_parameters') and callable(module.flatten_parameters):
                    module.flatten_parameters()

            logger.info(f"Modèle LSTM optimisé pour la mémoire (mode {'entraînement' if training_mode else 'inférence'})")
        except Exception as e:
            logger.warning(f"Impossible d'optimiser le modèle LSTM: {e}")

        return lstm_model

    @classmethod
    def register_memory_hooks(cls, model):
        """
        Enregistre des hooks sur le modèle pour surveiller et optimiser l'utilisation de la mémoire.
        Nouvelle fonctionnalité pour améliorer la gestion de la mémoire.
        """
        def hook_fn(module, input, output):
            # Libérer la mémoire des tenseurs intermédiaires après utilisation
            if hasattr(output, 'detach'):
                output.detach_()

        # Enregistrer le hook sur tous les modules du modèle
        for module in model.modules():
            module.register_forward_hook(hook_fn)

        return model

class PyTorchMemoryContext:
    """
    Gestionnaire de contexte pour optimiser et nettoyer automatiquement la mémoire PyTorch.
    Utilisation:

    ```python
    with PyTorchMemoryContext():
        # Code utilisant PyTorch
        model = create_model()
        output = model(input_data)
    # La mémoire est automatiquement nettoyée à la sortie du bloc with
    ```
    """

    def __init__(self, optimize_on_enter=True, cleanup_on_exit=True):
        """
        Initialise le gestionnaire de contexte.

        Args:
            optimize_on_enter: Si True, optimise la mémoire à l'entrée du contexte
            cleanup_on_exit: Si True, nettoie la mémoire à la sortie du contexte
        """
        self.optimize_on_enter = optimize_on_enter
        self.cleanup_on_exit = cleanup_on_exit

    def __enter__(self):
        """
        Appelé à l'entrée du bloc with.
        Optimise la mémoire PyTorch si optimize_on_enter est True.
        """
        if self.optimize_on_enter:
            MemoryManager.optimize_pytorch_memory()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        Appelé à la sortie du bloc with.
        Nettoie la mémoire PyTorch si cleanup_on_exit est True.
        """
        if self.cleanup_on_exit:
            MemoryManager.cleanup_pytorch_memory()
        return False  # Ne pas supprimer l'exception si elle existe

class LSTMMemoryContext:
    """
    Gestionnaire de contexte pour optimiser la mémoire d'un modèle LSTM.
    Bascule automatiquement entre les modes entraînement et inférence.

    Utilisation:

    ```python
    # Pour l'entraînement
    with LSTMMemoryContext(model, training=True):
        # Code d'entraînement du modèle
        output = model(input_data)
        loss = criterion(output, target)
        loss.backward()
        optimizer.step()

    # Pour l'inférence
    with LSTMMemoryContext(model, training=False):
        # Code d'inférence
        output = model(input_data)
    ```
    """

    def __init__(self, model, training=False):
        """
        Initialise le gestionnaire de contexte.

        Args:
            model: Le modèle LSTM à optimiser
            training: Si True, configure le modèle pour l'entraînement, sinon pour l'inférence
        """
        self.model = model
        self.training = training
        self.previous_training_state = None

    def __enter__(self):
        """
        Appelé à l'entrée du bloc with.
        Optimise la mémoire du modèle LSTM et configure son mode.
        """
        # Sauvegarder l'état d'entraînement précédent
        self.previous_training_state = self.model.training

        # Optimiser la mémoire du modèle LSTM
        MemoryManager.optimize_lstm_memory(self.model, training_mode=self.training)

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        Appelé à la sortie du bloc with.
        Restaure l'état d'entraînement précédent du modèle.
        """
        # Restaurer l'état d'entraînement précédent
        if self.previous_training_state is not None:
            if self.previous_training_state:
                self.model.train()
            else:
                self.model.eval()

        # Nettoyer la mémoire PyTorch
        MemoryManager.cleanup_pytorch_memory()

        return False  # Ne pas supprimer l'exception si elle existe

#############################################
# GESTIONNAIRE DE MODÈLES
#############################################

class ModelProvider:
    """
    Classe responsable de fournir des instances de modèles et des fonctions associées.
    Évite les importations circulaires et centralise la création d'instances.
    """

    # Variable de classe pour stocker l'instance unique
    _hbp_instance = None
    _hbp_instance_lock = threading.Lock()

    @classmethod
    def get_hbp_instance(cls, trial_id=None, config=None):
        """
        Implémentation du pattern Singleton pour HybridBaccaratPredictor.
        Retourne l'instance existante ou en crée une nouvelle si nécessaire.
        Désactive l'auto-update pour garantir la stabilité pendant l'optimisation.
        Initialise également un collecteur de statistiques pour réduire les logs excessifs.

        Args:
            trial_id: Identifiant de l'essai Optuna (optionnel)
            config: Configuration à utiliser pour l'instance HBP (optionnel)

        Returns:
            HybridBaccaratPredictor: L'instance unique de HybridBaccaratPredictor
        """
        # Vérifier si l'instance existe déjà
        if cls._hbp_instance is not None:
            # Si une configuration est fournie et différente de celle actuelle, mettre à jour la configuration
            if config is not None and hasattr(cls._hbp_instance, 'config') and cls._hbp_instance.config != config:
                logger.info("Mise à jour de la configuration de l'instance HBP existante")
                # Mettre à jour uniquement les attributs qui existent dans les deux configurations
                for attr_name in dir(config):
                    if not attr_name.startswith('_') and hasattr(cls._hbp_instance.config, attr_name):
                        setattr(cls._hbp_instance.config, attr_name, getattr(config, attr_name))

            # Retourner l'instance existante
            return cls._hbp_instance

        # Utiliser un verrou pour éviter les problèmes de concurrence
        with cls._hbp_instance_lock:
            # Vérifier à nouveau si l'instance a été créée pendant l'acquisition du verrou
            if cls._hbp_instance is not None:
                return cls._hbp_instance

            # Importer HybridBaccaratPredictor uniquement lorsque cette fonction est appelée
            from hbp import HybridBaccaratPredictor

            # Créer une instance avec la configuration fournie ou une configuration minimale
            if config is None:
                from config import PredictorConfig
                config = PredictorConfig()

            # Créer l'instance HBP avec la configuration
            temp_hbp = HybridBaccaratPredictor(config)
            logger.info(f"Instance HBP créée avec configuration {'fournie' if config is not None else 'par défaut'}")

            # Désactiver l'auto-update pour garantir la stabilité pendant l'optimisation
            if hasattr(temp_hbp, 'auto_update_enabled') and temp_hbp.auto_update_enabled is not None:
                temp_hbp.auto_update_enabled.set(False)
                logger.info("Auto-update désactivé pour l'instance HBP utilisée pendant l'optimisation")

            # Initialiser le collecteur de statistiques si disponible
            try:
                from optuna_optimizer import OptimizationStatsCollector
                stats_collector = OptimizationStatsCollector(trial_id=trial_id)
                temp_hbp.optimization_stats_collector = stats_collector
                logger.info(f"Collecteur de statistiques initialisé pour l'essai {trial_id}")
            except ImportError:
                logger.warning("OptimizationStatsCollector non disponible, collecteur de statistiques non initialisé")

            # Définir le flag pour indiquer que nous sommes en phase d'optimisation Optuna
            temp_hbp.is_optuna_running = True
            logger.info("Flag is_optuna_running défini à True pour l'instance HBP")

            # Initialiser la séquence avec une liste vide pour éviter les erreurs
            if not hasattr(temp_hbp, 'sequence') or temp_hbp.sequence is None:
                temp_hbp.sequence = []
                logger.info("Séquence initialisée avec une liste vide pour éviter les erreurs")

            # Initialiser le modèle Markov si nécessaire avec les paramètres corrects
            if hasattr(temp_hbp, 'markov') and temp_hbp.markov is None:
                from models import PersistentMarkov
                # Récupérer les paramètres de configuration pour Markov
                markov_max_order = temp_hbp.config.max_markov_order

                # Garantir une valeur minimale de 1.0 et maximale de 8.0
                markov_max_order = max(1.0, min(8.0, markov_max_order))

                markov_smoothing = temp_hbp.config.markov_smoothing

                # Initialiser le modèle Markov avec les paramètres corrects
                try:
                    # Utiliser directement la valeur flottante de max_order
                    temp_hbp.markov = PersistentMarkov(max_order=markov_max_order, smoothing=markov_smoothing)
                    logger.info(f"Modèle Markov initialisé pour l'instance HBP avec max_order={markov_max_order}, smoothing={markov_smoothing}")
                except Exception as e:
                    logger.error(f"Erreur lors de l'initialisation du modèle Markov: {e}")
                    # Laisser le modèle Markov à None pour qu'il soit exclu des prédictions
                    temp_hbp.markov = None
                    # Désactiver l'utilisation du modèle Markov dans la configuration
                    if hasattr(temp_hbp.config, 'use_markov_model'):
                        temp_hbp.config.use_markov_model = False
                        logger.warning("Modèle Markov désactivé (use_markov_model=False) en raison d'une erreur d'initialisation")

            # Stocker l'instance dans la variable de classe
            cls._hbp_instance = temp_hbp
            logger.info("Instance HBP stockée comme singleton")

            return cls._hbp_instance

    # Variable de classe pour stocker la fonction calculate_uncertainty
    _calculate_uncertainty_func = None

    @classmethod
    def get_calculate_uncertainty(cls):
        """
        Importe la méthode calculate_uncertainty depuis hbp.py de manière conditionnelle
        pour éviter les importations circulaires.
        Utilise un cache pour éviter de recréer l'instance HBP à chaque appel.

        Returns:
            function: La méthode calculate_uncertainty
        """
        # Vérifier si la fonction est déjà en cache
        if cls._calculate_uncertainty_func is not None:
            return cls._calculate_uncertainty_func

        # Obtenir une instance de HybridBaccaratPredictor (utilise le singleton)
        temp_hbp = cls.get_hbp_instance()

        # Stocker la méthode calculate_uncertainty en cache
        cls._calculate_uncertainty_func = temp_hbp.calculate_uncertainty

        # Retourner la méthode calculate_uncertainty
        # Nous utilisons directement la méthode de l'instance HBP pour garantir
        # que nous utilisons exactement la même logique que dans la production
        return cls._calculate_uncertainty_func

    @staticmethod
    def create_model_factory(model_class, **default_params):
        """
        Crée une factory pour instancier des modèles avec des paramètres par défaut.
        Nouvelle fonctionnalité pour simplifier la création de modèles.
        """
        def factory(**kwargs):
            # Combiner les paramètres par défaut avec ceux fournis
            params = {**default_params, **kwargs}
            return model_class(**params)

        return factory

#############################################
# CALCULATEUR DE CONFIANCE
#############################################

class ConfidenceCalculator:
    """
    Classe responsable du calcul de confiance et d'incertitude pour les prédictions.
    Centralise toutes les opérations liées à l'évaluation de la confiance.
    """

    @staticmethod
    def get_confidence_from_probabilities(probabilities, predicted_class):
        """
        Obtient la confiance pour une classe prédite à partir des probabilités.
        Utilise directement les indices 0-based (0=Player, 1=Banker).

        Args:
            probabilities: Tenseur de probabilités (après softmax) ou liste/array de probabilités
            predicted_class: Classe prédite (indice 0-based, 0 ou 1)

        Returns:
            float: Confiance pour la classe prédite
        """
        # Vérifier que la classe prédite est bien un indice 0-based
        if predicted_class < 0 or predicted_class > 1:
            logger.warning(
                f"ATTENTION: Indice invalide détecté dans predicted_class: {predicted_class}. "
                f"Doit être 0 (Player) ou 1 (Banker)."
            )
            # Corriger l'indice si possible
            predicted_class = max(0, min(1, predicted_class))

        # Obtenir la confiance directement avec l'indice 0-based
        if hasattr(probabilities, 'dim') and probabilities.dim() > 1:
            # Cas d'un tenseur PyTorch 2D
            confidence = probabilities[0][predicted_class].item()
        elif hasattr(probabilities, 'item'):
            # Cas d'un tenseur PyTorch 1D
            confidence = probabilities[predicted_class].item()
        else:
            # Cas d'une liste ou d'un array numpy
            confidence = float(probabilities[predicted_class])

        return confidence

    @staticmethod
    def train_consecutive_confidence_calculator(sequence_history: List[str],
                                               current_position: int,
                                               config=None) -> Dict[str, float]:
        """
        Calcule la confiance pour les recommandations consécutives basée sur l'historique des séquences.

        Args:
            sequence_history: Historique des résultats (liste de 'banker' ou 'player')
            current_position: Position actuelle dans la séquence
            config: Configuration optionnelle

        Returns:
            Dict[str, float]: Dictionnaire contenant les métriques de confiance
        """
        # Vérifier que nous avons suffisamment d'historique
        if not sequence_history or current_position < 10:
            return {
                "confidence": 0.5,
                "expected_consecutive": 0,
                "similar_patterns_count": 0,
                "success_rate": 0.0,
                "is_target_round": False,
                "wait_recommendation_strength": 0.0,
                "non_wait_recommendation_strength": 0.0,
                "wait_recommendation": True,
                "wait_reason": "Historique insuffisant"
            }

        # Récupérer les paramètres de configuration
        target_round_min = getattr(config, 'target_round_min', 31) if config else 31
        target_round_max = getattr(config, 'target_round_max', 60) if config else 60

        # Vérifier si nous sommes dans les manches cibles
        game_round = current_position + 1  # +1 car les positions commencent à 0
        is_target_round = target_round_min <= game_round <= target_round_max

        # Si nous ne sommes pas dans les manches cibles, retourner une confiance neutre
        if not is_target_round:
            return {
                "confidence": 0.5,  # Confiance neutre hors manches cibles
                "expected_consecutive": 0,
                "similar_patterns_count": 0,
                "success_rate": 0.0,
                "is_target_round": is_target_round,
                "wait_recommendation_strength": 0.0,
                "non_wait_recommendation_strength": 0.0,
                "wait_recommendation": True,
                "wait_reason": "Hors manches cibles"
            }

        # Extraire les N derniers résultats pour l'analyse des motifs
        pattern_length = min(10, current_position)
        current_pattern = sequence_history[current_position - pattern_length:current_position]

        # Rechercher des motifs similaires dans l'historique
        similar_patterns = []
        for i in range(pattern_length, current_position - pattern_length):
            historical_pattern = sequence_history[i - pattern_length:i]

            # Calculer la similarité (nombre de correspondances)
            similarity = sum(1 for a, b in zip(current_pattern, historical_pattern) if a == b)

            # Si la similarité est suffisante (au moins 70%)
            if similarity >= pattern_length * 0.7:
                # Ajouter le résultat qui a suivi ce motif similaire
                similar_patterns.append(sequence_history[i])

        # Calculer les statistiques basées sur les motifs similaires
        similar_patterns_count = len(similar_patterns)

        if similar_patterns_count > 0:
            # Compter les occurrences de 'banker' et 'player'
            banker_count = sum(1 for result in similar_patterns if result == 'banker')
            player_count = similar_patterns_count - banker_count

            # Déterminer la prédiction majoritaire
            majority_prediction = 'banker' if banker_count >= player_count else 'player'

            # Calculer la confiance basée sur la proportion
            confidence = max(banker_count, player_count) / similar_patterns_count

            # Calculer le nombre attendu de recommandations consécutives valides
            # basé sur les séquences historiques similaires
            expected_consecutive = min(5, int(confidence * 5))

            # Calculer la force de la recommandation WAIT ou NON-WAIT
            wait_threshold = getattr(config, 'wait_threshold', 0.65) if config else 0.65

            wait_recommendation = confidence < wait_threshold
            wait_reason = f"Confiance {confidence:.2f} < seuil {wait_threshold:.2f}" if wait_recommendation else "Confiance suffisante"

            wait_recommendation_strength = max(0.0, wait_threshold - confidence) / wait_threshold
            non_wait_recommendation_strength = max(0.0, confidence - wait_threshold) / (1.0 - wait_threshold)

            return {
                "confidence": confidence,
                "expected_consecutive": expected_consecutive,
                "similar_patterns_count": similar_patterns_count,
                "success_rate": confidence,
                "is_target_round": is_target_round,
                "wait_recommendation_strength": wait_recommendation_strength,
                "non_wait_recommendation_strength": non_wait_recommendation_strength,
                "wait_recommendation": wait_recommendation,
                "wait_reason": wait_reason
            }
        else:
            # Pas assez de motifs similaires trouvés
            return {
                "confidence": 0.5,  # Confiance neutre
                "expected_consecutive": 0,
                "similar_patterns_count": 0,
                "success_rate": 0.0,
                "is_target_round": is_target_round,
                "wait_recommendation_strength": 1.0,  # Forte recommandation WAIT
                "non_wait_recommendation_strength": 0.0,
                "wait_recommendation": True,
                "wait_reason": "Pas de motifs similaires"
            }

    @staticmethod
    def calculate_calibrated_confidence(probabilities, calibration_method='isotonic'):
        """
        Calcule une confiance calibrée à partir des probabilités brutes.
        Nouvelle fonctionnalité pour améliorer la fiabilité des scores de confiance.

        Args:
            probabilities: Probabilités brutes (après softmax)
            calibration_method: Méthode de calibration ('isotonic' ou 'platt')

        Returns:
            Probabilités calibrées
        """
        # Cette méthode nécessiterait une implémentation plus complète avec sklearn
        # Pour l'instant, nous retournons simplement les probabilités d'origine
        logger.warning("Calibration de confiance non implémentée, retour des probabilités brutes")
        return probabilities

#############################################
# INTERFACE ENTRAÎNEMENT/OPTIMISATION
#############################################

class ModelInterface(abc.ABC):
    """
    Interface abstraite pour les modèles.
    Tous les modèles doivent implémenter cette interface pour être utilisables
    à la fois dans les phases d'entraînement et d'optimisation.
    """

    @abc.abstractmethod
    def fit(self, X, y, **kwargs):
        """
        Entraîne le modèle sur les données fournies.

        Args:
            X: Features d'entraînement
            y: Labels d'entraînement
            **kwargs: Arguments supplémentaires

        Returns:
            self
        """
        pass

    @abc.abstractmethod
    def predict(self, X, **kwargs):
        """
        Prédit les labels pour les features fournies.

        Args:
            X: Features
            **kwargs: Arguments supplémentaires

        Returns:
            Prédictions
        """
        pass

    @abc.abstractmethod
    def predict_proba(self, X, **kwargs):
        """
        Prédit les probabilités pour les features fournies.

        Args:
            X: Features
            **kwargs: Arguments supplémentaires

        Returns:
            Probabilités
        """
        pass

    @abc.abstractmethod
    def save(self, path, **kwargs):
        """
        Sauvegarde le modèle dans le chemin fourni.

        Args:
            path: Chemin de sauvegarde
            **kwargs: Arguments supplémentaires

        Returns:
            True si la sauvegarde a réussi, False sinon
        """
        pass

    @abc.abstractmethod
    def load(self, path, **kwargs):
        """
        Charge le modèle depuis le chemin fourni.

        Args:
            path: Chemin de chargement
            **kwargs: Arguments supplémentaires

        Returns:
            self
        """
        pass

class TrainOptimizeInterface:
    """
    Interface entre les phases d'entraînement et d'optimisation.
    Permet de partager des modèles, des données et des métriques entre les phases.
    """

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(TrainOptimizeInterface, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        """Initialise l'interface."""
        self.models = {}
        self.data_sources = {}
        self.metrics = {}
        self.preprocessors = {}
        self.hyperparameters = {}
        self.results = {}

    def register_model(self, name: str, model: Any) -> None:
        """
        Enregistre un modèle dans l'interface.

        Args:
            name: Nom unique du modèle
            model: Modèle à enregistrer
        """
        self.models[name] = model
        logger.debug(f"Modèle '{name}' enregistré dans l'interface")

    def get_model(self, name: str) -> Any:
        """
        Récupère un modèle enregistré dans l'interface.

        Args:
            name: Nom du modèle à récupérer

        Returns:
            Le modèle enregistré

        Raises:
            KeyError: Si le modèle n'est pas enregistré
        """
        if name not in self.models:
            raise KeyError(f"Modèle '{name}' non enregistré dans l'interface")

        return self.models[name]

    def register_data_source(self, name: str, data_source: Any) -> None:
        """
        Enregistre une source de données dans l'interface.

        Args:
            name: Nom unique de la source de données
            data_source: Source de données à enregistrer
        """
        self.data_sources[name] = data_source
        logger.debug(f"Source de données '{name}' enregistrée dans l'interface")

    def get_data_source(self, name: str) -> Any:
        """
        Récupère une source de données enregistrée dans l'interface.

        Args:
            name: Nom de la source de données à récupérer

        Returns:
            La source de données enregistrée

        Raises:
            KeyError: Si la source de données n'est pas enregistrée
        """
        if name not in self.data_sources:
            raise KeyError(f"Source de données '{name}' non enregistrée dans l'interface")

        return self.data_sources[name]

    def register_metric(self, name: str, metric: Any) -> None:
        """
        Enregistre une métrique dans l'interface.

        Args:
            name: Nom unique de la métrique
            metric: Métrique à enregistrer
        """
        self.metrics[name] = metric
        logger.debug(f"Métrique '{name}' enregistrée dans l'interface")

    def get_metric(self, name: str) -> Any:
        """
        Récupère une métrique enregistrée dans l'interface.

        Args:
            name: Nom de la métrique à récupérer

        Returns:
            La métrique enregistrée

        Raises:
            KeyError: Si la métrique n'est pas enregistrée
        """
        if name not in self.metrics:
            raise KeyError(f"Métrique '{name}' non enregistrée dans l'interface")

        return self.metrics[name]

    def set_hyperparameters(self, model_name: str, hyperparameters: Dict[str, Any]) -> None:
        """
        Définit les hyperparamètres pour un modèle.

        Args:
            model_name: Nom du modèle
            hyperparameters: Dictionnaire des hyperparamètres
        """
        self.hyperparameters[model_name] = hyperparameters
        logger.debug(f"Hyperparamètres définis pour le modèle '{model_name}'")

    def get_hyperparameters(self, model_name: str) -> Dict[str, Any]:
        """
        Récupère les hyperparamètres pour un modèle.

        Args:
            model_name: Nom du modèle

        Returns:
            Dictionnaire des hyperparamètres

        Raises:
            KeyError: Si les hyperparamètres ne sont pas définis pour ce modèle
        """
        if model_name not in self.hyperparameters:
            raise KeyError(f"Hyperparamètres non définis pour le modèle '{model_name}'")

        return self.hyperparameters[model_name]

    def set_result(self, model_name: str, metric_name: str, value: float) -> None:
        """
        Définit un résultat pour un modèle et une métrique.

        Args:
            model_name: Nom du modèle
            metric_name: Nom de la métrique
            value: Valeur du résultat
        """
        if model_name not in self.results:
            self.results[model_name] = {}

        self.results[model_name][metric_name] = value
        logger.debug(f"Résultat '{metric_name}' défini à {value} pour le modèle '{model_name}'")

    def get_result(self, model_name: str, metric_name: str) -> float:
        """
        Récupère un résultat pour un modèle et une métrique.

        Args:
            model_name: Nom du modèle
            metric_name: Nom de la métrique

        Returns:
            Valeur du résultat

        Raises:
            KeyError: Si le résultat n'est pas défini pour ce modèle et cette métrique
        """
        if model_name not in self.results:
            raise KeyError(f"Résultats non définis pour le modèle '{model_name}'")

        if metric_name not in self.results[model_name]:
            raise KeyError(f"Résultat non défini pour la métrique '{metric_name}' du modèle '{model_name}'")

        return self.results[model_name][metric_name]

    def get_best_model(self, metric_name: str, higher_is_better: bool = True) -> Tuple[str, float]:
        """
        Récupère le meilleur modèle selon une métrique.

        Args:
            metric_name: Nom de la métrique
            higher_is_better: Si True, une valeur plus élevée est meilleure

        Returns:
            Tuple (nom du meilleur modèle, valeur de la métrique)

        Raises:
            ValueError: Si aucun résultat n'est disponible pour cette métrique
        """
        best_model = None
        best_value = float('-inf') if higher_is_better else float('inf')

        for model_name, metrics in self.results.items():
            if metric_name in metrics:
                value = metrics[metric_name]

                if higher_is_better:
                    if value > best_value:
                        best_value = value
                        best_model = model_name
                else:
                    if value < best_value:
                        best_value = value
                        best_model = model_name

        if best_model is None:
            raise ValueError(f"Aucun résultat disponible pour la métrique '{metric_name}'")

        return best_model, best_value

# Créer une instance singleton
train_optimize_interface = TrainOptimizeInterface()

#############################################
# FONCTIONS DE COMPATIBILITÉ
#############################################

# Ces fonctions sont fournies pour assurer la compatibilité avec le code existant
# Elles appellent simplement les méthodes des classes correspondantes

def optimize_pytorch_memory():
    """
    Configure PyTorch pour utiliser la mémoire disponible de manière optimale.
    Cette fonction doit être appelée au début du programme.

    Returns:
        bool: True si l'optimisation a réussi, False sinon
    """
    return MemoryManager.optimize_pytorch_memory()

def cleanup_pytorch_memory():
    """
    Nettoie la mémoire PyTorch après utilisation.
    Cette fonction doit être appelée à la fin de chaque époque d'entraînement.

    Returns:
        bool: True si le nettoyage a réussi, False sinon
    """
    return MemoryManager.cleanup_pytorch_memory()

def optimize_lstm_memory(lstm_model, training_mode=False):
    """
    Optimise la mémoire utilisée par un modèle LSTM.

    Args:
        lstm_model: Le modèle LSTM à optimiser
        training_mode: Si True, le modèle est en mode entraînement et les gradients sont conservés

    Returns:
        Le modèle LSTM optimisé
    """
    return MemoryManager.optimize_lstm_memory(lstm_model, training_mode)

def get_hbp_instance(trial_id=None, config=None):
    """
    Importe et crée une instance de HybridBaccaratPredictor de manière conditionnelle.

    Args:
        trial_id: Identifiant de l'essai Optuna (optionnel)
        config: Configuration à utiliser pour l'instance HBP (optionnel)

    Returns:
        HybridBaccaratPredictor: Une instance de HybridBaccaratPredictor
    """
    return ModelProvider.get_hbp_instance(trial_id, config)

def get_calculate_uncertainty():
    """
    Importe la méthode calculate_uncertainty depuis hbp.py de manière conditionnelle.

    Returns:
        function: La méthode calculate_uncertainty
    """
    return ModelProvider.get_calculate_uncertainty()

def get_confidence_from_probabilities(probabilities, predicted_class):
    """
    Obtient la confiance pour une classe prédite à partir des probabilités.

    Args:
        probabilities: Tenseur de probabilités (après softmax)
        predicted_class: Classe prédite (indice 0-based, 0 ou 1)

    Returns:
        float: Confiance pour la classe prédite
    """
    return ConfidenceCalculator.get_confidence_from_probabilities(probabilities, predicted_class)

def train_consecutive_confidence_calculator(sequence_history, current_position, config=None):
    """
    Calcule la confiance pour les recommandations consécutives basée sur l'historique des séquences.

    Args:
        sequence_history: Historique des résultats (liste de 'banker' ou 'player')
        current_position: Position actuelle dans la séquence
        config: Configuration optionnelle

    Returns:
        Dict[str, float]: Dictionnaire contenant les métriques de confiance
    """
    return ConfidenceCalculator.train_consecutive_confidence_calculator(sequence_history, current_position, config)

#############################################
# OPTIMISATION DANS UN THREAD SÉPARÉ
#############################################

class ThreadedTrainer:
    """
    Classe pour exécuter l'entraînement principal dans un thread séparé de l'interface graphique Tkinter.
    Permet d'éviter le blocage de l'interface graphique pendant l'entraînement.
    """

    def __init__(self, trainer_instance, callback=None, error_callback=None, progress_callback=None):
        """
        Initialise l'entraîneur threadé.

        Args:
            trainer_instance: Instance de HybridBaccaratPredictor à utiliser pour l'entraînement
            callback: Fonction à appeler lorsque l'entraînement est terminé
            error_callback: Fonction à appeler en cas d'erreur
            progress_callback: Fonction à appeler pour mettre à jour la progression
        """
        self.trainer_instance = trainer_instance
        self.callback = callback
        self.error_callback = error_callback
        self.progress_callback = progress_callback
        self.thread = None
        self.stop_event = threading.Event()
        self.is_running = False
        self.result = None
        self.error = None
        self.start_time = None

    def start(self, X_lgbm, y_lgbm, X_lstm, y_lstm, config_override=None):
        """
        Démarre l'entraînement dans un thread séparé.

        Args:
            X_lgbm: Features pour LGBM
            y_lgbm: Labels pour LGBM
            X_lstm: Features pour LSTM
            y_lstm: Labels pour LSTM
            config_override: Configuration à utiliser pour l'entraînement (optionnel)

        Returns:
            bool: True si l'entraînement a démarré, False sinon
        """
        if self.is_running:
            logger.warning("L'entraînement est déjà en cours d'exécution")
            return False

        # Réinitialiser les événements et les résultats
        self.stop_event.clear()
        self.result = None
        self.error = None
        self.start_time = time.time()

        # Vérifier que l'instance d'entraînement est valide
        if not hasattr(self.trainer_instance, '_train_models_async'):
            logger.error("L'instance d'entraînement ne possède pas la méthode '_train_models_async'")
            if self.error_callback:
                self.error_callback("L'instance d'entraînement ne possède pas la méthode '_train_models_async'")
            return False

        # Démarrer le thread d'entraînement
        self.thread = threading.Thread(
            target=self._run_training,
            args=(X_lgbm, y_lgbm, X_lstm, y_lstm, config_override),
            name="ThreadedTrainer",
            daemon=True
        )
        self.thread.start()
        self.is_running = True

        logger.info("Entraînement démarré dans un thread séparé")
        return True

    def _run_training(self, X_lgbm, y_lgbm, X_lstm, y_lstm, config_override=None):
        """
        Exécute l'entraînement dans le thread.
        Cette méthode est appelée par le thread et ne doit pas être appelée directement.

        Args:
            X_lgbm: Features pour LGBM
            y_lgbm: Labels pour LGBM
            X_lstm: Features pour LSTM
            y_lstm: Labels pour LSTM
            config_override: Configuration à utiliser pour l'entraînement (optionnel)
        """
        try:
            # Configurer le flag d'arrêt dans l'instance d'entraînement
            if hasattr(self.trainer_instance, 'stop_training'):
                # Créer une propriété dynamique qui vérifie l'état du stop_event
                self.trainer_instance.stop_training = False

                # Sauvegarder la méthode originale
                original_get_stop_training = None
                if hasattr(type(self.trainer_instance), 'stop_training') and isinstance(type(self.trainer_instance).stop_training, property):
                    original_get_stop_training = type(self.trainer_instance).stop_training.fget

                # Créer une fonction de vérification qui combine le stop_event et le flag stop_training
                def check_stop_training(self_instance):
                    return self_instance.stop_training or self.stop_event.is_set()

                # Remplacer temporairement la propriété stop_training
                if original_get_stop_training:
                    type(self.trainer_instance).stop_training = property(check_stop_training)

            # Configurer le callback de progression
            # Au lieu de remplacer la méthode _update_progress, nous allons utiliser
            # un mécanisme d'observation pour éviter la récursion infinie
            self._original_update_progress = None

            if self.progress_callback and hasattr(self.trainer_instance, '_update_progress'):
                # Sauvegarder la référence à la méthode originale sans la remplacer
                self._original_update_progress = self.trainer_instance._update_progress

                # Créer un observateur qui sera appelé par _train_models_async
                def observe_progress(progress, message):
                    # Appeler notre callback avec les mêmes paramètres
                    # sans créer de boucle de récursion
                    if self.progress_callback:
                        self.progress_callback(progress, message)

                # Ajouter l'observateur comme attribut de l'instance d'entraînement
                # pour qu'il puisse être appelé directement par _train_models_async
                self.trainer_instance._progress_observer = observe_progress

            # Exécuter l'entraînement
            logger.info("Début de l'entraînement principal...")
            self.trainer_instance._train_models_async(
                X_lgbm, y_lgbm, X_lstm, y_lstm,
                config_override=config_override if config_override else {}
            )

            # Vérifier si l'entraînement a été interrompu
            if self.stop_event.is_set():
                logger.warning("Entraînement interrompu par l'utilisateur")
                self.result = {
                    'success': False,
                    'message': "Entraînement interrompu par l'utilisateur",
                    'duration': time.time() - self.start_time
                }
            else:
                logger.info("Entraînement terminé avec succès")
                self.result = {
                    'success': True,
                    'message': "Entraînement terminé avec succès",
                    'duration': time.time() - self.start_time
                }

            # Appeler le callback si l'entraînement s'est terminé
            if self.callback:
                self.callback(self.result)

        except Exception as e:
            logger.error(f"Erreur lors de l'entraînement: {e}", exc_info=True)
            self.error = e
            self.result = {
                'success': False,
                'message': str(e),
                'duration': time.time() - self.start_time
            }

            # Appeler le callback d'erreur si une erreur s'est produite
            if self.error_callback:
                self.error_callback(e)
        finally:
            # Restaurer les méthodes originales
            if hasattr(self.trainer_instance, 'stop_training') and original_get_stop_training:
                type(self.trainer_instance).stop_training = property(original_get_stop_training)

            # Supprimer l'observateur de progression
            if hasattr(self.trainer_instance, '_progress_observer'):
                delattr(self.trainer_instance, '_progress_observer')

            self.is_running = False

    def stop(self):
        """
        Arrête l'entraînement en cours.

        Returns:
            bool: True si l'entraînement a été arrêté, False sinon
        """
        if not self.is_running:
            logger.warning("Aucun entraînement en cours d'exécution")
            return False

        # Définir l'événement d'arrêt
        self.stop_event.set()

        # Définir le flag d'arrêt dans l'instance d'entraînement
        if hasattr(self.trainer_instance, 'stop_training'):
            self.trainer_instance.stop_training = True

        # Attendre que le thread se termine (avec un timeout)
        if self.thread:
            self.thread.join(timeout=5.0)

            # Si le thread ne s'est pas terminé, le forcer à s'arrêter
            if self.thread.is_alive():
                logger.warning("Le thread d'entraînement ne s'est pas arrêté proprement, forçage de l'arrêt")
                # Nous ne pouvons pas forcer l'arrêt d'un thread en Python, mais nous pouvons le marquer comme terminé
                self.is_running = False

        logger.info("Entraînement arrêté")
        return True

    def get_result(self):
        """
        Récupère le résultat de l'entraînement.

        Returns:
            Le résultat de l'entraînement ou None si l'entraînement n'est pas terminé ou a échoué
        """
        return self.result

    def get_error(self):
        """
        Récupère l'erreur de l'entraînement.

        Returns:
            L'erreur de l'entraînement ou None si l'entraînement n'a pas échoué
        """
        return self.error

    def is_training_running(self):
        """
        Vérifie si l'entraînement est en cours d'exécution.

        Returns:
            bool: True si l'entraînement est en cours d'exécution, False sinon
        """
        return self.is_running

class ThreadedOptimizer:
    """
    Classe pour exécuter l'optimisation dans un thread séparé de l'interface graphique Tkinter.
    Permet d'éviter le blocage de l'interface graphique pendant l'optimisation.
    """

    def __init__(self, optimizer_class, callback=None, error_callback=None):
        """
        Initialise l'optimiseur threadé.

        Args:
            optimizer_class: Classe d'optimiseur à utiliser
            callback: Fonction à appeler lorsque l'optimisation est terminée
            error_callback: Fonction à appeler en cas d'erreur
        """
        self.optimizer_class = optimizer_class
        self.optimizer_instance = None
        self.callback = callback
        self.error_callback = error_callback
        self.thread = None
        self.stop_event = threading.Event()
        self.is_running = False
        self.result = None
        self.error = None

    def start(self, *args, **kwargs):
        """
        Démarre l'optimisation dans un thread séparé.

        Args:
            *args: Arguments à passer à l'optimiseur
            **kwargs: Arguments nommés à passer à l'optimiseur

        Returns:
            bool: True si l'optimisation a démarré, False sinon
        """
        if self.is_running:
            logger.warning("L'optimisation est déjà en cours d'exécution")
            return False

        # Réinitialiser les événements et les résultats
        self.stop_event.clear()
        self.result = None
        self.error = None

        # Créer une instance de l'optimiseur
        try:
            self.optimizer_instance = self.optimizer_class(*args, **kwargs)
        except Exception as e:
            logger.error(f"Erreur lors de la création de l'optimiseur: {e}")
            if self.error_callback:
                self.error_callback(e)
            return False

        # Démarrer le thread d'optimisation
        self.thread = threading.Thread(target=self._run_optimization)
        self.thread.daemon = True  # Le thread s'arrêtera lorsque le programme principal s'arrêtera
        self.thread.start()
        self.is_running = True

        logger.info("Optimisation démarrée dans un thread séparé")
        return True

    def _run_optimization(self):
        """
        Exécute l'optimisation dans le thread.
        Cette méthode est appelée par le thread et ne doit pas être appelée directement.
        """
        try:
            # Exécuter l'optimisation
            self.result = self.optimizer_instance.optimize(stop_event=self.stop_event)

            # Appeler le callback si l'optimisation s'est terminée normalement
            if not self.stop_event.is_set() and self.callback:
                self.callback(self.result)

        except Exception as e:
            logger.error(f"Erreur lors de l'optimisation: {e}")
            self.error = e

            # Appeler le callback d'erreur si une erreur s'est produite
            if self.error_callback:
                self.error_callback(e)
        finally:
            self.is_running = False

    def stop(self):
        """
        Arrête l'optimisation en cours.

        Returns:
            bool: True si l'optimisation a été arrêtée, False sinon
        """
        if not self.is_running:
            logger.warning("Aucune optimisation en cours d'exécution")
            return False

        # Définir l'événement d'arrêt
        self.stop_event.set()

        # Attendre que le thread se termine (avec un timeout)
        if self.thread:
            self.thread.join(timeout=5.0)

            # Si le thread ne s'est pas terminé, le forcer à s'arrêter
            if self.thread.is_alive():
                logger.warning("Le thread d'optimisation ne s'est pas arrêté proprement, forçage de l'arrêt")
                # Nous ne pouvons pas forcer l'arrêt d'un thread en Python, mais nous pouvons le marquer comme terminé
                self.is_running = False

        logger.info("Optimisation arrêtée")
        return True

    def get_result(self):
        """
        Récupère le résultat de l'optimisation.

        Returns:
            Le résultat de l'optimisation ou None si l'optimisation n'est pas terminée ou a échoué
        """
        return self.result

    def get_error(self):
        """
        Récupère l'erreur de l'optimisation.

        Returns:
            L'erreur de l'optimisation ou None si l'optimisation n'a pas échoué
        """
        return self.error

    def is_optimization_running(self):
        """
        Vérifie si l'optimisation est en cours d'exécution.

        Returns:
            bool: True si l'optimisation est en cours d'exécution, False sinon
        """
        return self.is_running
