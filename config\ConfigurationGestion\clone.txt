# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\config.py
# Lignes: 976 à 993
# Type: Méthode de la classe PredictorConfig

    def clone(self):
        """
        Crée une copie profonde de la configuration.

        Returns:
            PredictorConfig: Une nouvelle instance avec les mêmes attributs
        """
        import copy
        new_config = PredictorConfig()

        # Copier tous les attributs de l'instance actuelle vers la nouvelle instance
        for attr_name, attr_value in vars(self).items():
            setattr(new_config, attr_name, copy.deepcopy(attr_value))

        # Journaliser les attributs copiés pour le débogage
        logger.debug(f"Configuration clonée avec {len(vars(new_config))} attributs")

        return new_config