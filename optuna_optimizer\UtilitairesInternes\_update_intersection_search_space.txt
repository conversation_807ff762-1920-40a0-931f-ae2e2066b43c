# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 798 à 829
# Type: Méthode de la classe DynamicRangeAdjuster

    def _update_intersection_search_space(self, search_space, new_ranges):
        """
        Met à jour un espace de recherche de type IntersectionSearchSpace.

        Args:
            search_space: L'espace de recherche à mettre à jour
            new_ranges: Les nouvelles plages à appliquer
        """
        try:
            # Récupérer les espaces de recherche sous-jacents
            if hasattr(search_space, '_study_id_to_search_space'):
                # Pour les versions récentes d'Optuna
                for study_id, sub_space in search_space._study_id_to_search_space.items():
                    # Mettre à jour chaque sous-espace individuellement
                    if hasattr(sub_space, 'get') and callable(sub_space.get):
                        for param_name, (param_type, new_low, new_high) in new_ranges.items():
                            if param_name in sub_space:
                                # Mettre à jour la distribution pour ce paramètre
                                if param_type == 'float':
                                    sub_space[param_name] = optuna.distributions.FloatDistribution(low=new_low, high=new_high)
                                elif param_type == 'int':
                                    sub_space[param_name] = optuna.distributions.IntDistribution(low=int(new_low), high=int(new_high))
                                elif param_type == 'categorical' and isinstance(new_low, list):
                                    sub_space[param_name] = optuna.distributions.CategoricalDistribution(choices=new_low)

                logger.info(f"Espace de recherche IntersectionSearchSpace mis à jour avec {len(new_ranges)} paramètres")
            else:
                logger.warning("Structure interne de l'IntersectionSearchSpace non reconnue")
        except Exception as e:
            logger.error(f"Erreur lors de la mise à jour de l'IntersectionSearchSpace: {e}")
            import traceback
            logger.error(traceback.format_exc())