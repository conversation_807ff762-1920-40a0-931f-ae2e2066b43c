# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1117 à 1133
# Type: Méthode de la classe TrainOptimizeInterface

    def get_data_source(self, name: str) -> Any:
        """
        Récupère une source de données enregistrée dans l'interface.

        Args:
            name: Nom de la source de données à récupérer

        Returns:
            La source de données enregistrée

        Raises:
            KeyError: Si la source de données n'est pas enregistrée
        """
        if name not in self.data_sources:
            raise KeyError(f"Source de données '{name}' non enregistrée dans l'interface")

        return self.data_sources[name]