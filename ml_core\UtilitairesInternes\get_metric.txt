# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1146 à 1162
# Type: Méthode de la classe TrainOptimizeInterface

    def get_metric(self, name: str) -> Any:
        """
        Récupère une métrique enregistrée dans l'interface.

        Args:
            name: Nom de la métrique à récupérer

        Returns:
            La métrique enregistrée

        Raises:
            KeyError: Si la métrique n'est pas enregistrée
        """
        if name not in self.metrics:
            raise KeyError(f"Métrique '{name}' non enregistrée dans l'interface")

        return self.metrics[name]