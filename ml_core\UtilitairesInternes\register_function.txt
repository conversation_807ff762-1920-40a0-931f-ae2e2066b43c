# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 244 à 253
# Type: Méthode de la classe ModuleInterface

    def register_function(self, name: str, function: Callable) -> None:
        """
        Enregistre une fonction dans l'interface.

        Args:
            name: Nom unique de la fonction
            function: Fonction à enregistrer
        """
        self._functions[name] = function
        logger.debug(f"Fonction '{name}' enregistrée dans l'interface")