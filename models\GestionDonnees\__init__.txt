# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\models.py
# Lignes: 20 à 53
# Type: Méthode de la classe BaccaratDataset

    def __init__(self, sequences: np.ndarray, targets: np.n<PERSON><PERSON>, weights: Optional[np.ndarray] = None, sequence_positions: Optional[np.ndarray] = None):
        # Vérifications minimales pour la performance
        assert len(sequences) == len(targets), "Sequences and targets must have the same length"

        # Préchargement des données en mémoire pour un accès plus rapide
        # Conversion directe en tenseurs PyTorch avec pin_memory pour accélérer le transfert CPU->GPU
        self.sequences = torch.from_numpy(sequences).float().share_memory_()

        # Vérifier que les étiquettes sont bien des indices 0-based (0 = Player, 1 = Banker)
        unique_targets = np.unique(targets)
        if not np.all(np.isin(unique_targets, [0, 1])):
            error_msg = f"ERREUR CRITIQUE: Les étiquettes contiennent des valeurs invalides: {unique_targets}. Doivent être uniquement 0 (Player) ou 1 (Banker)."
            logging.getLogger(__name__).error(error_msg)
            raise ValueError(error_msg)

        # IMPORTANT: Utiliser UNIQUEMENT le système zero-based standard de PyTorch:
        # - 0 = Player
        # - 1 = Banker
        self.targets = torch.from_numpy(targets).long().share_memory_()

        # Pas de log de diagnostic pour éviter de surcharger les logs

        # Gestion optimisée des poids et positions
        self.has_weights = weights is not None
        self.has_sequence_positions = sequence_positions is not None

        if self.has_weights:
            self.weights = torch.from_numpy(weights).float().share_memory_()

        if self.has_sequence_positions:
            self.sequence_positions = torch.from_numpy(sequence_positions).long().share_memory_()

        # Préallouer les indices pour un accès plus rapide
        self.indices = torch.arange(len(sequences)).share_memory_()