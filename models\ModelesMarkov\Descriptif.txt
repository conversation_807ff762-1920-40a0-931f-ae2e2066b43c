DESCRIPTIF DÉTAILLÉ DES MÉTHODES - MODÈLES MARKOV
================================================================================

Ce fichier contient la description détaillée des méthodes des modèles Markov du système models.py.

ÉTAT DOCUMENTATION : PREMIÈRE VAGUE FONCTIONNELLE
- **Couverture** : Méthodes des modèles Markov documentées
- **Niveau** : Documentation fonctionnelle standardisée
- **Qualité** : 15-25 lignes par méthode minimum

DOMAINE FONCTIONNEL : MODÈLES MARKOV
- Persistance des modèles Markov
- Export et import des états
- Mise à jour global et session

================================================================================

1. export_models.txt (PersistentMarkov.export_models - EXPORT MODÈLES MARKOV)
   - Lignes 597-634 dans models.py (38 lignes)
   - FONCTION : Exporte l'état actuel des modèles Markov (global et session) avec configuration dans un format sérialisable pour sauvegarde
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VERROUILLAGE THREAD** : `with self.lock:` pour accès thread-safe aux modèles
     * **LOGGING DÉBUT** : `logger.info("Exportation des modèles Markov...")` pour traçabilité
     * **COMMENTAIRE FLOTTANT** : "Utiliser directement la valeur flottante de max_order" et "Pas besoin de conversion"
     * **COMMENTAIRE CONVERSION** : "Convertir les defaultdict en dict standard pour la sérialisation"
     * **COMMENTAIRE INDEX** : "On n'exporte que les ordres 1 à max_order (index 0 est vide)"
     * **INITIALISATION GLOBAL** : `global_export = []` liste vide pour accumulation
     * **BOUCLE GLOBAL** : `for order_model in self.global_models[1:]:` commentaire "Exclure index 0"
     * **COMPREHENSION GLOBAL** : `global_export.append({state: dict(outcomes) for state, outcomes in order_model.items()})`
     * **INITIALISATION SESSION** : `session_export = []` liste vide pour accumulation
     * **BOUCLE SESSION** : `for order_model in self.session_models[1:]:` commentaire "Exclure index 0"
     * **COMPREHENSION SESSION** : `session_export.append({state: dict(outcomes) for state, outcomes in order_model.items()})`
     * **CONSTRUCTION DICTIONNAIRE** : `export_data = {'config': {'max_order': self.max_order, 'smoothing': self.smoothing}, 'global': global_export, 'session': session_export}`
     * **LOGGING FIN** : `logger.info("Exportation Markov terminée.")` pour confirmation
     * **RETOUR DONNÉES** : `return export_data` dictionnaire complet prêt pour sérialisation
     * **CONVERSION DEFAULTDICT** : `dict(outcomes)` transforme defaultdict en dict standard pour JSON
     * **STRUCTURE COHÉRENTE** : Format identique pour global et session
   - RETOUR : Dict[str, Any] - Dictionnaire sérialisable avec config, global, session
   - UTILITÉ : Méthode essentielle pour la persistance des modèles Markov. Critique pour la sauvegarde d'état. Permet la continuité entre sessions.

2. load_models.txt (PersistentMarkov.load_models - CHARGEMENT MODÈLES MARKOV)
   - Lignes 636-800 dans models.py (165 lignes)
   - FONCTION : Charge l'état des modèles Markov depuis un dictionnaire avec validation robuste et gestion d'erreurs complète
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
     * data (Optional[Dict[str, Any]]) - Dictionnaire contenant les données à charger (format export_models)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉE** : Vérifie isinstance(data, dict) sinon retourne False
     * **VERROUILLAGE THREAD** : with self.lock pour accès thread-safe
     * **CHARGEMENT CONFIG** : Extrait 'config' avec validation max_order et smoothing
     * **CONVERSION TYPES** : Convertit float vers int pour max_order avec warnings
     * **VALIDATION PLAGES** : max_order entre 1-12, smoothing >= 0
     * **REDIMENSIONNEMENT** : Ajuste global_models et session_models si max_order change
     * **CHARGEMENT GLOBAL** : Parcourt 'global', valide format, reconstruit defaultdict
     * **CONVERSION TUPLES** : Convertit clés état en tuples pour compatibilité JSON
     * **CHARGEMENT SESSION** : Même processus que global pour modèles session
     * **GESTION ERREURS** : Try/catch avec reset('hard') en cas d'erreur majeure
     * **LOGGING DÉTAILLÉ** : Messages info/warning pour traçabilité complète
     * **VALIDATION CONTINUE** : Vérifications à chaque étape avec fallbacks
     * **RÉINITIALISATION SÉCURISÉE** : Modèles vides si données invalides
   - RETOUR : bool - True si chargement réussi, False sinon
   - UTILITÉ : Méthode critique pour la restauration d'état Markov. Essentielle pour la continuité. Robuste contre corruption de données.

3. update_global.txt (PersistentMarkov.update_global - MISE À JOUR MODÈLES GLOBAUX)
   - Lignes 498-535 dans models.py (38 lignes)
   - FONCTION : Met à jour les modèles Markov globaux avec plusieurs séquences de jeu pour apprentissage historique
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
     * sequences (List[List[str]]) - Liste de séquences de jeu avec résultats 'player'/'banker'
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉE** : Vérifie sequences non vide sinon warning et return
     * **VERROUILLAGE THREAD** : with self.lock pour accès thread-safe
     * **LOGGING DÉBUT** : logger.debug avec nombre de séquences à traiter
     * **COMPTEUR TRANSITIONS** : count = 0 pour tracking ajouts
     * **PARCOURS SÉQUENCES** : for seq in sequences avec validation isinstance(seq, list)
     * **FILTRAGE VIDES** : if not seq: continue pour ignorer séquences vides
     * **BOUCLE ORDRES** : for order in range(1, self.max_order_int + 1) pour tous ordres
     * **CONDITION LONGUEUR** : if len(seq) > order pour avoir assez d'éléments
     * **EXTRACTION ÉTAT** : state = tuple(seq[i - order : i]) pour contexte
     * **EXTRACTION OUTCOME** : outcome = seq[i] pour résultat suivant
     * **FILTRAGE VALIDES** : if outcome in ('player', 'banker') pour issues valides
     * **MISE À JOUR COMPTEURS** : self.global_models[order][state][outcome] += 1
     * **LOGGING FIN** : logger.debug avec nombre total transitions ajoutées
   - RETOUR : None (mise à jour en place)
   - UTILITÉ : Méthode essentielle pour l'apprentissage Markov historique. Critique pour la construction des modèles globaux. Permet l'accumulation de connaissances.

4. update_session.txt (PersistentMarkov.update_session - MISE À JOUR MODÈLE SESSION)
   - Lignes 537-594 dans models.py (58 lignes)
   - FONCTION : Met à jour les modèles Markov de session avec la séquence actuelle, ne traitant que le dernier coup
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
     * sequence (List[str]) - Séquence de jeu en cours avec résultats 'player'/'banker'
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION TYPE** : Vérifie isinstance(sequence, list) sinon warning et return
     * **VALIDATION VIDE** : if not sequence: return pour séquences vides
     * **GESTION ERREURS** : try/except avec logger.error pour robustesse
     * **VERROUILLAGE THREAD** : with self.lock pour accès thread-safe
     * **VALIDATION LONGUEUR** : if len(sequence) == 0: return pour double vérification
     * **EXTRACTION DERNIER** : last_outcome = sequence[-1] pour résultat récent
     * **VALIDATION OUTCOME** : Vérifie isinstance(last_outcome, str) et in ('player', 'banker')
     * **BOUCLE ORDRES** : for order in range(1, self.max_order_int + 1) pour tous ordres
     * **CONDITION LONGUEUR** : if len(sequence) > order pour contexte suffisant
     * **EXTRACTION ÉTAT** : state_elements = sequence[-(order + 1) : -1] pour contexte précédent
     * **VALIDATION ÉTAT** : Vérifie tous éléments sont str et in ('player', 'banker')
     * **CONSTRUCTION TUPLE** : state = tuple(state_elements) pour clé dictionnaire
     * **MISE À JOUR COMPTEUR** : self.session_models[order][state][last_outcome] += 1
     * **LOGGING CONDITIONNEL** : Messages debug commentés pour performance
   - RETOUR : None (mise à jour en place)
   - UTILITÉ : Méthode critique pour l'apprentissage Markov temps réel. Essentielle pour adaptation session courante. Permet ajustement dynamique.

5. reset.txt (PersistentMarkov.reset - RÉINITIALISATION MODÈLES MARKOV)
   - Lignes 802-821 dans models.py (20 lignes)
   - FONCTION : Réinitialise les compteurs des modèles Markov selon le type spécifié (soft/hard)
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
     * reset_type (str) - Type réinitialisation: 'soft' (session) ou 'hard' (global+session)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VERROUILLAGE THREAD** : with self.lock pour accès thread-safe
     * **CALCUL TAILLE** : max_order_int = int(self.max_order) + 1 si fractionnaire
     * **RESET HARD** : if reset_type == 'hard' réinitialise global_models
     * **CRÉATION GLOBAL** : [defaultdict(lambda: defaultdict(int)) for _ in range(max_order_int + 1)]
     * **LOGGING GLOBAL** : logger.info("Modèles Markov globaux réinitialisés (hard reset)")
     * **RESET SESSION** : Toujours réinitialise session_models (soft et hard)
     * **CRÉATION SESSION** : Même structure que global avec defaultdict imbriqués
     * **LOGGING SESSION** : logger.info avec type de reset pour traçabilité
     * **STRUCTURE COHÉRENTE** : Garantit taille max_order_int + 1 pour tous ordres
   - RETOUR : None (réinitialisation en place)
   - UTILITÉ : Méthode essentielle pour nettoyage des modèles. Critique pour nouveau démarrage. Permet gestion flexible de la persistance.

6. get_combined_probs.txt (PersistentMarkov.get_combined_probs - CALCUL PROBABILITÉS COMBINÉES)
   - Lignes 229-430 dans models.py (202 lignes)
   - FONCTION : Combine probabilités globales et session pour prédire le prochain coup avec lissage, pondération et analyse contextuelle
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
     * sequence (List[str]) - Séquence de jeu actuelle jusqu'au coup t-1
     * global_weight (float, optional) - Poids modèles globaux (défaut: config ou 0.15)
     * context_weight (float, optional) - Poids contexte local (défaut: config ou 0.8)
     * decay_factor (float, optional) - Facteur décroissance ordres (défaut: config ou 0.95)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉE** : Vérifie isinstance(sequence, list) et éléments in ('player', 'banker')
     * **FALLBACK 50/50** : Retourne {'player': 0.5, 'banker': 0.5} si données insuffisantes
     * **RÉCUPÉRATION CONFIG** : Import PredictorConfig pour paramètres par défaut
     * **CLAMPING PARAMÈTRES** : max(0.0, min(1.0, value)) pour tous poids
     * **CALCUL POIDS ORDRES** : order_weights[order] = 1.0 / (order ** order_weight_exponent)
     * **NORMALISATION ORDRES** : norm_order_weights avec total_order_weight
     * **ANALYSE CONTEXTE** : context_factor = self._analyze_sequence_context(sequence)
     * **BOUCLE ORDRES** : for order in range(1, self.max_order_int + 1)
     * **EXTRACTION ÉTAT** : state = tuple(sequence[-order:]) pour contexte
     * **PROBABILITÉS GLOBALES** : Lissage avec (counts + smoothing) / (total + smoothing * num_classes)
     * **PROBABILITÉS SESSION** : Même calcul que global pour modèles session
     * **COMBINAISON PONDÉRÉE** : adaptive_global_weight * global + adaptive_session_weight * session
     * **ADAPTATION DYNAMIQUE** : Poids ajustés selon context_factor et decay_factor
     * **ACCUMULATION** : final_probs += combined_probs * adaptive_order_weight
     * **NORMALISATION FINALE** : Division par accumulated_weight avec gestion missing_weight
   - RETOUR : Dict[str, float] - Probabilités {'player': float, 'banker': float} normalisées
   - UTILITÉ : Méthode centrale pour prédiction Markov. Essentielle pour intelligence du système. Critique pour combinaison sophistiquée des modèles.

7. _analyze_sequence_context.txt (PersistentMarkov._analyze_sequence_context - ANALYSE CONTEXTE SÉQUENCE)
   - Lignes 432-496 dans models.py (65 lignes)
   - FONCTION : Analyse le contexte de la séquence pour adapter les poids des modèles selon volatilité et streaks
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
     * sequence (List[str]) - Séquence de résultats à analyser
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉE** : Vérifie isinstance(sequence, list) sinon retourne 0.5
     * **SEUIL LONGUEUR** : if len(sequence) < 10 retourne 0.5 (données insuffisantes)
     * **VALIDATION ÉLÉMENTS** : Vérifie tous éléments str et in ('player', 'banker')
     * **GESTION ERREURS** : try/except global avec logger.error et retour 0.5
     * **CALCUL VOLATILITÉ** : recent_seq = sequence[-min(10, len(sequence)):]
     * **COMPTAGE ALTERNANCES** : for i in range(1, len(recent_seq)) si recent_seq[i] != recent_seq[i-1]
     * **NORMALISATION VOLATILITÉ** : volatility = alternances / (len(recent_seq) - 1)
     * **DÉTECTION STREAK** : current_streak = 1 puis boucle arrière pour compter identiques
     * **NORMALISATION STREAK** : streak_factor = min(current_streak / 10, 1.0)
     * **COMBINAISON FACTEURS** : context_factor = (volatility * 0.7) + (streak_factor * 0.3)
     * **CLAMPING FINAL** : max(0.0, min(1.0, context_factor)) pour garantir [0,1]
     * **INTERPRÉTATION** : 0=stable (global pertinent), 1=volatile (session pertinent)
   - RETOUR : float - Facteur contextuel entre 0 et 1
   - UTILITÉ : Méthode essentielle pour adaptation intelligente des modèles. Critique pour pondération dynamique. Permet optimisation selon contexte de jeu.

8. __init___2.txt (PersistentMarkov.__init__ - INITIALISATION GESTIONNAIRE MARKOV)
   - Lignes 195-227 dans models.py (33 lignes)
   - FONCTION : Initialise le gestionnaire de modèles Markov avec validation des paramètres et création des structures de données
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
     * max_order (int) - Ordre maximal des chaînes Markov (1-12)
     * smoothing (float) - Facteur de lissage Laplace (alpha) pour éviter probabilités nulles
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION TYPE** : if isinstance(max_order, float) conversion avec warning
     * **CLAMPING ORDRE** : self.max_order = max(1, min(12, max_order)) pour plage valide
     * **VALIDATION SMOOTHING** : Vérifie isinstance(smoothing, (int, float)) et >= 0
     * **EXCEPTION SMOOTHING** : raise ValueError si smoothing invalide
     * **ASSIGNATION SMOOTHING** : self.smoothing: float = smoothing avec type hint
     * **ORDRE ENTIER** : self.max_order_int = self.max_order pour structures données
     * **CRÉATION GLOBAL** : [defaultdict(lambda: defaultdict(int)) for _ in range(max_order_int + 1)]
     * **CRÉATION SESSION** : Même structure que global avec defaultdict imbriqués
     * **VERROUILLAGE THREAD** : self.lock: threading.RLock = threading.RLock() pour thread-safety
     * **LOGGING INIT** : logger.info avec max_order, max_order_int, smoothing
     * **LOGGING TAILLES** : logger.info avec len(global_models), len(session_models)
     * **INDEX 0 INUTILISÉ** : Structure où index correspond à ordre (0 non utilisé)
   - RETOUR : None (constructeur)
   - UTILITÉ : Méthode fondamentale pour créer gestionnaire Markov. Essentielle pour initialisation robuste. Critique pour structures thread-safe.

3. update_global.txt (PersistentMarkov.update_global - MISE À JOUR MODÈLES GLOBAUX)
   - Lignes 498-535 dans models.py (38 lignes)
   - FONCTION : Met à jour les modèles Markov globaux avec plusieurs séquences de jeu pour apprentissage historique
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
     * sequences (List[List[str]]) - Liste de séquences de jeu avec résultats 'player'/'banker'
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉE** : Vérifie sequences non vide sinon warning et return
     * **VERROUILLAGE THREAD** : with self.lock pour accès thread-safe
     * **LOGGING DÉBUT** : logger.debug avec nombre de séquences à traiter
     * **COMPTEUR TRANSITIONS** : count = 0 pour tracking ajouts
     * **PARCOURS SÉQUENCES** : for seq in sequences avec validation isinstance(seq, list)
     * **FILTRAGE VIDES** : if not seq: continue pour ignorer séquences vides
     * **BOUCLE ORDRES** : for order in range(1, self.max_order_int + 1) pour tous ordres
     * **CONDITION LONGUEUR** : if len(seq) > order pour avoir assez d'éléments
     * **EXTRACTION ÉTAT** : state = tuple(seq[i - order : i]) pour contexte
     * **EXTRACTION OUTCOME** : outcome = seq[i] pour résultat suivant
     * **FILTRAGE VALIDES** : if outcome in ('player', 'banker') pour issues valides
     * **MISE À JOUR COMPTEURS** : self.global_models[order][state][outcome] += 1
     * **LOGGING FIN** : logger.debug avec nombre total transitions ajoutées
   - RETOUR : None (mise à jour en place)
   - UTILITÉ : Méthode essentielle pour l'apprentissage Markov historique. Critique pour la construction des modèles globaux. Permet l'accumulation de connaissances.

4. update_session.txt (PersistentMarkov.update_session - MISE À JOUR MODÈLE SESSION)
   - Lignes 537-594 dans models.py (58 lignes)
   - FONCTION : Met à jour les modèles Markov de session avec la séquence actuelle, ne traitant que le dernier coup
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
     * sequence (List[str]) - Séquence de jeu en cours avec résultats 'player'/'banker'
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION TYPE** : Vérifie isinstance(sequence, list) sinon warning et return
     * **VALIDATION VIDE** : if not sequence: return pour séquences vides
     * **GESTION ERREURS** : try/except avec logger.error pour robustesse
     * **VERROUILLAGE THREAD** : with self.lock pour accès thread-safe
     * **VALIDATION LONGUEUR** : if len(sequence) == 0: return pour double vérification
     * **EXTRACTION DERNIER** : last_outcome = sequence[-1] pour résultat récent
     * **VALIDATION OUTCOME** : Vérifie isinstance(last_outcome, str) et in ('player', 'banker')
     * **BOUCLE ORDRES** : for order in range(1, self.max_order_int + 1) pour tous ordres
     * **CONDITION LONGUEUR** : if len(sequence) > order pour contexte suffisant
     * **EXTRACTION ÉTAT** : state_elements = sequence[-(order + 1) : -1] pour contexte précédent
     * **VALIDATION ÉTAT** : Vérifie tous éléments sont str et in ('player', 'banker')
     * **CONSTRUCTION TUPLE** : state = tuple(state_elements) pour clé dictionnaire
     * **MISE À JOUR COMPTEUR** : self.session_models[order][state][last_outcome] += 1
     * **LOGGING CONDITIONNEL** : Messages debug commentés pour performance
   - RETOUR : None (mise à jour en place)
   - UTILITÉ : Méthode critique pour l'apprentissage Markov temps réel. Essentielle pour adaptation session courante. Permet ajustement dynamique.

5. reset.txt (PersistentMarkov.reset - RÉINITIALISATION MODÈLES MARKOV)
   - Lignes 802-821 dans models.py (20 lignes)
   - FONCTION : Réinitialise les compteurs des modèles Markov selon le type spécifié (soft/hard)
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
     * reset_type (str) - Type réinitialisation: 'soft' (session) ou 'hard' (global+session)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VERROUILLAGE THREAD** : with self.lock pour accès thread-safe
     * **CALCUL TAILLE** : max_order_int = int(self.max_order) + 1 si fractionnaire
     * **RESET HARD** : if reset_type == 'hard' réinitialise global_models
     * **CRÉATION GLOBAL** : [defaultdict(lambda: defaultdict(int)) for _ in range(max_order_int + 1)]
     * **LOGGING GLOBAL** : logger.info("Modèles Markov globaux réinitialisés (hard reset)")
     * **RESET SESSION** : Toujours réinitialise session_models (soft et hard)
     * **CRÉATION SESSION** : Même structure que global avec defaultdict imbriqués
     * **LOGGING SESSION** : logger.info avec type de reset pour traçabilité
     * **STRUCTURE COHÉRENTE** : Garantit taille max_order_int + 1 pour tous ordres
   - RETOUR : None (réinitialisation en place)
   - UTILITÉ : Méthode essentielle pour nettoyage des modèles. Critique pour nouveau démarrage. Permet gestion flexible de la persistance.

6. get_combined_probs.txt (PersistentMarkov.get_combined_probs - CALCUL PROBABILITÉS COMBINÉES)
   - Lignes 229-430 dans models.py (202 lignes)
   - FONCTION : Combine probabilités globales et session pour prédire le prochain coup avec lissage, pondération et analyse contextuelle
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
     * sequence (List[str]) - Séquence de jeu actuelle jusqu'au coup t-1
     * global_weight (float, optional) - Poids modèles globaux (défaut: config ou 0.15)
     * context_weight (float, optional) - Poids contexte local (défaut: config ou 0.8)
     * decay_factor (float, optional) - Facteur décroissance ordres (défaut: config ou 0.95)
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉE** : Vérifie isinstance(sequence, list) et éléments in ('player', 'banker')
     * **FALLBACK 50/50** : Retourne {'player': 0.5, 'banker': 0.5} si données insuffisantes
     * **RÉCUPÉRATION CONFIG** : Import PredictorConfig pour paramètres par défaut
     * **CLAMPING PARAMÈTRES** : max(0.0, min(1.0, value)) pour tous poids
     * **CALCUL POIDS ORDRES** : order_weights[order] = 1.0 / (order ** order_weight_exponent)
     * **NORMALISATION ORDRES** : norm_order_weights avec total_order_weight
     * **ANALYSE CONTEXTE** : context_factor = self._analyze_sequence_context(sequence)
     * **BOUCLE ORDRES** : for order in range(1, self.max_order_int + 1)
     * **EXTRACTION ÉTAT** : state = tuple(sequence[-order:]) pour contexte
     * **PROBABILITÉS GLOBALES** : Lissage avec (counts + smoothing) / (total + smoothing * num_classes)
     * **PROBABILITÉS SESSION** : Même calcul que global pour modèles session
     * **COMBINAISON PONDÉRÉE** : adaptive_global_weight * global + adaptive_session_weight * session
     * **ADAPTATION DYNAMIQUE** : Poids ajustés selon context_factor et decay_factor
     * **ACCUMULATION** : final_probs += combined_probs * adaptive_order_weight
     * **NORMALISATION FINALE** : Division par accumulated_weight avec gestion missing_weight
   - RETOUR : Dict[str, float] - Probabilités {'player': float, 'banker': float} normalisées
   - UTILITÉ : Méthode centrale pour prédiction Markov. Essentielle pour intelligence du système. Critique pour combinaison sophistiquée des modèles.

7. _analyze_sequence_context.txt (PersistentMarkov._analyze_sequence_context - ANALYSE CONTEXTE SÉQUENCE)
   - Lignes 432-496 dans models.py (65 lignes)
   - FONCTION : Analyse le contexte de la séquence pour adapter les poids des modèles selon volatilité et streaks
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
     * sequence (List[str]) - Séquence de résultats à analyser
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION ENTRÉE** : Vérifie isinstance(sequence, list) sinon retourne 0.5
     * **SEUIL LONGUEUR** : if len(sequence) < 10 retourne 0.5 (données insuffisantes)
     * **VALIDATION ÉLÉMENTS** : Vérifie tous éléments str et in ('player', 'banker')
     * **GESTION ERREURS** : try/except global avec logger.error et retour 0.5
     * **CALCUL VOLATILITÉ** : recent_seq = sequence[-min(10, len(sequence)):]
     * **COMPTAGE ALTERNANCES** : for i in range(1, len(recent_seq)) si recent_seq[i] != recent_seq[i-1]
     * **NORMALISATION VOLATILITÉ** : volatility = alternances / (len(recent_seq) - 1)
     * **DÉTECTION STREAK** : current_streak = 1 puis boucle arrière pour compter identiques
     * **NORMALISATION STREAK** : streak_factor = min(current_streak / 10, 1.0)
     * **COMBINAISON FACTEURS** : context_factor = (volatility * 0.7) + (streak_factor * 0.3)
     * **CLAMPING FINAL** : max(0.0, min(1.0, context_factor)) pour garantir [0,1]
     * **INTERPRÉTATION** : 0=stable (global pertinent), 1=volatile (session pertinent)
   - RETOUR : float - Facteur contextuel entre 0 et 1
   - UTILITÉ : Méthode essentielle pour adaptation intelligente des modèles. Critique pour pondération dynamique. Permet optimisation selon contexte de jeu.

8. __init___2.txt (PersistentMarkov.__init__ - INITIALISATION GESTIONNAIRE MARKOV)
   - Lignes 195-227 dans models.py (33 lignes)
   - FONCTION : Initialise le gestionnaire de modèles Markov avec validation des paramètres et création des structures de données
   - PARAMÈTRES :
     * self - Instance de PersistentMarkov
     * max_order (int) - Ordre maximal des chaînes Markov (1-12)
     * smoothing (float) - Facteur de lissage Laplace (alpha) pour éviter probabilités nulles
   - FONCTIONNEMENT DÉTAILLÉ :
     * **VALIDATION TYPE** : if isinstance(max_order, float) conversion avec warning
     * **CLAMPING ORDRE** : self.max_order = max(1, min(12, max_order)) pour plage valide
     * **VALIDATION SMOOTHING** : Vérifie isinstance(smoothing, (int, float)) et >= 0
     * **EXCEPTION SMOOTHING** : raise ValueError si smoothing invalide
     * **ASSIGNATION SMOOTHING** : self.smoothing: float = smoothing avec type hint
     * **ORDRE ENTIER** : self.max_order_int = self.max_order pour structures données
     * **CRÉATION GLOBAL** : [defaultdict(lambda: defaultdict(int)) for _ in range(max_order_int + 1)]
     * **CRÉATION SESSION** : Même structure que global avec defaultdict imbriqués
     * **VERROUILLAGE THREAD** : self.lock: threading.RLock = threading.RLock() pour thread-safety
     * **LOGGING INIT** : logger.info avec max_order, max_order_int, smoothing
     * **LOGGING TAILLES** : logger.info avec len(global_models), len(session_models)
     * **INDEX 0 INUTILISÉ** : Structure où index correspond à ordre (0 non utilisé)
   - RETOUR : None (constructeur)
   - UTILITÉ : Méthode fondamentale pour créer gestionnaire Markov. Essentielle pour initialisation robuste. Critique pour structures thread-safe.