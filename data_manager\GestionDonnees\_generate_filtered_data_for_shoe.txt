# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\data_manager.py
# Lignes: 87 à 190
# Type: Méthode de la classe BaccaratSequenceManager

    def _generate_filtered_data_for_shoe(self,
                                        shoe_pb_sequence: List[str],
                                        game_index_offset: int # Pour calculer l'index global
                                        ) -> Tuple[List, List, List, List, List]:
        """
        (Méthode interne) Génère les features (LGBM, LSTM), labels (y), séquences préfixes,
        et indices d'origine pour UN SEUL sabot, en appliquant le filtre min_target_hand_index.
        MODIFIÉ: Garantit que toutes les manches 31-60 sont utilisées pour l'entraînement.
        Utilise une fenêtre adaptative pour les modèles LGBM et LSTM.

        Args:
            shoe_pb_sequence (List[str]): Séquence P/B ('player'/'banker') du sabot.
            game_index_offset (int): Index global du premier coup potentiel de ce sabot.

        Returns:
            tuple: Listes contenant [X_lgbm], [y], [X_lstm], [prefix_seq], [origin_idx] pour ce sabot.
        """
        X_lgbm_list, y_list, X_lstm_list, prefix_list, origin_list = [], [], [], [], []
        game_len = len(shoe_pb_sequence)

        # MODIFICATION: Vérifier si ce sabot a 60 manches (sans log pour éviter de surcharger les logs)
        if game_len != 60:
            self.logger.warning(f"Sabot de longueur {game_len} != 60 détecté (offset={game_index_offset})")

        # Nous avons besoin d'au moins 2 éléments pour générer des features
        # (1 pour l'historique et 1 pour la prédiction)
        if game_len < 2:
            self.logger.warning(f"Sabot trop court ({game_len} < 2), ignoré.")
            return X_lgbm_list, y_list, X_lstm_list, prefix_list, origin_list

        # MODIFICATION: Définir les limites des manches cibles (31-60)
        target_round_min = 31
        target_round_max = 60

        # Itérer sur les indices des coups CIBLES possibles dans ce sabot
        # On commence à l'index 1 (pour avoir au moins 1 élément d'historique)
        for i in range(1, game_len):
            # 'i' est l'index (0-based) de la main à prédire (la CIBLE y)
            # La position 1-indexée est i+1

            # --- >>> FILTRE PRINCIPAL <<< ---
            # MODIFICATION: Toujours traiter les manches 31-60 (positions 1-indexées)
            position_1_indexed = i + 1
            is_target_round = (position_1_indexed >= target_round_min and position_1_indexed <= target_round_max)

            if i >= self.min_target_hand_index or is_target_round:  # MODIFICATION: Ajouter la condition is_target_round
                # Séquence d'entrée pour le générateur de features (jusqu'à i-1)
                # Utilise toute la séquence disponible jusqu'à i-1 (fenêtre adaptative)
                input_sequence = shoe_pb_sequence[:i]
                actual_outcome = shoe_pb_sequence[i] # La cible à l'index i

                # Appeler la fonction de génération de features fournie
                features_lgbm, features_lstm_np = self.hybrid_feature_creator(input_sequence)

                # --- Validation des features générées ---
                # Utiliser self.lgbm_feature_count pour la validation
                valid_lgbm = (features_lgbm is not None and len(features_lgbm) == self.lgbm_feature_count)

                # Utiliser maintenant la nouvelle propriété : self.lstm_feature_count
                valid_lstm = (features_lstm_np is not None and features_lstm_np.shape == self.lstm_expected_shape)

                if valid_lgbm and valid_lstm:
                    # IMPORTANT: Utiliser UNIQUEMENT le système zero-based standard de PyTorch:
                    # - 0 = Player
                    # - 1 = Banker
                    label = 1 if actual_outcome == 'banker' else 0
                    global_origin_index = game_index_offset + i # Index global approximatif

                    # Nous ne loggons plus les conversions d'étiquettes ni les manches cibles individuellement
                    # pour éviter de surcharger les logs

                    X_lgbm_list.append(features_lgbm)
                    y_list.append(label)
                    X_lstm_list.append(features_lstm_np)
                    prefix_list.append(input_sequence[:]) # Copie du préfixe
                    origin_list.append(global_origin_index)
                else:
                    # Log l'échec si la génération ou la validation échoue
                    reason = []
                    if features_lgbm is None: reason.append("LGBM feats None")
                    elif not valid_lgbm: reason.append(f"LGBM len {len(features_lgbm)}!={self.lgbm_feature_count}")
                    if features_lstm_np is None: reason.append("LSTM feats None")
                    elif not valid_lstm: reason.append(f"LSTM shape {features_lstm_np.shape}!={self.lstm_expected_shape}")

                    # Conserver uniquement le log d'erreur pour les manches cibles (important pour le débogage)
                    if is_target_round:
                        self.logger.warning(f"Manche cible {position_1_indexed} (31-60) ignorée: {', '.join(reason)}")
                    else:
                        self.logger.debug(f"  Sabot Idx~{game_index_offset}, Coup Idx {i}: Ignoré ({', '.join(reason)})")

            # else: Coup filtré car i < min_target_hand_index et pas dans la plage 31-60

        # Vérifier si toutes les manches 31-60 ont été traitées (log concis)
        if game_len >= target_round_max:
            # Calculer combien de manches cibles ont été traitées
            target_indices = [i for i in range(len(origin_list)) if (origin_list[i] - game_index_offset + 1) >= target_round_min and (origin_list[i] - game_index_offset + 1) <= target_round_max]
            target_count = len(target_indices)
            expected_target_count = target_round_max - target_round_min + 1

            # Log uniquement si des manches sont manquantes (pour réduire la verbosité)
            if target_count < expected_target_count:
                self.logger.warning(f"ATTENTION: Seulement {target_count}/{expected_target_count} manches cibles (31-60) traitées pour ce sabot")

        return X_lgbm_list, y_list, X_lstm_list, prefix_list, origin_list