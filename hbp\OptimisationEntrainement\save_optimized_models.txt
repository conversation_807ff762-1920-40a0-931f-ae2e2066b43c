# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\hbp.py
# Lignes: 4905 à 5002
# Type: Méthode de la classe HybridBaccaratPredictor

    def save_optimized_models(self, params_file_path: str) -> bool:
        """
        Sauvegarde les modèles entraînés avec les hyperparamètres optimisés.
        Cette fonction est spécifiquement conçue pour être utilisée après une optimisation Optuna.

        Args:
            params_file_path (str): Chemin vers le fichier JSON contenant les paramètres optimisés

        Returns:
            bool: True si la sauvegarde a réussi, False sinon
        """
        import json
        import os
        from utils import apply_params_to_config

        if self.is_training or self.is_fast_updating:
            logger.warning("Impossible de sauvegarder les modèles optimisés pendant l'entraînement ou la mise à jour rapide.")
            if self.is_ui_available():
                messagebox.showwarning("Action Impossible", "Veuillez arrêter tout processus ML avant de sauvegarder les modèles optimisés.")
            return False

        # Vérifier que le fichier de paramètres existe
        if not os.path.exists(params_file_path):
            logger.error(f"Le fichier de paramètres optimisés n'existe pas: {params_file_path}")
            if self.is_ui_available():
                messagebox.showerror("Erreur", f"Le fichier de paramètres optimisés n'existe pas:\n{params_file_path}")
            return False

        try:
            # Charger les paramètres optimisés
            with open(params_file_path, 'r', encoding='utf-8') as f:
                optimized_params = json.load(f)

            logger.info(f"Paramètres optimisés chargés depuis {params_file_path}")

            # Créer une copie de la configuration actuelle
            from config import PredictorConfig
            optimized_config = PredictorConfig()

            # Appliquer les paramètres optimisés à la configuration
            if not apply_params_to_config(optimized_config, optimized_params.get('params', {})):
                logger.error("Échec de l'application des paramètres optimisés à la configuration")
                if self.is_ui_available():
                    messagebox.showerror("Erreur", "Échec de l'application des paramètres optimisés à la configuration")
                return False

            # Sauvegarder la configuration optimisée
            config_save_path = os.path.join(os.path.dirname(params_file_path), "optimized_config.json")
            with open(config_save_path, 'w', encoding='utf-8') as f:
                # Sauvegarder uniquement les attributs qui ne commencent pas par '_'
                config_dict = {k: v for k, v in optimized_config.__dict__.items() if not k.startswith('_')}
                json.dump(config_dict, f, indent=2)

            logger.info(f"Configuration optimisée sauvegardée dans {config_save_path}")

            # Créer un nom de fichier pour la sauvegarde des modèles
            models_save_dir = MODEL_SAVE_DIR  # Utiliser la constante globale
            os.makedirs(models_save_dir, exist_ok=True)

            # Utiliser le score comme partie du nom de fichier
            score = optimized_params.get('score', 0.0)
            models_save_path = os.path.join(
                models_save_dir,
                f"optimized_models_score_{score:.4f}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.joblib"
            )

            # Mettre à jour la configuration actuelle avec les paramètres optimisés
            original_config = self.config
            self.config = optimized_config

            # Sauvegarder les modèles avec la configuration optimisée
            if self.is_ui_available():
                self._update_progress(10, f"Sauvegarde des modèles optimisés...")

            save_success = self._perform_save(models_save_path)

            # Restaurer la configuration originale
            self.config = original_config

            if save_success:
                logger.info(f"Modèles optimisés sauvegardés avec succès dans {models_save_path}")
                if self.is_ui_available():
                    self._update_progress(100, f"Sauvegarde terminée: {os.path.basename(models_save_path)}")
                    messagebox.showinfo("Sauvegarde Réussie",
                                       f"Les modèles optimisés ont été sauvegardés avec succès dans:\n{models_save_path}\n\n"
                                       f"Configuration optimisée sauvegardée dans:\n{config_save_path}")
                return True
            else:
                logger.error("Échec de la sauvegarde des modèles optimisés")
                if self.is_ui_available():
                    self._update_progress(0, "Erreur sauvegarde.")
                return False

        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde des modèles optimisés: {e}", exc_info=True)
            if self.is_ui_available():
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde des modèles optimisés:\n{str(e)}")
            return False