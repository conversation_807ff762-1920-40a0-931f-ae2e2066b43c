# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 14067 à 14086
# Type: Méthode de la classe MetaOptimizer

    def _get_param_range(self, param_name):
        """
        Récupère la plage de valeurs pour un paramètre.

        Args:
            param_name: Nom du paramètre

        Returns:
            tuple: (min_val, max_val) ou None si non disponible
        """
        # Vérifier si le paramètre est dans l'espace de recherche
        if hasattr(self, 'config') and self.config is not None:
            search_space = getattr(self.config, 'optuna_search_space', {})
            if param_name in search_space:
                param_type, *param_args = search_space[param_name]

                if param_type in ['int', 'float']:
                    return param_args[0], param_args[1]

        return None