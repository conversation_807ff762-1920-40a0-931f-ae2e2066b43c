# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 9534 à 9556
# Type: Méthode de la classe OptunaOptimizer

                    def calculate_difficulty_batch(batch_indices):
                        batch_difficulties = []
                        with torch.no_grad():
                            for i in batch_indices:
                                # Obtenir la séquence
                                sequence = X_lstm_train_tensor[i].unsqueeze(0).to(device)
                                # Faire une prédiction initiale
                                outputs = lstm_model(sequence)
                                probabilities = torch.nn.functional.softmax(outputs, dim=1)
                                # Obtenir la confiance pour la classe correcte
                                target_class = y_train_tensor[i].item()  # Classe cible en indice 0-based (0 ou 1)

                                # Vérifier que l'indice est valide (0 ou 1)
                                if target_class < 0 or target_class > 1:
                                    # Utiliser une valeur par défaut sécurisée
                                    target_class = 0

                                # Utiliser notre fonction adaptée pour obtenir la confiance
                                confidence = get_confidence_from_probabilities(probabilities, target_class)
                                # Plus la confiance est élevée, plus l'exemple est facile
                                difficulty = 1.0 - confidence
                                batch_difficulties.append((i, difficulty))
                        return batch_difficulties