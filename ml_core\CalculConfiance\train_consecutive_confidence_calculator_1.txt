# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 1343 à 1355
# Type: Méthode
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

def train_consecutive_confidence_calculator(sequence_history, current_position, config=None):
    """
    Calcule la confiance pour les recommandations consécutives basée sur l'historique des séquences.

    Args:
        sequence_history: Historique des résultats (liste de 'banker' ou 'player')
        current_position: Position actuelle dans la séquence
        config: Configuration optionnelle

    Returns:
        Dict[str, float]: Dictionnaire contenant les métriques de confiance
    """
    return ConfidenceCalculator.train_consecutive_confidence_calculator(sequence_history, current_position, config)