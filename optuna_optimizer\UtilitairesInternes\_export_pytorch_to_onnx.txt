# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 2858 à 2935
# Type: Méthode de la classe OptunaOptimizer

    def _export_pytorch_to_onnx(self, model, output_path, input_shape=None, input_names=None, output_names=None,
                              opset_version=12, dynamic_axes=None):
        """
        Exporte un modèle PyTorch au format ONNX.

        Args:
            model: Modèle PyTorch à exporter
            output_path: Chemin du fichier ONNX de sortie
            input_shape: Forme des données d'entrée
            input_names: Noms des entrées du modèle
            output_names: Noms des sorties du modèle
            opset_version: Version de l'ensemble d'opérations ONNX
            dynamic_axes: Axes dynamiques pour les entrées/sorties

        Returns:
            str: Chemin du fichier ONNX généré
        """
        try:
            import torch
            import onnxruntime as rt

            # Mettre le modèle en mode évaluation
            model.eval()

            # Déterminer la forme des données d'entrée
            if input_shape is None:
                # Forme par défaut pour un vecteur de caractéristiques
                input_shape = [1, 10]  # [batch_size, n_features]

            # Créer un tenseur d'entrée factice
            dummy_input = torch.randn(*input_shape)

            # Configurer les axes dynamiques si non spécifiés
            if dynamic_axes is None and input_shape[0] == 1:
                # Par défaut, considérer la première dimension comme dynamique (batch_size)
                dynamic_axes = {input_names[0]: {0: 'batch_size'}}
                for output_name in output_names:
                    dynamic_axes[output_name] = {0: 'batch_size'}

            # Exporter le modèle
            torch.onnx.export(
                model,
                dummy_input,
                output_path,
                export_params=True,
                opset_version=opset_version,
                do_constant_folding=True,
                input_names=input_names,
                output_names=output_names,
                dynamic_axes=dynamic_axes
            )

            logger.warning(f"Modèle PyTorch exporté au format ONNX: {output_path}")

            # Vérifier que le modèle ONNX est valide
            try:
                sess = rt.InferenceSession(output_path)
                input_name = sess.get_inputs()[0].name

                # Créer des données d'entrée factices pour tester le modèle
                X_test = np.random.rand(*input_shape).astype(np.float32)

                # Exécuter une inférence de test
                _ = sess.run(None, {input_name: X_test})

                logger.warning("Validation du modèle ONNX réussie")
            except Exception as e:
                logger.warning(f"Erreur lors de la validation du modèle ONNX: {e}")

            return output_path

        except ImportError as e:
            logger.warning(f"Impossible d'exporter le modèle PyTorch en ONNX: {e}")
            logger.warning("Installez les packages requis: pip install torch onnxruntime")
            return None
        except Exception as e:
            logger.warning(f"Erreur lors de l'exportation du modèle PyTorch en ONNX: {e}")
            return None