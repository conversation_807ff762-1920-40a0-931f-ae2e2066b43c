#!/usr/bin/env python3
"""
Tests d'intégration complets du système
"""

import sys
import os
import unittest
import tempfile
import time
import json
from pathlib import Path

# Ajouter le dossier du programme au path
PROG_DIR = r"C:\Users\<USER>\Desktop\travail\plateforme\notes_travail\prog"
sys.path.insert(0, PROG_DIR)

try:
    from hbp import HybridBaccaratPredictor
    from config import PredictorConfig
except ImportError as e:
    print(f"ERREUR: Impossible d'importer modules: {e}")
    sys.exit(1)


class TestIntegrationBasic(unittest.TestCase):
    """Tests d'intégration de base"""
    
    def setUp(self):
        """Initialisation avant chaque test"""
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        
        # Charger données réelles si disponibles
        historical_file = os.path.join(PROG_DIR, "historical_data.txt")
        if os.path.exists(historical_file):
            try:
                with open(historical_file, 'r') as f:
                    content = f.read().strip()
                    self.real_data = [int(x) for x in content.split(',')]
            except:
                self.real_data = None
        else:
            self.real_data = None
            
        # Données de test par défaut
        self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 100
        
    def test_full_system_workflow(self):
        """Test workflow complet du système"""
        try:
            # 1. Initialisation
            self.assertIsNotNone(self.predictor)
            
            # 2. Chargement données
            data_to_use = self.real_data if self.real_data else self.test_data
            self.assertGreater(len(data_to_use), 100)
            
            # 3. Préparation données
            prepared_data = self.predictor.prepare_training_data(data_to_use)
            if prepared_data is not None:
                self.assertIsNotNone(prepared_data)
                
            # 4. Entraînement
            training_result = self.predictor.train_all_models(data_to_use[:1000])
            if training_result is not None:
                self.assertTrue(training_result)
                
            # 5. Prédiction
            recent_data = data_to_use[-50:]
            prediction = self.predictor.predict_hybrid(recent_data)
            
            if prediction is not None:
                self.assertIsInstance(prediction, dict)
                self.assertIn('player', prediction)
                self.assertIn('banker', prediction)
                
        except Exception as e:
            self.fail(f"Échec workflow complet: {e}")
            
    def test_system_with_real_data(self):
        """Test système avec vraies données"""
        if not self.real_data:
            self.skipTest("Pas de données réelles disponibles")
            
        try:
            # Utiliser sous-ensemble des vraies données
            train_data = self.real_data[:2000]
            test_data = self.real_data[2000:2100]
            
            # Entraîner
            training_result = self.predictor.train_all_models(train_data)
            
            # Tester prédictions
            predictions = []
            for i in range(min(10, len(test_data) - 20)):
                recent = train_data[-(20-i):] + test_data[:i+1]
                pred = self.predictor.predict_hybrid(recent[-20:])
                if pred:
                    predictions.append(pred)
                    
            # Vérifier qu'on a des prédictions
            self.assertGreater(len(predictions), 0)
            
        except Exception as e:
            self.fail(f"Échec test données réelles: {e}")


class TestIntegrationModels(unittest.TestCase):
    """Tests d'intégration entre modèles"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 200
        
    def test_models_interaction(self):
        """Test interaction entre modèles"""
        try:
            # Entraîner tous les modèles
            self.predictor.train_all_models(self.test_data)
            
            # Tester prédictions individuelles
            recent_data = self.test_data[-30:]
            
            # Prédiction LGBM
            lgbm_pred = self.predictor.predict_lgbm(recent_data)
            
            # Prédiction LSTM
            lstm_pred = self.predictor.predict_lstm(recent_data)
            
            # Prédiction Markov
            markov_pred = self.predictor.predict_markov(recent_data)
            
            # Prédiction hybride
            hybrid_pred = self.predictor.predict_hybrid(recent_data)
            
            # Vérifier cohérence
            if all(pred is not None for pred in [lgbm_pred, lstm_pred, markov_pred, hybrid_pred]):
                # Prédiction hybride devrait être combinaison des autres
                self.assertIsInstance(hybrid_pred, dict)
                
        except Exception as e:
            self.fail(f"Échec interaction modèles: {e}")
            
    def test_model_weights_consistency(self):
        """Test cohérence des poids des modèles"""
        try:
            # Vérifier poids initiaux
            initial_weights = {
                'lgbm': self.config.weight_lgbm,
                'lstm': self.config.weight_lstm,
                'markov': self.config.weight_markov
            }
            
            total_weight = sum(initial_weights.values())
            self.assertAlmostEqual(total_weight, 1.0, places=6)
            
            # Entraîner et vérifier que les poids restent cohérents
            self.predictor.train_all_models(self.test_data)
            
            # Tester prédiction avec poids
            recent_data = self.test_data[-20:]
            prediction = self.predictor.predict_hybrid(recent_data)
            
            if prediction:
                # Vérifier que la prédiction respecte les contraintes probabilistes
                if isinstance(prediction, dict) and 'player' in prediction and 'banker' in prediction:
                    total_prob = prediction['player'] + prediction['banker']
                    self.assertAlmostEqual(total_prob, 1.0, places=2)
                    
        except Exception as e:
            self.fail(f"Échec cohérence poids: {e}")


class TestIntegrationOptimization(unittest.TestCase):
    """Tests d'intégration avec optimisation"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        
        # Données limitées pour tests rapides
        self.test_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 100
        
    def test_optimization_integration(self):
        """Test intégration optimisation avec système"""
        try:
            # Tester optimisation rapide
            optimization_result = self.predictor.optimize_parameters(
                self.test_data, 
                n_trials=3, 
                timeout=60
            )
            
            if optimization_result:
                self.assertIsInstance(optimization_result, dict)
                
                # Vérifier que les paramètres optimisés sont appliqués
                if 'weight_lgbm' in optimization_result:
                    self.assertGreaterEqual(optimization_result['weight_lgbm'], 0.0)
                    self.assertLessEqual(optimization_result['weight_lgbm'], 1.0)
                    
        except Exception as e:
            self.fail(f"Échec intégration optimisation: {e}")
            
    def test_optimization_improves_performance(self):
        """Test que l'optimisation améliore les performances"""
        try:
            # Performance avant optimisation
            self.predictor.train_all_models(self.test_data[:800])
            
            test_subset = self.test_data[800:900]
            predictions_before = []
            
            for i in range(min(10, len(test_subset) - 20)):
                recent = self.test_data[780+i:800+i]
                pred = self.predictor.predict_hybrid(recent)
                if pred and isinstance(pred, dict):
                    predictions_before.append(pred)
                    
            # Optimiser
            optimization_result = self.predictor.optimize_parameters(
                self.test_data[:800], 
                n_trials=2, 
                timeout=120
            )
            
            if optimization_result:
                # Performance après optimisation
                predictions_after = []
                
                for i in range(min(10, len(test_subset) - 20)):
                    recent = self.test_data[780+i:800+i]
                    pred = self.predictor.predict_hybrid(recent)
                    if pred and isinstance(pred, dict):
                        predictions_after.append(pred)
                        
                # Vérifier qu'on a des prédictions dans les deux cas
                self.assertGreater(len(predictions_before), 0)
                self.assertGreater(len(predictions_after), 0)
                
        except Exception as e:
            self.fail(f"Échec test amélioration performance: {e}")


class TestIntegrationRobustness(unittest.TestCase):
    """Tests de robustesse du système intégré"""
    
    def setUp(self):
        self.config = PredictorConfig()
        self.predictor = HybridBaccaratPredictor(self.config)
        
    def test_system_with_corrupted_data(self):
        """Test système avec données corrompues"""
        # Données avec valeurs aberrantes
        corrupted_data = [0, 1, -1, 1, 0, 2, 1, 0, 999, 1, 0, 1] * 50
        
        try:
            # Le système devrait gérer les données corrompues
            result = self.predictor.train_all_models(corrupted_data)
            
            # Même si l'entraînement échoue, ça ne devrait pas crasher
            self.assertTrue(True)  # Si on arrive ici, pas de crash
            
        except Exception as e:
            # Vérifier que l'erreur est gérée proprement
            self.assertIsInstance(e, (ValueError, TypeError))
            
    def test_system_with_insufficient_data(self):
        """Test système avec données insuffisantes"""
        # Très peu de données
        minimal_data = [0, 1, 0, 1, 0]
        
        try:
            result = self.predictor.train_all_models(minimal_data)
            
            # Devrait gérer gracieusement le manque de données
            self.assertTrue(True)
            
        except Exception as e:
            # Erreur attendue pour données insuffisantes
            self.assertIsInstance(e, (ValueError, IndexError))
            
    def test_system_recovery_after_error(self):
        """Test récupération système après erreur"""
        try:
            # Provoquer erreur avec données invalides
            try:
                self.predictor.train_all_models([])
            except:
                pass
                
            # Vérifier que le système peut encore fonctionner
            valid_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 100
            result = self.predictor.train_all_models(valid_data)
            
            # Le système devrait pouvoir récupérer
            self.assertTrue(True)
            
        except Exception as e:
            self.fail(f"Système ne récupère pas après erreur: {e}")


class TestIntegrationEndToEnd(unittest.TestCase):
    """Tests end-to-end complets"""
    
    def setUp(self):
        self.config = PredictorConfig()
        
    def test_complete_session_simulation(self):
        """Test simulation session complète"""
        try:
            # 1. Créer nouveau prédicteur
            predictor = HybridBaccaratPredictor(self.config)
            
            # 2. Charger données historiques
            historical_file = os.path.join(PROG_DIR, "historical_data.txt")
            if os.path.exists(historical_file):
                with open(historical_file, 'r') as f:
                    content = f.read().strip()
                    historical_data = [int(x) for x in content.split(',')]
            else:
                historical_data = [0, 1, 1, 0, 1, 0, 1, 1, 0, 1] * 500
                
            # 3. Entraîner système
            training_data = historical_data[:2000]
            predictor.train_all_models(training_data)
            
            # 4. Simuler session de jeu
            session_data = historical_data[2000:2100]
            predictions_made = []
            
            for i in range(min(20, len(session_data) - 20)):
                # Données disponibles jusqu'à ce point
                available_data = training_data + session_data[:i]
                recent_data = available_data[-50:]
                
                # Faire prédiction
                prediction = predictor.predict_hybrid(recent_data)
                
                if prediction:
                    predictions_made.append({
                        'round': i,
                        'prediction': prediction,
                        'actual': session_data[i] if i < len(session_data) else None
                    })
                    
            # 5. Vérifier résultats
            self.assertGreater(len(predictions_made), 0)
            
            # Calculer précision si possible
            correct_predictions = 0
            total_predictions = 0
            
            for pred_data in predictions_made:
                if pred_data['actual'] is not None:
                    prediction = pred_data['prediction']
                    actual = pred_data['actual']
                    
                    if isinstance(prediction, dict) and 'player' in prediction and 'banker' in prediction:
                        predicted_class = 0 if prediction['player'] > prediction['banker'] else 1
                        
                        if predicted_class == actual:
                            correct_predictions += 1
                        total_predictions += 1
                        
            if total_predictions > 0:
                accuracy = correct_predictions / total_predictions
                print(f"\nPrécision session: {accuracy:.2%} ({correct_predictions}/{total_predictions})")
                
                # Précision devrait être au moins aléatoire
                self.assertGreaterEqual(accuracy, 0.0)
                self.assertLessEqual(accuracy, 1.0)
                
        except Exception as e:
            self.fail(f"Échec simulation session: {e}")


if __name__ == '__main__':
    print("=== TESTS INTÉGRATION ===")
    print(f"Répertoire programme: {PROG_DIR}")
    
    # Créer suite de tests
    suite = unittest.TestSuite()
    
    # Ajouter tests
    suite.addTest(unittest.makeSuite(TestIntegrationBasic))
    suite.addTest(unittest.makeSuite(TestIntegrationModels))
    suite.addTest(unittest.makeSuite(TestIntegrationOptimization))
    suite.addTest(unittest.makeSuite(TestIntegrationRobustness))
    suite.addTest(unittest.makeSuite(TestIntegrationEndToEnd))
    
    # Exécuter tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Afficher résultats
    print(f"\n=== RÉSULTATS INTÉGRATION ===")
    print(f"Tests exécutés: {result.testsRun}")
    print(f"Échecs: {len(result.failures)}")
    print(f"Erreurs: {len(result.errors)}")
    
    if result.failures:
        print("\nÉCHECS:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
            
    if result.errors:
        print("\nERREURS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
            
    sys.exit(0 if result.wasSuccessful() else 1)
