# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 510 à 524
# Type: Méthode de la classe MemoryManager

    def register_memory_hooks(cls, model):
        """
        Enregistre des hooks sur le modèle pour surveiller et optimiser l'utilisation de la mémoire.
        Nouvelle fonctionnalité pour améliorer la gestion de la mémoire.
        """
        def hook_fn(module, input, output):
            # Libérer la mémoire des tenseurs intermédiaires après utilisation
            if hasattr(output, 'detach'):
                output.detach_()

        # Enregistrer le hook sur tous les modules du modèle
        for module in model.modules():
            module.register_forward_hook(hook_fn)

        return model