# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\ml_core.py
# Lignes: 800 à 832
# Type: Méthode de la classe ConfidenceCalculator

    def get_confidence_from_probabilities(probabilities, predicted_class):
        """
        Obtient la confiance pour une classe prédite à partir des probabilités.
        Utilise directement les indices 0-based (0=Player, 1=Banker).

        Args:
            probabilities: Tenseur de probabilités (après softmax) ou liste/array de probabilités
            predicted_class: Classe prédite (indice 0-based, 0 ou 1)

        Returns:
            float: Confiance pour la classe prédite
        """
        # Vérifier que la classe prédite est bien un indice 0-based
        if predicted_class < 0 or predicted_class > 1:
            logger.warning(
                f"ATTENTION: Indice invalide détecté dans predicted_class: {predicted_class}. "
                f"Doit être 0 (Player) ou 1 (<PERSON>er)."
            )
            # Corriger l'indice si possible
            predicted_class = max(0, min(1, predicted_class))

        # Obtenir la confiance directement avec l'indice 0-based
        if hasattr(probabilities, 'dim') and probabilities.dim() > 1:
            # Cas d'un tenseur PyTorch 2D
            confidence = probabilities[0][predicted_class].item()
        elif hasattr(probabilities, 'item'):
            # Cas d'un tenseur PyTorch 1D
            confidence = probabilities[predicted_class].item()
        else:
            # Cas d'une liste ou d'un array numpy
            confidence = float(probabilities[predicted_class])

        return confidence