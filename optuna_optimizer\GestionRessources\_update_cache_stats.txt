# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\pro\optuna_optimizer.py
# Lignes: 10598 à 10664
# Type: Méthode de la classe OptunaOptimizer

    def _update_cache_stats(self, cache_type, key, hit=False):
        """
        Met à jour les statistiques d'utilisation du cache pour une clé donnée.

        Cette méthode permet de suivre l'utilisation du cache et d'optimiser
        la stratégie de nettoyage en fonction des patterns d'accès.

        Args:
            cache_type: Type de cache ('preprocessed', 'feature', ou 'validation')
            key: Clé de l'entrée
            hit: True si c'est un hit, False si c'est un miss
        """
        import time

        # Initialiser les statistiques d'utilisation si elles n'existent pas
        if not hasattr(self._advanced_data_cache, 'cache_stats'):
            self._advanced_data_cache['cache_stats'] = {
                'access_counts': {},  # Nombre d'accès par clé
                'last_access': {},    # Timestamp du dernier accès par clé
                'creation_time': {},  # Timestamp de création par clé
                'hit_value': {},      # Valeur de l'entrée (temps économisé) par clé
                'compressed': set()   # Ensemble des clés compressées
            }

        stats = self._advanced_data_cache['cache_stats']
        current_time = time.time()

        # Mettre à jour le compteur d'accès
        if key not in stats['access_counts']:
            stats['access_counts'][key] = 0
        stats['access_counts'][key] += 1

        # Mettre à jour le timestamp du dernier accès
        stats['last_access'][key] = current_time

        # Initialiser le timestamp de création si nécessaire
        if key not in stats['creation_time']:
            stats['creation_time'][key] = current_time

        # Mettre à jour la valeur de l'entrée (temps économisé)
        if hit:
            # Estimer le temps économisé en fonction du type de cache
            if cache_type == 'preprocessed':
                time_saved = 0.5  # 500 ms pour le prétraitement
            elif cache_type == 'feature':
                time_saved = 2.0  # 2 secondes pour l'extraction de features
            elif cache_type == 'validation':
                time_saved = 5.0  # 5 secondes pour la validation
            else:
                time_saved = 1.0  # Valeur par défaut

            if key not in stats['hit_value']:
                stats['hit_value'][key] = 0
            stats['hit_value'][key] += time_saved

        # Décompresser l'entrée si elle est compressée et qu'elle est fréquemment utilisée
        if key in stats['compressed'] and stats['access_counts'][key] >= 3:
            cache_dict = None
            if cache_type == 'preprocessed':
                cache_dict = self._advanced_data_cache.get('preprocessed_data', {})
            elif cache_type == 'feature':
                cache_dict = self._advanced_data_cache.get('feature_cache', {})
            elif cache_type == 'validation':
                cache_dict = self._advanced_data_cache.get('validation_cache', {})

            if cache_dict is not None and key in cache_dict:
                self._decompress_entry(cache_dict, key)